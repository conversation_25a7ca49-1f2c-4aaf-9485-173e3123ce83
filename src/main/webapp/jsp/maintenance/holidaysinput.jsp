
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title><fmt:message key="holidays.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">

/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */

var dateFormat = '${sessionScope.CDM.dateFormat}';
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";
	
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function disableAllButtons(){
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort(){
	updateColors();
	disableAllButtons();
}


function bodyOnLoad(){
	xl = new XLSheet("holidayList","table_2", ["String",dateFormat, "String"],"211");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("holidayList");
	
	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	
	<!--Betcy Commeneted 18/06/2008 -->
	<!--var dropBox1 = new SwSelectBox(document.forms[0].elements["holiday.id.countryCode"],document.getElementById("countryName"));-->
	var dropBox1 = new SwSelectBox(document.forms[0].elements["holiday.id.entityId"],document.getElementById("entityName"));

	 <%if (request.getAttribute(SwtConstants.ADD_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
 <%}else{ %>

		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>

	
	 <%if (request.getAttribute(SwtConstants.DEL_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
 <%}else{ %>

		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>
	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}	
		
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */

 
 		/* START: Create a select Export - Code Added by Med Amine -*/

        var headerData = [];
		var dataprovider = new Array();
		
		var newElement1 = {};
		newElement1[headerData[0]] = 'Pdf';
		dataprovider.push(newElement1);
				
		var newElement2 = {};
		newElement2[headerData[0]] = 'Excel';
		dataprovider.push(newElement2);
				
		var newElement3 = {};
		newElement3[headerData[0]] = 'Csv';
		dataprovider.push(newElement3);
		
		$("#exportReport").exportselect ({
			dataprovider: dataprovider,
			change: exportReport,
			selectedIndex:0
		  });
 
if(record()== 0)
			$("#exportReport").disabled(true);
		
		else
			$("#exportReport").disabled(false);				

}
function exportReport(){
	  var type=$("#exportReport").getSelected(0);
	  return exportData(type.toLowerCase());
}

function submitFormDelete(methodName){
	document.forms[0].method.value = methodName;
	var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
	if (yourstate==true){ 
	    document.forms[0].submit();
	}
}	
	
function submitForm(methodName){
     document.forms[0].method.value = methodName;
	 document.forms[0].submit();
}


function buildAddHolidayURL(methodName){
	var param = 'holiday.do?method='+methodName+'&entityCode=';
	    param +=document.forms[0].elements['holiday.id.entityId'].value;
	    param +='&HolidayDate=';
	    param +=document.forms[0].selectedHolidayDate.value;
	    param +='&EntityName=';
		param +=document.getElementById('entityName').innerText;
	    return param;
}

	
function onSelectTableRow(rowElement, isSelected){
	console.log("inside seleccct",rowElement.getElementsByTagName("input"));
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedHolidayDate.value = hiddenElement.value;
   
	document.forms[0].selectedCountryCode.value=rowElement.getElementsByTagName("input")[1].value;
	
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	if(menuEntityCurrGrpAccess == "0" && isSelected){
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else{
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
}
var totalcount="${totalcount}"

/* START: Code added for implementing the export options  by chinniah-Manti_348, 10-SEP-2010 */
function record()
{
return document.getElementById ("ddscrolltable").getElementsByTagName('tr').length-1;

}


function exportData(exportType) 
{

document.forms[0].method.value = 'exportHoliday';

document.forms[0].exportType.value = exportType.trim();
document.forms[0].entityId.value = document.forms[0].elements['holiday.id.entityId'].value;


if(record() > 0){
document.forms[0].submit();
}
/* End: Code added for implementing the export options  by chinniah-Manti_348, 10-SEP-2010 */
}



</script>
</head>
<form action="holiday.do">
	<input name="method" type="hidden" value="display">
	<input name="selectedHolidayDate" type="hidden" value="GBP">
	<input name="selectedCountryCode" type="hidden" value="GBP">
	<input name="totalcount" type="hidden" value="GBP">
	
	<!--START: code added for implementing export options by chinniah Mantis_348 on 10-sep-10 -->
	<input name="entityId" type="hidden" value=" ">
	<input name="exportType" type="hidden" value="">
    <input type="hidden" name="screen" id="exportDataScreen" value="HolidaysMaintenance-SmartPredict"/>
  <!--END: code added for implementing export options by chinniah Mantis_348 on 10-sep-10 -->
	<bean:define id="CDM" name="CDM"
		type="org.swallow.util.CommonDataManager" scope="session" />

	<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->
	<input name="menuAccessId" type="hidden">
	<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->

	<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
		marginheight="0"
		onLoad="ShowErrMsgWindow('${actionError}'); setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();"
		onunload="call();">

	<div id="Holiday"
		style="position: absolute; left: 20px; top: 20px; width: 460px; height: 36px; border: 2px outset;"
		color="#7E97AF">
	<div id="Holiday"
		style="position: absolute; left: 8px; top: 4px; width: 450px; height: 38;">
	<table width="450" border="0" cellpadding="0" cellspacing="0"
		height="25" style="font-family: verdana, helvetica; font-size: 9pt;">
		<tr height="24">
			<td width="56px"><b><fmt:message key="bookCode.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			
			
			<td width="120px">
		 
			<select id="holidayId" name="holiday.id.entityId"
			tabindex="1" titleKey="tooltip.selectEntityid" class="htmlTextAlpha" onchange="submitForm('showDetails')"
			style="width: 120px">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option value="${entity.value}"
					 <c:if test="${holiday.id.entityId == entity.value}">selected="selected"</c:if>>
					 ${entity.label}
				</option>
			</c:forEach>
			</select></td>
			
			
			<td width="20">&nbsp;</td>
			<td width="280"><span id="entityName" name="#request.entityName"
				class="spantext"></td>
		</tr>
	</table>
	</div>
	</div>
	<div id="Holiday" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 20px; top: 60px; width: 460px; height: 429px;">
	<div id="Holiday"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 440px; height: 25px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="438"
		border="0" cellspacing="0" cellpadding="0" height="20">
		<thead>
			<tr height="20px">
				<td width="135px" align="center" style="border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortCountryCode"/>'><b><fmt:message key="entity.general.country" /></b></td>
				<td width="180px" align="center"
					title='<fmt:message key="tooltip.sortHolidayDate"/>'><b><fmt:message key="holiday.holidayDate" /></b></td>
				<td width="120px" align="center"
					title='<fmt:message key="tooltip.sortHolidayDay"/>'><b><fmt:message key="holiday.holidayDay" /></b></td>
			</tr>
		</thead>
	</table>
	</div>



	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 458px; height:  425px; overflowY: scroll">
	<div id="Holiday"
		style="position: absolute; z-index: 99; left: 1px; top: 21px; width: 435px; height: 10px;">

	<table class="sort-table" id="holidayList" width="437" border="0"
		cellspacing="1" cellpadding="0" height="402">

	<tbody>
			<c:set var="count" value="0" />
			<c:forEach var="holiday" items="${holidayList}">
				<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
					<input type="hidden" name="holidayList" value="${holiday.holidayDate_Date}" disabled="true" />
					<td width="135px" align="center">
						<input type="hidden" name="holidayList" value="${holiday.id.countryCode}" disabled="true" />
						<c:out value="${holiday.id.countryCode}" />&nbsp;
					</td>
					<td width="180px" align="center">
						<c:out value="${holiday.holidayDate_Date}" />&nbsp;
					</td>
					<td width="120px" align="left">
						<c:out value="${holiday.holidayDay}" />&nbsp;
					</td>
				</tr>
				<c:set var="count" value="${count + 1}" />
			</c:forEach>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
     <!--START: code added for implementing export options by chinniah Mantis_348 on 10-sep-10 -->
     <div id="exholi">
	    <div id="pnlReport"
		     style="position: absolute; left: 405; top: 507px; width: 70px; height: 30px;z-index: 5; visibility: visible;">
	       <table >
		    <div id="exportReport"  style="margin-top:-100;" />
		   </table>
		 </div>
	    <div id="help" style=" position: absolute; left: 450; top: 505;">
	           <a tabindex="5" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Holiday Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a>
	</div>

<!--End: code added for implementing export options by chinniah Mantis_348 on 10-sep-10 -->
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 495px; width: 460px; height: 39px; visibility: visible;">
	<div id="Holiday"
		style="position: absolute; left: 6; top: 4; width: 220; height: 15px; visibility: visible;">
	<table width="210" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="addbutton" width="70px"></td>
			<td id="deletebutton" width="70px"></td>
			<td id="closebutton" width="70px"><a
				title='<fmt:message key="tooltip.close"/>' tabindex="4"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>

	<div
		style="position: absolute; left: 6; top: 4; width: 140; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>
			<td id="addenablebutton"><a
				title='<fmt:message key="tooltip.addNewHoliday"/>' tabindex="2"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildAddHolidayURL('add'),'','left=50,top=190,width=600,height=170,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add" /></a></td>
			<td id="adddisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.add" /></a></td>

			<td id="deleteenablebutton"><a
				title='<fmt:message key="tooltip.deleteSelHoliday"/>' tabindex="3"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitFormDelete('delete')"><fmt:message key="button.delete" /></a></td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.delete" /></a></td>

		</tr>
	</table>
	</div>
	</div>
</form>
</body>
</html>