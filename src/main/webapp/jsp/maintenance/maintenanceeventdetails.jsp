<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Maintenance Event Details - SMART-Predict</title>
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body >
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var appName = "<%=SwtUtil.appName%>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "MaintenanceEventDetails";

	function buildFacilityURL(faciltity,  methodName){
		try{
			if("AccountGroupDetail" == faciltity){
				var screenName = '';
				if("view" ==methodName){
					screenName = 'viewScreen';
				}else {
					screenName = 'addScreen';
				}
				var param = '/' + appName + '/accountGroupsPCM.do?method='+screenName
				param += "&screenName=" +methodName ;
				var 	mainWindow = openWindow (param, 'AccountGroupDetail','left=10,top=230,width=1200,height=600,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
			}else if("stopRuleAdd" == faciltity){
				var param = '/' + appName + '/stopRulesPCM.do?method=stopRuleAdd';
				var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=1100,height=725,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
			}else if("spreadProfilesAdd" == faciltity){
				var screenName = '';
				if("view" ==methodName){
					screenName = 'spreadProfilesView';
				}else {
					screenName = 'spreadProfilesAdd';
				}
				var param = '/' + appName + '/spreadProfilesPCM.do?method='+screenName;

				param += "&screenName=" + methodName;
				var mainWindow = openWindow(
						param,
						'spreadProfiles',
						'left=10,top=230,width=900,height=495,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');

			}
			else if("criticalMvtUpdateDetail" == faciltity){
				var screenName = '';
				if("view" ==methodName){
					screenName = 'subScreen';
				}else {
					screenName = 'subScreen';
				}
				var param = '/' + appName + '/criticalPaymentType.do?method='+screenName;

				param += "&screen=" + methodName;

				var 	mainWindow = openWindow (param, 'criticalPayUpdateDetail','left=10,top=230,width=1200,height=900,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');

			}
		}catch(e){
			console.log('e',e)
		}


		return false;
	}




</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>