<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.Location"%>
<html>
<head>
<title><fmt:message key="location.mainScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function bodyOnLoad()
{
    var methodGlobal ;
    setParentChildsFocus();
    setFocus(document.forms[0]);
    ShowErrMsgWindow('${actionError}');
    xl = new XLSheet("LocationDetailsList","table_2", ["String","String",],"22",false);
    xl.onsort = xl.onfilter = onFilterandSort;

    highlightTableRows("LocationDetailsList");

    var entityBox = new SwSelectBox(document.forms[0].elements["location.id.entityId"],document.getElementById("entityDesc"));
    if(menuEntityCurrGrpAccess == "0") {
	    document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	} else {
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}
    document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
    document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
}
function onSelectTableRow(rowElement, isSelected)
{
    document.forms[0].selectedLocationId.value = rowElement.cells[0].innerText;
	document.forms[0].selectedLocationName.value = rowElement.cells[1].innerText;

    var count = getCountRowsSelected(rowElement);
    if(menuEntityCurrGrpAccess == "0" && count == 1)
    {
	    document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
        document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;

    }else{
        document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
        document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
    }

}
function onFilterandSort(){
    updateColors();
	disableAllButtons();
}

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

}

function isOk() {
	document.forms[0].method.value = methodGlobal;
	document.forms[0].submit();
}

function submitForm(methodName){
	if(methodName == 'displayList') {
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	} else {
		let errmsg = '<fmt:message key="confirm.delete"/>';
		methodGlobal = methodName;
		ShowErrMsgWindowWithBtn("", errmsg, OK_CANCEL, isOk);

		// var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
		// if (yourstate==true){
			// document.forms[0].method.value = methodName;
			// document.forms[0].submit();
		//}
	}
}
function buildLocation(methodName){
document.forms[0].method.value = methodName;
document.forms[0].selectedEntityCode.value=document.getElementById("entityDesc").innerText;

   var	param = 'location.do?method='+methodName;
	param +='&selectedEntityId=';
	param +=document.forms[0].elements["location.id.entityId"].value;
	param +='&selectedLocationId=';
	param +=document.forms[0].selectedLocationId.value;
	param +='&selectedLocationName=';
	param +=document.forms[0].selectedLocationName.value;
	param +='&selectedEntityCode=';
	param +=document.forms[0].selectedEntityCode.value;

	return param;
}
</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();" onunload="call()">
<form action = "location" method = "post">

<input name="method" type="hidden" value="display">
<input name="selectedLocationId" type="hidden">
<input name="selectedEntityId" type="hidden">
<input name="selectedLocationName" type="hidden">
<input name="selectedEntityCode" type="hidden">
<input name="menuAccessId" type="hidden" >

<div id="Location" style="position:absolute; left:20px; top:20px; width:424px; height:42px; border:2px outset;" color="#7E97AF">
<div id="Location" style="position:absolute; left:8px; top:4px; width:404px; height:400;">
<table width="400" border="0" cellpadding="0" cellspacing="0" height="30">
    <tr color="black" border="0">
      <td width="38"><b><fmt:message key="entity.id"/></b></td>
      <td width="28">&nbsp;</td>
      <td width="140px">
        <select
                id="location.id.entityId"
                name="location.id.entityId"
                class="htmlTextAlpha"
                onchange="submitForm('displayList')"
                style="width:140px"
                titleKey="tooltip.selectEntity"
                tabindex="1"
            >
                <c:forEach var="entity" items="${requestScope.entities}">
                    <option value="${entity.value}"
                        <c:if test="${entity.value == location.id.entityId}">selected="selected"</c:if>>
                        ${entity.label}
                    </option>
                </c:forEach>
            </select>

      </td>
       <td width="20">&nbsp;</td>
      <td width="280">
          <span id="entityDesc" name="entityDesc" class="spantext"></span>
       </td>
     </tr>
</table>
</div>
</div>
<div id="Location" style="position:absolute; left:20px; top:67px; width:424px; height:369px; border-left:2px outset;" color="#7E97AF">
    <div id="Location" style="position:absolute;z-index:99;left:0px; top:0px; width:404px; height:10px;">
        <table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="400px" border="0" cellspacing="1" cellpadding="0"  height="20px">
            <thead>
                <tr>
                    <td width="120px" height="23px" style="border-left-width: 0px;" title='<fmt:message key="tooltip.sortLocationId"/>' align="left"><b><fmt:message key="location.locationId"/></b></td>
                    <td width="280px" height="23px" title='<fmt:message key="tooltip.sortLocationName"/>' align="left"><b><fmt:message key="location.locationName"/></b></td>
                </tr>
            </thead>
        </table>
    </div>
   <div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:420px; height:365px;overflowY:scroll">
            <div id="Location" style="position:absolute;z-index:99;left:1px; top:22px; width:403px; height:10px;">
                <table class="sort-table" id="LocationDetailsList" width="400px" border="0" cellspacing="1" cellpadding="0" height="343px">
                   <tbody>
                        <c:set var="count" value="0" scope="page" />
                        <c:forEach var="location" items="${requestScope.LocationDetailsList}">
                            <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
                                <c:set var="count" value="${count + 1}" scope="page" />
                                <input type="hidden" name="locationId" value="${location.id.locationId}" />
                                <td width="120px" align="left">${location.id.locationId}</td>
                                <td width="280px">${fn:replace(location.locationName, ' ', '&nbsp;')}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                    <tfoot><tr><td colspan="2"></td></tr></tfoot>
                </table>
            </div>
    </div>
</div>
<!-- Button and Help icon Starting -->
<div id="Location" style="position:absolute; left:375; top:450px; width:70px; height:15px; visibility:visible;">
    <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
        <tr>
        <td align="Right">
                <a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Location Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>
        </td>

            <td align="right" id="Print">
                <a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
            </td>
        </tr>
    </table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:440px; width:424px; height:39px; visibility:visible;">
<div id="Location" style="position:absolute; left:6; top:4; width:360pxpx; height:15px; visibility:visible;">
  <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20">
    <tr>
        <td width="70px" id="addbutton" ></td>
        <td id="changebutton"  width="70px"></td>
        <td id="deletebutton" width="70px"></td>
        <td id="closebutton" width="70px" title='<fmt:message key="tooltip.close"/>'>
            <a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
        </td>
    </tr>
    </table>
</div>
    <div style="position:absolute; left:6; top:4; width:440px; height:15px; visibility:hidden; display:none;">
    <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
    <tr>
        <td id="addenablebutton">
            <a  tabindex="1"  title='<fmt:message key="tooltip.addNewLocation"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildLocation('add'),'locationmaintenanceaddWindow','left=50,top=190,width=595,height=205,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
        </td>
        <td id="adddisablebutton">
            <a  class="disabled" disabled="disabled" ><fmt:message key="button.add"/></a>
        </td>
        <td id="changeenablebutton" >
            <a tabindex="2" title='<fmt:message key="tooltip.changeSelectedLocation"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildLocation('change'),'locationmaintenanceaddWindow','left=50,top=190,width=595,height=205,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
        </td>
        <td id="changedisablebutton">
            <a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
        </td>
        <td id="deleteenablebutton" >
            <a tabindex="4" title='<fmt:message key="tooltip.deleteSelectedLocation"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('delete')"><fmt:message key="button.delete"/></a>
        </td>
        <td id="deletedisablebutton">
            <a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
        </td>
    </tr>
    </table>
  </div>
</div>
</form>
</body>
</html>