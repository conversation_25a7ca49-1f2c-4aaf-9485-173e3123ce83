<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>

<head>
    <title>
     <c:if test="${'change' != requestScope.method}">
            <c:if test="${'add' == requestScope.method}">
                <fmt:message key="title.archiveadd" />
            </c:if>
        </c:if>
        <c:if test="${'add' != requestScope.method}">
            <c:if test="${'change' != requestScope.method}">
                <c:if test="${'view' == requestScope.method}">
                    <fmt:message key="title.archivechange" />
                </c:if>
            </c:if>
        </c:if>
        <c:if test="${'change' == requestScope.method}">
            <fmt:message key="title.archivechange" />
        </c:if>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <SCRIPT language="JAVASCRIPT">
        mandatoryFieldsArray = ["*"];
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
var dbFlag;

function onChangeDbLinkType(){
	
	 if(document.forms[0].elements["archive.archiveType"].value == "S"){
		 $("#dbLinkLabel b").html(schemaNameLabelValue);
		 $("#dbLinkTextInput").attr('title', schemaNameTooltipValue);
	 }else{
		 $("#dbLinkLabel b").html(dbLinkLabelValue);
		 $("#dbLinkTextInput").attr('title', dbLinkTooltipValue);
		 
	 }
	 
}

var dbLinkLabelValue = '<fmt:message key="archive.db_link"/>';
var schemaNameLabelValue = '<fmt:message key="archive.schemaName"/>';


var dbLinkTooltipValue = '<fmt:message key="tooltip.db_Link"/>';
var schemaNameTooltipValue = '<fmt:message key="tooltip.schemaName"/>';



<c:if test="${'yes' == requestScope.parentFormRefresh}">
    window.opener.document.forms[0].method.value = "display";
    window.opener.document.forms[0].submit();
    self.close();
</c:if>

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
/*Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen*/
/**
* This function is used to send AJAX request for test connection
*/
function makeConnection(){
	var oXMLHTTP = new XMLHttpRequest();
	//framing url
	var sURL = requestURL + appName+"/archive.do?method=testConnection";
	sURL = sURL + "&dbLink="+document.forms[0].elements["archive.db_link"].value;
	sURL += "&moduleId="+document.forms[0].elements["archive.moduleId"].value;
	sURL += "&archiveType="+document.forms[0].elements["archive.archiveType"].value;
	oXMLHTTP.open( "POST", sURL, false );
	//sending request
	oXMLHTTP.send();
	//getting the response
	return oXMLHTTP.responseText;
}
/**
* This function is used to test the connection
*/
function testConnection(){
	//get the connection status
	var connFlag = new String(makeConnection());
	//check connection status
	if(connFlag == "true"){
		
		ShowErrMsgWindowWithBtn("", '<fmt:message key="connection.passed"/>', null);
	}
	else{
		if(connFlag == "false")
			ShowErrMsgWindowWithBtn("", '<fmt:message key="connection.failed"/>', null);
		else
			ShowErrMsgWindowWithBtn("", connFlag, null);
	}
}


 
 
/**
* This function is used to validate current archive flag
*/
function checkFlag() {	
	//get the status of the chrrent archive check box
	var newFlag = document.forms[0].elements["archive.defaultDb"].checked;
	//Check for whether the we have to allow the current archive check box to change it's status
	if(dbFlag == true) {
		if(newFlag == false)
		{   
			document.forms[0].elements["archive.defaultDb"].checked=true;
			ShowErrMsgWindowWithBtn('', '<fmt:message key="connection.dbError"/>', null);
			
		}
	} else {
		if(newFlag == true)
		{
			document.forms[0].elements["archive.defaultDb"].checked=true;
			ShowErrMsgWindowWithBtn('', '<fmt:message key="connection.confirmDBChange"/>', null);
			
		} else {
			document.forms[0].elements["archive.defaultDb"].checked=false;
		}
	}
}
/*End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen*/
function bodyOnLoad() {	
	
		 dbFlag = document.forms[0].elements["archive.defaultDb"].checked;
		 
		 if(document.forms[0].elements["archive.archiveType"].value == ""){
			 document.forms[0].elements["archive.archiveType"].value = "D";
		 }
		 if(document.forms[0].elements["archive.archiveType"].value == "S"){
			 $("#dbLinkLabel b").html(schemaNameLabelValue);
			 $("#dbLinkTextInput").attr('title', schemaNameTooltipValue);
		 }else{
			 $("#dbLinkLabel b").html(dbLinkLabelValue);
			 $("#dbLinkTextInput").attr('title', dbLinkTooltipValue);
			 
		 }
		 
		 
		 
		
	}
	/*Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen*/
/**
* This function is used submit the form on click Ok button
* @param methodName
*/
function submitForm(methodName) {
	//validation for acrchive ID
	if(validateField(document.forms[0].elements['archive.id.archiveId'],'archive.id.archiveId','alphaNumPatWithUnderScore'))
	{
		//validation for acrchive Name
		if(validateField(document.forms[0].elements['archive.archiveName'],'archive.archiveName','alphaNumPatWithUnderScore'))
		{
/*IBouferes:Remove unnecessary validation for Mantis 2274 On 6/3/2013*/ 
				document.forms[0].method.value = methodName;
				//test for connecton and mandatory feilds
				if(validateForm(document.forms[0]) ){	 
					var connFlag = new String(makeConnection());
					if(connFlag == "false"){
						ShowErrMsgWindowWithBtn('', '<fmt:message key="connection.failed"/>', null);	
					} else {			
						document.forms[0].elements["archive.id.archiveId"].disabled="";
						document.forms[0].elements["archive.moduleId"].disabled="";
						document.forms[0].submit();  
					}	
				}  		
/*IBouferes:Remove unnecessary validation for Mantis 2274 On 6/3/2013*/ 
		} else {
			document.forms[0].elements['archive.archiveName'].focus();
		}
	} else {
		document.forms[0].elements['archive.id.archiveId'].focus();
	}	

}
/**
* This function is used to validate the form
* @param objForm
*/
function validateForm(objForm){
	//intialzing array
	var elementsRef = new Array(3); 
	//assigning values in array
	elementsRef[0] = objForm.elements["archive.id.archiveId"];
	elementsRef[1] = objForm.elements["archive.archiveName"];
	elementsRef[2] = objForm.elements["archive.db_link"];  
	//valiadation 
	return validate(elementsRef);
}
/*End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen*/
</SCRIPT>
</head>
<!-- Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen-->

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
    <form action="archive.do" onsubmit="return validate(this);">
        <input name="method" type="hidden" value="">
        <input name="selectedDefaultDb" type="hidden" value="">
        <div id="ArchiveMaintenace" color="#7E97AF" style="position: absolute; left: 20px; border: 2px outset; top: 20px; width: 400px; height: 175px;">
            <div style="position: absolute; left: 8px; top: 4px; width: 390px; height: 165px;">
                <fieldset style="width: 380px; border: 2px groove; height: 142px; left: 8px; top: 8px;">
                    <legend>
                        <fmt:message key="contact.fieldet"/>
                    </legend>
                    <table width="370" border="0" cellpadding="0" cellspacing="0" height="100">
                        <c:if test="${requestScope.method != 'add'}">
                            <tr height="25px">
                                <td width="360px">&nbsp;<b>
                                        <fmt:message key="archive.id.archiveId"/>*</b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="100px">
                                    <input type="text" name="archive.id.archiveId"
                                           title="<fmt:message key='tooltip.archiveId'/>"
                                           class="htmlTextAlpha"
                                           tabindex="1"
                                           style="width:220px;"
                                           disabled="disabled"
                                           maxlength="12"
                                           value="${archive.id.archiveId}" />
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${requestScope.method == 'add'}">
                            <tr height="25px">
                                <td width="360px">&nbsp;<b>
                                        <fmt:message key="archive.id.archiveId"/>*</b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="100px">
                                    <input type="text" name="archive.id.archiveId"
                                           title="<fmt:message key='tooltip.archiveId'/>"
                                           class="htmlTextAlpha"
                                           tabindex="1"
                                           style="width:220px;"
                                           onchange="return validateField(this,'archive.id.archiveId','alphaNumPatWithUnderScore');"
                                           maxlength="12"
                                           value="${archive.id.archiveId}" />
                                </td>
                            </tr>
                        </c:if>
                        <tr height="25px">
                            <td width="360px">&nbsp;<b>
                                    <fmt:message key="archive.archiveName"/>*</b></td>
                            <td width="28px">&nbsp;</td>
                            <td width="100px">
                                <input type="text" name="archive.archiveName"
                                       title="<fmt:message key='tooltip.archiveName'/>"
                                       class="htmlTextAlpha"
                                       tabindex="2"
                                       style="width:220px;"
                                       onchange="return validateField(this,'archive.archiveName','alphaNumPatWithUnderScore');"
                                       maxlength="30"
                                       value="${archive.archiveName}" />
                            </td>
                        </tr>
                        <tr height="25px">
                            <td id="dbLinkLabel" width="360px">&nbsp;<b>
                                    <fmt:message key="archive.db_link"/>*</b></td>
                            <td width="28px">&nbsp;</td>
                            <td width="320px">
                                <input type="text" name="archive.db_link"
                                       id="dbLinkTextInput"
                                       class="htmlTextAlpha"
                                       title="<fmt:message key='tooltip.db_Link'/>"
                                       tabindex="3"
                                       style="width:220px;"
                                       maxlength="128"
                                       value="${archive.db_link}" />
                            </td>
                        </tr>
                        <tr height="25px">
                            <td width="420px">&nbsp;<b>
                                    <fmt:message key="archive.defaultDb"/></b></td>
                            <td width="28px">&nbsp;</td>
                            <td width="320px">
                                <input type="checkbox"
                                       name="archive.defaultDb"
                                       value="Y"
                                       style="width:13px;"
                                       title="<fmt:message key='tooltip.defaultDb'/>"
                                       class="htmlTextAlpha"
                                       tabindex="4"
                                       <c:if test="${requestScope.method != 'add'}">onkeydown="checkFlag();" onclick="checkFlag();"</c:if>
                                       <c:if test="${archive.defaultDb == 'Y'}">checked</c:if> />
                            </td>
                        </tr>
                        <tr height="25px">
                            <c:if test="${requestScope.method == 'add'}">
                                <td width="120px">&nbsp;<b>
                                        <fmt:message key="archive.module"/>*</b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="100px">
                                    <div style="width: 96px">
                                        <select id="archive.moduleId"
                                                name="archive.moduleId"
                                                tabindex="5"
                                                title="<fmt:message key='tooltip.chooseModule'/>"
                                                class="htmlTextAlpha"
                                                style="width:96px">
                                            <c:forEach items="${requestScope.module}" var="moduleOption">
                                                <option value="${moduleOption.value}">${moduleOption.label}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </td>
                            </c:if>
                            <c:if test="${requestScope.method == 'change'}">
                                <td width="90px">&nbsp;<b>
                                        <fmt:message key="archive.module"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="100px">
                                    <input type="text"
                                           style="width:82px"
                                           name="archive.moduleId"
                                           class="htmlTextAlpha"
                                           disabled="disabled"
                                           value="${archive.moduleId}" />
                                </td>
                            </c:if>
                        </tr>
                    </table>
                    <table width="370" border="0" cellpadding="0" cellspacing="0">
                        <tr height="25px">
                            <td width="176px">&nbsp;<b>
                                    <fmt:message key="archive.type"/></b></td>
                            <td width="28px">&nbsp;</td>
                            <td width="144px" height="15px">
                                <input type="radio"
                                       id="1"
                                       style="width:13px;height:15px"
                                       name="archive.archiveType"
                                       value="D"
                                       onclick="onChangeDbLinkType();"
                                       <c:if test="${archive.archiveType == 'D'}">checked</c:if> />
                                <label tabindex="1" title="<fmt:message key='tooltip.selectUsers'/>" for="1">
                                    <fmt:message key="archive.typeD"/></label>
                            </td>
                            <td width="68px">&nbsp;</td>
                            <td width="144px" height="15px">
                                <input type="radio"
                                       id="2"
                                       style="width:13px;height:15px"
                                       name="archive.archiveType"
                                       value="S"
                                       onclick="onChangeDbLinkType();"
                                       <c:if test="${archive.archiveType == 'S'}">checked</c:if> />
                                <label tabindex="2" title="<fmt:message key='tooltip.selectRole'/>" for="2">
                                    <fmt:message key="archive.typeS"/></label>
                            </td>
                        </tr>
                    </table>
                </fieldset>
            </div>
        </div>
        <div id="ArchiveMaintenance" style="position: absolute; left: 330; top: 208; width: 70px; height: 39px; visibility: visible;">
            <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr>
                    <c:if test="${requestScope.method == 'change'}">
                        <td align="Right">
                            <a title="<fmt:message key='tooltip.helpScreen'/>"
                               tabindex="8"
                               href="#"
                               onclick="javascript:openWindow(buildPrintURL('print','Archieve Change Details'),'sectionprintdwindow','left=100,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
                               onMouseOut="MM_swapImgRestore()"
                               onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
                                <img src="images/help_default.GIF" name="Help" border="0">
                            </a>
                        </td>
                    </c:if>
                    <c:if test="${requestScope.method != 'change'}">
                        <td align="Right">
                            <a title="<fmt:message key='tooltip.helpScreen'/>"
                               tabindex="8"
                               href="#"
                               onclick="javascript:openWindow(buildPrintURL('print','Archieve Add Details'),'sectionprintdwindow','left=100,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
                               onMouseOut="MM_swapImgRestore()"
                               onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
                                <img src="images/help_default.GIF" name="Help" border="0">
                            </a>
                        </td>
                    </c:if>
                    <td align="right" id="Print">
                        <a title="<fmt:message key='tooltip.printScreen'/>"
                           tabindex="9"
                           onclick="printPage();"
                           onMouseOut="MM_swapImgRestore()"
                           onMouseOver="MM_swapImage('Print','','images/Print_R.gif',1)">
                            <img src="images/Print.gif" name="Print" border="0">
                        </a>
                    </td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" color="#7E97AF" style="position: absolute; border: 2px outset; left: 20; top: 200px; width: 400px; height: 39px; visibility: visible;">
            <c:if test="${requestScope.screenStatus == 'readonly'}">
                <div id="ArchiveMaintenance" style="position: absolute; left: 0; top: 4; width: 320px; height: 15px; visibility: visible;">
                    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
                        <tr>
                            <td id="closebutton" width="70" title="<fmt:message key='tooltip.close'/>">
                                <a onMouseOut="collapsebutton(this)"
                                   onMouseOver="highlightbutton(this)"
                                   onMouseDown="expandbutton(this)"
                                   onMouseUp="highlightbutton(this)"
                                   onclick="window.close();"
                                   onKeyDown="submitEnter(this,event)">
                                    <fmt:message key="button.close"/>
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </c:if>
            <c:if test="${requestScope.screenStatus != 'readonly'}">
                <div id="AccountMaintenanceContact" style="position: absolute; left: 0; top: 4; width: 360px; height: 15px; visibility: visible;">
                    <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
                        <tr>
                            <td id="okbutton" width="70">
                                <a tabindex="5"
                                   title="<fmt:message key='tooltip.ok'/>"
                                   onMouseOut="collapsebutton(this)"
                                   onMouseOver="highlightbutton(this)"
                                   onMouseDown="expandbutton(this)"
                                   onMouseUp="highlightbutton(this)"
                                   onclick="javascript:submitForm('${requestScope.method == 'change' ? 'update' : 'save'}');"
                                   onKeyDown="submitEnter(this,event)">
                                    <fmt:message key="button.ok"/>
                                </a>
                            </td>
                            <td id="testbutton" width="70">
                                <a tabindex="6"
                                   title="<fmt:message key='tooltip.testconnection'/>"
                                   onMouseOut="collapsebutton(this)"
                                   onMouseOver="highlightbutton(this)"
                                   onMouseDown="expandbutton(this)"
                                   onMouseUp="highlightbutton(this)"
                                   onclick="testConnection()"
                                   onKeyDown="submitEnter(this,event)">
                                    <fmt:message key="button.testcon"/>
                                </a>
                            </td>
                            <td id="cancelbutton" width="70">
                                <a tabindex="7"
                                   title="<fmt:message key='tooltip.cancel'/>"
                                   onMouseOut="collapsebutton(this)"
                                   onMouseOver="highlightbutton(this)"
                                   onMouseDown="expandbutton(this)"
                                   onMouseUp="highlightbutton(this)"
                                   onclick="window.close();"
                                   onKeyDown="submitEnter(this,event)">
                                    <fmt:message key="button.cancel"/>
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </c:if>
        </div>
    </form>
</body>
<!-- End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen-->

</html>
