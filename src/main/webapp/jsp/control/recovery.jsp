<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<fmt:message key="recovery.title.window"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
var entityAccess = "${requestScope.EntityAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;
/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */

function bodyOnLoad()
{

	xl = new XLSheet("CollmatchDriver","table_2", ["String","String","String"],"212");
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	xl.onsort = xl.onfilter = onFilterandSort;
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	highlightTableRows("CollmatchDriver");
	var dropBox1 = new SwSelectBox(document.forms[0].elements["matchDriver.id.entityId"],document.getElementById("entityDesc"));
	document.getElementById("runbutton").innerHTML = document.getElementById("rundisablebutton").innerHTML;
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("runbutton").innerHTML = document.getElementById("runenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("runbutton").innerHTML = document.getElementById("rundisablebutton").innerHTML;
<%}%>

}
function onFilterandSort()
{
	updateColors();
	//disableAllButtons();

}
function submitFormRun(methodName){
	document.forms[0].method.value = methodName;


	var yourstate=window.confirm('<fmt:message key="recovery.confrim.continue"/>');
	if (yourstate==true){

	document.forms[0].submit();

	}
}
function submitForm(methodName){

	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}


function onSelectTableRow(rowElement, isSelected)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[1];
	document.forms[0].selectedRun.value = hiddenElement.value;
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedCurrency.value = hiddenElement.value;


	document.getElementById("runbutton").innerHTML = document.getElementById("rundisablebutton").innerHTML;

	if(isSelected)
	{


		if(document.forms[0].selectedRun.value=="Not Running")
		{
		/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
			if(menuEntityCurrGrpAccess=="0")
			document.getElementById("runbutton").innerHTML = document.getElementById("runenablebutton").innerHTML;
		/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
		}

	}

}



</SCRIPT>
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">


<form action="matchDriver.do" method="post">
<input name="selectedCurrency" type="hidden">
<input name="selectedRun" type="hidden">
<input name="method" type="hidden" value="displayList">

<div id="MatchDriverMaintenance" style="position:absolute; left:20px; top:20px; width:508px; height:36px; border:2px outset;" color="#7E97AF">
	<div id="MatchDriverControl" style="position:absolute; left:8px; top:4px; width:500px; height:38px; ">

<table width="498" border="0" cellpadding="0" cellspacing="0" height="25">
  <tr height="25">
		  <td width="30"><b><fmt:message key="party.entityId"/></b></td>
		  <td width="28">&nbsp;</td>
		  <td width="140">
		  <select name="matchDriver.id.entityId" id="matchDriver.id.entityId" tabindex="1"
				titleKey="tooltip.selectEntityid" onchange="submitForm('displayList');" style="width:140px">
				<c:forEach var="entity" items="${requestScope.entities}">
					<option value="${entity.value}" <c:if test="${entity.value == matchDriver.id.entityId}">selected</c:if>>
						${entity.label}
					</option>
				</c:forEach>
			</select>



	</td>
	<td width="20">&nbsp;</td>
    <td width="280">
		<span id="entityDesc" name="entityDesc" class="spantext">
	   </td>
	 	 </tr>
</table>
</div>
</div>
<div id="MatchDriverControl" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:60px; width:508px; height:427px;">
<div id="MatchDriverControl" style="position:absolute;z-index:99;left:0px; top:0px; width:508px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="490px" border="0" cellspacing="1" cellpadding="0"  height="20px">
	<thead>
		<tr>
			<td title='<fmt:message key="tooltip.sortCurrency"/>' width="135px" height="20px"  style="border-left-width: 0px;"><b><fmt:message key="balance.currency"/></b></td>

			<td title='<fmt:message key="tooltip.sortStatus"/>' width="150px" height="20px"><b><fmt:message key="movementDisplay.status"/></b></td>
				<td title='<fmt:message key="tooltip.sortLastRun"/>' width="200px" height="20px"><b><fmt:message key="recovery.lastRun"/></b></td>

		</tr>
	</thead>
</table>
</div>


<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:507;height:423px">
<div id="MatchDriverControl" style="position:absolute;z-index:99;left:0px; top:21px; width:490; height:10px;">
<table class="sort-table" id="CollmatchDriver" width="490px" border="0" cellspacing="1" cellpadding="0" height="402px">
<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="item" items="${requestScope.CollmatchDriver}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <td width="135px">
                <input type="hidden" name="currencyCode" value="${item.id.currencyCode}" />
                ${item.id.currencyCode}&nbsp;
            </td>
            <td align="left" width="150px">
                <input type="hidden" name="lastRun" value="${item.lastRun}" />
                ${item.lastRun}&nbsp;
            </td>
            <td align="center" width="200px">
                <input type="hidden" name="updateDateAsString" value="${item.updateDateAsString}" />
                ${item.updateDateAsString}&nbsp;
            </td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>
<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>
</div>
<div id="MatchDriverControl" style="position:absolute ;left:455; top:498px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Recovery '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:490; width:508; height:39px; visibility:visible;">
  <div id="CustodianMaintenance" style="position:absolute; left:6; top:4; width:508; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td id="runbutton">
			</td>

			<td id="closebutton">
				<a tabindex="5" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
	</div>
<div style="position:absolute; left:6; top:4; width:508; height:15px; visibility:hidden;">
    <table width="140" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="runenablebutton">
			<a tabindex="4" title='<fmt:message key="tooltip.setRun"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitFormRun('setRun');"><fmt:message key="button.setRun"/></a>
		</td>
		<td id="rundisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.setRun"/></a>
		</td>

	</tr>
    </table>
  </div>
</div>

</form>
</body>
</html>