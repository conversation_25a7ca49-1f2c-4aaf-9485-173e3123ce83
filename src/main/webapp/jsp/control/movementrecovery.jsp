<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@page import="org.swallow.util.struts.TokenHelper"%>
<html>
<head>
<title><fmt:message key="MovementRecovery.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var entityAccess = "${requestScope.EntityAccess}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;

function bodyOnLoad()
{

xl = new XLSheet("movementLockDetails","table_2", ["String","String","String","String","String","String","String","String","String","String"],"2121111111");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("movementLockDetails");

	document.getElementById("unlockbutton").innerHTML = document.getElementById("unlockdisablebutton").innerHTML;
	var entitydropBox = new SwSelectBox(document.forms[0].elements["movementrecovery.entityId"],document.getElementById("entityName"));
	document.onkeydown=function(e) {
	    e=e||window.event||window.URL||document.URL
	    if (e.keyCode == 116) {
	    	 e.keyCode = 0;
	      if(e.preventDefault)e.preventDefault();
	      else e.returnValue = false;
	    	 var url=getStoredParam('href');
			   submitFormFromURL(url,window);
			   document.location.href=document.URL;
			   return false;
	    }
  }
}

function onFilterandSort(){
	updateColors();
	}

function onSelectTableRow(rowElement)
{

	var hiddenEl1 = rowElement.cells[0].getElementsByTagName("input")[0];
	document.forms[0].selectedMovementId.value = new String(rowElement.cells[0].innerText).valueOf().trim();
	var count = getCountRowsSelected(rowElement);
	if(count == 1)
	{
	if(menuEntityCurrGrpAccess=="0")
		document.getElementById("unlockbutton").innerHTML = document.getElementById("unlockenablebutton").innerHTML;
	}else{

		document.getElementById("unlockbutton").innerHTML = document.getElementById("unlockdisablebutton").innerHTML;
	}

}

function submitForm(methodName)
{
	if(methodName == 'unlockMovement')
	{
		var yourstate=window.confirm('<fmt:message key="MovementRecovery.confirm.unlock"/>');
		if (yourstate==true)
		{
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}
	}
	else{
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
}

</SCRIPT>

</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();ShowErrMsgWindow('${actionError}');bodyOnLoad();setTitleSuffix(document.forms[0]);" onunload="call();">
<form action="movementrecovery.do" method="post" >
<!--Start: Code added for Mantis 1829 by Chinniah on 24-Sep-2012:Unable to lock the movement which is unlocked through movement recovery screen  -->
	<%
		if (request.getSession().getAttribute(TokenHelper.TOKEN_NAME_FIELD) != null) {
			String tokenValue = ""+request.getSession().getAttribute(TokenHelper.TOKEN_NAME_FIELD);
	%>

	<input type="hidden" name="<%=TokenHelper.TOKEN_NAME_FIELD%>" value="<%=TokenHelper.DEFAULT_TOKEN_NAME%>">
	<input type="hidden" name="<%=TokenHelper.DEFAULT_TOKEN_NAME%>" value="<%=tokenValue%>">

	<%
		}
	%>
<!--End: Code added for Mantis 1829 by Chinniah on 24-Sep-2012:Unable to lock the movement which is unlocked through movement recovery screen  -->
<input name="method" type="hidden" value="display">
<input name="selectedMovementId" type="hidden" value="">
<div id="MovementLock" style="position:absolute; left:20px; top:20px; width:907px; height:37px; border:2px outset;" color="#7E97AF">
	<div style="position:absolute; left:8px; top:4px; width:610px; height:25px;">
		<table width="608px" border="0" cellpadding="0" cellspacing="1" height="25px">
		  <tr height="25px">
			  <td width="40px"><b><fmt:message key="message.entityId"/></b></td>
  			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				<select name="movementrecovery.entityId" id="movementrecovery.entityId"
	class="htmlTextAlpha" titleKey="tooltip.selectEntityid"
	onchange="submitForm('detailsByEntity')" tabindex="1"
	style="width:120px" ${requestScope.screenFieldsStatus ? 'disabled' : ''}>
	<c:forEach var="entity" items="${requestScope.entities}">
		<option value="${entity.value}" <c:if test="${entity.value == movementrecovery.entityId}">selected</c:if>>
			${entity.label}
		</option>
	</c:forEach>
</select>


			  </td>
  			  <td width="20px">&nbsp;</td>
			  <td width="400px">
					<span id="entityName" name="entityName" class="spantext">
			   </td>
		  </tr>
		</table>
	</div>
</div>
<!-- Modified by KaisBS for mantis 2096, minimize the width of the div && the tab to get the right visibility of the scroll arrow -->
<div id="MovementLock" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:61px; width:907px; height:385px;">
	<div id="MovementLock" style="position:absolute;z-index:99;left:0px; top:0px; width:886px; height:10px;">
		<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="886" border="0" cellspacing="1" cellpadding="0" height="23px">
			<thead>
				<tr height="23px">
					<td width="120px" style="border-left-width: 0px;"title='<fmt:message key="tooltip.sortMvmId"/>'><b><fmt:message key="movement.id.movement"/></b></td>
					<td width="120px" title='<fmt:message key="tooltip.sortUpdateUser"/>'><b><fmt:message key="movementRecovery.updateUser"/></b></td>
					<td width="140px" title='<fmt:message key="tooltip.sortLockTime"/>'><b><fmt:message key="movementRecovery.lockTime"/></b></td>
					<td width="75px" title='<fmt:message key="tooltip.sortPositionLevel"/>'><b><fmt:message key="movement.position"/></b></td>
					<td width="85px" title='<fmt:message key="tooltip.sortValueDate"/>'><b><fmt:message key="movement.value"/></b></td>
					<td width="145px" title='<fmt:message key="tooltip.sortAmount"/>'><b><fmt:message key="movement.amount1"/></b></td>
					<td width="72px" title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="movement.ccy"/></b></td>
					<td width="225px" title='<fmt:message key="tooltip.sortAccountId"/>'><b><fmt:message key="movement.account"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.sortBookcodeId"/>'><b><fmt:message key="movement.bookcode"/></b></td>
					<td width="300px" title='<fmt:message key="tooltip.sortMatchStatus"/>'><b><fmt:message key="movement.status"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:904px; height:380px; overflow:scroll">
		<div id="MovementLock" style="position:absolute;z-index:99;left:0px; top:22px; width:890px; height:10px;">
			<table class="sort-table" id="movementLockDetails" width="1337" border="0" cellspacing="1" cellpadding="0" height="341">
<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="item" items="${requestScope.movementLockDetails}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <input type="hidden" name="movementId" value="${item.id.movementId}" />
            <td width="120px" align="right">${item.id.movementId}&nbsp;</td>
            <td width="120px">${item.updateUser}&nbsp;</td>
            <td width="140px" align="center">${item.lockTime}&nbsp;</td>
            <td width="75px" align="center">${item.positionLevel}&nbsp;</td>
            <td width="85px" align="center">${item.valueDateAsString}&nbsp;</td>
            <td width="145px" align="right">${item.amountAsString}&nbsp;</td>
            <td width="72px">${item.currencyCode}&nbsp;</td>
            <td width="225px">${item.accountId}&nbsp;</td>
            <td width="100px">${item.bookCode}&nbsp;</td>
            <td style="color:green" width="300px">${item.matchStatusDesc}&nbsp;</td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>

			<tfoot><tr><td colspan="10" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>


<div id="printButtonDiv" style="position:absolute; left:850; width:70px;top:458px; height:45px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="10" href=# onclick="javascript:openWindow(buildPrintURL('print','Movement Recovery '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		 </td>

			<td align="right" id="Print">
				<a title='<fmt:message key="tooltip.printScreen"/>' tabindex="10" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0"></a>
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:450px; width:907px; height:39px; visibility:visible;">
<div id="Role" style="position:absolute; left:6; top:4; width:360pxpx; height:15px; visibility:visible;">
  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="unlockbutton"  width="70px"></td>
		<td id="closebutton" width="70px" title='<fmt:message key="tooltip.close"/>'>
			<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
		</td>
	</tr>
	</table>
</div>
	<div style="position:absolute; left:6; top:4; width:424px; height:15px; visibility:hidden;">
	<table width="140px" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>

		<td id="unlockenablebutton" >
			<a tabindex="2" title='<fmt:message key="tooltip.unlockMovement"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('unlockMovement')"><fmt:message key="button.unlock"/></a>
		</td>
		<td id="unlockdisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.unlock"/></a>
		</td>

	</tr>
	</table>
  </div>
</div>

</form>
</body>
</html>