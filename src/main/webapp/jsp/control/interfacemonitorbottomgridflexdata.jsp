<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!-- Setting locale and resource bundle -->



<interfacemonitor>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
		<timing>
			<c:forEach items="${requestScope.opTimes}" var="opTime">
				<operation id="${opTime.key}">${opTime.value}</operation>
			</c:forEach>
		</timing>
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<c:forEach items="${requestScope.column_order_bottom}" var="columnOrder">
					<c:choose>
						<c:when test="${columnOrder == 'interfaceid'}">
							<column
									heading="<fmt:message key='label.interfaceMonitor.header.interfaceId'/>"
									tooltip="<fmt:message key='tooltip.interfaceMonitor.interfaceId'/>"
									draggable="false"
									filterable="true"
									type="str"
									sort="true"
									dataelement="interfaceid"
									width="150" />
						</c:when>
						<c:when test="${columnOrder == 'messageType'}">
							<column
									heading="<fmt:message key='label.interfaceMonitor.bottomGrid.header.msgType'/>"
									tooltip="<fmt:message key='tooltip.interfaceMonitor.messageType'/>"
									draggable="true"
									filterable="true"
									type="str"
									gridtype="bottom"
									sort="true"
									dataelement="messageType"
									width="200" />
						</c:when>
						<c:when test="${columnOrder == 'messageStatus'}">
							<column
									heading="<fmt:message key='label.interfaceMonitor.bottomGrid.header.msgStatus'/>"
									tooltip="<fmt:message key='tooltip.interfaceMonitor.messageStatus'/>"
									draggable="true"
									filterable="true"
									type="str"
									gridtype="bottom"
									sort="true"
									dataelement="messageStatus"
									width="200" />
						</c:when>
						<c:when test="${columnOrder == 'lastExecution'}">
							<column
									heading="<fmt:message key='label.interfaceMonitor.bottomGrid.header.lastExecution'/>"
									tooltip="<fmt:message key='tooltip.interfaceMonitor.lastExecution'/>"
									draggable="true"
									filterable="true"
									type="num"
									gridtype="bottom"
									sort="true"
									dataelement="lastExecution"
									width="170" />
						</c:when>
					</c:choose>
				</c:forEach>
			</columns>
		</metadata>

		<rows size="${requestScope.bottomGridInfo.size()}">
			<c:forEach items="${requestScope.bottomGridInfo}" var="bottomOrder">
				<row>
					<interfaceid clickable="false">
						<c:out value="${bottomOrder.interfaceId}" />
					</interfaceid>
					<messageType clickable="false">
						<c:out value="${bottomOrder.messageType}" />
					</messageType>
					<messageStatus clickable="false">
						<c:out value="${bottomOrder.messageStatus}" />
					</messageStatus>
					<lastExecution clickable="false">
						<c:out value="${bottomOrder.lastRecievedDate}" />
					</lastExecution>
				</row>
			</c:forEach>
		</rows>
	</grid>
</interfacemonitor>
