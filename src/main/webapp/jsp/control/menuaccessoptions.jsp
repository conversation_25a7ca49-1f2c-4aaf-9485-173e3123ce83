<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<c:if test="${'yes' != requestScope.isViewRole}">

	<c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

		<title><fmt:message key="menuaccessoptions.addScreen"/></title>

</c:if>

	<c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

		<title><fmt:message key="menuaccessoptions.changeScreen"/></title>

</c:if>

	<c:if test="${'copyFrom' == sessionScope.roleMaintenanceOperation}">

		<c:if test="${'addRole' == requestScope.screenTitle}">
			<title><fmt:message key="menuaccessoptions.addScreen"/></title>

</c:if>

		<c:if test="${'changeRole' == requestScope.screenTitle}">

			<title><fmt:message key="menuaccessoptions.changeScreen"/></title>

</c:if>

</c:if>


</c:if>
<c:if test="${'yes' == requestScope.isViewRole}">

	<title><fmt:message key="menuaccessoptions.viewScreen"/></title>

</c:if>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
//alert("The value of the parameter isViewRole is==>"+'${requestScope.isViewRole}');

//alert("screenFieldStatus is"+'${requestScope.screenFieldsStatus}');
var isSelected=false;//this variable holds the status whether any menuItem is selected

mandatoryFieldsArray ="undefined";

<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="add";
window.opener.document.forms[0].clearTempRecords.value="false";
/*-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
if ('<%=request.getParameter("accountAccessSize")%>'!='null')
	window.opener.document.forms[0].accountAccessSize1.value = '<%=request.getParameter("accountAccessSize")%>';
/*-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
window.opener.document.forms[0].submit();
self.close();

</c:if>

function submitForm(methodName){

	document.forms[0].method.value = methodName;
	validateForm();
	if(isSelected)
	document.forms[0].submit();
	else{
		alert('<fmt:message key="menuaccessoptions.alert.menuOption"/>');
		isSelected=false;
	}
}

function validateForm(){

 var table = document.getElementById("itemDescList");
 var tbody = table.getElementsByTagName("tbody")[0];
 var rows = tbody.getElementsByTagName("tr");
 var selectedList = "";

 for (i=0; i < rows.length; i++){
	var colls = rows[i].getElementsByTagName("td");
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	var radioElement1 =colls[3].getElementsByTagName("input")[0];
	var radioElement2 =colls[4].getElementsByTagName("input")[0];
	if(radioElement1.checked || radioElement2.checked)
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	{
		isSelected=true;
		break;
	}
  }
}

function bodyOnLoad(){
	/*-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
	if ('<%=request.getParameter("accountAccessSize")%>'!='null')
		window.opener.document.forms[0].accountAccessSize1.value = '<%=request.getParameter("accountAccessSize")%>';
	/*-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
	<c:if test="${'yes' != requestScope.isViewRole}">

	 document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;

</c:if>
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";
}


function checkAllItems(element)
{
	var table = document.getElementById("itemDescList");
	var tbody = table.getElementsByTagName("tbody")[0];
	var rows = tbody.getElementsByTagName("tr");

	<c:if test="${'yes' != requestScope.isViewRole}">

	if(element.checked)
		{
			for (i=0; i < rows.length; i++)
			{
				var colls = rows[i].getElementsByTagName("td");
				var checkBoxElement =colls[3].getElementsByTagName("input")[0];
				checkBoxElement.checked = true;
			}
		}
	else
		{
			for (i=0; i < rows.length; i++)
			{
				var colls = rows[i].getElementsByTagName("td");
				var checkBoxElement =colls[3].getElementsByTagName("input")[0];
				checkBoxElement.checked = false;

			}

		}


</c:if>
}

</SCRIPT>
</head>

<form action="role.do" method="post">
<input name="method" type="hidden" value="display">
<!-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 -->
<input name="accountAccessSize" type="hidden" value='<%=request.getParameter("accountAccessSize")%>'>
<!-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 -->

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
<div id="Role" style="position:absolute; left:20px; top:20px; width:973px; height:353px; border:2px outset;" color="#7E97AF">
	<div id="Role" style="position:absolute;z-index:99;left:0px; top:0px; width:970px; height:10px;">
		<table class="sort-table" width="953px" border="0" cellspacing="1" cellpadding="0" height="20px">
			<thead>
				<tr height="20px">
					<td width="85px" title='<fmt:message key="tooltip.menuLevel1Desc"/>' class="topbar" style="border-left-width: 0px;"><b><fmt:message key="role.menuaccess.level1"/></b></td>
					<td width="280px" title='<fmt:message key="tooltip.menuLevel2Desc"/>' class="topbar"><b><fmt:message key="role.menuaccess.level2"/></b></td>
					<td width="280px" title='<fmt:message key="tooltip.menuLevel3Desc"/>' class="topbar"><b><fmt:message key="role.menuaccess.level3"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.fullAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.fullAccess"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.viewAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.readOnly"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.noAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.noAccess"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:969px; height:348px;overflowY:scroll">
		<div id="Role" style="position:absolute;z-index:99;left:1px; top:21px; width:945px; height:10px;">
			<table id="itemDescList" class="sort-table" width="948px" border="0" cellspacing="1" cellpadding="0" height="348px">
			<tbody>
				<%int count = 0; %>
				 <c:forEach items="${requestScope.itemDescList}" var="itemDescList" >
					<% if( count%2 == 0 ) {%><tr class="evencheckbox"><% }else  { %> <tr class="oddcheckbox"> <%}++count; %>
						<input type="hidden" name="description" value="${itemDescList.description}" />
						<!--<td width="345px" align="left">${itemDescList.description}&nbsp;</td>-->
						<td width="85px" align="left">${itemDescList.descriptionLevel1}&nbsp;</td>
						<td width="280px" align="left">${itemDescList.descriptionLevel2}&nbsp;</td>
						<td width="280px" align="left">${itemDescList.descriptionLevel3}&nbsp;</td>

						 <c:if test="${'yes' == requestScope.isViewRole}">
						 		<td width="100px" align="center">
						 		<input type="radio" name="menuAccessHTML<%=count%>" value="0" ${itemDescList.menuAccessHTMLFullAccess == 'checked' ? 'checked="checked"' : ''} disabled="true"/>
								</td>
								<td width="100px" align="center">
								<input type="radio" name="menuAccessHTML<%=count%>" value="1" ${itemDescList.menuAccessHTMLViewAccess == 'checked' ? 'checked="checked"' : ''} disabled="true"/>
								</td>
								<td width="100px" align="center">
								<input type="radio" name="menuAccessHTML<%=count%>" value="2" ${itemDescList.menuAccessHTMLNoAccess == 'checked' ? 'checked="checked"' : ''} disabled="true"/>
								</td>

</c:if>

						 <c:if test="${'yes' != requestScope.isViewRole}">

						 		<td width="100px" align="center"><input type="radio" name="menuAccessHTML<%=count%>" value="0" ${itemDescList.menuAccessHTMLFullAccess == 'checked' ? 'checked="checked"' : ''}/></td>
						 		<td width="100px" align="center"><input type="radio" name="menuAccessHTML<%=count%>" value="1" ${itemDescList.menuAccessHTMLViewAccess == 'checked' ? 'checked="checked"' : ''}/></td>
								<td width="100px" align="center"><input type="radio" name="menuAccessHTML<%=count%>" value="2" ${itemDescList.menuAccessHTMLNoAccess == 'checked' ? 'checked="checked"' : ''}/></td>

</c:if>

					</tr>
				</c:forEach>
				</tbody>
				<tfoot><tr><td colspan="4"></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->

<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
<div id="Role" style="position:absolute; left:915; top:386px; width:70px; height:15px; visibility:visible;">
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
          <!--Betcy:19/11/08:Added(Start):To display the help content correctly-->
		  <td align="Right">
		<c:if test="${'yes' != requestScope.isViewRole}">

		  <c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

				<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Menu Access Options '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>
		  <c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

				<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Menu Access Options '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>

</c:if>
		<c:if test="${'yes' == requestScope.isViewRole}">

			<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','View Menu Access Options '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>
	      </td>
   <!--Betcy:19/11/08:Added(end):To display the help content correctly-->

			<td align="right" id="Print">
				<a  onclick="printPage();" tabindex = "3"  onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:378; width:973px; height:39px; visibility:visible;">
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
	<div id="Role" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
	  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
		 <c:if test="${'yes' == requestScope.isViewRole}">

			<tr>
				<td width="70px" id="closebutton">
					<a onMouseOut="collapsebutton(this)" title = '<fmt:message key="tooltip.close"/>'  onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
				</td>
			</tr>

</c:if>
		<c:if test="${'yes' != requestScope.isViewRole}">

			<tr>
				<td width="70px"  id="savebutton"></td>
				<td width="70px"  id="cancelbutton">
					<a tabindex= "2" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)"  onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>
				</td>
			</tr>

</c:if>
		</table>
	</div>
	<div style="position:absolute; left:6; top:4; width:438px; height:15px; visibility:hidden;">
	<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td width="70px"  id="saveenablebutton">
			<a tabindex = "1" title='<fmt:message key="tooltip.ok"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveMenuAccessOptions');"><fmt:message key="button.ok"/></a></td>
		<td width="70px" id="savedisablebutton">
			<a tabindex = "1" class="disabled" disabled="disabled"><fmt:message key="button.ok"/></a>
		</td>


	</tr>
	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>