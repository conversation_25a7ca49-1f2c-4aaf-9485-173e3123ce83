 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
 <%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="screenProfile.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
 
</head>

<SCRIPT language="JAVASCRIPT">

//START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box	
function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["userprofiles.userProfileName"];
  
  return validate(elementsRef);
}
function submitForm(methodName){
if(methodName!='displayProfile')	
{	var flag = validateField(document.forms[0].elements['userprofiles.userProfileName'],'userprofiles.userProfileName','alphaNumPatWithSpace');
	if(flag  )
		{
		document.forms[0].method.value = methodName;
		if(validateForm(document.forms[0]))
			document.forms[0].submit();
		}	

	}
	else{
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
	
	}
	
	}

//END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box
//closeWindow
function bodyonload()
{
setParentChildsFocus();
setFocus(document.forms[0]);
	<c:if test='${requestScope.closeWindow == "yes"}'>

	var optionColl = getMenuWindow().document.forms[0].elements["user.profileId"].options;
	var profileId = document.forms[0].elements["userprofiles.id.profileId"].value;
	var profileName = document.forms[0].elements["userprofiles.userProfileName"].value;

	for(var idx = 0 ; idx < optionColl.length; ++idx)
	{
		if(optionColl[idx].value == profileId )
			optionColl[idx].text = profileName;
	}

	alert('<fmt:message key="screenProfile.alert.profileChanged"/>');
	self.close();
</c:if>




}

</SCRIPT>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyonload()" onunload="call()">

<form action="userprofiles.do" method="post">

<input name="method" type="hidden" value="display">

<!--<input type="hidden" name="userprofiles.userProfileName" value="${userprofiles.userProfileName}"> -->


	<div id="UserProfile" style="position:absolute; left:20px; top:20px; width:414px; height:85px; border:2px outset;"color="#7E97AF">

  <div id="UserProfile" style="position:absolute; left:8px; top:4px; width:395px; height:38px;">
	<table width="390" border="0" cellpadding="0" cellspacing="0" height="25">
   		<tr>
		  <td width="179"align="left"><b><fmt:message key="userprofile.option"/></b></td>
		  <td width="28">&nbsp;</td>
		  <!--START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To display tool tip-->
		  <td height="23" width="188" >
			  <select id="userprofiles.id.profileId" name="userprofiles.id.profileId" tabindex="1" style="width:152px;" onchange="submitForm('displayProfile')" title="<fmt:message key='tooltip.enterProfileOption'/>">
				<c:forEach var="profile" items="${requestScope.userProfileList}">
					<option value="${profile.value}" ${profile.value == userprofiles.id.profileId ? 'selected="selected"' : ''}>
						${profile.label}
					</option>
				</c:forEach>
			</select>

 	   		
			
		<!--END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To display tool tip-->
		</td>		
		</tr>

		<tr style="height:30">	
		<!--START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->
            <td height="23" width="179"><b><fmt:message key="userprofile.name"/></b>*</td>
		<!--END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->
            <td width="28">&nbsp;</td>
		    <td width="188">
		<!--START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->
				<input type="text" class="htmlTextAlpha" title="<fmt:message key='tooltip.enterProfileName'/>"
					   tabindex="2" name="userprofiles.userProfileName" style="width:188px;"
					   maxlength="20" onchange="return validateField(this,'userprofiles.userProfileName','alphaNumPatWithSpace');"
					   value="${userprofiles.userProfileName}" />
		<!--END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->	 
		 </td>
		</tr>

		<tr>
		   <td width="179"><b><fmt:message key="userprofile.default"/></b></td>
		   <td width="28">&nbsp;</td>
		   <td width="188">
			   <input type="checkbox" title="<fmt:message key='tooltip.defaultProfile'/>" tabindex="2"
					  style="width:13px;" name="userprofiles.currentProfile" value="Y"
			   ${requestScope.userprofiles.currentProfile == 'Y' ? 'checked="checked"' : ''} />
    		</td>
    	</tr>

		<!-- <tr>	
            <td width="179"><b><fmt:message key="userprofile.overwrite"/></b></td>
            <td width="28">&nbsp;</td>
		    <td width="188">
		      <c:choose>
				<c:when test="${requestScope.methodName == 'save'}">
					<input type="checkbox" title="<fmt:message key='tooltip.saveProfile'/>"
						   tabindex="3" style="width:13px;" name="userprofiles.overWriteFlag" disabled="disabled" />
				</c:when>
				<c:when test="${requestScope.methodName == 'update'}">
					<input type="checkbox" title="<fmt:message key='tooltip.overwriteExistingProfile'/>"
						   tabindex="3" style="width:13px;" name="userprofiles.overWriteFlag" value="Y"
						   ${requestScope.userprofiles.overWriteFlag == 'Y' ? 'checked="checked"' : ''} />
				</c:when>
			</c:choose>
		 </td>
		</tr> -->

          
	</table>
  </div>
</div>
 

 <div id="user" style="position:absolute ;left:360; top:119px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
			<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Screen Profile '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a> 
		</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
 
 
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:110; width:414px; height:39px; visibility:visible;">
<div id="UserProfile" style="position:absolute; left:6; top:4; width:350px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
    <tr>
	  <td>
	     <a title='<fmt:message key="tooltip.save"/>' tabindex="4"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('${methodName}');"><fmt:message key="button.save"/></a></td>

	    <td>
	        <a title='<fmt:message key="tooltip.cancel"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:window.close();"><fmt:message key="button.cancel"/></a></td>
	</tr>
  </table>
</div>
</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
<input type="hidden" name="wndsetting" value='${wndsetting}'/>	



</form>
</body>
</html>