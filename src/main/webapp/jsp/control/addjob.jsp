<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:choose>
    <c:when test="${requestScope.methodName == 'add'}">
        <fmt:message key="addJobDetails.title.Window" />
    </c:when>
    <c:when test="${requestScope.methodName == 'change'}">
        <fmt:message key="changeJob.title.Window" />
    </c:when>
    <c:otherwise>
        <fmt:message key="viewJob.title.Window" />
    </c:otherwise>
</c:choose>

</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
/**
 * When window is load, find each 'tr' that contains a 'td'  has an input
 * which value contains  '*',
 * then apply the style 'required' for each empty input
 * Added by Med Amine <PERSON>
 *
 **/
mandatoryFieldsArray = ["*"];
var reportSchedulerData = new Object();
var newILMReportSchedConfig = "true";
var newOpportunityCostReportSchedConfig = "true";
var newTurnoverReportSchedConfig = "true";
var newCurrencyFundingReportSchedConfig = "true";
var newInterestChargesReportSchedConfig = "true";
var parametersStatusValue;
var selectedRoleBeforeChange;
var selectedRoleNameBeforeChange;
var selectedReportTypeBeforeChange;
var selectedReportNameBeforeChange;
var reportTypePreviousSelectedIndex;

$(window).on("load",function(){

	callOnBodyLoad();

	$("tr:has(td:has(input))").each(function()
		{
				var tr = $(this);
				if(tr.find("input").val() == '*' || tr.find("input").attr("name")=="scheduledReportParams.reportName")
				{
					tr.find('input').css('border-color',function(index)
					{
						if ($(this).val() == ''){
							return 'red';
						}else{
							return '#a9a9a9';
					    }
					}).on('blur keyup',function()
					{
						if ($(this).val() == '')
							$(this).css('border','red 1px solid');
				  		else{
						  		$(this).css('border-color','');
								$(this).addClass('inputText');
							}
					});
				}
		});

	$("input[type='radio']").on('click', function(){
	 	$("input[type='text']").removeClass('is-disabled');
	   	$("input[disabled='disabled'][type='text']").addClass('is-disabled');
	    $("input[type='text']").css('border-color','');
	    $("input[type='text']").not('[disabled="disabled"],[tabIndex="-1"]').css('background','white');
	    $("input[disabled='disabled'][type='text']").not('[tabIndex="-1"]').css('background','#e5e5e5');
	    $("input[disabled=''][type='text']").not('[tabIndex="-1"]').css('background','#e5e5e5');
		$("input[type='text']").not("[name*='Mandatory'],[name*='mandatory']").css('border','1px solid');
		$("input[type='text']").not("[name*='Mandatory'],[name*='mandatory']").css('border-color','#a9a9a9');
		$("tr:has(td:has(input))").each(function()
		{
			var tr = $(this);
			var exist = (tr.find('input').val() == '*' || tr.find("input").attr("name")=="scheduledReportParams.reportName" );
			if(exist){
				tr.find('input').css('border-color',function(index)
				{
					if ($(this).val() == '')
						return 'red';
				}).on('blur keyup',function()
					{
						if ($(this).val() == '')
							$(this).css('border','red 1px solid');
				  		else
				  		{
				  			$(this).css('border-color','');
							$(this).addClass('inputText');
						}
					});
			}
		});
	});

	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		document.getElementById("firstsc").style.color = "black";
	} else {
		document.getElementById("firstsc").style.color = "grey";
	}

});
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";


function checkWeekly(){

 var checkAll = document.forms[0].elements["scheduler.scheduleDayAll"].checked;
 var checkSun = document.forms[0].elements["scheduler.scheduleDaySun"].checked;
 var checkMon = document.forms[0].elements["scheduler.scheduleDayMon"].checked;
 var checkTue = document.forms[0].elements["scheduler.scheduleDayTue"].checked;
 var checkWed = document.forms[0].elements["scheduler.scheduleDayWed"].checked;
 var checkThu = document.forms[0].elements["scheduler.scheduleDayThr"].checked;
 var checkFri = document.forms[0].elements["scheduler.scheduleDayFri"].checked;
 var checkSat = document.forms[0].elements["scheduler.scheduleDaySat"].checked;
 if (checkAll == true  || checkSun == true|| checkMon == true|| checkTue == true|| checkWed == true|| checkThu == true|| checkFri == true|| checkSat == true)
	return true;
 else
	return false;
}

function checkMonthly(){
	var checkDay = document.forms[0].elements["scheduler.monthDateAsString"].value;
	var checkFirst = document.forms[0].elements["scheduler.monthFirst"].checked;
	var checkLast = document.forms[0].elements["scheduler.monthLast"].checked;

if (checkDay.length>0 || checkFirst== true || checkLast == true)
	return true;
 else
	return false;
}

<c:if test="${requestScope.parentFormRefresh == 'yes'}">

try {
	window.opener.submitFormFromAddJob("display", "${requestScope.scheduledJobType}");
}catch (e) {

}
self.close();
</c:if>



var systemDate= '${systemDate}';
var systemTime= '${systemTime}';
var dateFormat = '${sessionScope.CDM.dateFormat}';
var schedReportTypeParams;
var schedReportTypeParamsAsJson;
var mapDateMethod;
var outputFormat;
var menuItemId;
var configIncorrectCause;
function callOnBodyLoad(){
	configIncorrectCause='${requestScope.configIncorrectCause}';
	document.forms[0].selectedUsers.value = '${requestScope.selectedUsers}';
	document.forms[0].selectedRoles.value = '${requestScope.selectedRoles}';
	document.forms[0].selectedEmailUsers.value = '${requestScope.selectedEmailUsers}';
	document.forms[0].selectedEmailRoles.value = '${requestScope.selectedEmailRoles}';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		if ('${methodName}' == "change") {
			document.forms[0].selectedjobId.value = '${requestScope.selectedjobId}';
			parametersStatusValue = '${requestScope.parametersStatusValue}';
			document.forms[0].elements["scheduledReportParams.reportTypeId"].className='is-disabled';
			document.forms[0].elements["scheduledReportParams.reportTypeId"].disabled="true";
			newILMReportSchedConfig = false;
			newOpportunityCostReportSchedConfig = false;
			newTurnoverReportSchedConfig = false;
			newCurrencyFundingReportSchedConfig = false;
			newInterestChargesReportSchedConfig = false;
			document.forms[0].schedulerConfigXML.value = document.forms[0].elements['scheduledReportParams.reportConfig'].value;
		} else if ('${methodName}' == "add") {
			if ('${requestScope.schedulerConfigXML}' != "") {
				parametersStatusValue = '${requestScope.parametersStatusValue}';
				newILMReportSchedConfig = false;
				newOpportunityCostReportSchedConfig = false
				newTurnoverReportSchedConfig = false;
				newCurrencyFundingReportSchedConfig = false;
				newInterestChargesReportSchedConfig = false;
				reportTypePreviousSelectedIndex = '${requestScope.reportTypePreviousSelectedIndex}';
			} else {

				parametersStatusValue = "noConfig";
				var e = document.forms[0].elements["scheduledReportParams.reportTypeId"];
				reportTypePreviousSelectedIndex = e.selectedIndex;
				document.forms[0].elements["scheduledReportParams.reportName"].value = e.options[e.selectedIndex].text;
			}
			document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
			selectedReportTypeBeforeChange = document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
			selectedReportNameBeforeChange = document.forms[0].elements["scheduledReportParams.reportName"].value;
			document.forms[0].schedulerConfigXML.value = '${requestScope.schedulerConfigXML}';
		}
		schedReportTypeParams = '${requestScope.schedReportTypeParams}';
		schedReportTypeParamsAsJson =  JSON.parse(schedReportTypeParams);
	    setJobReportTypes();
		var roleDropBoxElement = new SwSelectBox(document.forms[0].elements["scheduledReportParams.executionRole"],document.getElementById("roleName"));
		selectedRoleBeforeChange = document.forms[0].elements["scheduledReportParams.executionRole"].value;
		selectedRoleNameBeforeChange = document.getElementById("roleName").innerHTML;
		updateParametersStatus(parametersStatusValue);
	}

	var jobType;
	var radioElements = document.forms[0].elements["scheduler.jobType"];

	for(var idx = 0 ; idx < radioElements.length; ++idx)
	{
		if(radioElements[idx].checked)
		jobType = radioElements[idx].value;
	}
	if (jobType == 'C')
	{
	<c:if test="${requestScope.methodName != 'view'}">

		onceDisable();
			dailyDisable();
			weeklyDisable();
			monthlyDisable();
	</c:if>
	document.forms[0].elements["CyclicDurationmandatory"].value = "*";
	}
	else if (jobType == 'O') {
		<c:if test="${requestScope.methodName != 'view'}">
			cyclicDisable();
			dailyDisable();
			weeklyDisable();
			monthlyDisable();
		</c:if>

		<c:if test="${requestScope.methodName == 'view'}">
			document.forms[0].elements["scheduler.scheduleTime"][1].value = "";
			document.forms[0].elements["scheduler.scheduleTime"][2].value = "";
			document.forms[0].elements["scheduler.scheduleTime"][3].value = "";
		</c:if>
		document.forms[0].elements["OnceDateMandatory"].value = "*";
		document.forms[0].elements["OnceTimeMandatory"].value = "*";
	} else if (jobType == 'D') {
		<c:if test="${requestScope.methodName != 'view'}">
			cyclicDisable();
			onceDisable();
			weeklyDisable();
			monthlyDisable();
		</c:if>

		<c:if test="${requestScope.methodName == 'view'}">
			document.forms[0].elements["scheduler.scheduleTime"][0].value = "";
			document.forms[0].elements["scheduler.scheduleTime"][2].value = "";
			document.forms[0].elements["scheduler.scheduleTime"][3].value = "";
		</c:if>

		document.forms[0].elements["dailyTimeMandatory"].value = "*";
	} else if (jobType == 'W') {
		<c:if test="${requestScope.methodName != 'view'}">
		cyclicDisable();
		onceDisable();
		dailyDisable();
		monthlyDisable();
		</c:if>

		<c:if test="${requestScope.methodName == 'view'}">
		document.forms[0].elements["scheduler.scheduleTime"][0].value = "";
		document.forms[0].elements["scheduler.scheduleTime"][1].value = "";
		document.forms[0].elements["scheduler.scheduleTime"][3].value = "";
		</c:if>

		document.forms[0].elements["weeklyTimeMandatory"].value = "*";
	} else if (jobType == 'M') {
		<c:if test="${requestScope.methodName != 'view'}">
		cyclicDisable();
		onceDisable();
		dailyDisable();
		weeklyDisable();
		</c:if>

		<c:if test="${requestScope.methodName == 'view'}">
		document.forms[0].elements["scheduler.scheduleTime"][0].value = "";
		document.forms[0].elements["scheduler.scheduleTime"][1].value = "";
		document.forms[0].elements["scheduler.scheduleTime"][2].value = "";
		</c:if>

		document.forms[0].elements["monthlyDayMandatory"].value = "*";
		document.forms[0].elements["monthlyTimeMandatory"].value = "*";
	} else if (jobType == 'N') {
		cyclicDisable();
		onceDisable();
		dailyDisable();
		weeklyDisable();
		monthlyDisable();

	}

	var radios = document.getElementsByName("scheduledReportParams.mapDateMethod");
	for (var i = 0; i < radios.length; i++) {
		if (radios[i].checked) {
			handleMapDateMethodChange(radios[i]);
			break;
		}
	}


}

function buildAddURL(methodName) {

	var param = 'scheduler.do?method='+methodName;
	return param;
}

function submitForm(methodName){
 var jobType;
 var siguiente= false;
 var validComplete= false;

	  document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="";

		var radioElements = document.forms[0].elements["scheduler.jobType"];
		for(var idx = 0 ; idx < radioElements.length; ++idx)
		{
			if(radioElements[idx].checked)
			jobType = radioElements[idx].value;
		}
	var typeJob= document.forms[0].elements["job.jobType"].value
	 var scheduleDate = document.forms[0].elements['scheduler.scheduleDateAsString'].value;
	 var scheduleTime = document.forms[0].elements['scheduler.scheduleTime'][0].value;
	 var reportName = document.forms[0].elements['scheduledReportParams.reportName'].value;
	 var reportTypeId = document.forms[0].elements['scheduledReportParams.reportTypeId'].value;
	 var days = document.forms[0]["scheduledReportParams.retainDays"].value;
	 var outputLocation = document.forms[0]["scheduledReportParams.outputLocation"].value.trim();
	 var fileNamePrefix = document.forms[0]["scheduledReportParams.fileNamePrefix"].value.trim();
	 document.forms[0].scheduledJobType.value = document.forms[0].elements["job.jobType"].value;
	 document.forms[0].reportTypePreviousSelectedIndex.value = reportTypePreviousSelectedIndex;

	 if (validateFrecuencyData(jobType,methodName,"add")){
		if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
			if(validateReportName(reportName)){
				if(outputLocation == "" || validateOutputLocation(outputLocation)){
					if(fileNamePrefix == "" || validatePrefix(fileNamePrefix)){
						if(days == "" || validateRetainDays(days)){
							document.forms[0]["scheduledReportParams.outputLocation"].value = outputLocation;
							document.forms[0]["scheduledReportParams.fileNamePrefix"].value = fileNamePrefix;
							document.forms[0].submit();
						}
					}

				}
			 }
		}else{
			document.forms[0].submit();
		}
	}

}

function validateOutputLocation(path){
	var reg = new RegExp(/^(?:[\w]\:|[\/])?([\\|\/]?[\w\-\s0-9]+)+$/);

	if(!reg.test(path)){
		alert('<fmt:message key="addjob.alert.invalidOutputFileLocation"/>');
	}
	return reg.test(path);
}


function validatePrefix(prefix){
	var reg = new RegExp(/^[-_a-zA-Z0-9]+$/);
	if(!reg.test(prefix)){
		alert('<fmt:message key="addjob.alert.invalidFileNamePrefix"/>');
	}
	return reg.test(prefix);
}

function validateReportName(reportName) {

	if(reportName == "")
	{
		alert('<fmt:message key="alert.pleaseFillAllMandatoryFields"/>');
		document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";

		return false;
	} else {
		return true;
	}
}

function validateRetainDays(days){

    if(!days.match(/^\d+$/))
     {
    	alert('<fmt:message key="addjob.alert.invalidRetainFiles"/>');
        return false ;
     }
  	else
  		 return true;
}



function validateFrecuencyData(jobType, methodName, cameFrom) {
	var isValid = true;
	 var checkforzero=true;
	if (validateForm(document.forms[0])) {
		if (jobType == 'W') {
			isValid = checkWeekly();
		}
		if (jobType == 'M') {
			isValid = checkMonthly();
		}
		if (isValid == false) {
			alert('<fmt:message key="addJob.alert.selectDays"/>');
		}

		if (isValid == true) {

			if (jobType == 'D') {
				if (validateField(
						document.forms[0].elements['scheduler.scheduleTime'][1],
						'Number 2', 'timePat')) {

					document.forms[0].method.value = methodName;
					if (cameFrom == "change") {
						document.forms[0].selectedjobId.value = '${selectedjobId}';
						enableFields();
					} else {
						document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
					}
					return true;
				} else {
					document.forms[0].elements['scheduler.scheduleTime'][1]
							.focus();
					return false;
				}

			} else if (jobType == 'O') {
				if (validateField(
						document.forms[0].elements['scheduler.scheduleDateAsString'],
						'scheduler.scheduleDateAsString', dateFormat)) {
					if (validateField(
							document.forms[0].elements['scheduler.scheduleTime'][0],
							'Number 2', 'timePat')) {
						document.forms[0].method.value = methodName;
						if (cameFrom == "change") {
							document.forms[0].selectedjobId.value = '${selectedjobId}';
							enableFields();
						} else {
							document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
						}
						return true;
					} else {
						document.forms[0].elements['scheduler.scheduleTime'][0]
								.focus();
						return false;
					}
				} else {
					document.forms[0].elements['scheduler.scheduleDateAsString']
							.focus();
					return false;
				}

			} else if (jobType == 'C' || jobType == 'c') {
				if (validateField(
						document.forms[0].elements['scheduler.durationHoursasString'],
						'Number 2', 'numberPatExpand', 23, 0)) {
					if (validateField(
							document.forms[0].elements['scheduler.durationMinsasString'],
							'Number 2', 'numberPatExpand', 59, 0)) {
						if (validateField(
								document.forms[0].elements['scheduler.durationSecsasString'],
								'Number 2', 'numberPatExpand', 59, 0)) {
							checkforzero = checkZero();
							if (checkforzero == true) {
								document.forms[0].method.value = methodName;
								if (cameFrom == "change") {
									document.forms[0].selectedjobId.value = '${selectedjobId}';
									enableFields();
								} else {
									document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
								}
								return true;
							}
						} else {
							document.forms[0].elements['scheduler.durationSecsasString']
									.focus();
							return false;
						}
					} else {
						document.forms[0].elements['scheduler.durationMinsasString']
								.focus();
						return false;
					}
				} else {
					document.forms[0].elements['scheduler.durationHoursasString']
							.focus();
					return false;
				}

			} else if (jobType == 'W') {
				if (validateField(
						document.forms[0].elements['scheduler.scheduleTime'][2],
						'Number 2', 'timePat')) {
					document.forms[0].method.value = methodName;
					if (cameFrom == "change") {
						document.forms[0].selectedjobId.value = '${selectedjobId}';
						enableFields();
					} else {
						document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
					}
					return true;
				} else {
					document.forms[0].elements['scheduler.scheduleTime'][2]
							.focus();
					return false;
				}

			} else if (jobType == 'M') {

				if (validateField(
						document.forms[0].elements['scheduler.monthDateAsString'],
						'Number 2', 'numberPat', 31, 1)) {
					if (validateField(
							document.forms[0].elements['scheduler.scheduleTime'][3],
							'Number 2', 'timePat')) {

						document.forms[0].method.value = methodName;
						if (cameFrom == "change") {
							document.forms[0].selectedjobId.value = '${selectedjobId}';
							enableFields();
						} else {
							document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
						}
						return true;
					} else {
						document.forms[0].elements['scheduler.scheduleTime'][3]
								.focus();
						return false;
					}
				} else {
					document.forms[0].elements['scheduler.monthDateAsString']
							.focus();
					return false;
				}

			} else {
				document.forms[0].method.value = methodName;
				if (cameFrom == "change") {
					document.forms[0].selectedjobId.value = '${selectedjobId}';
					enableFields();
				} else {
					document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
				}
				return true;
			}
		}
	}
}



function checkZero()
{

		var hrs=document.forms[0].elements["scheduler.durationHoursasString"].value;
		var min=document.forms[0].elements["scheduler.durationMinsasString"].value;
		var sec=document.forms[0].elements["scheduler.durationSecsasString"].value;

		if(hrs == "00" && min == "00" && sec =="00")
		{
			alert('<fmt:message key="alert.cyclicInervalCannotBeZero"/>');
			document.forms[0].elements["scheduler.durationHoursasString"].value="";
			document.forms[0].elements["scheduler.durationMinsasString"].value="";
			document.forms[0].elements["scheduler.durationSecsasString"].value="";
			document.forms[0].elements["scheduler.durationHoursasString"].focus();
			return false;
		}
		else
		{
			return true;
		}
}
// Code modification for Server Log Issue Ends



function updateSubmitForm(methodName){
	var jobType;
	var isValid = true;
	var checkforzero=true;
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="";
	var radioElements = document.forms[0].elements["scheduler.jobType"];
	for(var idx = 0 ; idx < radioElements.length; ++idx)
	{
		if(radioElements[idx].checked)
		jobType = radioElements[idx].value;
	}
	var scheduleDate = document.forms[0].elements['scheduler.scheduleDateAsString'].value;
	var scheduleTime = document.forms[0].elements['scheduler.scheduleTime'][0].value;


	var typeJob = document.forms[0].elements["job.jobType"].value
	var reportName = document.forms[0].elements['scheduledReportParams.reportName'].value;
	var days = document.forms[0]["scheduledReportParams.retainDays"].value;
	var outputLocation = document.forms[0]["scheduledReportParams.outputLocation"].value.trim();
	var fileNamePrefix = document.forms[0]["scheduledReportParams.fileNamePrefix"].value.trim();
	document.forms[0].scheduledJobType.value = document.forms[0].elements["job.jobType"].value;
	document.forms[0].reportTypePreviousSelectedIndex.value = reportTypePreviousSelectedIndex;

	if (validateFrecuencyData(jobType, methodName, "change")) {
		if (document.forms[0].elements["job.jobType"].value == 'R'
				|| document.forms[0].elements["job.jobType"].value == 'Report') {
			if (validateReportName(reportName)) {
				if(outputLocation == "" || validateOutputLocation(outputLocation)){
					if(fileNamePrefix == "" || validatePrefix(fileNamePrefix)){
						if (days == "" || validateRetainDays(days)) {
							document.forms[0]["scheduledReportParams.outputLocation"].value = outputLocation;
							document.forms[0]["scheduledReportParams.fileNamePrefix"].value = fileNamePrefix;
							if("E" == document.forms[0].elements["scheduler.jobStatus"].value && 'N' != document.forms[0].elements["scheduler.jobType"].value && 'O' != document.forms[0].elements["scheduler.jobType"].value){
								var message = "<fmt:message key="addjob.alert.configChanged"/>";
								ShowErrMsgWindowWithBtn("", message, YES_NO, deleteYes,deleteNo );

							}else{
								document.forms[0].submit();
							}
						}
					}
				}
			}
		} else {
			document.forms[0].submit();
		}
	}
}


function deleteNo(){
	document.forms[0].changeAndRunNow.value = false;
	document.forms[0].submit();
}
function deleteYes(){
	document.forms[0].changeAndRunNow.value = true;
	document.forms[0].submit();
}

function cyclic(){

    onceDisable();
	dailyDisable();
	weeklyDisable();
	monthlyDisable();
	document.forms[0].elements["CyclicDurationmandatory"].value = "*";
	document.forms[0].elements["scheduler.durationHoursasString"].disabled="";
	document.forms[0].elements["scheduler.durationMinsasString"].disabled="";
	document.forms[0].elements["scheduler.durationSecsasString"].disabled="";


}
function once(){

	cyclicDisable();
    dailyDisable();
	weeklyDisable();
	monthlyDisable();
	document.forms[0].elements["OnceDateMandatory"].value = "*";
	document.forms[0].elements["OnceTimeMandatory"].value = "*";

	document.forms[0].elements["scheduler.scheduleDateAsString"].disabled="";
	document.forms[0].elements["scheduler.scheduleTime"][0].disabled="";

}


function manual()
{
	cyclicDisable();
    onceDisable();
	weeklyDisable();
	monthlyDisable();
	dailyDisable();
}

function daily(){

	cyclicDisable();
    onceDisable();
	weeklyDisable();
	monthlyDisable();
	document.forms[0].elements["dailyTimeMandatory"].value = "*";
	document.forms[0].elements["scheduler.scheduleTime"][1].disabled="";

}
function weekly(){

	cyclicDisable();
	onceDisable();
    dailyDisable();
	monthlyDisable();
   document.forms[0].elements["weeklyTimeMandatory"].value = "*";
	document.forms[0].elements["scheduler.scheduleDayAll"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayMon"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayTue"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayWed"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayThr"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayFri"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySat"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySun"].disabled="";
	document.forms[0].elements["scheduler.scheduleTime"][2].disabled="";
}
function monthly(){

	cyclicDisable();
	onceDisable();
    dailyDisable();
	weeklyDisable();
	document.forms[0].elements["monthlyDayMandatory"].value = "*";
	document.forms[0].elements["monthlyTimeMandatory"].value = "*";
	document.forms[0].elements["scheduler.monthFirst"].disabled="";
	document.forms[0].elements["scheduler.monthLast"].disabled="";
	document.forms[0].elements["scheduler.monthDateAsString"].disabled="";
	document.forms[0].elements["scheduler.scheduleTime"][3].disabled="";
}

function cyclicDisable(){

	 document.forms[0].elements["CyclicDurationmandatory"].value = "";
	document.forms[0].elements["scheduler.durationHoursasString"].value="";
	document.forms[0].elements["scheduler.durationMinsasString"].value="";
	document.forms[0].elements["scheduler.durationSecsasString"].value="";
	document.forms[0].elements["scheduler.durationHoursasString"].disabled="true";
	document.forms[0].elements["scheduler.durationHoursasString"].className='is-disabled';
	document.forms[0].elements["scheduler.durationMinsasString"].disabled="true";
	document.forms[0].elements["scheduler.durationMinsasString"].className='is-disabled';
	document.forms[0].elements["scheduler.durationSecsasString"].disabled="true";
	document.forms[0].elements["scheduler.durationSecsasString"].className='is-disabled';

}
function onceDisable(){

	document.forms[0].elements["OnceDateMandatory"].value = "";
	document.forms[0].elements["OnceTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.scheduleDateAsString"].value="";
	document.forms[0].elements["scheduler.scheduleTime"][0].value="";
	document.forms[0].elements["scheduler.scheduleDateAsString"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDateAsString"].className='is-disabled';
	document.forms[0].elements["scheduler.scheduleTime"][0].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][0].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}
function dailyDisable(){
	document.forms[0].elements["dailyTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.scheduleTime"][1].value="";
	document.forms[0].elements["scheduler.scheduleTime"][1].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][1].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}
function weeklyDisable(){
	weeklyDisableAll(0);
    weeklyDisableOthers(0);
	 document.forms[0].elements["weeklyTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.scheduleTime"][2].value="";
	document.forms[0].elements["scheduler.scheduleTime"][2].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][2].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}
function weeklyDisableAll(flag){

 var checkSun = document.forms[0].elements["scheduler.scheduleDaySun"].checked;
 var checkMon = document.forms[0].elements["scheduler.scheduleDayMon"].checked;
 var checkTue = document.forms[0].elements["scheduler.scheduleDayTue"].checked;
 var checkWed = document.forms[0].elements["scheduler.scheduleDayWed"].checked;
 var checkThu = document.forms[0].elements["scheduler.scheduleDayThr"].checked;
 var checkFri = document.forms[0].elements["scheduler.scheduleDayFri"].checked;
 var checkSat = document.forms[0].elements["scheduler.scheduleDaySat"].checked;

if (checkSun == true|| checkMon == true|| checkTue == true|| checkWed == true|| checkThu == true|| checkFri == true|| checkSat == true || (flag != 'undefined' && flag == '0') ){
	document.forms[0].elements["scheduler.scheduleDayAll"].checked="";
	document.forms[0].elements["scheduler.scheduleDayAll"].disabled="true";
  }else{
    document.forms[0].elements["scheduler.scheduleDayAll"].disabled="";
  }
}
function weeklyDisableOthers(flag){
if ( document.forms[0].elements["scheduler.scheduleDayAll"].checked == true || (flag != 'undefined' && flag == '0')){

	document.forms[0].elements["scheduler.scheduleDayMon"].checked="";
	document.forms[0].elements["scheduler.scheduleDayTue"].checked="";
	document.forms[0].elements["scheduler.scheduleDayWed"].checked="";
	document.forms[0].elements["scheduler.scheduleDayThr"].checked="";
	document.forms[0].elements["scheduler.scheduleDayFri"].checked="";
	document.forms[0].elements["scheduler.scheduleDaySat"].checked="";
	document.forms[0].elements["scheduler.scheduleDaySun"].checked="";
	document.forms[0].elements["scheduler.scheduleDayMon"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayTue"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayWed"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayThr"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayFri"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDaySat"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDaySun"].disabled="true";
  }else{
	    weeklyEnableOthers();
  }
}
function weeklyEnableOthers(){

	document.forms[0].elements["scheduler.scheduleDayMon"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayTue"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayWed"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayThr"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayFri"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySat"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySun"].disabled="";
}

function monthlyDisable(){
	document.forms[0].elements["monthlyDayMandatory"].value = "";
	document.forms[0].elements["monthlyTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.monthFirst"].checked="";
	document.forms[0].elements["scheduler.monthLast"].checked="";
	document.forms[0].elements["scheduler.scheduleTime"][3].value="";
	document.forms[0].elements["scheduler.monthDateAsString"].value="";
	document.forms[0].elements["scheduler.monthFirst"].disabled="true";
	document.forms[0].elements["scheduler.monthLast"].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][3].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][3].className='is-disabled';
	document.forms[0].elements["scheduler.monthDateAsString"].disabled="true";
	document.forms[0].elements["scheduler.monthDateAsString"].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}

function validateForm(objForm) {
  var elementsRef;
  var jobType;
  var radioElements = document.forms[0].elements["scheduler.jobType"];

  for (var idx = 0; idx < radioElements.length; ++idx) {
    if (radioElements[idx].checked)
      jobType = radioElements[idx].value;
  }

  if (jobType == 'O') {
    <c:choose>
      <c:when test="${requestScope.methodName == 'add'}">
        elementsRef = new Array(3);
      </c:when>
      <c:otherwise>
        elementsRef = new Array(2);
      </c:otherwise>
    </c:choose>

    elementsRef[0] = objForm.elements["scheduler.scheduleTime"][0];
    elementsRef[1] = objForm.elements["scheduler.scheduleDateAsString"];

    <c:if test="${requestScope.methodName == 'add'}">
      elementsRef[2] = objForm.elements["job.jobDescription"];
    </c:if>
  }
  else if (jobType == 'C') {
    <c:choose>
      <c:when test="${requestScope.methodName == 'add'}">
        elementsRef = new Array(4);
      </c:when>
      <c:otherwise>
        elementsRef = new Array(3);
      </c:otherwise>
    </c:choose>

    elementsRef[0] = objForm.elements["scheduler.durationHoursasString"];
    elementsRef[1] = objForm.elements["scheduler.durationMinsasString"];
    elementsRef[2] = objForm.elements["scheduler.durationSecsasString"];

    <c:if test="${requestScope.methodName == 'add'}">
      elementsRef[3] = objForm.elements["job.jobDescription"];
    </c:if>
  }
  else if (jobType == 'D') {
    <c:choose>
      <c:when test="${requestScope.methodName == 'add'}">
        elementsRef = new Array(2);
      </c:when>
      <c:otherwise>
        elementsRef = new Array(1);
      </c:otherwise>
    </c:choose>

    elementsRef[0] = objForm.elements["scheduler.scheduleTime"][1];

    <c:if test="${requestScope.methodName == 'add'}">
      elementsRef[1] = objForm.elements["job.jobDescription"];
    </c:if>
  }
  else if (jobType == 'W') {
    <c:choose>
      <c:when test="${requestScope.methodName == 'add'}">
        elementsRef = new Array(2);
      </c:when>
      <c:otherwise>
        elementsRef = new Array(1);
      </c:otherwise>
    </c:choose>

    elementsRef[0] = objForm.elements["scheduler.scheduleTime"][2];

    <c:if test="${requestScope.methodName == 'add'}">
      elementsRef[1] = objForm.elements["job.jobDescription"];
    </c:if>
  }
  else if (jobType == 'M') {
    <c:choose>
      <c:when test="${requestScope.methodName == 'add'}">
        elementsRef = new Array(2);
      </c:when>
      <c:otherwise>
        elementsRef = new Array(1);
      </c:otherwise>
    </c:choose>

    elementsRef[0] = objForm.elements["scheduler.scheduleTime"][3];

    <c:if test="${requestScope.methodName == 'add'}">
      elementsRef[1] = objForm.elements["job.jobDescription"];
    </c:if>
  }
  else if (jobType == 'N') {
    <c:choose>
      <c:when test="${requestScope.methodName == 'add'}">
        elementsRef = new Array(1);
        elementsRef[0] = objForm.elements["job.jobDescription"];
      </c:when>
      <c:otherwise>
        elementsRef = new Array(0);
      </c:otherwise>
    </c:choose>
  }

  return validate(elementsRef);
}

function validationForMonthTypeFirstDay()
{

		document.forms[0].elements["scheduler.monthLast"].checked = "";
		document.forms[0].elements["scheduler.monthDateAsString"].value = "";
}


function validationForMonthTypeLastDay()
{
	document.forms[0].elements["scheduler.monthFirst"].checked = "";
	document.forms[0].elements["scheduler.monthDateAsString"].value = "";

}
function validationForMonthTypeDate()
{
	document.forms[0].elements["scheduler.monthFirst"].checked = "";
	document.forms[0].elements["scheduler.monthLast"].checked = "";

}

/**
* This method is used to check the maximum character length
*
* @param field
* @param maxChars
* @return none
*/
function maxLengthTextArea (field,maxChars)
{
	if(field.value.length > maxChars)
		field.value = field.value.substring(0, maxChars);
}

function jobTypeChange()
{
	submitFormJobType("add");
}

function submitFormJobType(methodName)
{
	document.forms[0].method.value = methodName;
	document.forms[0].selectedJobType.value = document.forms[0].elements["job.jobType"].value;
	document.forms[0].submit();
}


function jobDescriptionChange()
{
	submitFormJobDescription("add");
}

function submitFormJobDescription(methodName)
{
	document.forms[0].method.value = methodName;
	document.forms[0].selectedJobType.value = document.forms[0].elements["job.jobType"].value;
	document.forms[0].selectedJobDescription.value = document.forms[0].elements["job.jobDescription"].value;
	document.forms[0].submit();
}

function onmouseoutfirstsc(tab, element)
{
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		revertback(tab,element);

	}
}

function onmouseoverfirstsc(tab, element)
{
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		changecontent(tab,element);
	}
}

function onClickfirstsc(tab, element)
{
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		changeselected(tab);
		expandcontent(tab, this);
	}
}

/** This function is passes  url base on  screen and display the subscreens
* @ param MethodName
* @ param screenName
* @ return param
*/
function buildDistList(methodName)
{
	var param = 'scheduler.do?method='+methodName;
	param+= '&selectedUsers='+document.forms[0].selectedUsers.value;
	param+= '&selectedRoles='+document.forms[0].selectedRoles.value;
    return param;
}

/** This function is passes  url base on  screen and display the subscreens
* @ param MethodName
* @ param screenName
* @ return param
*/
function buildAccessList(methodName)
{
	var param = 'scheduler.do?method='+methodName;
    return param;
}

function buildConfigParams()
{

	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	requestURL = requestURL + appName+"/scheduler.do?method=getScreenDetails";
	requestURL = requestURL + "&menuItemId=" + menuItemId;
	var oXMLHTTP = new XMLHttpRequest();
	oXMLHTTP.open( "POST", requestURL, false );
	oXMLHTTP.send();
	var screenDetails=new String(oXMLHTTP.responseText);

	var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
	var programName = screenDetailsList[0];
	if (programName.indexOf("?") == -1)
		programName += '?';
	var width = screenDetailsList[1];
	var height = screenDetailsList[2];

	param= '&configScheduler=true';
	param+= '&newILMReportSchedConfig='+newILMReportSchedConfig;
	param+= '&newOpportunityCostReportSchedConfig='+newOpportunityCostReportSchedConfig;
	param+= '&newTurnoverReportSchedConfig='+newTurnoverReportSchedConfig;
	param+= '&newCurrencyFundingReportSchedConfig='+newCurrencyFundingReportSchedConfig;
	param+= '&newInterestChargesReportSchedConfig='+newInterestChargesReportSchedConfig;
	param+= '&jobId='+document.forms[0].selectedjobId.value;
	param+= '&roleId='+document.forms[0].elements["scheduledReportParams.executionRole"].value;
	param+= '&reportType='+document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
	param+= '&schedulerConfigXML='+document.forms[0].schedulerConfigXML.value;
	openWindow(programName + param, 'confiParamsScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
}

function showXML() {
	var param = 'scheduler.do?method=showXMLWindow';
	param+="&schedulerConfigXML="+document.forms[0].schedulerConfigXML.value;
	openWindow(param,'showXMLScreenWindow','left=50,top=190,width=560,height=455,toolbar=0, resizable=yes, scrollbars=yes','true');

}

function updateSchedulerConfigParams(outputFormat)
{
	if(outputFormat!=""){
	document.forms[0].elements["scheduledReportParams.outputFormat"].value=outputFormat;
	}
	newILMReportSchedConfig = "false";
	newOpportunityCostReportSchedConfig = "false";
	newTurnoverReportSchedConfig = "false";
	newCurrencyFundingReportSchedConfig = "false";
	newInterestChargesReportSchedConfig = "false"
	if(checkIfConfigParamsIsCorrect())
		parametersStatusValue = 'configCorrect';
	else
		parametersStatusValue = 'configIncorrect';

	updateParametersStatus(parametersStatusValue);
}

function updateParametersStatus(parametersStatusValue) {
	if (parametersStatusValue == 'noConfig') {
		document.getElementById("showXMLButton").disabled=true;
		document.getElementById("showXMLButton").className = "disabled";
		document.getElementById("parametersStatusInfo").innerHTML = "<fmt:message key="addjob.label.configParamStatusNoConfig"/>";
		document.getElementById("parametersStatusInfo").style.color = "black";
	} else if (parametersStatusValue == 'configCorrect') {
		document.getElementById("showXMLButton").disabled = "";
		document.getElementById("showXMLButton").className = "";
		document.getElementById("parametersStatusInfo").innerHTML = "<fmt:message key="addjob.label.configParamStatusCorrectConfig"/>";
		document.getElementById("parametersStatusInfo").style.color = "green";
	} else if (parametersStatusValue == 'configIncorrect') {
		document.getElementById("showXMLButton").disabled = "";
		document.getElementById("showXMLButton").className = "";
		document.getElementById("parametersStatusInfo").innerHTML = "<fmt:message key="addjob.label.configParamStatusIncorrectConfig"/>";
		document.getElementById("parametersStatusInfo").style.color = "red";
	}
}

function checkParametersConifBeforeSubmit(cameFrom) {

	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		if (parametersStatusValue == 'noConfig') {
			alert('<fmt:message key="addjob.alert.noConfigBeforeSave"/>');
		} else {
			var selectedJobId = document.forms[0].selectedjobId.value;
			if (selectedJobId == 100){
				if ( checkIfConfigParamsIsCorrect()) {
					if (cameFrom == "add") {
						submitForm('Scheduleradd');
					} else if (cameFrom == "change") {
						updateSubmitForm('schedulerChange');
					}
				} else {
					alert("<fmt:message key="addjob.alert.paramConfigNotCorrectBeforeSave"/>" + configIncorrectCause);
				}
			}else {
				if (cameFrom == "add") {
					submitForm('Scheduleradd');
				} else if (cameFrom == "change") {
					updateSubmitForm('schedulerChange');
				}
			}
		}
	} else {
		if (cameFrom == "add") {
			submitForm('Scheduleradd');
		} else if (cameFrom == "change") {
			updateSubmitForm('schedulerChange');
		}
	}
}

function onRoleChange() {
	if (parametersStatusValue != 'noConfig') {
		var yourstate=window.confirm('By changing the selected role, the existing report job parameters will be reset. Are you sure to continue?');
		if (yourstate!=true) {
			document.forms[0].elements["scheduledReportParams.executionRole"].value = selectedRoleBeforeChange;
			document.getElementById("roleName").innerHTML = selectedRoleNameBeforeChange;
		} else {
			selectedRoleBeforeChange = document.forms[0].elements["scheduledReportParams.executionRole"].value;
			selectedRoleNameBeforeChange = document.getElementById("roleName").innerHTML;
			initParametersConfig();
		}
	} else {
		selectedRoleBeforeChange = document.forms[0].elements["scheduledReportParams.executionRole"].value;
		selectedRoleNameBeforeChange = document.getElementById("roleName").innerHTML;
	}
}

function onReportTypeChange() {
	if ('${methodName}' == "add") {
		var e = document.forms[0].elements["scheduledReportParams.reportTypeId"];
		if (e.options[reportTypePreviousSelectedIndex].text == document.forms[0].elements["scheduledReportParams.reportName"].value) {
			document.forms[0].elements["scheduledReportParams.reportName"].value = e.options[e.selectedIndex].text;
			reportTypePreviousSelectedIndex = e.selectedIndex;
		}
		if (parametersStatusValue != 'noConfig') {
			var yourstate=window.confirm('By changing the selected report type, the existing report parameter configuration values will be reset. Are you sure to continue?');
			if (yourstate!=true) {
				document.forms[0].elements["scheduledReportParams.reportTypeId"].value = selectedReportTypeBeforeChange;
				document.forms[0].elements["scheduledReportParams.reportName"].value =selectedReportNameBeforeChange;
			} else {
				selectedReportTypeBeforeChange = document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
				selectedReportNameBeforeChange = document.forms[0].elements["scheduledReportParams.reportName"].value;
				initParametersConfig();
			}
		} else {
			selectedReportTypeBeforeChange = document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
			selectedReportNameBeforeChange = document.forms[0].elements["scheduledReportParams.reportName"].value;
		}
		setJobReportTypes();
	}
}

function initParametersConfig() {
	reportSchedulerData = new Object();
	parametersStatusValue = "noConfig";
	newILMReportSchedConfig = true;
	newOpportunityCostReportSchedConfig = true;
	newTurnoverReportSchedConfig = true;
	newCurrencyFundingReportSchedConfig = true;
	updateParametersStatus(parametersStatusValue);
}

function setJobReportTypes() {
	for (var j = 0; j < schedReportTypeParamsAsJson.length; j++) {
    	if (schedReportTypeParamsAsJson[j].reportTypeId == document.forms[0].elements["scheduledReportParams.reportTypeId"].value) {
    		mapDateMethod = schedReportTypeParamsAsJson[j].mapDateMethod;
    		outputFormat = schedReportTypeParamsAsJson[j].outputFormat.split(',')[0];
    		menuItemId = schedReportTypeParamsAsJson[j].menuItemId;
    		break;
    	}
    }

	if ('${methodName}' == "add") {
	document.forms[0].elements["scheduledReportParams.outputFormat"].value= outputFormat;
	}
	// Update the run date as value in the screen
	if (mapDateMethod == 'S') {
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].className='is-disabled';
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled=false;
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled="true";
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].checked = true;
	} else if (mapDateMethod == 'E') {
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].className='is-disabled';
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled="true";
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled=false;
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].checked = true;
	} else if (mapDateMethod == 'S,E') {
		if ('${methodName}' == "add") {
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].checked = true;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled=false;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled=false;
		}
	} else if (mapDateMethod == 'E,S') {
		if ('${methodName}' == "add") {
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].checked = true;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled=false;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled=false;
		}
	}
	// Update output format provide
	/*var outputFormatAsArray = outputFormat.split(",");
	var outputFormatSelect = document.forms[0].elements["scheduledReportParams.outputFormat"];
	outputFormatSelect.innerHTML = "";
	for (var k = 0; k < outputFormatAsArray.length; k++) {
		 var option = document.createElement("option");
		 option.value = outputFormatAsArray[k];
		 option.text = outputFormatAsArray[k];
		 outputFormatSelect.appendChild(option);
	}*/
}

function showAlertConfigIncorrectCause() {
	if (parametersStatusValue == "configIncorrect") {
		alert(configIncorrectCause);
	}
}

function onMouseOverPararmStatus() {
	if (parametersStatusValue == "configIncorrect") {
		document.getElementById("parametersStatusInfo").style.cursor  = "pointer";
		document.getElementById("parametersStatusInfo").style.textDecoration  = "underline";
	}
}
function onMouseOutParamStatus() {
	if (parametersStatusValue == "configIncorrect") {
		document.getElementById("parametersStatusInfo").style.cursor  = "";
		document.getElementById("parametersStatusInfo").style.textDecoration  = "none";
	}
}

function checkIfConfigParamsIsCorrect() {
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	requestURL = requestURL + appName+"/scheduler.do?method=checkIfConfigParamsIsCorrect";
	requestURL += "&reportConfig=" + document.forms[0].schedulerConfigXML.value;
	requestURL += "&executionRole=" + document.forms[0].elements["scheduledReportParams.executionRole"].value;
	var oXMLHTTP = new XMLHttpRequest();
	oXMLHTTP.open( "POST", requestURL, false );
	oXMLHTTP.send();
	var result=new String(oXMLHTTP.responseText);
	var resultList = result.split("<%=SwtConstants.SEPARATOR_FACILITIES%>");
	var configIsCorrect = resultList[0];
	if (configIsCorrect == "false") {
		parametersStatusValue = "configIncorrect";
		updateParametersStatus(parametersStatusValue);
		configIncorrectCause = resultList[1];
		return false;
	} else {
		return true;
	}
}

function enableFields(){
	document.forms[0].elements["scheduledReportParams.reportTypeId"].disabled = "";
}

function handleMapDateMethodChange(radio) {
    // Get the select and checkbox elements
    var selectElement = document.forms[0].elements["scheduledReportParams.runDateEntity"];
    var checkboxElement = document.forms[0].elements["scheduledReportParams.runDateWorkdays"];
    // Check if the first radio button is selected
    if (radio.name === "scheduledReportParams.mapDateMethod" && radio.value === "S") {
        // Disable the select and checkbox
        selectElement.disabled = true;
        selectElement.className = 'is-disabled';
        checkboxElement.disabled = true;
        checkboxElement.className = 'is-disabled';
    } else {
        // Enable the select and checkbox if the second radio button is selected
        selectElement.disabled = false;
        selectElement.className = '';
        checkboxElement.disabled = false;
        checkboxElement.className = '';
    }
}
</SCRIPT>
  <script language="JAVASCRIPT">
      var cal3 = new CalendarPopup("caldiv",true);
	   cal3.offsetX = 5;
      cal3.offsetY = -115;
      var cal4 = new CalendarPopup("caldiv");

  </script>

<style>

#ddimagetabs #firstsc{
   background-image: url(images/new_bluetaboverBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.default{
   background-image: url(images/new_bluetaboverBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.current{
   background-image: url(images/new_bluetabBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.hoverall{
   background-image: url(images/new_bluetabover_glowBIS.gif); /* URL to tab image */
}

#parametersStatusInfo.hover{
	text-decoration: underline;
}

</style>
</head>
<!--Start:Code Modified by Chinniah on 4-JUL-2011 for Mantis 1488:For Toll tip and alignment issues -->
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="scheduler.do" onsubmit="validate(this);" method="post">
<input name="selectedjobId" type="hidden" value="GA">
<input name="selectedJobType" type="hidden" value="">
<input name="changeAndRunNow" type="hidden" value="false">
<input name="selectedJobDescription" type="hidden" value="">
<input name="selectedUsers" type="hidden" value="">
<input name="selectedRoles" type="hidden" value="">
<input name="selectedEmailUsers" type="hidden" value="">
<input name="selectedEmailRoles" type="hidden" value="">
<input name="method" type="hidden" value="displaySchedule">
<input name="sourceScreen" type="hidden" value="addJob">
<input name="schedulerConfigXML" type="hidden" value="">
<input name="scheduledJobType" type="hidden" value="">
<input name="reportTypePreviousSelectedIndex" type="hidden" value="">
	<%
		String ADDJOB_ENABLE = org.swallow.util.SwtConstants.ADDJOB_ENABLE;
		String ADDJOB_DISABLE = org.swallow.util.SwtConstants.ADDJOB_DISABLE;
		pageContext.setAttribute("ADDJOB_ENABLE", ADDJOB_ENABLE);
		pageContext.setAttribute("ADDJOB_DISABLE", ADDJOB_DISABLE);

	%>

<div id="JobDetails" style="z-index:99;position:absolute; left:10px; top:10px; width:615px; height:72px; border: 2px outset ; " color="#7E97AF">
	<div id="JobDetails" style="position:absolute; z-index:99; left:8; top:4px; width:608px; height:20px;">
    <input type="hidden" name="scheduler.scheduleId" value="${scheduler.scheduleId}" />

    <table width="610" border="0" cellpadding="2" cellspacing="0" height="22" style="padding-top: 3px;">
        <tr height="22px">
            <!-- Job Type Label -->
            <td width="73px" align="left">
                <b><fmt:message key="addJob.title.jobType"/></b>
                <c:if test="${requestScope.methodName == 'add'}">*</c:if>
            </td>

            <!-- Job Type Input -->
            <td width="110px">
                <c:choose>
                    <c:when test="${requestScope.methodName == 'add'}">
                        <c:choose>
                            <c:when test="${requestScope.scheduledJobType == 'R'}">
                                <div style="width:100px">
                                    <input type="text" name="job.jobType" tabindex="1" style="width:100px;" readonly="readonly" value="${job.jobType}" />
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div style="width:100px">
                                    <select id="job.jobType" name="job.jobType" titleKey="tooltip.selectJobType" tabindex="1"
                                            style="width:100px"
                                            onchange="jobTypeChange()" <c:if test="${screenFieldsStatus}">disabled</c:if>>
                                        <c:forEach var="item" items="${requestScope.jobTypeList}">
                                            <option value="${item.value}" <c:if test="${job.jobType == item.value}">selected</c:if>>${item.label}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </c:when>
                    <c:when test="${requestScope.methodName == 'change'}">
                        <input type="text" name="job.jobType" tabindex="1" style="width:100px;" readonly="readonly" value="${job.jobType}" />
                    </c:when>
                    <c:when test="${requestScope.methodName == 'view'}">
                        <input type="text" name="job.jobType" tabindex="1" style="width:100px;" value="${job.jobType}"
                               <c:if test="${screenFieldsStatus}">disabled</c:if> />
                    </c:when>
                </c:choose>
            </td>

            <!-- Job Name Label -->
            <td width="73px" align="left">
                <b><fmt:message key="Jobmaintenance.JobName"/></b>
                <c:if test="${requestScope.methodName == 'add'}">*</c:if>
            </td>

            <!-- Job Name Input -->
            <td width="93px">
                <c:choose>
                    <c:when test="${requestScope.methodName == 'add'}">
                        <div style="width:290px">
                            <select id="job.jobDescription" name="job.jobDescription" titleKey="tooltip.selectJobName" tabindex="1"
                                    style="width:290px"
                                    onchange="jobDescriptionChange()" <c:if test="${screenFieldsStatus}">disabled</c:if>>
                                <c:forEach var="item" items="${requestScope.jobNameList}">
                                    <option value="${item.value}" <c:if test="${job.jobDescription == item.value}">selected</c:if>>${item.label}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </c:when>
                    <c:when test="${requestScope.methodName == 'change'}">
                        <input type="text" name="scheduler.job.jobDescription" tabindex="1" style="width:290px;" readonly="readonly"
                               value="${scheduler.job.jobDescription}" />
                    </c:when>
                    <c:when test="${requestScope.methodName == 'view'}">
                        <input type="text" name="scheduler.job.jobDescription" tabindex="1" style="width:290px;"
                               value="${scheduler.job.jobDescription}" <c:if test="${screenFieldsStatus}">disabled</c:if> />
                    </c:when>
                </c:choose>
            </td>
        </tr>
    </table>

    <!-- Job Status Radio Buttons -->
    <table width="344" border="0" cellpadding="0" cellspacing="1" height="40">
        <tr height="22px">
            <td width="75px"><b><fmt:message key="addjob.jobStatus"/></b></td>
            <td width="13px">&nbsp;</td>

            <td width="100px">
              <input type="radio" id="2" name="scheduler.jobStatus" value="<%=ADDJOB_ENABLE%>"
				   tabindex="2" style="width:13px;"
				   titleKey="tooltip.enable"
				   <c:if test="${scheduler.jobStatus == ADDJOB_ENABLE}">checked="checked"</c:if> />
			<label tabindex="2" title='<fmt:message key="tooltip.enable"/>' for="2">
				&nbsp;&nbsp;<fmt:message key="addjob.enable"/>&nbsp;&nbsp;&nbsp;
			</label>
            </td>

            <td width="100px">
                <input type="radio" id="3" name="scheduler.jobStatus" value="<%=ADDJOB_DISABLE%>"
					   tabindex="3" style="width:13px;"
					   titleKey="tooltip.disable"
					   <c:if test="${scheduler.jobStatus == ADDJOB_DISABLE}">checked="checked"</c:if> />
				<label tabindex="3" title='<fmt:message key="tooltip.disable"/>' for="3">
					&nbsp;&nbsp;<fmt:message key="addjob.disable"/>
				</label>
            </td>

            <td width="28px">&nbsp;</td>
            <td width="55px">&nbsp;</td>
        </tr>
    </table>
</div>


</div>

	<div id="ddimagetabs"
		style=" left:10px; position: absolute;  top: 90px; width: 320px; height: 20px;">

		<a href="#" onmouseout="revertback('sc1',this);" tabindex="3"
							onmouseover="changecontent('sc1',this)"
							onClick="changeselected('sc1');expandcontent('sc1', this)">
					<b><fmt:message key="addJob.tab.scheduling"/></b>
		</a>
		<a href="#" id="firstsc" onmouseout="onmouseoutfirstsc('sc2',this);" tabindex="4"
					onmouseover="onmouseoverfirstsc('sc2',this)"
					onClick="onClickfirstsc('sc2', this)" style="width: 120px;leftmargin:-20px;">
			<b><fmt:message key="addJob.tab.reportSettings"/></b>
      	</a>
	</div>
	<div id="Line"
		style="position: absolute; left: 210px; top: 108px; width: 417px; height: 20px;">
	<table width="100%">
		<tr>
			<td><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
	</table>
	</div>
<div id="sc1" class="tabcontent">
 <div id="JobDetails" style="z-index:99; position:absolute;left:10px; top:112px; width:615px; height:380px; border: 2px outset ; " color="#7E97AF">
 <div id="JobDetails" style="z-index:99;position:absolute; left:2px; top:6px; width:560px; height:22px; " color="#7E97AF">
	  <table width="550" border="0" cellspacing="1" cellpadding="0" height="22" style="margin-left: 10px;">
	    <tr height="22px">
		  <td  width="95" height="10px" align="left">&nbsp;<b><fmt:message key="addJob.title.frequency"/></b></td>
		   <td width="23px">&nbsp;</td>
		  <td width="375px" style="word-spacing: 2.9px;">


		  <%
				String JOB_TYPE_MANUAL = org.swallow.util.SwtConstants.JOB_TYPE_MANUAL;
			  pageContext.setAttribute("JOB_TYPE_MANUAL", JOB_TYPE_MANUAL);

		  %>

			<input type="radio" id="4" tabindex="4" style="width:13px;" titleKey="tooltip.once"
				   name="scheduler.jobType" value="O"
				   <c:if test="${scheduler.jobType == 'O'}">checked="checked"</c:if>
				   <c:if test="${screenFieldsStatus}">disabled</c:if>
				   onclick="once();" />
			<label title='<fmt:message key="tooltip.once"/>' for="4"><fmt:message key="addjob.Once"/></label>&nbsp;

			<input type="radio" id="5" tabindex="5" style="width:13px;" titleKey="tooltip.cyclic"
				   name="scheduler.jobType" value="C"
				   <c:if test="${scheduler.jobType == 'C'}">checked="checked"</c:if>
				   <c:if test="${screenFieldsStatus}">disabled</c:if>
				   onclick="cyclic();" />
			<label title='<fmt:message key="tooltip.cyclic"/>' for="5"><fmt:message key="addjob.Cyclic"/></label>&nbsp;

			<input type="radio" id="6" tabindex="6" style="width:13px;" titleKey="tooltip.daily"
				   name="scheduler.jobType" value="D"
				   <c:if test="${scheduler.jobType == 'D'}">checked="checked"</c:if>
				   <c:if test="${screenFieldsStatus}">disabled</c:if>
				   onclick="daily();" />
			<label title='<fmt:message key="tooltip.daily"/>' for="6"><fmt:message key="addjob.Daily2"/></label>&nbsp;

			<input type="radio" id="7" tabindex="7" style="width:13px;" titleKey="tooltip.weekly"
				   name="scheduler.jobType" value="W"
				   <c:if test="${scheduler.jobType == 'W'}">checked="checked"</c:if>
				   <c:if test="${screenFieldsStatus}">disabled</c:if>
				   onclick="weekly();" />
			<label title='<fmt:message key="tooltip.weekly"/>' for="7"><fmt:message key="addjob.Weekly2"/></label>&nbsp;

			<input type="radio" id="8" tabindex="8" style="width:13px;" titleKey="tooltip.monthly"
				   name="scheduler.jobType" value="M"
				   <c:if test="${scheduler.jobType == 'M'}">checked="checked"</c:if>
				   <c:if test="${screenFieldsStatus}">disabled</c:if>
				   onclick="monthly();" />
			<label title='<fmt:message key="tooltip.monthly"/>' for="8"><fmt:message key="addjob.Monthly2"/></label>

			<input type="radio" id="9" tabindex="9" style="width:13px;" titleKey="tooltip.manual"
				   name="scheduler.jobType" value="<%= JOB_TYPE_MANUAL %>"
				   <c:if test="${scheduler.jobType == JOB_TYPE_MANUAL}">checked="checked"</c:if>
				   <c:if test="${screenFieldsStatus}">disabled</c:if>
				   onclick="manual();" />
			<label title='<fmt:message key="tooltip.manual"/>' for="9"><fmt:message key="addjob.Manual"/></label>




		  </td>
	  </tr>
	  </table>


	  <div style="left:8px; top:4px;height:40px;margin-left: 10px;">
	     <fieldset style="width:583px;border:2px groove;">
		<legend><fmt:message key="addjob.Once"/></legend>
		  <table width="381" border="0" cellspacing="0" cellpadding="0" height="22">
		  <tr height="22px">

		    <td width="100px" align="left">&nbsp;<b><fmt:message key="addjob.Date"/></b><input tabindex="-1" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="OnceDateMandatory" size="1"></td>

			  <td width="50px" style="padding-right:25px;">&nbsp;</td>

		   <td  width="150px"  align="left">

			<input type="text"
			   name="scheduler.scheduleDateAsString"
			   value="${scheduler.scheduleDateAsString}"
			   maxlength="10"
			   tabindex="11"
			   style="width:78px;height: 21px;"
			   titleKey="tooltip.enterDate"
			   onchange="validateField(this,'scheduler.scheduleDateAsString',dateFormat);"
			   <c:if test="${screenFieldsStatus}">disabled</c:if> />



			<a title='<fmt:message key="tooltip.accountMonitorNew.date"/>'
			   name="datelink3"
			   id="datelink3"
			   onclick="cal3.select(document.forms[0].elements['scheduler.scheduleDateAsString'], 'datelink3', '${sessionScope.CDM.dateFormatValue}'); return false;">
			   <img style="margin-bottom: -4px;" src="images/calendar-16.gif" />
			</a>


		        </td>
				  <td width="28px">&nbsp;</td>
				  <c:choose>
					<c:when test="${requestScope.methodName != 'view'}">
						<td width="40px" align="left">
							<nobr>
								<b><fmt:message key="addjob.Time"/></b>
								<input tabindex="-1" class="textAlpha" style="background:transparent; border: thin;"
									   readonly name="OnceTimeMandatory" size="1" />
							</nobr>
						</td>
					</c:when>
					<c:otherwise>
						<td width="40px" align="left">
							<b><fmt:message key="addjob.Time"/></b>
						</td>
					</c:otherwise>
				</c:choose>


				     <td width="28px">&nbsp;</td>
		  <td  width="45px">
			<input type="text"
			   name="scheduler.scheduleTime"
			   value="${scheduler.scheduleTime}"
			   maxlength="5"
			   tabindex="10"
			   style="width:43px;"
			   titleKey="tooltip.enterTime"
			   onchange="return validateField(this,'Number 2','timePat');"
			   <c:if test="${screenFieldsStatus}">disabled</c:if> />

		  </td>
		  </tr>
		  </table>
	    </fieldset>
		</div>
		 <div style="left:8px; top:4px;height:67px;margin-left: 10px;">


	  <fieldset style="width:583px;border:2px groove;">
		<legend><fmt:message key="tooltip.cyclic"/></legend>
		  <table width="286" border="0" cellspacing="0" cellpadding="0" height="50">
         <tr height="22px">

		  <td  width="135px"  align="left">&nbsp;<b><fmt:message key="addjob.CycleDuration"/></b><input tabindex="-1"  class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="CyclicDurationmandatory" size="1"></td>

		   <td   width="155px" height="20px" align="left">
			   <input type="text"
					  name="scheduler.durationHoursasString"
					  value="${scheduler.durationHoursasString}"
					  maxlength="2"
					  tabindex="13"
					  style="width:35px;"
					  titleKey="tooltip.durationHours"
					  onchange="return validateField(this,'Number 2','numberPatExpand',23,0);"
					  <c:if test="${screenFieldsStatus}">disabled</c:if> />
			   :

			   <input type="text"
					  name="scheduler.durationMinsasString"
					  value="${scheduler.durationMinsasString}"
					  maxlength="2"
					  tabindex="11"
					  style="width:35px;"
					  titleKey="tooltip.durationMins"
					  onchange="return validateField(this,'Number 2','numberPatExpand',59,0);"
					  <c:if test="${screenFieldsStatus}">disabled</c:if> />
			   :

			   <input type="text"
					  name="scheduler.durationSecsasString"
					  value="${scheduler.durationSecsasString}"
					  maxlength="2"
					  tabindex="12"
					  style="width:35px;"
					  titleKey="tooltip.durationSecs"
					  onkeydown="return checkZero();"
					  onchange="return validateField(this,'Number 2','numberPatExpand',59,0);"
					  <c:if test="${screenFieldsStatus}">disabled</c:if> />
		   </td>
			</tr>
			<tr height="22px">
			 <td  width="133px"> </td>

			  <td   width="155px">&nbsp;<fmt:message key="addjob.Hours"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="addjob.Minutes"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="addjob.Seconds"/>
			  </td>
			</tr>
	    </table>
	   </fieldset>
	   </div>

		  <div style="left:8px; top:4px;height:41px;margin-left: 10px;">
	     <fieldset style="width:583px;border:2px groove;">
		<legend><fmt:message key="addjob.Daily"/></legend>
		  <table width="178" border="0" cellspacing="1" cellpadding="0" height="22">
		  <tr height="22px">
				<c:choose>
				<c:when test="${requestScope.methodName != 'view'}">
					<td width="100px" align="left">
						&nbsp;<b><fmt:message key="addjob.Time"/></b>
						<input tabindex="-1"
							   class="textAlpha"
							   style="background:transparent; border: thin;"
							   readonly
							   name="dailyTimeMandatory"
							   size="1" />
					</td>
				</c:when>
				<c:otherwise>
					<td width="100px" align="left">
						&nbsp;<b><fmt:message key="addjob.Time"/></b>
					</td>
				</c:otherwise>
			</c:choose>


			  <td width="28px">&nbsp;</td>
		 <td width="45px">
				<input type="text"
				   name="scheduler.scheduleTime"
				   value="${scheduler.scheduleTime}"
				   maxlength="5"
				   tabindex="13"
				   style="width:45px;"
				   titleKey="tooltip.enterTime"
				   onchange="return validateField(this,'Number 2','timePat');"
				   <c:if test="${screenFieldsStatus}">disabled</c:if> />

				</tr>
		</table>
		 </fieldset>
		 </div>

		    <div style="left:8px; top:4px;height:127px;margin-left: 10px;">
		 <fieldset style="width:583;border:2px groove;">
		<legend><fmt:message key="addjob.Weekly"/></legend>
		  <table width="367" border="0" cellspacing="0" cellpadding="0" height="110">

			  <tr height="22px">
					<c:choose>
					<c:when test="${requestScope.methodName != 'view'}">
						<td width="100px">
							&nbsp;<b><fmt:message key="addjob.Time"/></b>
							<input tabindex="-1"
								   class="textAlpha"
								   style="background:transparent; border: thin;"
								   readonly
								   name="weeklyTimeMandatory"
								   size="1" />
						</td>
					</c:when>
					<c:otherwise>
						<td width="100px">
							&nbsp;<b><fmt:message key="addjob.Time"/></b>
						</td>
					</c:otherwise>
				</c:choose>


				  <td width="28px">&nbsp;</td>


				<td width="100px">
					<input type="text"
						   name="scheduler.scheduleTime"
						   value="${scheduler.scheduleTime}"
						   maxlength="5"
						   tabindex="14"
						   style="width:45px;"
						   titleKey="tooltip.enterTime"
						   onchange="return validateField(this,'Number 2','timePat');"
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
				</td>
				</tr>

				<tr height="22px">
					<td width="100px"></td>
					<td width="28px">&nbsp;</td>
					<td width="100px">
						<input type="checkbox"
							   name="scheduler.scheduleDayAll"
							   value="ALL"
							   tabindex="15"
							   style="width:13px;"
							   titleKey="tooltip.all"
							   onclick="weeklyDisableOthers();"
							   <c:if test="${scheduler.scheduleDayAll == 'ALL'}">checked="checked"</c:if>
							   <c:if test="${screenFieldsStatus}">disabled</c:if> />
						&nbsp;&nbsp;<fmt:message key="addjob.All"/>
					</td>

					<td width="28px">&nbsp;</td>


				<td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDaySun"
						   value="SUN"
						   tabindex="16"
						   style="width:13px;"
						   titleKey="tooltip.sunday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDaySun == 'SUN'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;&nbsp;<fmt:message key="addjob.Sunday"/>
				</td>

				</tr>
				<tr height="22px">
				 <td width="100px" ></td>
				 <td width="28px">&nbsp;</td>

				<td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDayMon"
						   value="MON"
						   tabindex="17"
						   style="width:13px;"
						   titleKey="tooltip.monday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDayMon == 'MON'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;<fmt:message key="addjob.Monday"/>
				</td>

				<td width="28px">&nbsp;</td>

				<td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDayTue"
						   value="TUE"
						   tabindex="18"
						   style="width:13px;"
						   titleKey="tooltip.tuesday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDayTue == 'TUE'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;&nbsp;<fmt:message key="addjob.Tuesday"/>
				</td>

				</tr>
				<tr height="22px">
				 <td width="100px" ></td>
				 <td width="28px">&nbsp;</td>

				 <td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDayWed"
						   value="WED"
						   tabindex="19"
						   style="width:13px;"
						   titleKey="tooltip.wednesday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDayWed == 'WED'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;<fmt:message key="addjob.Wednesday"/>
				</td>

				<td width="28px">&nbsp;</td>

				<td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDayThr"
						   value="THU"
						   tabindex="20"
						   style="width:13px;"
						   titleKey="tooltip.thursday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDayThr == 'THU'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;&nbsp;<fmt:message key="addjob.Thursday"/>
				</td>

		  </tr>
		<tr height="22px">
				 <td width="100px" ></td>
				 <td width="28px">&nbsp;</td>

				<td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDayFri"
						   value="FRI"
						   tabindex="21"
						   style="width:13px;"
						   titleKey="tooltip.friday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDayFri == 'FRI'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;&nbsp;<fmt:message key="addjob.Friday"/>
				</td>

				<td width="28px">&nbsp;</td>

				<td width="100px">
					<input type="checkbox"
						   name="scheduler.scheduleDaySat"
						   value="SAT"
						   tabindex="22"
						   style="width:13px;"
						   titleKey="tooltip.saturday"
						   onclick="weeklyDisableAll();"
						   <c:if test="${scheduler.scheduleDaySat == 'SAT'}">checked="checked"</c:if>
						   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					&nbsp;&nbsp;<fmt:message key="addjob.Saturday"/>
				</td>

		  </tr>

		 </table>


		  </fieldset>
		  </div>
		   <div style="left:8px; top:4px;height:61px;margin-left: 10px;">
		 <fieldset style="width:583;border:2px groove;">
		<legend><fmt:message key="addjob.Monthly"/></legend>
		  <table width="385" border="0" cellspacing="0" cellpadding="0" height="22">
				<tr height="22px">
					<td width="100px">
						&nbsp;<nobr><b><fmt:message key="addjob.DayMonth"/></b>
						<input tabindex="-1" class="textAlpha"
							   style="background:transparent; border: thin;"
							   readonly name="monthlyDayMandatory" size="1" />
						</nobr>
					</td>

					<td width="11px">&nbsp;</td>

					<td width="112px">
						<input type="text"
							   name="scheduler.monthDateAsString"
							   value="${scheduler.monthDateAsString}"
							   maxlength="2"
							   tabindex="23"
							   style="width:20px;"
							   titleKey="tooltip.enterDayMonth"
							   onchange="return validateField(this,'Number 2','numberPat',31, 1);"
							   onkeyup="validationForMonthTypeDate();"
							   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					</td>

					<td width="28px">&nbsp;</td>

					<c:choose>
						<c:when test="${requestScope.methodName != 'view'}">
							<td width="45px">
								<nobr><b><fmt:message key="addjob.Time"/>
									<input tabindex="-1" class="textAlpha"
										   style="background:transparent; border: thin;"
										   readonly name="monthlyTimeMandatory" size="1" />
								</b></nobr>
							</td>
						</c:when>
						<c:otherwise>
							<td width="45px">
								<b><fmt:message key="addjob.Time"/></b>
							</td>
						</c:otherwise>
					</c:choose>

					<td width="28px">&nbsp;</td>

					<td width="50px">
						<input type="text"
							   name="scheduler.scheduleTime"
							   value="${scheduler.scheduleTime}"
							   maxlength="5"
							   tabindex="24"
							   style="width:43px;"
							   titleKey="tooltip.enterTime"
							   onchange="return validateField(this,'Number 2','timePat');"
							   <c:if test="${screenFieldsStatus}">disabled</c:if> />
					</td>
				</tr>
			</table>

			<table width="356" border="0" cellspacing="0" cellpadding="0" height="22">
				<tr height="22px">
					<td width="104px">&nbsp;</td>
					<td width="28px">&nbsp;</td>

					<td width="104px">
						<input type="checkbox"
							   name="scheduler.monthFirst"
							   value="Y"
							   tabindex="25"
							   style="width:13px;"
							   titleKey="tooltip.firstDay"
							   onclick="validationForMonthTypeFirstDay();"
							   <c:if test="${scheduler.monthFirst == 'Y'}">checked="checked"</c:if>
							   <c:if test="${screenFieldsStatus}">disabled</c:if> />
						&nbsp;<fmt:message key="addjob.FirstDate"/>
					</td>

					<td width="20px">&nbsp;</td>

					<td width="100px">
						<input type="checkbox"
							   name="scheduler.monthLast"
							   value="Y"
							   tabindex="26"
							   style="width:13px;"
							   titleKey="tooltip.lastDay"
							   onclick="validationForMonthTypeLastDay();"
							   <c:if test="${scheduler.monthLast == 'Y'}">checked="checked"</c:if>
							   <c:if test="${screenFieldsStatus}">disabled</c:if> />
						&nbsp;<fmt:message key="addjob.LastDate"/>
					</td>
				</tr>
			</table>

		    </fieldset>
			</div>

	  </div>
</div>
</div>
<div id="sc2" class="tabcontent">
	<div id="JobDetails" style="z-index:99; position:absolute;left:10px; top:112px; width:615px; height:380px; border: 2px outset ; " color="#7E97AF">
		<div id="ddimagebuttons" style="z-index:99;position:absolute; left:2px; top:6px; width:560px; height:22px; " color="#7E97AF">
			<table width="610" border="0" cellpadding="2" cellspacing="0" height="22" style="padding-top: 3px; padding-left: 10px;">
    <tr height="25px">
        <input type="hidden" name="scheduledReportParams.reportConfig" value="${scheduledReportParams.reportConfig}" />
        <td align="left"><b><fmt:message key="addJob.title.reportType" /></b>*</td>
        <td width="93px">
            <div style="width:290px">
                <select name="scheduledReportParams.reportTypeId"
                        id="scheduledReportParams.reportTypeId"
                        titleKey="tooltip.SelectReportType"
                        tabindex="1"
                        onchange="onReportTypeChange()"
                        style="width:290px"
                        <c:if test="${screenFieldsStatus}">disabled</c:if>>
                    <c:forEach var="item" items="${requestScope.reportTypeList}">
                        <option value="${item.value}" <c:if test="${scheduledReportParams.reportTypeId == item.value}">selected</c:if>>${item.label}</option>
                    </c:forEach>
                </select>
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.reportName" /></b>*</td>
        <td width="93px">
            <div style="width:290px">
                <input type="text" name="scheduledReportParams.reportName"
                       value="${scheduledReportParams.reportName}"
                       titleKey="tooltip.selectJobName"
                       tabindex="1"
                       maxlength="60"
                       style="width:395px;"
                       onblur="maxLengthTextArea(this,60);"
                       onkeydown="maxLengthTextArea(this,60);"
                       onkeyup="maxLengthTextArea(this,60);" />
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td style="width: 165px !important" align="left"><b><fmt:message key="addJob.title.reportDecription" /></b></td>
        <td width="93px">
            <div>
                <textarea name="scheduledReportParams.reportDesc"
                          titleKey="tooltip.jobDescription"
                          tabindex="1"
                          style="width:395px;height: 70px"
                          onchange="maxLengthTextArea(this,250);"
                          onblur="maxLengthTextArea(this,250);"
                          onkeyup="maxLengthTextArea(this,250);"
                          onkeydown="maxLengthTextArea(this,250);">${scheduledReportParams.reportDesc}</textarea>
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left" style="vertical-align: top;"><b><fmt:message key="addJob.title.evaluateRunDateAs" /></b></td>
        <td width="40px">
			<c:set var="MAP_DATE_SYSTEM" value="<%= org.swallow.util.SwtConstants.MAP_DATE_SYSTEM %>" />

			 <%
				String MAP_DATE_SYSTEM = org.swallow.util.SwtConstants.MAP_DATE_SYSTEM;
				  pageContext.setAttribute("MAP_DATE_SYSTEM", MAP_DATE_SYSTEM);

			  %>

            <div style="display: inline-block; vertical-align: top; margin-right: 10px;">
             <input type="radio"
				   id="10"
				   name="scheduledReportParams.mapDateMethod"
				   value="${MAP_DATE_SYSTEM}"
				   tabindex="10"
				   style="width:13px;"
				   titleKey="tooltip.jobEvaluateRunDate"
				   onclick="handleMapDateMethodChange(this);"
				   <c:if test="${scheduledReportParams.mapDateMethod == MAP_DATE_SYSTEM}">checked</c:if> />
			<label title='<fmt:message key="addJob.title.evaluateRunDateAs.value1"/>' for="10">
				<fmt:message key="addJob.title.evaluateRunDateAs.value1" />
			</label>
            </div>

            <div style="display: block; margin-top: 5px;">
              <%
				String MAP_DATE_ENTITY = org.swallow.util.SwtConstants.MAP_DATE_ENTITY;
				  pageContext.setAttribute("MAP_DATE_ENTITY", MAP_DATE_ENTITY);

			  %>
			<input type="radio"
				   id="11"
				   name="scheduledReportParams.mapDateMethod"
				   value="<%= MAP_DATE_ENTITY %>"
				   tabindex="11"
				   style="width:13px;"
				   titleKey="tooltip.jobEvaluateRunDate"
				   onclick="handleMapDateMethodChange(this);"
				   <c:if test="${scheduledReportParams.mapDateMethod == MAP_DATE_ENTITY}">checked</c:if> />
			<label title='<fmt:message key="addJob.title.evaluateRunDateAs.value2"/>' for="11">
				<fmt:message key="addJob.title.evaluateRunDateAs.value2" />
			</label>

                <span style="display: inline-block; margin-left: 10px; vertical-align: middle;">
                    <select name="scheduledReportParams.runDateEntity"
                            tabindex="4"
                            style="width:140px"
                            class="htmlTextAlpha"
                            titleKey="scheduledReportParams.tooltip.runDateEntity">
                        <c:forEach var="item" items="${requestScope.entityList}">
                            <option value="${item.value}" <c:if test="${scheduledReportParams.runDateEntity == item.value}">selected</c:if>>${item.value}</option>
                        </c:forEach>
                    </select>
                </span>

                <span style="display: inline-block; margin-left: 10px; vertical-align: middle;">
                    <input type="checkbox"
                           name="scheduledReportParams.runDateWorkdays"
                           value="Y"
                           style="width:13px;"
                           tabindex="16"
                           titleKey="scheduledReportParams.tooltip.runDateWorkdays"
                           <c:if test="${scheduledReportParams.runDateWorkdays == 'Y'}">checked="checked"</c:if>
                           <c:if test="${screenFieldsStatus}">disabled</c:if> />
                    <fmt:message key="scheduledReportParams.runDateWorkdays" />
                </span>
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.executeAsRole" /></b>*</td>
        <td width="93px">
            <div style="width:290px">
                <div style="display: inline-table; width:120px; height: 22px; position:relative; float:left">
                    <select name="scheduledReportParams.executionRole"
                            id="scheduledReportParams.executionRole"
                            tabindex="4"
                            style="width:140px"
                            class="htmlTextAlpha"
                            titleKey="tooltip.jobExecuteAsRole"
                            onchange="onRoleChange()"
                            <c:if test="${screenFieldsStatus}">disabled</c:if>>
                        <c:forEach var="item" items="${requestScope.roleList}">
                            <option value="${item.value}" <c:if test="${scheduledReportParams.executionRole == item.value}">selected</c:if>>${item.label}</option>
                        </c:forEach>
                    </select>
                </div>
                &nbsp;&nbsp;&nbsp;
                <span style="display: inline; position: absolute; margin-top: 2px;" id="roleName" name="roleName" class="spantext"></span>
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.parameters" /></b>*</td>
        <td width="70" id="configureButton">
            <a tabindex="8" title='<fmt:message key="addJob.button.configure"/>' onMouseOut="collapsebutton(this)"
               onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
               onclick="javascript:buildConfigParams()">
                <fmt:message key="addJob.button.configure"/>
            </a>
            <a tabindex="8" id="showXMLButton" title='<fmt:message key="screen.showXML"/>'
               onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
               onMouseUp="highlightbutton(this)" onclick="javascript:showXML()">
                <fmt:message key="screen.showXML"/>
            </a>
            <label id="parametersStatusInfo"
                   style="font-style:italic; left: 340px; top: 205px; width:240px; position: absolute;"
                   onclick="showAlertConfigIncorrectCause()"
                   onmouseout="onMouseOutParamStatus()" onmouseover="onMouseOverPararmStatus()"></label>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.outputFileType" /></b></td>
        <td width="93px">
            <div style="width:290px">
                <input type="text" name="scheduledReportParams.outputFormat"
                       value="${scheduledReportParams.outputFormat}"
                       titleKey="tooltip.jobOutputFileType"
                       tabindex="1" style="width:80px;" />
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.outputFileLocation" /></b></td>
        <td width="93px">
            <div style="width:140px">
                <input type="text" name="scheduledReportParams.outputLocation"
                       value="${scheduledReportParams.outputLocation}"
                       titleKey="tooltip.jobFileLocation"
                       tabindex="1" style="width:395px;" />
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.fileNamePrefix" /></b></td>
        <td width="93px">
            <div style="width:290px">
                <input type="text" name="scheduledReportParams.fileNamePrefix"
                       value="${scheduledReportParams.fileNamePrefix}"
                       titleKey="tooltip.jobNamePrefix"
                       tabindex="1" style="width:395px;" />
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.retainFilesFor" /></b></td>
        <td width="150px">
            <div style="width:80px">
                <input type="text" name="scheduledReportParams.retainDays"
                       value="${scheduledReportParams.retainDays}"
                       titleKey="tooltip.jobRetainFilesFor"
                       onchange="return validateField(this,'pwd.specialChar','numberPat');"
                       onkeydown="maxLengthTextArea(this,4);"
                       tabindex="1" style="width:80px;" />
            </div>
        </td>
        <td width="30px" align="left">
            <div style="left: 275px; top: 310px; position: absolute;">
                <b><fmt:message key="addJob.title.days" /></b>
            </div>
        </td>
    </tr>

    <tr height="25px">
        <td align="left"><b><fmt:message key="addJob.title.accessList" /></b></td>
        <td width="70">
            <a tabindex="8" title='<fmt:message key="tooltip.jobAccessList"/>'
               onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
               onclick="javascript:openWindow(buildAccessList('displayAccessList'),'alertmessagechange','left=50,top=190,width=560,height=455,toolbar=0, resizable=yes, scrollbars=yes','true')">
                <fmt:message key="addJob.title.accessList"/>
            </a>
        </td>
    </tr>
</table>

		</div>
	</div>
</div>
<div id="SchedulerMaintenance" style="position:absolute; left:540; top:510; width:70; height:25px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">

			<c:choose>
				<c:when test="${requestScope.methodName == 'add'}">
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="29"
						   href="#"
						   onclick="javascript:openWindow(buildPrintURL('print','Add Job Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onKeyDown="submitEnter(this,event)"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
							<img src="images/help_default.GIF" name="Help" border="0">
						</a>
					</td>
				</c:when>
				<c:when test="${requestScope.methodName == 'change'}">
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="29"
						   href="#"
						   onclick="javascript:openWindow(buildPrintURL('print','Change Job Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onKeyDown="submitEnter(this,event)"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
							<img src="images/help_default.GIF" name="Help" border="0">
						</a>
					</td>
				</c:when>
				<c:when test="${requestScope.methodName == 'view'}">
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="29"
						   href="#"
						   onclick="javascript:openWindow(buildPrintURL('print','View Job Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onKeyDown="submitEnter(this,event)"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
							<img src="images/help_default.GIF" name="Help" border="0">
						</a>
					</td>
				</c:when>
			</c:choose>


	   </td>

			<td align="right" id="Print">
				<a tabindex="30" onclick="printPage();"  onMouseOut="MM_swapImgRestore()" onKeyDown="submitEnter(this,event)" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'/> </a>
			</td>
		</tr>
	</table>
</div>
<DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>

<!------------------------ BUTTONS STARTS ----------------------------------------------------------------------->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:500px; width:615px; height:39px; visibility:visible;">
	<div id="addJobMaintenance" style="position:absolute; left:4; top:4; width:615px; height:15px; visibility:visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<c:choose>
					<c:when test="${requestScope.methodName == 'add'}">
						<td width="70" title='<fmt:message key="tooltip.save"/>' id="savebutton">
							<a tabindex="27" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
							   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							   onClick="javascript:checkParametersConifBeforeSubmit('add')">
							   <fmt:message key="button.save"/>
							</a>
						</td>
						<td width="70" id="cancelbutton">
							<a tabindex="28" title='<fmt:message key="tooltip.cancel"/>'
							   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
							   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							   onKeyDown="submitEnter(this,event)" onclick="confirmClose('C');">
							   <fmt:message key="button.cancel"/>
							</a>
						</td>
					</c:when>

					<c:when test="${requestScope.methodName == 'change'}">
						<td width="70" title='<fmt:message key="tooltip.save"/>' id="savebutton">
							<a tabindex="27" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
							   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							   onKeyDown="submitEnter(this,event)" onClick="javascript:checkParametersConifBeforeSubmit('change')">
							   <fmt:message key="button.save"/>
							</a>
						</td>
						<td width="70" id="cancelbutton">
							<a tabindex="28" title='<fmt:message key="tooltip.cancel"/>'
							   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
							   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							   onKeyDown="submitEnter(this,event)" onclick="confirmClose('C');">
							   <fmt:message key="button.cancel"/>
							</a>
						</td>
					</c:when>

					<c:when test="${requestScope.methodName == 'view'}">
						<td width="70" title='<fmt:message key="tooltip.close"/>' id="closebutton">
							<a tabindex="27" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
							   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							   onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();">
							   <fmt:message key="button.close"/>
							</a>
						</td>
					</c:when>
				</c:choose>

			</tr>
		</table>
	</div>
</div>

<!------------------------ BUTTONS ENDS ----------------------------------------------------------------------->

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>
