<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title><fmt:message key="matchAuditLog.screen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="style/displaytag.css">


<SCRIPT language="JAVASCRIPT">
var dateFormat = '${sessionScope.CDM.dateFormat}';
var cancelcloseElements = new Array(1);
cancelcloseElements[0]="closebutton";

mandatoryFieldsArray=["fromDateAsString","toDateAsString"];
var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var dateSelected = false;
//Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen
var dateFlag=true;

function maintainSortFilterStatus(){

	var sortColumn = xl.dataTable.sortColumn ;
	document.forms[0].selectedSortStatus.value = sortColumn ;
	var sSortDescending =  xl.dataTable.descending;
	document.forms[0].selectedSortDescending.value = sSortDescending ;

	var filterArr = new Array(3);

	if(document.getElementById("auditLogList").innerText != "")
	filterArr = xl.getFilterStatus();

	for(var idy = 0 ; idy < filterArr.length-1; ++idy)
	document.forms[0].selectedFilterStatus.value += filterArr[idy] + "," ;

}


function onFilterandSort()
{
	updateColors();
}

function bodyOnLoad()
{
	xl = new XLSheet("auditLogList","table_2", [dateFormat, "String", "String","Number","String"],"11111","false");

	document.forms[0].selectedMatchId.value = '${requestScope.selectedMatchId}';


			 if(filterstatus !="")
			 {
				var filterStatus1 ;
				filterStatus1 = filterstatus.split(",");

				if(document.getElementById("auditLogList").innerText != "")
				xl.setFilterStatus(filterStatus1);
			 }

				if(sortDescending !="" && sortDescending !="null" && sortDescending == "true")
			 {
				xl.dataTable.defaultDescending = true;
				xl.dataTable.doManualSorting(sortStatus);
			 }

				if(sortDescending !="" && sortDescending !="null" && sortDescending == "false")
			 {
				xl.dataTable.doManualSorting(sortStatus);
			 }

		xl.onsort = xl.onfilter = onFilterandSort;
}

/**
 * This fuction is used to submit the form based up on the method name we are passing
 */
function submitForm(methodName){
	//to maiuntain sort status
	maintainSortFilterStatus();
	//validate form
	//Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen
	if(validateForm(document.forms[0]) && validDates()){

					document.forms[0].method.value = methodName;

					document.forms[0].submit();



	}
}


function storeToDate(){
document.forms[0].elements['preMatchToDateAsString'].value = document.forms[0].elements['auditLog.toDateAsString'].value;

}
/*This fuction is used to change the date in  toDate field*/
function onToDateChange(){

	if(dateSelected)
	{
	//storing previus date
	var preToDate = document.forms[0].elements['preMatchToDateAsString'].value;
	//getting selected matchid
	document.forms[0].elements['selectedMatchId'].value = document.forms[0].elements['selectedMatchId'].value;
	//get current from date
	var from_date=document.forms[0].elements['auditLog.fromDateAsString'].value;
	//get current todate
	var to_date=document.forms[0].elements['auditLog.toDateAsString'].value;
	//getting current date format
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	//
	//validating from date & todate relation
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	/*Start:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen*/
	if(validDates()) {
				cal2.hideCalendar();
		if(compare_date == 1)
		{
			document.forms[0].elements['auditLog.toDateAsString'].value=preToDate;
			cal2.hideCalendar()
			alert('<fmt:message key="alert.matchAuditLog.toDate"/>');
		} else  {


			submitForm('refresh');

		     }

	    } else {

			dateSelected = false;

	       }

	/*End:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen*/

	}
}




function storeFromDate(){

document.forms[0].elements['preMatchFromDateAsString'].value = document.forms[0].elements['auditLog.fromDateAsString'].value;
}
/*This fuction is used to change the date in from date field*/
function onFromDateChange()
{

	if(dateSelected)
	{
	//storing previous date
	var preFromDate = document.forms[0].elements['preMatchFromDateAsString'].value;
	//storing from date
	var from_date=document.forms[0].elements['auditLog.fromDateAsString'].value;
	//storing toDate
	var to_date=document.forms[0].elements['auditLog.toDateAsString'].value;
	//get current date format
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	//validating from date & todate relation
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	/*Start:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen*/
	if(validDates()) {
				cal.hideCalendar();
	if(compare_date == 1)
		{
			document.forms[0].elements['auditLog.fromDateAsString'].value=preFromDate;
			cal.hideCalendar()
			alert('<fmt:message key="alert.matchAuditLog.toDate"/>');
		} else {


		  submitForm('refresh');

		}

	  } else {

				document.forms[0].elements['auditLog.toDateAsString'].focus();
				dateSelected = false;
	    }
	/*End:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen*/
  }

}



function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["auditLog.fromDateAsString"];
  elementsRef[1] = objForm.elements["auditLog.toDateAsString"];
  if(validate(elementsRef))
  {
    if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date'))
     {
      return true;
     }
   }
   return false;
}
/*Start:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen*/

/**
 * This fuction is called from sortabletable.js -used to validate date fileds while sorting || filtering datagrid
 */
function sortDateValidation() {
	if(validateForm(document.forms[0]) ) {
		//validate date field

		if(validDates()) {
			dateFlag = true;
			} else {
			dateFlag = false;
	   }
		return dateFlag;
	}

}

/**
 * This function is used to validate the From && To date fileds
 *
 * @param fromDateField
 * @param toDateField
 */
function onKeyPressFromToDate(fromDateField, toDateField,e) {
	dateSelected = false;
	var event = (window.event|| e);
	if(event.keyCode == 9 || event.keyCode == 13){ 	// tab || enter
		//validate date field
		if(validateField(toDateField, 'toDateAsString', dateFormat) && validateField(fromDateField, 'fromDateAsString', dateFormat)){
			//validating from date & todate relation
			if(comparedates(fromDateField.value, toDateField.value, dateFormat, 'From Date', 'To Date')){
				submitForm('refresh');
			}
		}
			return false;

	}
}
/**
 * This function is used to validate the From && To date fileds
 */
function validDates()
{
			//validation of date fileds
			if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'fromDateAsString',dateFormat) &&
				validateField(document.forms[0].elements['auditLog.toDateAsString'],'toDateAsString',dateFormat)) {

					return true;

					} else {

					return false;

				}
}
/*End:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen*/
</SCRIPT>

<SCRIPT language="JAVASCRIPT">
  var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';

      var cal = new CalendarPopup("caldiv",true);


      cal.offsetX = -82;
      cal.offsetY = 22;

	  var cal2 = new CalendarPopup("caldiv",true);


      cal2.offsetX = -82;
      cal2.offsetY = 22;

 </SCRIPT>


</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
	marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]);"
	onunload="call()">

<form action="auditlog.do" method="post" onsubmit="return validate(this);">

	<input name="method" type="hidden" value="display">
	<input name="selectedMatchId" type="hidden" value="">

	<!--Start:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen-->
	<input name="selectedFilterStatus" type="hidden"
		value='${filterStatus}'>
	<input name="selectedSortDescending" type="hidden"
		value='${sortDescending}'>
	<input name="selectedSortStatus" type="hidden" value='${sortStatus}'>
	<!--End:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen-->

	<input name="preMatchFromDateAsString" type="hidden" value="">
	<input name="preMatchToDateAsString" type="hidden" value="">


	<c:set var="CDM" value="${sessionScope.CDM}"/>
	<DIV ID="caldiv"
		STYLE="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></DIV>

	<div id="UserLog"
		style="position: absolute; left: 20px; top: 20px; width: 555px; height: 37px; border: 2px outset;"
		color="#7E97AF">
	<div id="UserLog"
		style="position: absolute; left: 8px; top: 4px; width: 555px; height: 15px; visibility: visible;">
	<table width="410px" border="0" cellpadding="0" cellspacing="0"
		height="24">
		<tr height="24">
			<td width="40px" align="left"><b><fmt:message key="auditLog.from"/></b>*</td>
			<td width="26px">&nbsp;</td>
			<td width="147px" align="left" id="fromDateAsString"><!--Start:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen-->
			<input type="text" titleKey="tooltip.fromDate" cssClass="htmlTextAlpha"
				tabindex="1" name="auditLog.fromDateAsString" value="${auditLog.fromDateAsString}"  maxlength="10"
				style="width:80px; margin-bottom: 5px;" onblur="onFromDateChange();"
				onkeydown="onKeyPressFromToDate(this, document.forms[0].elements['auditLog.toDateAsString'],event)" />
			<A title='<fmt:message key="tooltip.selectFromDate"/>' tabindex="2"
				name="datelink" ID="datelink"
				onClick="cal.select(document.forms[0].elements['auditLog.fromDateAsString'],'datelink',dateFormatValue);storeFromDate();dateSelected = true; return false;"><img
				src="images/calendar-16.gif"></A></td>

			<td width="40px"><b ><fmt:message key="auditLog.to"/></b>*</td>
			<td width="8px">&nbsp;</td>
			<td width="147px" align="left" id="toDateAsString">
			<input type="text" titleKey="tooltip.toDate" tabindex="3" cssClass="htmlTextAlpha" name="auditLog.toDateAsString" value="${auditLog.toDateAsString}"  style="width:80px;margin-bottom: 5px;"
				maxlength="10" onblur="onToDateChange();"
				onkeydown="onKeyPressFromToDate(document.forms[0].elements['auditLog.fromDateAsString'], this,event);" />
			<!--End:Code Modified by chinniah on 13-Dec-2011 for Mantis 1578:Date field is not editable by keyboard in Match Audit Log screen-->

			<A title='<fmt:message key="tooltip.selectToDate"/>' tabindex="4"
				name="datelink2" ID="datelink2"
				onClick="cal2.select(document.forms[0].elements['auditLog.toDateAsString'],'datelink2',dateFormatValue);storeToDate();dateSelected = true; return false;"><img
				src="images/calendar-16.gif"></A></td>


			<td>
		</tr>
	</table>
	</div>
	</div>

	<DIV ID="caldiv"
		STYLE="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></DIV>

	<div id="AuditLog" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 20px; top: 61px; width: 555px; height: 430;">
	<div id="AuditLog"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 560px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="529"
		border="0" cellspacing="1" cellpadding="0">
		<thead>
			<tr height="20px">
				<td title='<fmt:message key="tooltip.sortLogDate"/>' width="82px"  style="border-left-width: 0px;"
					align="center"><b><fmt:message key="auditLog.logDate_Date"/></b></td>

				<td title='<fmt:message key="tooltip.sortLogTime"/>' width="78px"
					align="center"><b><fmt:message key="auditLog.logDate_Time"/></b></td>

				<td title='<fmt:message key="tooltip.sortUserId"/>' width="144px"
					align="center"><b><fmt:message key="auditLog.userId"/></b></td>

				<td title='<fmt:message key="tooltip.sortItemNum"/>' width="140px"
					align="center"><b><fmt:message key="matchAuditLog.movement"/></b></td>

				<td title='<fmt:message key="tooltip.sortAction"/>' width="85px"
					align="center"><b><fmt:message key="auditLog.id.action"/></td>

			</tr>
		</thead>
	</table>
	</div>

	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 551px; height: 425; overflowY: scroll">
	<div id="AuditLog"
		style="position: absolute; z-index: 99; left: 1px; top: 22px; width: 530px; height: 10px;">
	<table class="sort-table" id="auditLogList" width="533" border="0" cellspacing="1" cellpadding="0" height="403">
    <tbody>
        <% int count = 0; %>
        <c:forEach var="auditLog" items="${requestScope.auditLogList}">
            <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
                <% ++count; %>
                <input type="hidden" name="auditLogList.id.reference" value="${auditLog.id.reference}" />
                <input type="hidden" name="auditLogList.id.referenceId" value="${auditLog.id.referenceId}" />
                <input type="hidden" name="auditLogList.logDate_Date" value="${auditLog.logDate_Date}" />
                <input type="hidden" name="auditLogList.logDate_Time" value="${auditLog.logDate_Time}" />
                <input type="hidden" name="auditLogList.id.userId" value="${auditLog.id.userId}" />
                <input type="hidden" name="auditLogList.id.action" value="${auditLog.id.action}" />

                <td width="80px" align="left">${auditLog.logDate_Date}&nbsp;</td>
                <td width="78px" align="left">${auditLog.logDate_Time}&nbsp;</td>
                <td width="144px" align="left">${auditLog.id.userId}&nbsp;</td>
                <td width="140px" align="right">${auditLog.id.referenceId}&nbsp;</td>
                <td width="85px" align="left">${auditLog.id.action}&nbsp;</td>
            </tr>
        </c:forEach>
    </tbody>
    <tfoot>
        <tr>
            <td colspan="5"></td>
        </tr>
    </tfoot>
</table>

	</div>
	</div>
	</div>

	<div id="UserAuditLog"
		style="position: absolute; left: 500; top: 508px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">

		<tr>

			<td align="Right"><a
				title='<fmt:message key="tooltip.helpScreen"/>' tabindex="8" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Match Audit Log '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"></a></td>


			<td align="right" id="Print"><a tabindex="8"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 500px; width: 555px; height: 39px; visibility: visible;">
	<div id="AuditLog"
		style="position: absolute; left: 6; top: 4; width: 200px; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td><a id="refreshbutton"
				title='<fmt:message key="tooltip.refreshUserLog"/>' tabindex="5"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitForm('refresh')"><fmt:message key="button.Refresh"/></a></td>

			<td id="closebutton"><a
				title='<fmt:message key="tooltip.close"/>' tabindex="7" tabindex="4"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>

		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
</form>
</body>
</html>