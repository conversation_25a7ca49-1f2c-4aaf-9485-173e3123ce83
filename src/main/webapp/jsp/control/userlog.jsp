<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<title><fmt:message key="userAuditLog.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<script type="text/javascript" src="js/jquery.exportselect.js"></script>

	<link rel="stylesheet"	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
	<script type="text/javascript">
		var appName = "<%=SwtUtil.appName%>";
		var baseURL = new String('<%=request.getRequestURL()%>');
		var screenRoute = "MyUserAuditLog";

		function viewDetails(methodName, selectedReferenceId, selectedUserId, selectedDate, selectedTime,
							 selectedReference, selectedAction){
			var param = '/' + appName + '/userlog.do?method='+methodName;
			param += '&selectedReferenceId=' + selectedReferenceId;
			param += '&selectedUserId=' + selectedUserId;
			param += '&selectedDate=' + selectedDate;
			param += '&selectedTime=' + selectedTime;
			param += '&selectedReference=' + selectedReference;
			param += '&selectedAction=' + selectedAction;
			var 	mainWindow = openWindow (param, 'userLogView','left=50,top=190,width=843,height=368,toolbar=0, resizable=yes scrollbars=yes','true');
		return false;
	}

	function help(){

	openWindow(buildPrintURL('print','User Log'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	}
     
  </script>
</head>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>

</html>
  
