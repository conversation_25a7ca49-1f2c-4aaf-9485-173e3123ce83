<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>

<c:if test="${'add' == requestScope.methodName}">

<title>	<fmt:message key="locationAccess.title.addWindow"/></title>

</c:if>

<c:if test="${'change' == requestScope.methodName}">

	<title><fmt:message key="locationAccess.title.changeWindow"/></title>

</c:if>


<c:if test="${'view' == requestScope.methodName}">

	<title><fmt:message key="locationAccess.title.viewWindow"/></title>

</c:if>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">


<c:if test="${'yes' == requestScope.parentFormRefresh}">

self.close();

</c:if>


var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
mandatoryFieldsArray ="undefined";

function onFilterandSort()
{
	updateColors();
}


function submitForm(methodName){

 document.forms[0].method.value = methodName;
 document.forms[0].selectedEntityId.value = document.forms[0].elements["locationAccess.entityId"].value;
 document.forms[0].selectedRoleId.value = '${selectedRoleId}';
 document.forms[0].calleeMethodName.value = '${methodName}';

 document.forms[0].submit();

}

function submitFormDropDownBox(methodName){

 document.forms[0].method.value = methodName;

 document.forms[0].selectedEntityId.value = document.forms[0].elements["locationAccess.entityId"].value;
 document.forms[0].selectedRoleId.value = '${selectedRoleId}';

document.forms[0].submit();

}

function bodyOnLoad()
{
	xl = new XLSheet("locationDetailsList","table_2", ["String","String"],"22");
	xl.onsort = xl.onfilter = onFilterandSort;


	var dropBox1 = new SwSelectBox(document.forms[0].elements["locationAccess.entityId"],document.getElementById("entityDesc"));

	<c:if test="${'view' != requestScope.methodName}">

	document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

</c:if>


	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

}

</SCRIPT>

</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call();">

<form action="role.do" method="post">
<input name="method" type="hidden" value="display">
<input name="selectedRoleId" type="hidden" >
<input name="selectedEntityId" type="hidden">
<input name="calleeMethodName" type="hidden">


<input name="menuAccessId" type="hidden" >


<div id="LocationAccess" style="position:absolute; left:20px; top:20px; width:574px; height:39px; border:2px outset;" color="#7E97AF">

<div id="LocationAccess" style="position:absolute; left:8px; top:4px; width:573px; height:400;">
<table width="500" border="0" cellpadding="0" cellspacing="0" height="30">
<!--start:modified the mantis 1549 by sunil on 10-02-2012.The purpose to display the meangful tooltip  -->
	<tr height="25px">
		  <td width="38"><b><fmt:message key="entity.id"/></b></td>
		  <td width="28">&nbsp;</td>
		  <td width="140px">
			<c:if test="${'view' == requestScope.methodName}">

				   <select id="locationAccess.entityId" name="locationAccess.entityId" class="htmlTextAlpha"
				  titleKey="tooltip.selectEntityid" style="width:140px" tabindex="1"  onchange="submitFormDropDownBox('viewLocationAccessList');">
			  <c:forEach items="${requestScope.entities}" var="entity">
				  <option value="${entity.value}" <c:if test="${locationAccess.entityId == entity.value}">selected</c:if> >
						  ${entity.label}
				  </option>
			  </c:forEach>
		  </select>

</c:if>
			<c:if test="${'change' == requestScope.methodName}">

<select id="locationAccess.entityId" name="locationAccess.entityId" class="htmlTextAlpha"
				  titleKey="tooltip.selectEntityid" style="width:140px" tabindex="1"  onchange="submitFormDropDownBox('changeLocationAccessList');">
			  <c:forEach items="${requestScope.entities}" var="entity">
				  <option value="${entity.value}" <c:if test="${locationAccess.entityId == entity.value}">selected</c:if> >
						  ${entity.label}
				  </option>
			  </c:forEach>
		  </select>

</c:if>
 			<c:if test="${'add' == requestScope.methodName}">


<select id="locationAccess.entityId" name="locationAccess.entityId" class="htmlTextAlpha"
				  titleKey="tooltip.selectEntityid" style="width:140px" tabindex="1"  onchange="submitFormDropDownBox('addLocationAccessList');">
			  <c:forEach items="${requestScope.entities}" var="entity">
				  <option value="${entity.value}" <c:if test="${locationAccess.entityId == entity.value}">selected</c:if> >
						  ${entity.label}
				  </option>
			  </c:forEach>
		  </select>
</c:if>
		  </td>
		   <td width="20">&nbsp;</td>
		  <td width="280">
			<span id="entityDesc" name="entityDesc" class="spantext">
		   </td>
	 </tr>
</table>
</div>
</div>

<div id="LocationAccess" color="#7E97AF" style="position:absolute; border-left:0px outset; left:20px; top:64px; width:573px; height:431px;">
<div id="LocationAccess" style="position:absolute;z-index:99;left:0px; top:0px; width:566px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="546" border="0" cellspacing="1" cellpadding="0"  height="23">
	<thead>
		<tr>
			<td  title='<fmt:message key="tooltip.sortByLocation"/>' width="170" height="20px" align="left" style="border-left-width: 0px;"><b><fmt:message key="locationaccess.location"/></b></td>
			<td  title='<fmt:message key="tooltip.sortByName"/>'  width="280" height="20px" align="left"><b><fmt:message key="locationaccess.locationName"/></b></td>
			<td  title='<fmt:message key="tooltip.access"/>' width="100px" height="20px" align="center"><b><fmt:message key="locationaccess.access"/></b></td>

		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:570px; height:426px;overflow-x:hidden;">
<div id="LocationAccess" style="position:absolute;z-index:99;left:0px; top:22px; width:554px; height:10px;">
<table class="sort-table" id="locationDetailsList" width="554" border="0" cellspacing="1" cellpadding="0" height="404">
	<tbody>
	<%int count = 0; %>

 <c:forEach  items="${requestScope.locationDetailsList}" var="locationDetailsList" >
		<% if( count%2 == 0 ) {%><tr class="evencheckbox"><% }else  { %> <tr class="oddcheckbox"> <%}++count; %>
		    <input type="hidden" name="entityId" value="${locationDetailsList.locationId}" />
			<td  width="170" align="left">${locationDetailsList.locationId}&nbsp;</td>
			<td width="280" align="left" >${locationDetailsList.locationName}&nbsp;</td>
			<c:if test="${'view' == requestScope.methodName}">

				<td width="100px" height="20px" align="center">
				<input type="checkbox"  name="locAccessHTML<%=count%>" value="Y" ${locationDetailsList.locAccess == 'checked' ? 'checked' : ''} fieldValue="${locationDetailsList.locAccess}" disabled="true" /></td>

</c:if>
			<c:if test="${'view' != requestScope.methodName}">

				<td width="100px" height="20px" align="center">
				<input type="checkbox"  name="locAccessHTML<%=count%>" value="Y" ${locationDetailsList.locAccess == 'checked' ? 'checked' : ''} fieldValue="${locationDetailsList.locAccess}" /></td>

</c:if>
		</tr>
</c:forEach>
	</tbody>
<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="LocationAccess" style="position:absolute; left:518; top:509px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">

			<c:if test="${'add' == requestScope.methodName}">

				<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Location'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
			<c:if test="${'change' == requestScope.methodName}">

				<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Location'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
			<c:if test="${'view' == requestScope.methodName}">

				<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','View Location'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
			</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:500; width:573; height:39px; visibility:visible;">
  <div id="LocationAccess" style="position:absolute; left:6; top:4; width:154px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<c:if test="${'view' != requestScope.methodName}">

			<td id="okbutton" width="70px">
			</td>

</c:if>
			<td title='<fmt:message key="tooltip.close"/>' width="70px" id="closebutton">
				<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
	</div>
<div style="position:absolute; left:6; top:4; width:154px; height:15px; visibility:hidden;">
    <table width="140" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="okenablebutton">
		<a  title='<fmt:message key="tooltip.ok"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('saveLocationAccessList');"><fmt:message key="button.ok"/></a>
		</td>
		<td id="okdisablebutton">
			<a  class="disabled" disabled="disabled" title='<fmt:message key="tooltip.ok"/>' ><fmt:message key="button.ok"/></a>
		</td>
<!--End:modified the mantis 1549 by sunil on 10-02-2012.The purpose to display the meangful tooltip  -->
	</tr>
    </table>
  </div>
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

<script type="text/javascript">
</script>
</form>
</body>
</html>