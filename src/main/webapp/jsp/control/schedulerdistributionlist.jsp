<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><fmt:message key="addjob.title.distlist"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var fromSchedulerReportHist= '${fromSchedReportHist}';
var fullFilePath= '${filePath}';
var fileId= '${fileId}';
	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad(){
		xl = new XLSheet("rolesList","table_1", ["String","String", "String"],"110");
		xl.onsort = xl.onfilter = updateColors;
		xl = new XLSheet("usersList","table_2", ["String","String", "String", "String"],"1110");
		xl.onsort = xl.onfilter = updateColors;
		if(fromSchedulerReportHist != 'true') {
			var AllUserCheckBoxList = document.getElementsByName('checkUserAccessList');
			// test if the value stored in the opener window is different of the original value || no
			var openerWindowAccessList = window.opener.document.forms[0].selectedEmailUsers.value.split(",");
			for (var i = 0; i < AllUserCheckBoxList.length; i++) {
				if(AllUserCheckBoxList[i].type=="checkbox"){
					if(openerWindowAccessList.indexOf(AllUserCheckBoxList[i].value) > -1){
						AllUserCheckBoxList[i].setAttribute("checked", "true");
						AllUserCheckBoxList[i].checked = true;
					}else{
						AllUserCheckBoxList[i].setAttribute("checked", "false");
						AllUserCheckBoxList[i].checked = false;
					}
			    }
			}

			var AllRoleCheckBoxList = document.getElementsByName('checkRoleAccessList');
			// test if the value stored in the opener window is different of the original value || no
			var openerWindowAccessList = window.opener.document.forms[0].selectedEmailRoles.value.split(",");
			for (var i = 0; i < AllRoleCheckBoxList.length; i++) {
				if(AllRoleCheckBoxList[i].type=="checkbox"){
					if(openerWindowAccessList.indexOf(AllRoleCheckBoxList[i].value) > -1){
						AllRoleCheckBoxList[i].setAttribute("checked", "true");
						AllRoleCheckBoxList[i].checked = true;
					}else{
						AllRoleCheckBoxList[i].setAttribute("checked", "false");
						AllRoleCheckBoxList[i].checked = false;
					}
			    }
			}

			checkIfAllUserCheckboxChecked();
			checkIfAllRoleCheckboxChecked();
		}
	}

	/**
	 * This method is used to send the "selectedUsers" to the parent screen while clicking on the save button.
	 *
	 **/
	function submitForm(){
		var selectedUsers ="";
		var selectedRoles ="";
		var allCheckBoxList = document.getElementsByName('checkUserAccessList');
		for (var i = 0; i < allCheckBoxList.length; i++) {
			if(allCheckBoxList[i].type=="checkbox"){
		      if(allCheckBoxList[i].checked==true){
		        selectedUsers+=allCheckBoxList[i].value+",";
		      }
		    }
		}
		var AllRoleCheckBoxList = document.getElementsByName('checkRoleAccessList');
		for (var i = 0; i < AllRoleCheckBoxList.length; i++) {
			if(AllRoleCheckBoxList[i].type=="checkbox"){
		      if(AllRoleCheckBoxList[i].checked==true){
		        selectedRoles+=AllRoleCheckBoxList[i].value+",";
		      }
		    }
		}
		if(fromSchedulerReportHist != 'true') {
			window.opener.document.forms[0].selectedEmailUsers.value = selectedUsers.substring(0, selectedUsers.length - 1);
			window.opener.document.forms[0].selectedEmailRoles.value = selectedRoles.substring(0, selectedRoles.length - 1);
			window.close();
		}else {
			if(selectedRoles  || selectedUsers){
				var requestURL = new String('<%=request.getRequestURL()%>');
				var appName = "<%=SwtUtil.appName%>";
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;
				requestURL = requestURL + appName+"/schedReportHist.do?method=sendMailToUsers";
				requestURL = requestURL + "&selectedRoles=" + selectedRoles+ "&selectedUsers=" + selectedUsers+ "&fullFilePath=" + fullFilePath+"&fileId="+fileId ;

				var oXMLHTTP = new XMLHttpRequest();
	 			oXMLHTTP.open( "POST", requestURL, true );
	 			oXMLHTTP.onreadystatechange = function() {
					if (oXMLHTTP.readyState == 4) {
						if (oXMLHTTP.status == 200)
						{
 							var result = String(oXMLHTTP.responseText);
							if( result == "SUCCESS"){
								alert("<fmt:message key="alert.mail.mailSentWithSuccess"/>");
							}else if ( result == "NOT_FOUND"){
								alert("<fmt:message key="alert.mail.fileNoutFound"/>");
							}else if ( result == "EXCEPTION"){
								alert("<fmt:message key="alert.mail.errorWhenSendingMail"/>");
							}else if(result == 'MAILNOTSENT') {
								alert("<fmt:message key="alert.mail.oneOrManyMailsNotSent"/>");
							}
							window.close();
						}
						else
						{
							alert("<fmt:message key="alert.mail.connectionProblem"/>");
						}
					}
	 			};
	 			oXMLHTTP.send();

			}else {
				alert("<fmt:message key="addjob.distlist.alert"/>");
			}
		}


	}

	function fCallback (){

	}

	/**
	 * Called when click on checkbox
	 */
	function handleClick(cb) {
	  if(cb.checked){
		  cb.setAttribute("checked", "true");
		  cb.checked = true;
		  }else{
			  cb.setAttribute("checked", "false");
			  cb.checked = false;
		  }
	  if (cb.name == "checkUserAccessList") {
		  checkIfAllUserCheckboxChecked();
	  } else if (cb.name == "checkRoleAccessList") {
		  checkIfAllRoleCheckboxChecked();
	  }
	}

	/**
	 * Called when click the select/deselect all checkbox
	 */
	function selectedallInput(cb)
	{
		if (cb.name == "inputUserCheckAll") {
			var allCheckBoxList = document.getElementsByName('checkUserAccessList');
		} else if (cb.name == "inputRoleCheckAll") {
			var allCheckBoxList = document.getElementsByName('checkRoleAccessList');
		}
		if(cb.checked){
			for (var i = 0; i < allCheckBoxList.length; i++) {
				if(allCheckBoxList[i].type=="checkbox"){
					allCheckBoxList[i].setAttribute("checked", "true");
					allCheckBoxList[i].checked = true;
			    }
			}
		  }else{
			  for (var i = 0; i < allCheckBoxList.length; i++) {
					if(allCheckBoxList[i].type=="checkbox"){
						allCheckBoxList[i].checked = false;
				    }
				}
		  }
	}

	/**
	 * this method is used to check if all checkbox are cheked || no
	 */
	function checkIfAllUserCheckboxChecked()
	{
		var checkall = document.getElementsByName('inputUserCheckAll');
		var allCheckBoxList = document.getElementsByName('checkUserAccessList');
		if(allCheckBoxList.length!=0){
			var test = true;
			for (var i = 0; i < allCheckBoxList.length; i++) {
				if(allCheckBoxList[i].type=="checkbox"){
			      if(allCheckBoxList[i].checked==false){
			       test = false
			      }
			    }
			}
			if(test == true){
				  checkall[0].setAttribute("checked", "true");
				  checkall[0].checked = true;
			}else{
				checkall[0].setAttribute("checked", "false");
				  checkall[0].checked = false;
				}
		}else{
			checkall[0].disabled = 'true';
		}
	}

	/**
	 * this method is used to check if all checkbox are cheked || no
	 */
	function checkIfAllRoleCheckboxChecked()
	{
		var checkall = document.getElementsByName('inputRoleCheckAll');
		var allCheckBoxList = document.getElementsByName('checkRoleAccessList');
		if(allCheckBoxList.length!=0){
			var test = true;
			for (var i = 0; i < allCheckBoxList.length; i++) {
				if(allCheckBoxList[i].type=="checkbox"){
			      if(allCheckBoxList[i].checked==false){
			       test = false
			      }
			    }
			}
			if(test == true){
				  checkall[0].setAttribute("checked", "true");
				  checkall[0].checked = true;
			}else{
				checkall[0].setAttribute("checked", "false");
				  checkall[0].checked = false;
				}
		}else{
			checkall[0].disabled = 'true';
		}
	}

</SCRIPT>
</head>

<form action="scenMaintenance.do" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<div id="ddimagetabs"
		style=" left:10px; position: absolute;  top: 10px; width: 320px; height: 20px;">

		<a href="#" onmouseout="revertback('sc1',this);" tabindex="3"
							onmouseover="changecontent('sc1',this)"
							onClick="changeselected('sc1');expandcontent('sc1', this)">
					<b><fmt:message key="addJob.tab.role"/></b>
		</a>
		<a href="#" id="firstsc" onmouseout="revertback('sc2',this);" tabindex="4"
					onmouseover="changecontent('sc2',this)"
					onClick="changeselected('sc2', this);expandcontent('sc2', this);">
			<b><fmt:message key="addJob.tab.user"/></b>
      	</a>
	</div>
	<div id="Line"
		style="position: absolute; left: 172px; top: 29px; width: 494px; height: 20px;">
	<table width="100%">
		<tr>
			<td><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
	</table>
</div>
<div id="sc1" class="tabcontent">
	<div id="DistributionList" style="position:absolute; border:2px outset; left:10px; top:33px; width:653px; height:365px; border-left:0px outset;" color="#7E97AF">
		<div id="DistributionList" style="position:absolute; border:2px outset; left:0px; top:0px; width:640px; height:1px; border-left:0px outset;" color="#7E97AF">
			<div id="DistributionList" style="position:absolute;z-index:99;left:0px; top:0px; width:630px; height:10px;">
				<table class="sort-table" id="table_1" width="630px" border="0" cellspacing="1" cellpadding="0"  height="20px">
					<thead>
						<tr height="20px" >
							<td width="200px" title='<fmt:message key="tooltip.sortRoleId"/>' class="topbar" style="border-left-width: 0px;"><b><fmt:message key="role.roleId"/></b></td>
							<td width="380px" title='<fmt:message key="tooltip.sortRoleName"/>' class="topbar"><b><fmt:message key="usermaintenance.userName"/></b></td>
							<td width="50px" title='<fmt:message key="auditLog.to"/>' class="topbar"><b><fmt:message key="auditLog.to"/></b></td>
						</tr>
					</thead>
				</table>
			</div>
			<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:646px; height:331px; overflow-x:hidden;">
				<div id="Role" style="position:absolute;z-index:99;left:0px; width:630px; height:10px;">
					<table id="rolesList" class="sort-table" width="630px" border="0" cellspacing="1" cellpadding="0" height="309px">
						<tbody>
							<%int count = 0; %>
							<c:forEach items="${requestScope.roleList}" var="roleList" >
								<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
										<td width="200px">${roleList.roleId}&nbsp;</td>
										<td width="380px">${roleList.roleName}&nbsp;</td>

										<td width="50" height="20" align="center">
										<input type="checkbox" value='${roleList.roleId}' name="checkRoleAccessList" onclick='handleClick(this);' title='<fmt:message key="tooltip.selectUsers"/>'>
									&nbsp;</td>
								</tr>
							</c:forEach>
						</tbody>
						<tfoot><tr><td colspan="5" ></td></tr></tfoot>
					</table>
				</div>
			</div>
		</div>
	</div>
	<div style="position:absolute; left:20px; top:370px; width:375px; height:25px; visibility:visible;">
		<table width="605px" border="0" cellspacing="0" cellpadding="0" height="25">
			<tr>
				<td width="508" align="left">
				</td>
				<td width="140" align="left">
					<b><fmt:message key="scenario.distributionlist.selectall"/></b>
				</td>
				<td width="14" align="left">
					<input type="checkbox" name="inputRoleCheckAll"  onclick ="selectedallInput(this);" title='<fmt:message key="tooltip.selectAllUsers"/>'>
				</td>
			</tr>
		</table>
	</div>
</div>

<div id="sc2" class="tabcontent">
	<div id="DistributionList" style="position:absolute; border:2px outset; left:10px; top:33px; width:653px; height:365px; border-left:0px outset;" color="#7E97AF">
		<div id="DistributionList" style="position:absolute; border:2px outset; left:0px; top:0px; width:640px; height:1px; border-left:0px outset;" color="#7E97AF">
			<div id="DistributionList" style="position:absolute;z-index:99;left:0px; top:0px; width:630px; height:10px;">
				<table class="sort-table" id="table_2" width="630px" border="0" cellspacing="1" cellpadding="0"  height="20px">
					<thead>
						<tr height="20px" >
							<td width="120px" title='<fmt:message key="tooltip.sortUserId"/>' class="topbar" style="border-left-width: 0px;"><b><fmt:message key="usermaintenance.userId"/></b></td>
							<td width="200px" title='<fmt:message key="tooltip.sortUserName"/>' class="topbar"><b><fmt:message key="usermaintenance.userName"/></b></td>
							<td width="260px" title='<fmt:message key="tooltip.sendMail"/>' class="topbar"><b><fmt:message key="usermaintenance.emailId"/></b></td>
							<td width="50px" title='<fmt:message key="auditLog.to"/>' class="topbar"><b><fmt:message key="auditLog.to"/></b></td>
						</tr>
					</thead>
				</table>
			</div>
			<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:646px; height:331px; overflow-x:hidden;">
				<div id="Role" style="position:absolute;z-index:99;left:0px; width:630px; height:10px;">
					<table id="usersList" class="sort-table" width="630px" border="0" cellspacing="1" cellpadding="0" height="309px">
						<tbody>
							<%int count2 = 0; %>
							<c:forEach items="${requestScope.usersList}" var="usersList" >
								<% if( count2%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count2; %>
										<td width="120px">${usersList.id.userId}&nbsp;</td>
										<td width="200px">${usersList.userName}&nbsp;</td>
										<td width="260px">${usersList.emailId}&nbsp;</td>

										<td width="50" height="20" align="center">
										<input type="checkbox" value='${usersList.id.userId}' name="checkUserAccessList" onclick='handleClick(this);' title='<fmt:message key="tooltip.selectUsers"/>'>
									&nbsp;</td>
								</tr>
							</c:forEach>
						</tbody>
						<tfoot><tr><td colspan="5" ></td></tr></tfoot>
					</table>
				</div>
			</div>
		</div>
	</div>
	<div style="position:absolute; left:20px; top:370px; width:375px; height:25px; visibility:visible;">
		<table width="605px" border="0" cellspacing="0" cellpadding="0" height="25">
			<tr>
				<td width="508" align="left">
				</td>
				<td width="140" align="left">
					<b><fmt:message key="scenario.distributionlist.selectall"/></b>
				</td>
				<td width="14" align="left">
					<input type="checkbox" name="inputUserCheckAll"  onclick ="selectedallInput(this);" title='<fmt:message key="tooltip.selectAllUsers"/>'>
				</td>
			</tr>
		</table>
	</div>
</div>
<div id="DistributionListHelp" style="left: 580px; top: 418px; width: 70px; height: 29px; visibility: visible; position: absolute; z-index: 150;">
	<table width="60" height="20" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr>
				<td align="Right">
					<a tabindex="10" title="Help screen content" onmouseover="MM_swapImage('Help','','images/help_default.GIF ',1)" onmouseout="MM_swapImgRestore()" onclick="javascript:openWindow(buildPrintURL('print','Distribution List'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" href="#"><img name="Help" src="images/help_default.GIF " border="0"></a>
				</td>
				<td align="right" id="Print">&nbsp;
					<a tabindex="11" onmouseover="MM_swapImage('Print','','images/Print_R.gif ',1)" onmouseout="MM_swapImgRestore()" onclick="printPage();"><img name="Print" title="Print screen content" src="images/Print.gif " border="0"></a>
				</td>
			</tr>
		</tbody>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:410; width:654px; height:39px; visibility:visible;">
	<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:200px; height:15px; visibility:visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td id="savebutton">
					<a title='<fmt:message key="tooltip.ok"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm();"><fmt:message key="button.ok"/></a>
				</td>
				<td id="cancelbutton" width="70px">
					<a title='<fmt:message key="tooltip.cancel"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>
				</td>
			</tr>
		</table>
	</div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>