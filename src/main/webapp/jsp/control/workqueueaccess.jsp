<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="workqueueaccess.mainScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

var entityAccess = "0";
var isViewRole = "${requestScope.isViewRole}";
//alert("The value of the isViewRole is ==>"+isViewRole);

function disableAllButtons()
{
	<c:if test="${'yes' != requestScope.isViewRole}">

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		//document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

</c:if>

	<c:if test="${'yes' == requestScope.isViewRole}">

		//document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

</c:if>



}

function onFilterandSort(){
	updateColors();
	disableAllButtons();
}

function bodyOnLoad()
{
	xl = new XLSheet("workQAccessDetails","table_2", ["String","String", "String", "String","String","String", "String","String","String"],"111111111");

	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("workQAccessDetails");

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";

	var entitydropBox = new SwSelectBox(document.forms[0].elements["workQAccess.id.entityId"],document.getElementById("entityDesc"));
	var currencydropBox = new SwSelectBox(document.forms[0].elements["workQAccess.id.currencyCode"],document.getElementById("currencyDesc"));

	<c:if test="${'yes' == requestScope.isViewRole}">

		//document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

</c:if>

	<c:if test="${'yes' != requestScope.isViewRole}">

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		//document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

</c:if>

}

function populateDropBoxes(){
	bodyOnLoad();
}

function submitForm(methodName){
	if( methodName == 'delete')
	{
	var yourstate=window.confirm('<fmt:message key="workqueueaccess.confirm.selectedCurrency1"/>' + " " +'<fmt:message key="workqueueaccess.confirm.selectedCurrency2"/>');
		if (yourstate==true)
		{
			document.getElementById("workQAccess.id.currencyCode").disabled = "";
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}
	}else
	{
		document.forms[0].isViewRole.value = isViewRole;
		//alert("The value of parameter isViewRole is ==>"+document.forms[0].isViewRole.value);
		document.getElementById("workQAccess.id.currencyCode").disabled = "";
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
}

function buildAddWorkQAccess(methodName){
	var param = 'workQAccess.do?method='+methodName+'&selectedCurrencyCode=';
	param += document.forms[0].selectedCurrencyCode.value;
	param += '&entityCode=' + document.forms[0].elements['workQAccess.id.entityId'].value;
	param += '&entityName=' +document.getElementById('entityDesc').innerText;
	param += '&currencyCode=' +document.forms[0].elements['workQAccess.id.currencyCode'].value;
	//alert(param);
		return  param;
}

function onSelectTableRow(rowElement, isSelected)
{
	document.forms[0].selectedCurrencyCode.value = rowElement.cells[0].innerText;

	var hiddenElement = rowElement.getElementsByTagName("input")[0];

	document.forms[0].selectedCurrencyCode.value = hiddenElement.value;
	document.forms[0].entityCode.value = document.forms[0].elements['workQAccess.id.entityId'].value;

	var count = getCountRowsSelected(rowElement);


	if(entityAccess == "0" && count ==1)
	{
		<c:if test="${'yes' == requestScope.isViewRole}">

//			document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;

</c:if>

		<c:if test="${'yes' != requestScope.isViewRole}">

			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
//			document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;

</c:if>
	}else

	{
		<c:if test="${'yes' == requestScope.isViewRole}">

			//document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

</c:if>

		<c:if test="${'yes' != requestScope.isViewRole}">

			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			//document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

</c:if>

	}

}
</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="workQAccess.do" method="post">
<input name="method" type="hidden" value="display">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="entityCode" type="hidden" value="">
<input name="isViewRole" type="hidden" value="">


<div id="WorkQueueAccess" style="position:absolute; left:20px; top:20px; width:706px; height:60px; border:2px outset;" color="#7E97AF">
	<div id="WorkQueueAccess" style="position:absolute;left:8px; top:4px; width:670px; height:20px;">
		<table width="510px" border="0" cellpadding="0" cellspacing="0" height="50px">
			<tr height="23px">
			  <td width="62px"><b><fmt:message key="entity.id"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				  <select id="workQAccess.id.entityId" name="workQAccess.id.entityId" class="htmlTextAlpha"
						  titleKey="tooltip.selectEntityId" onchange="submitForm('displayDetails')"
						  style="width:140px" tabindex="1">
					  <c:forEach items="${requestScope.entities}" var="item">
						  <option value="${item.value}"
								  <c:if test="${workQAccess.id.entityId == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
				  </select>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">
					<span id="entityDesc" path="entityDesc" class="spantext">
			   </td>
			</tr>
			<tr>
			  <td width="62px"><b><fmt:message key="role.workQueueAccess.currency1"/></b>*</td>
			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				  <select id="workQAccess.id.currencyCode" name="workQAccess.id.currencyCode" class="htmlTextAlpha"
						  titleKey="tooltip.sortCurrencyCode" onchange="submitForm('displayDetailsByCurrency')" style="width:50px" tabindex="2">
					  <c:forEach items="${requestScope.currencyMaster}" var="item">
						  <option value="${item.value}"
								  <c:if test="${workQAccess.id.currencyCode == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
				  </select>
			  </td>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">
					<span id="currencyDesc" name="currencyDesc" class="spantext">
			   </td>
			</tr>
		</table>
	</div>
</div>
<div id="WorkQueueAccess" style="position:absolute; left:20px; top:85px; width:706px; height:300px; border:2px outset;" color="#7E97AF">
	<div id="WorkQueueAccess" style="position:absolute;z-index:99;left:0px; top:0px; width:686px; height:10px;">
		<table class="sort-table" id="table_2" width="685px" border="0" cellspacing="1" cellpadding="0" height="20px">
			<thead>
				<tr height="20px">
					<td width="70px" title='<fmt:message key="tooltip.sortCurrencyId"/>'><b><fmt:message key="role.workQueueAccess.currency"/></b></td>
					<td width="135px" title='<fmt:message key="tooltip.sortWorkQueue"/>'><b><fmt:message key="role.workQueueAccess.matchStatus"/></b></td>
					<td width="55px" title='<fmt:message key="tooltip.sortQualityA"/>'><b><fmt:message key="role.workQueueAccess.qualityA"/></b></td>
					<td width="55px" title='<fmt:message key="tooltip.sortQualityB"/>'><b><fmt:message key="role.workQueueAccess.qualityB"/></b></td>
					<td width="55px" title='<fmt:message key="tooltip.sortQualityC"/>'><b><fmt:message key="role.workQueueAccess.qualityC"/></b></td>
					<td width="55px" title='<fmt:message key="tooltip.sortQualityD"/>'><b><fmt:message key="role.workQueueAccess.qualityD"/></b></td>
					<td width="55px" title='<fmt:message key="tooltip.sortQualityE"/>'><b><fmt:message key="role.workQueueAccess.qualityE"/></b></td>
					<td width="55px" title='<fmt:message key="tooltip.sortQualityZ"/>'><b><fmt:message key="role.workQueueAccess.qualityZ"/></b></td>
					<td width="130px" title='<fmt:message key="tooltip.sortOutstanding"/>'><b><fmt:message key="role.workQueueAccess.outStanding"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:701px; height:295px;overflowY:scroll">
		<div id="WorkQueueAccess" style="position:absolute;z-index:99;left:0px; top:21px; width:706px; height:10px;">
			<table id="workQAccessDetails" class="sort-table" width="685px" border="0" cellspacing="1" cellpadding="0" height="274px">
			<tbody>
				<%int count = 0; %>
				<c:forEach items="${requestScope.workQAccessDetails}" var="workQAccessDetails" >
					<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
						<input type="hidden" name="workQAccessDetails.currencyCode" value= "${workQAccessDetails.currencyCode}">
						<td width="70px" align="left">${workQAccessDetails.currencyCode}&nbsp;</td>
						<td width="135px">${workQAccessDetails.matchStatus}&nbsp;</td>
						<td width="55px" align="center">${workQAccessDetails.qualA}&nbsp;</td>
						<td width="55px" align="center">${workQAccessDetails.qualB}&nbsp;</td>
						<td width="55px" align="center">${workQAccessDetails.qualC}&nbsp;</td>
						<td width="55px" align="center">${workQAccessDetails.qualD}&nbsp;</td>
						<td width="55px" align="center">${workQAccessDetails.qualE}&nbsp;</td>
						<td width="55px" align="center">${workQAccessDetails.qualZ}&nbsp;</td>
						<td width="130px" align="center">${workQAccessDetails.outstanding}&nbsp;</td>
					</tr>
				</c:forEach>
				</tbody>
				<tfoot><tr><td colspan="9" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<div id="WorkQueueAccess" style="position:absolute; left:1; top:398; width:706px; height:15px; visibility:visible;">
	<table width="711px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="right" id="Print">
				<a title='<fmt:message key="tooltip.printScreen"/>' tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" />
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:389px; width:706px; height:39px; visibility:visible;">
<div id="WorkQueueAccess" style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:visible;">
<c:if test="${'yes' == requestScope.isViewRole}">

  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>			<!-- <td width="70px" id="viewbutton"></td> -->
				<td width="70px" id="closebutton">
					<a title='<fmt:message key="tooltip.close"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
				</td>
				</tr>
	</table>

</c:if>

		<c:if test="${'yes' != requestScope.isViewRole}">

			 <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20">
			 <tr>
			  <td id="addbutton" width="70px" title='<fmt:message key="tooltip.addNewWrkQAcclist"/>'>
				<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddWorkQAccess('add'),'workqueueaccessaddWindow','left=50,top=190,width=558px,height=256px,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
			</td>
			<td id="changebutton" width="70px"></td>
			<!-- <td id="viewbutton" width="70px"></td> -->
			<td id="deletebutton" width="70px"></td>
			<td id="closebutton" width="70px" title='<fmt:message key="tooltip.close"/>'>
			<a tabindex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.close"/></a></td>
			</tr>
			</table>

</c:if>
	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:hidden;">
    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="changeenablebutton" title='<fmt:message key="tooltip.changeWrkQAcclist"/>'>
			<a title='<fmt:message key="tooltip.changeQAcclist"/>' tabindex="4"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddWorkQAccess('change'),'workqueueaccesschangeWindow','left=50,top=190,width=558px,height=256px,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
		</td>
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
		</td>

		<!-- <td id="viewenablebutton" title='<fmt:message key="tooltip.viewWrkQAcclist"/>'>
			<a title='<fmt:message key="tooltip.viewQAcclist"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddWorkQAccess('view'),'workqueueaccessviewWindow','left=50,top=190,width=558px,height=256px,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
		</td> -->
		<!-- <td id="viewdisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td> -->

		<td id="deleteenablebutton" title='<fmt:message key="tooltip.deleteWrkQAcclist"/>' >
			<a title='<fmt:message key="tooltip.deleteQAcclist"/>'  tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('delete')"><fmt:message key="button.delete"/></a>
		</td>
		<td id="deletedisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>
	</tr>
    </table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

</form>
</body>
</html>