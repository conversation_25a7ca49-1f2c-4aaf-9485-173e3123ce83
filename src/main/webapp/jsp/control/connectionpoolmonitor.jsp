<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><fmt:message key="connectionPool.title.window"/></title>
<!-- <base href="."> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "connectionPool";
// document.write('<base href="' + window.location.href  + '" />');


function openChildWindow(methodName){
	var param = '/' + appName + '/connectionPool.do?method=connectionPoolView';
	var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=750,height=620,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
	return false;
}

/**
 * help
 * This function opens the help screen
 * @return none
 */
function help(){
	openWindow(buildPrintURL('print','Connection Pool Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
}


function exportDataExcel(type , str) {
	document.getElementById('exportDataForm').action='flexdataexport.do?method=' + type ;
	document.getElementById('exportData').value= str;
	document.getElementById('exportDataForm').submit();

}


</script>
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post"><input
	type="hidden" name="data" id="exportData" />
	<input type="hidden"	name="screen" id="exportDataScreen"
	value="<fmt:message key="connectionPool.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%"  src="#" />	
</body>
</html>
