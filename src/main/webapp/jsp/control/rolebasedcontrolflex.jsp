<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">

<c:if test="${'yes' != requestScope.isViewRole}">

	<c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

		<title><fmt:message key="roleBasedControl.addScreen"/></title>

</c:if>

	<c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

		<title><fmt:message key="roleBasedControl.changeScreen"/></title>

</c:if>

	<c:if test="${'copyFrom' == sessionScope.roleMaintenanceOperation}">

		<c:if test="${'addRole' == requestScope.screenTitle}">

			<title><fmt:message key="roleBasedControl.addScreen"/></title>

</c:if>

		<c:if test="${'changeRole' == requestScope.screenTitle}">

			<title><fmt:message key="roleBasedControl.changeScreen"/></title>

</c:if>

</c:if>


</c:if>
<c:if test="${'yes' == requestScope.isViewRole}">

	<title><fmt:message key="roleBasedControl.viewScreen"/></title>

</c:if>


<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body  onunload="saveSettings()">
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "roleBasedControl";


 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>