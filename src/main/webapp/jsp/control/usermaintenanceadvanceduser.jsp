<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>

<%@page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title>
<c:choose>
    <c:when test="${'save' == requestScope.methodName}">
        <fmt:message key="userSetup.addScreen"/>
    </c:when>
    <c:when test="${'update' == requestScope.methodName}">
        <fmt:message key="userSetup.changeScreen"/>
    </c:when>
    <c:when test="${'view' == requestScope.methodName}">
        <fmt:message key="userSetup.viewScreen"/>
    </c:when>
</c:choose>

</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">


function populateDropDown(){
ShowErrMsgWindow('${actionError}');
 
bodyOnLoad();
 
}
/** 
function bodyOnLoad

Draws and loads the role,entity,curreny group and selection dropdown boxes 
Changes the status radio button disabled mode value. 

**/
function bodyOnLoad() {
    // Gets the selected User ID
    var selectedUserID = document.forms[0].elements["usermaintenance.id.userId"].value;
    // Gets the currently logged in user ID
    var currentUserID = "<%=SwtUtil.getCurrentUserId(session)%>";
    // Checks the user id and method name. if user id is currently logged in user or admin then disabled the status radio button
    if ((selectedUserID == currentUserID || selectedUserID == "<%=SwtConstants.ADMIN%>") && '<%=request.getAttribute("methodName")%>' == "update") {
        document.forms[0].elements["usermaintenance.status"][0].disabled = true;
        document.forms[0].elements["usermaintenance.status"][1].disabled = true;
    }
    document.forms[0].elements["usermaintenance.status"].checked = "";
    
    
    var headerData = [];
	var dataprovider = new Array();
	
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
			
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
	
	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });
}
function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return submitPrint('report',type.toLowerCase());
}

<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();
</c:if>

/* Mantis 2178: Function Added by M.Bouraoui to convert the password field value to dummy password that should not be seen in network */
function dummyPass(pwd){
	var password='';
		for(var i=0;i<pwd.length;i++){
			password+="#";		
		}	
	return password;
}

function submitRoleForm(methodName){
	enableFields();
	document.forms[0].method.value = methodName;
	
	document.forms[0].submit();
	
}

function submitPrint(methodName,fileType){
	document.forms[0].fileType.value = fileType.trim();

	document.forms[0].method.value = methodName;
	document.forms[0].selectedUserCodeId.value=document.forms[0].elements["usermaintenance.id.userId"].value;
	document.forms[0].submit();	
	setParentChildsFocus();	
}
 
function submitForm(methodName){

	<% session=request.getSession(); %>
	var sessionId='<%=session.getId()%>';

    if(validateForm(document.forms[0]) ){
	document.forms[0].method.value = methodName;
	
	enableFields();
	document.forms[0].userId.value = document.forms[0].elements["usermaintenance.id.userId"].value;	
	/* Mantis 2178: Added by M.Bouraoui to encrypt the password and set it in the hidden variable  */ 
	try
	{
		document.forms[0].elements["encnewpasswd"].value=encryptPass(document.forms[0].elements["usermaintenance.id.userId"].value,document.forms[0].elements["usermaintenance.newPassword"].value);					
		document.forms[0].elements["usermaintenance.newPassword"].value=dummyPass(document.forms[0].elements["usermaintenance.newPassword"].value);
		document.forms[0].elements["encpasswd"].value=encryptPass(document.forms[0].elements["usermaintenance.id.userId"].value,document.forms[0].elements["usermaintenance.password"].value);	
		document.forms[0].elements["usermaintenance.password"].value=dummyPass(document.forms[0].elements["usermaintenance.password"].value);	 	 				
		document.forms[0].elements["encconfirmpasswd"].value=encryptPass(document.forms[0].elements['usermaintenance.id.userId'].value,document.forms[0].elements["usermaintenance.confirmPassword"].value);	
		document.forms[0].elements["usermaintenance.confirmPassword"].value=dummyPass(document.forms[0].elements["usermaintenance.confirmPassword"].value);
		// submit form 
	document.forms[0].submit();
	}
	catch(e)
	{
		alert('<fmt:message key="alert.htmlInternalErrorOccurred"/>'+e.message);				
	}

	}

}

  
function enableFields(){
	document.forms[0].elements["usermaintenance.id.userId"].disabled = "";
	document.forms[0].elements["usermaintenance.username"].disabled = "";	
	document.forms[0].elements["usermaintenance.password"].disabled = "";	 
}
function validateForm(objForm){
  var elementsRef = new Array(4);
  elementsRef[0] = objForm.elements["usermaintenance.id.userId"];
  elementsRef[1] = objForm.elements["usermaintenance.username"];
 <c:choose>
    <c:when test="${'update' == requestScope.methodName}">
        elementsRef[2] = objForm.elements["usermaintenance.newPassword"];
    </c:when>
    <c:when test="${'save' == requestScope.methodName}">
        elementsRef[2] = objForm.elements["usermaintenance.password"];
    </c:when>
</c:choose>

var passwordFlag='true';//Variable to check whether entered passwords are correct
<c:choose>
    <c:when test="${'update' == requestScope.methodName}">
        elementsRef[3] = objForm.elements["usermaintenance.confirmPassword"];
        if (objForm.elements["usermaintenance.confirmPassword"].value != objForm.elements["usermaintenance.newPassword"].value){
            alert('<fmt:message key="changePassword.alert.confirmPass"/>')
            passwordFlag='false';
        }
    </c:when>
    <c:when test="${'save' == requestScope.methodName}">
        elementsRef[3] = objForm.elements["usermaintenance.confirmPassword"];
        if (objForm.elements["usermaintenance.confirmPassword"].value != objForm.elements["usermaintenance.password"].value){
            alert('<fmt:message key="changePassword.alert.confirmPass"/>')
            passwordFlag='false';
        }
    </c:when>
</c:choose>
 	//If both the passwords are same then it will either add the new user or update the existing user
 	if (passwordFlag == 'true'){
		return validate(elementsRef);
	}
  }



</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"  onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropDown();" onunload="call()">
<form action="usermaintenance.do" method="post" onsubmit="return validate(this);">
  <!-- Added for Mantis 2178 to call encrypt function from Flex side to java script  jsflexbridge.swf file -->
<div id="swf">
	<object id='mySwf' classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' height='0' width='0'>
		<param name='src' value='js/jsflexbridge.swf?version=<%= SwtUtil.appVersion %>'/>
		<param name='flashVars' value=''/>
		<embed name='mySwf' src='js/jsflexbridge.swf?version=<%= SwtUtil.appVersion %>' height='0' width='0' flashVars=''/>
	</object>
</div>

<input type="hidden" name="usermaintenance.invPassAttempt" value="${usermaintenance.invPassAttempt}" />
<input type="hidden" name="usermaintenance.roleId" value="${usermaintenance.roleId}" />
<input type="hidden" name="usermaintenance.currententity" value="${usermaintenance.currententity}" />
<input type="hidden" name="usermaintenance.currentCcyGrpId" value="${usermaintenance.currentCcyGrpId}" />
<input type="hidden" name="usermaintenance.sectionid" value="${usermaintenance.sectionid}" />
<input type="hidden" name="usermaintenance.language" value="${usermaintenance.language}" />
<input type="hidden" name="usermaintenance.phonenumber" value="${usermaintenance.phonenumber}" />
<input type="hidden" name="usermaintenance.emailId" value="${usermaintenance.emailId}" />
<input type = "hidden" value="${uDate}" name="uDate"/>
<input type = "hidden" value="${loginDate}" name="loginDate"/>
<input type = "hidden" value="${logoutDate}" name="logoutDate"/>
<input name="selectedUserCodeId" type="hidden" value="">
<input name="fileType" type="hidden" value="">
<input name="method" type="hidden" value="">
<input name="userId" type="hidden" value="">
<input name="oldValue" type="hidden" value= "${oldValue}">
<input name="method1" type="hidden" value="add">

<!-- Mantis 2178: Added by M.Bouraoui to pass the hidden password parameter "encpasswd" -->
<input name="encpasswd" type="hidden" value="">
<!-- Mantis 2178: Added by M.Bouraoui to pass the hidden confirm password parameter "encconfirmpasswd" -->
<input name="encconfirmpasswd" type="hidden" value="">
<!-- Mantis 2178: Added by M.Bouraoui to pass the hidden new password "encnewpasswd" -->
<input name="encnewpasswd" type="hidden" value="">

<div id="usermaintenance" style="position:absolute; left:20px; top:20px; width:609px; height:160px; border:2px outset;" color="#7E97AF">
<div id="usermaintenance" style="position:absolute; left:8px; top:4px; width:590px; height:38px;">




<!------------------------------first fieldset------------------------------------------->
<div style="left:8px; top:4px;  height:142px;">
<fieldset style="width:589px;border:2px groove;height:105px;">

<legend>
<fmt:message key="usermaintenance.user"/>
</legend>

<table width="522" border="0" cellpadding="0" cellspacing="1" height="100">      
	 
	<tr height="24">
	   <c:choose>
			<c:when test="${'save' == requestScope.methodName}">
				<td width="163px">&nbsp;<b><fmt:message key="usermaintenance.userId*"/></b>*</td>
				<td width="28px">&nbsp;</td>
				<td width="330px" height="25">
					<input type="text" title="<fmt:message key='tooltip.enterNewUserId'/>" tabindex="1" class="htmlTextAlpha" name="usermaintenance.id.userId" style="width:140px;" 
						   onchange="return validateField(this,'usermaintenance.id.userId','alphaNumPat');" maxlength="12" ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.id.userId}" />
				</td>
			</c:when>
		
			<c:when test="${'view' == requestScope.methodName}">
				<td width="163px">&nbsp;<b><fmt:message key="usermaintenance.userId*"/></b></td>
				<td width="28px">&nbsp;</td>
				<td width="330px" height="25">
					<input type="text" title="<fmt:message key='tooltip.enterNewUserId'/>" name="usermaintenance.id.userId" style="width:140px;" class="htmlTextAlpha" 
						   ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.id.userId}" />
				</td>
			</c:when>
		
			<c:when test="${'update' == requestScope.methodName}">
				<td width="163px">&nbsp;<b><fmt:message key="usermaintenance.userId*"/></b></td>
				<td width="28px">&nbsp;</td>
				<td width="330px" height="25">
					<input type="text" name="usermaintenance.id.userId" title="<fmt:message key='tooltip.enterNewUserId'/>" style="width:140px;" class="htmlTextAlpha" maxlength="15" 
						   onchange="return validateField(this,'usermaintenance.id.userId','alphaNumPat');" disabled value="${usermaintenance.id.userId}" />
				</td>
			</c:when>
		</c:choose>
        
       </td>
    </tr>
	
	
	<tr height="24">
		<td width="163px">&nbsp;<b><fmt:message key="usermaintenance.userName"/></b></td>
		<td width="28px">&nbsp;</td>
		<td width="330px">
			<c:choose>
				<c:when test="${'save' == requestScope.methodName}">
					<input type="text" title="<fmt:message key='tooltip.enterUserName'/>" tabindex="2" class="htmlTextAlpha" name="usermaintenance.username" style="width:280px;" maxlength="30" 
						   ${screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'usermaintenance.username','alphaNumPatWithSpace');" value="${usermaintenance.username}" />
				</c:when>
	
				<c:when test="${'view' == requestScope.methodName}">
					<input type="text" title="<fmt:message key='tooltip.enterUserName'/>" name="usermaintenance.username" class="htmlTextAlpha" style="width:280px;" maxlength="30" 
						   ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.username}" />
				</c:when>
	
				<c:when test="${'update' == requestScope.methodName}">
					<input type="text" title="<fmt:message key='tooltip.enterUserName'/>" name="usermaintenance.username" class="htmlTextAlpha" style="width:280px;" maxlength="30" disabled value="${usermaintenance.username}" />
				</c:when>
			</c:choose>
		</td>
	</tr>
   <!-- Start:Code Modified by Alibasha for Mantis 1608 to show the tooltip  -->
 <tr height="24">
    <td width="163px">
        <b><fmt:message key="usermaintenance.password"/></b>
        <c:if test="${'view' != requestScope.methodName}">*</c:if>
    </td>
    <td width="28px">&nbsp;</td>
    <td width="330px">
        <c:choose>
            <c:when test="${'update' == requestScope.methodName}">
                <input type="hidden" name="usermaintenance.password" value="${usermaintenance.password}" />
                <input type="password" title="<fmt:message key='tooltip.userPassword'/>" tabindex="3" class="htmlTextAlpha" name="usermaintenance.newPassword" style="width:116px;" maxlength="35" 
                       ${screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'usermaintenance.username','alphaNumPatPassWord');" value="${usermaintenance.newPassword}" />
            </c:when>
            <c:when test="${'view' == requestScope.methodName}">
                <input type="password" title="<fmt:message key='tooltip.userPassword'/>" name="usermaintenance.newPassword" style="width:116px;" class="htmlTextAlpha" 
                       ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.newPassword}" />
            </c:when>
            <c:when test="${'save' == requestScope.methodName}">
                <input type="password" title="<fmt:message key='tooltip.enterUserPwd'/>" tabindex="3" class="htmlTextAlpha" name="usermaintenance.password" style="width:116px;" maxlength="35" 
                       ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.password}" />
            </c:when>
        </c:choose>
    </td>
</tr>

<tr height="24">
    <td width="163px">
        <b><fmt:message key="password.confirmPassword"/></b>
        <c:if test="${'view' != requestScope.methodName}">*</c:if>
    </td>
    <td width="28px">&nbsp;</td>
    <td width="330px">
        <c:choose>
            <c:when test="${'update' == requestScope.methodName}">
                <input type="password" title="<fmt:message key='tooltip.confirmPassword'/>" tabindex="3" class="htmlTextAlpha" name="usermaintenance.confirmPassword" style="width:116px;" maxlength="35" 
                       ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.confirmPassword}" />
            </c:when>
            <c:when test="${'view' == requestScope.methodName}">
                <input type="password" title="<fmt:message key='tooltip.confirmPassword'/>" name="usermaintenance.confirmPassword" style="width:116px;" class="htmlTextAlpha" 
                       ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.confirmPassword}" />
            </c:when>
            <c:when test="${'save' == requestScope.methodName}">
                <input type="password" title="<fmt:message key='tooltip.confirmPassword'/>" tabindex="3" class="htmlTextAlpha" name="usermaintenance.confirmPassword" style="width:116px;" maxlength="35" 
                       ${screenFieldsStatus ? 'disabled' : ''} value="${usermaintenance.confirmPassword}" />
            </c:when>
        </c:choose>
    </td>
</tr>

<tr height="24">
    <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.status"/></b></td>
    <td width="28px">&nbsp;</td>
    <td width="315px">
        <c:choose>
            <c:when test="${'update' == requestScope.methodName || 'view' == requestScope.methodName || 'save' == requestScope.methodName}">
                <input type="radio" title="<fmt:message key='tooltip.selectUserStatus'/>" id="1" style="width:13px;" class="htmlTextAlpha" name="usermaintenance.status" value="1" 
                       ${screenFieldsStatus ? 'disabled' : ''} ${usermaintenance.status == '1' ? 'checked' : ''} />
                <label tabindex="4" for="1"><fmt:message key="userSetup.Label.Enabled"/></label>
                <input type="radio" title="<fmt:message key='tooltip.selectUserStatus'/>" id="2" style="width:13px;" class="htmlTextAlpha" name="usermaintenance.status" value="2" 
                       ${screenFieldsStatus ? 'disabled' : ''} ${usermaintenance.status == '2' ? 'checked' : ''} />
                <label tabindex="5" for="2"><fmt:message key="userSetup.Label.Disabled"/></label>
            </c:when>
        </c:choose>
    </td>
</tr>
  </table>
  </fieldset>
  </div>
<!--------------------------------------end of first fieldset---------------------------------->
</div>
</div>
<div id="UserMaintenanceAdd" style="position:absolute; left:530; top:190px; width:70; height:39px;z-index:5; visibility:visible;">
<c:choose>
    <c:when test="${'view' == requestScope.methodName}">
        <div style="margin-top: 10px;">
            <table>
                <div id="exportReport"></div>
            </table>
        </div>
    </c:when>
    <c:otherwise>
        <table style="margin-top: 5px">
            <tr>
                <td align="right" id="Print">
                    <a tabindex="17" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif',1)">
                        <img src="images/Print.gif" name="Print" border="0" title='<fmt:message key="tooltip.printScreen"/>'>
                    </a>
                </td>
            </tr>
        </table>
    </c:otherwise>
</c:choose>
</div>
	<div style="position:absolute; left:580; top:198px; width:30; height:39px; visibility:visible;">
	<a tabindex="17" href=# onclick="javascript:openWindow(buildPrintURL('print','Add User '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:189px; width:609px; height:39px; visibility:visible;">
  <div id="UserMaintenance" style="position:absolute; left:8; top:4; width:591px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
					  

		<c:choose>
			<c:when test="${'save' == requestScope.methodName}">
				<td width="70" title='<fmt:message key="tooltip.save"/>' id="savebutton">
					<a tabindex="15" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('save')">
						<fmt:message key="button.save"/>
					</a>
				</td>
				<td width="70" id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
					<a tabindex="16" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
						<fmt:message key="button.cancel"/>
					</a>
				</td>
			</c:when>

			<c:when test="${'update' == requestScope.methodName}">
				<td width="70" title='<fmt:message key="tooltip.save"/>' id="savebutton">
					<a tabindex="15" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('update')">
						<fmt:message key="button.save"/>
					</a>
				</td>
				<td width="70" id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
					<a tabindex="16" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
						<fmt:message key="button.cancel"/>
					</a>
				</td>
			</c:when>

			<c:when test="${'view' == requestScope.methodName}">
				<td width="70" title='<fmt:message key="tooltip.close"/>' id="closebutton">
					<a tabindex="15" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
						<fmt:message key="button.close"/>
					</a>
				</td>
			</c:when>
		</c:choose>
		</tr>
		</table>
	</div>
</div>
</form>
</body>
</html>