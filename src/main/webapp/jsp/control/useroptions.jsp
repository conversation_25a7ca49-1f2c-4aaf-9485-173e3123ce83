<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<html>
<head>
 <c:if test="${'update' != requestScope.methodName}">

<title><fmt:message key="myUserDetails.title.window"/></title>

</c:if>

<c:if test="${'update' == requestScope.methodName}">

<title><fmt:message key="changeUserDetails.title.window"/></title>

</c:if>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>

<SCRIPT language="JAVASCRIPT">

<c:if test="${'updated' == requestScope.updated}">


self.close();

</c:if>

var cancelcloseElements = new Array(2);
cancelcloseElements[0] = "cancelbutton";
cancelcloseElements[1] = "closebutton";

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";



function bodyOnLoad(){
	var roleDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.roleId"],document.getElementById("roleName"));

	var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.currententity"],document.getElementById("entityName"));

	var currGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.currentCcyGrpId"],document.getElementById("currGrpName"));

	var sectionDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.sectionid"],document.getElementById("sectionDesc"));

    document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;


 	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
		document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
		document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
	<%}%>


	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	var headerData = [];
	var dataprovider = new Array();

	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);

	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);

	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });

	document.forms[0].formatAltered.value = '${requestScope.formatAltered}';
	if (document.forms[0].formatAltered.value == 'Y')
	{

		alert('<fmt:message key="alert.DateAmountFormatChanged"/>');
	}
}

function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return submitPrint('report',type.toLowerCase());
}

<c:if test="${'yes' == requestScope.parentFormRefresh}">


window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();

</c:if>

function submitRoleForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	alert('<fmt:message key="userOptions.alert.me"/>');
}
function submitCancel(methodName){

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>


	     var yourstate=window.confirm('<fmt:message key="userOptions.confirm.close"/>');
		    if (yourstate==true){
	                 window.close();
			}

	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
	document.forms[0].method.value = methodName;
	 window.close();
	 <%}%>

}
/*Start:Code Modified by Alibasha on 19-Mar-2012 For TPR_530 related to the Mantis 1557 */
/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/
function submitForm(methodName){

    var phoneValue=validateField(document.forms[0].elements['useroptions.phonenumber'],'useroptions.phonenumber','numberPat');
    document.forms[0].currententity.value = document.forms[0].elements["useroptions.currententity"].value;
	if(phoneValue)
	{
	  var emailValue=validateField(document.forms[0].elements['useroptions.emailId'],'useroptions.emailId','emailPat');
	  if(emailValue)
	  {

        elementTrim(document.forms[0]);


	    if(validateForm(document.forms[0]) ){
			document.forms[0].method.value = methodName;
			document.forms[0].language.value = document.forms[0].elements["useroptions.language"].value;
			document.forms[0].phonenumber.value = document.forms[0].elements["useroptions.phonenumber"].value;
			document.forms[0].emailId.value = document.forms[0].elements["useroptions.emailId"].value;
			document.forms[0].dateFormat.value = document.forms[0].elements["useroptions.dateFormat"].value;
			document.forms[0].amountDelimiter.value = document.forms[0].elements["useroptions.amountDelimiter"].value;
			enableFields();
			document.forms[0].submit();

		}

	}
	else
	{
	  document.forms[0].elements['useroptions.emailId'].focus();
	}
	}else
	{
	  document.forms[0].elements['useroptions.phonenumber'].focus();
	}

}
/*End:Code Modified by Alibasha on 19-Mar-2012 For TPR_530 related to the Mantis 1557  */

function submitPrint(methodName,fileType){
	document.forms[0].fileType.value = fileType.trim();
	document.forms[0].method.value = methodName;
	document.forms[0].selectedUserCodeId.value = document.forms[0].elements["useroptions.id.userId"].value;
	document.forms[0].submit();

	setParentChildsFocus();


}



function onEntityChange(methodName){
	document.forms[0].currententity.value = document.forms[0].elements["useroptions.currententity"].value;
	document.forms[0].language.value = document.forms[0].elements["useroptions.language"].value;
	document.forms[0].phonenumber.value = document.forms[0].elements["useroptions.phonenumber"].value;
	document.forms[0].emailId.value = document.forms[0].elements["useroptions.emailId"].value;
	document.forms[0].dateFormat.value = document.forms[0].elements["useroptions.dateFormat"].value;
	document.forms[0].amountDelimiter.value = document.forms[0].elements["useroptions.amountDelimiter"].value;
	document.forms[0].entityChange.value = "Y";
   	document.forms[0].method.value = methodName;
	enableFields();
	document.forms[0].submit();
}

 function submitFormCancel(methodName){


	       enableFields();
		  document.forms[0].method.value = methodName;
		  document.forms[0].submit();



}
function enableFields(){
	document.forms[0].elements["useroptions.id.userId"].disabled = "";
	document.forms[0].elements["useroptions.username"].disabled = "";
	document.forms[0].elements["useroptions.sectionid"].disabled = "";
	document.forms[0].elements["useroptions.language"].disabled = "";
	document.forms[0].elements["useroptions.phonenumber"].disabled = "";
	document.forms[0].elements["useroptions.emailId"].disabled = "";
	document.forms[0].elements["useroptions.roleId"].disabled = "";
	document.forms[0].elements["useroptions.currententity"].disabled = "";
	document.forms[0].elements["useroptions.currentCcyGrpId"].disabled = "";
	document.forms[0].elements["useroptions.dateFormat"].disabled = "";
	document.forms[0].elements["useroptions.amountDelimiter"].disabled = "";

}
function validateForm(objForm){
  var elementsRef = new Array(7);
  elementsRef[0] = objForm.elements["useroptions.id.userId"];
  elementsRef[1] = objForm.elements["useroptions.username"];
  elementsRef[2] = objForm.elements["useroptions.language"];
  elementsRef[3] = objForm.elements["useroptions.roleId"];
  elementsRef[4] = objForm.elements["useroptions.dummyPassword"];
  elementsRef[5] = objForm.elements["useroptions.currententity"];
  elementsRef[6] = objForm.elements["useroptions.currentCcyGrpId"];
  return validate(elementsRef);
 }

</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"  onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);" onunload="call()">
<form action="useroptions.do" method="post" method="post" onsubmit="return validate(this);">
<input type="hidden" name="useroptions.invPassAttempt" value= "${useroptions.invPassAttempt}">
<input name="method" type="hidden" value="roleEntityAccess">
<input name="oldValue" type="hidden" value= "${oldValue}">

<input name="menuAccessId" type="hidden" >

<input name="selectedUserCodeId" type="hidden" >
<input name="fileType" type="hidden" value="">
<input name="currententity" type="hidden" value="">
<input name="language" type="hidden" value="">
<input name="phonenumber" type="hidden" value="">
<input name="emailId" type="hidden" value="">
<input name="entityChange" type="hidden" value="">
<input name="formatAltered" type="hidden" value="">
<input name="dateFormat" type="hidden" value="">
<input name="amountDelimiter" type="hidden" value="">

<div id="useroptions" style="position:absolute; left:20px; top:20px; width:603px; height:568px; border:2px outset;" color="#7E97AF">
<div id="useroptions" style="position:absolute; left:8px; top:4px; width:590px; height:38px;">


<!--------------------------first fieldset---------------------------------------------------->
<div style="left:8px; top:4px;height:120px">
<fieldset style="width:585px;border:2px groove;">

<legend>
<fmt:message key="usermaintenance.user"/>
</legend>

<table width="517" border="0" cellpadding="0" cellspacing="1" height="100">

	 <tr height="24">
	  <td width="163px">&nbsp;<b  style="font-size:9pt;" ><fmt:message key="usermaintenance.userId*"/></b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="330px" height="25">

	  <c:if test="${'view' == requestScope.methodName}">

         <input type="text"  name="useroptions.id.userId" value="${useroptions.id.userId}"  titleKey="tooltip.user.id" cssClass="htmlTextAlpha" style="width:140px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>

        <c:if test="${'update' == requestScope.methodName}">

         <input type="text"  name="useroptions.id.userId" value="${useroptions.id.userId}"  cssClass="htmlTextAlpha" style="width:140px;" onchange="return validateField(this,'useroptions.id.userId','alphaNumPat');" ${requestScope.screenFieldsStatus ? 'disabled' : ''} titleKey="tooltip.user.id" />

</c:if>

		</td>
		</tr>


	<tr height="24">
	  <td width="163px">&nbsp;<b  style="font-size:9pt;"><fmt:message key="usermaintenance.userName"/>*</b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="330px">

	  <c:if test="${'view' == requestScope.methodName}">

         <input type="text"  name="useroptions.username" value="${useroptions.username}"  style="width:280px;" class="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} titleKey="tooltip.user.name" />

</c:if>

          <c:if test="${'update' == requestScope.methodName}">

         <input type="text"  name="useroptions.username" value="${useroptions.username}"  style="width:280px;" class="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} titleKey="tooltip.user.name" />

</c:if>
		 </td>
		 </tr>


	<tr height="24">
	  <td width="163px">&nbsp;<b  style="font-size:9pt;"><fmt:message key="usermaintenance.password"/></b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="330px">
	   <c:if test="${'update' == requestScope.methodName}">

         <input type="password" name="useroptions.dummyPassword" value="${useroptions.dummyPassword}"  style="width:140px;" cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} titleKey="tooltip.userpassword" />

</c:if>

        <c:if test="${'view' == requestScope.methodName}">

        <input type="password" name="useroptions.dummyPassword" value="${useroptions.dummyPassword}"  style="width:140px;" cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''}  titleKey="tooltip.userpassword" />

</c:if>
		 </td>
	 </tr>

	  <tr height="24">
	   <td width="163px" > &nbsp;<b  style="font-size:9pt;"><fmt:message key="usermaintenance.status"/></b></td>
	   <td width="28px">&nbsp;</td>
		<td width="330px">
		    <c:if test="${'update' == requestScope.methodName}">

		        <input type="radio" name="useroptions.status" ${useroptions.status == '1' ? 'checked="checked"' : ''} value="${'1'}"    style="width:13;" titleKey="tooltip.selectUserStatus" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />&nbsp;&nbsp;<fmt:message key="userOptions.Label.enabled"/>
		        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		        <input type="radio" name="useroptions.status" ${useroptions.status == '2' ? 'checked="checked"' : ''} value="${'2'}"    style="width:13;" titleKey="tooltip.selectUserStatus" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />&nbsp;&nbsp;<fmt:message key="userOptions.Label.disabled"/>

</c:if>

		    <c:if test="${'view' == requestScope.methodName}">

		        <input type="radio" name="useroptions.status" ${useroptions.status == '1' ? 'checked="checked"' : ''} value="${'1'}"    style="width:13;" titleKey="tooltip.selectUserStatus" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />&nbsp;&nbsp;<fmt:message key="userOptions.Label.enabled"/>
		        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		        <input type="radio" name="useroptions.status" ${useroptions.status == '2' ? 'checked="checked"' : ''} value="${'2'}"    style="width:13;" titleKey="tooltip.selectUserStatus" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />&nbsp;&nbsp;<fmt:message key="userOptions.Label.disabled"/>

</c:if>
		</td>
	 </tr>



</table>
</fieldset>
</div>

<!--------------------------------------end of first fieldset---------------------------------------------->

<!-----------------------------------second fieldset-------------------------------------------->
<div style="left:8px; top:4px;height:120px;">
<fieldset style="width:585px;border:2px groove;">

<legend>
<fmt:message key="usermaintenance.profile"/>
</legend>

<table width="580" border="0" cellpadding="0" cellspacing="1" height="75px">

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="alertMessage.roleId"/>*</b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="404px">

     <c:if test="${'view' == requestScope.methodName}">
<select id="useroptions.roleId" name="useroptions.roleId" class="htmlTextAlpha"
		style="width:140px;" titleKey="tooltip.userrole"
		onchange="submitRoleForm('roleEntityAccess');" tabindex="1"
		${requestScope.screenFieldsStatus ? 'disabled' : ''} >
	<c:forEach items="${requestScope.roleIdList}" var="role">
		<option value="${role.value}" <c:if test="${useroptions.roleId == role.value}">selected</c:if>>
				${role.label}
		</option>
	</c:forEach>
</select>

	 </c:if>

		<c:if test="${'update' == requestScope.methodName}">
<select id="useroptions.roleId" name="useroptions.roleId" class="htmlTextAlpha"
		style="width:140px;" titleKey="tooltip.userrole"
		onchange="submitRoleForm('roleEntityAccess');" tabindex="1"
		${requestScope.screenFieldsStatus ? 'disabled' : ''} >
	<c:forEach items="${requestScope.roleIdList}" var="role">
		<option value="${role.value}" <c:if test="${useroptions.roleId == role.value}">selected</c:if>>
				${role.label}
		</option>
	</c:forEach>
</select>

		 </c:if>
&nbsp;&nbsp;
		 <span id="roleName" class="spantext">

	  </td>
</tr>

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.entity"/>*</b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="404px">
			<c:if test="${'view' == requestScope.methodName}">
<select id="useroptions.currententity" name="useroptions.currententity" class="htmlTextAlpha"
		style="width:140px" titleKey="tooltip.selectEntity" tabindex="1"
		${requestScope.screenFieldsStatus ? 'disabled' : ''}>
	<c:forEach items="${requestScope.roleEntityList}" var="entity">
		<option value="${entity.value}" <c:if test="${useroptions.currententity == entity.value}">selected</c:if>>
				${entity.label}
		</option>
	</c:forEach>
</select>

			</c:if>

			<c:if test="${'update' == requestScope.methodName}">
<select id="useroptions.currententity" name="useroptions.currententity" class="htmlTextAlpha"
		style="width:140px" titleKey="tooltip.selectEntity" tabindex="1" onchange="javascript:onEntityChange('change')">
	<c:forEach items="${requestScope.roleEntityList}" var="entity">
		<option value="${entity.value}" <c:if test="${useroptions.currententity == entity.value}">selected</c:if>>
				${entity.label}
		</option>
	</c:forEach>
</select>

			</c:if>&nbsp;&nbsp;
					 <span id="entityName" class="spantext">

	  </td>
</tr>

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.currGrp"/>*</b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="404px">
			<c:if test="${'view' == requestScope.methodName}">
<select id="useroptions.currentCcyGrpId" name="useroptions.currentCcyGrpId" class="htmlTextAlpha"
		style="width:140px" titleKey="tooltip.selectCuurencyGrp" tabindex="1"
		${requestScope.screenFieldsStatus ? 'disabled' : ''}>
	<c:forEach items="${requestScope.currencyGroupList}" var="currencyGroup">
		<option value="${currencyGroup.value}" <c:if test="${useroptions.currentCcyGrpId == currencyGroup.value}">selected</c:if>>
			${currencyGroup.label}
		</option>
	</c:forEach>
</select>

			</c:if>

			<c:if test="${'update' == requestScope.methodName}">
<select id="useroptions.currentCcyGrpId" name="useroptions.currentCcyGrpId" class="htmlTextAlpha"
		style="width:140px" titleKey="tooltip.selectCuurencyGrp" tabindex="1">
	<c:forEach items="${requestScope.currencyGroupList}" var="currencyGroup">
		<option value="${currencyGroup.value}" <c:if test="${useroptions.currentCcyGrpId == currencyGroup.value}">selected</c:if>>
			${currencyGroup.label}
		</option>
	</c:forEach>
</select>

			</c:if>&nbsp;&nbsp;
					 <span id="currGrpName" class="spantext">

	  </td>
</tr>

<tr height="24">
<td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.section"/></b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="404px">
	 	  <c:if test="${'view' == requestScope.methodName}">

<select id="useroptions.sectionid" name="useroptions.sectionid" class="htmlTextAlpha"
		style="width:140px" disabled="${requestScope.screenFieldsStatus ? 'disabled' : ''}">
	<c:forEach items="${requestScope.sectiondetails}" var="section">
		<option value="${section.value}" <c:if test="${useroptions.sectionid == section.value}">selected</c:if>>
				${section.label}
		</option>
	</c:forEach>
</select>

</c:if>


          <c:if test="${'update' == requestScope.methodName}">




<select id="useroptions.sectionid" name="useroptions.sectionid" class="htmlTextAlpha"
		style="width:140px" disabled="${requestScope.screenFieldsStatus ? 'disabled' : ''}">
	<c:forEach items="${requestScope.sectiondetails}" var="section">
		<option value="${section.value}" <c:if test="${useroptions.sectionid == section.value}">selected</c:if>>
				${section.label}
		</option>
	</c:forEach>
</select>


</c:if>
          &nbsp;&nbsp;&nbsp;<span id="sectionDesc" name="sectionDesc" class="spantext"></span>
		</td>
</tr>

</table>
</fieldset>
</div>

<!--------------------------------------end of second fieldset-------------------------------------------->

<!-----------------------------------third fieldset------------------------------------------------------->
<div style="left:8px; top:4px;height:95px;">
<fieldset style="width:585px;border:2px groove;">

<legend>
<fmt:message key="usermaintenance.Personal"/>
</legend>

<table width="517" border="0" cellpadding="0" cellspacing="1" height="75px">

<tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.language"/></b></td>
		  <td width="28px">&nbsp;</td>
          <td width="330px">
		   <c:if test="${'update' == requestScope.methodName}">

<select id="useroptions.language" name="useroptions.language" titleKey="tooltip.enterLanguage"
		class="htmlTextAlpha" style="width:140px">
	<c:forEach items="${requestScope.languagedetails}" var="language">
		<option value="${language.value}" <c:if test="${useroptions.language == language.value}">selected</c:if>>
				${language.label}
		</option>
	</c:forEach>
</select>


</c:if>

           <c:if test="${'view' == requestScope.methodName}">

<select id="useroptions.language" name="useroptions.language" titleKey="tooltip.enterLanguage"
		class="htmlTextAlpha" style="width:140px"
		disabled="${requestScope.screenFieldsStatus ? 'disabled' : ''}">
	<c:forEach items="${requestScope.languagedetails}" var="language">
		<option value="${language.value}" <c:if test="${useroptions.language == language.value}">selected</c:if>>
				${language.label}
		</option>
	</c:forEach>
</select>


</c:if>
		  </td>
</tr>


<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.phoneNo"/></b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="330px">

          <c:if test="${'view' == requestScope.methodName}">

         <input type="text"  name="useroptions.phonenumber" value="${useroptions.phonenumber}"  titleKey="tooltip.enterPhNo" style="width:140px;" class="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>

          <c:if test="${'update' == requestScope.methodName}">

         <input type="text"  name="useroptions.phonenumber" value="${useroptions.phonenumber}"  titleKey="tooltip.enterPhNo" style="width:140px;" maxlength="20" onchange="return validateField(this,'useroptions.phonenumber','numberPat');" class="htmlTextAlpha" />

</c:if>
         </td>
	</tr>

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.emailId"/></b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="330px">
         <c:if test="${'view' == requestScope.methodName}">

         <input type="text"  name="useroptions.emailId" value="${useroptions.emailId}"  maxlength="50" style="width:300px;" class="htmlTextAlpha" titleKey="tooltip.emailId" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>

         <c:if test="${'update' == requestScope.methodName}">

         <input type="text"  name="useroptions.emailId" value="${useroptions.emailId}"  maxlength="50" titleKey="tooltip.emailId" style="width:300px;" onchange="return validateField(this,'useroptions.emailId','emailPat');" class="htmlTextAlpha" />

</c:if>
	</td>
</tr>


</table>
</fieldset>
</div>

<!-----------------------------end of third fieldset------------------------------------------------>

<!------------------------------------fourth fieldset------------------------------------------------->
<div style="left:8px; top:4px;height:143px;">

<fieldset style="width:585px;border:2px groove;">

<legend>
<fmt:message key="usermaintenance.info"/>
</legend>

<table width="505" border="0" cellpadding="0" cellspacing="1" height="100px">



	<tr height="24">
	  <td  width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.lastLogin"/></b></td>
	   <td width="32px">&nbsp;</td>
	  <td  width="160px">
		  <input value="${loginDate}" name="loginDate" class="textNumeric" style="width:145px;" disabled="true" title='<fmt:message key="tooltip.user.lastlog"/>' />
	 </td>
	 	  <td  width="160px">
		  <input value="${lastLoginIP}" name="loginDate" class="textNumeric" style="width:145px;" disabled="true" title='<fmt:message key="login.notification.tooltip.lastLoginIp"/>' />
	 </td>
    </tr>



	<tr height="24">
          <td width="250px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.pwdDate"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px">
		   <c:if test="${'update' == requestScope.methodName}">

			<input value="${uDate}" name="uDate"  class="textNumeric" style="width:145px;" disabled="true" title='<fmt:message key="tooltip.user.lastpass"/>' />

</c:if>


		  <c:if test="${'view' == requestScope.methodName}">

          <input value="${uDate}" name="uDate"  class="textNumeric" style="width:145px;" disabled="true" title='<fmt:message key="tooltip.user.lastpass"/>' />

</c:if>
		 </td>
	</tr>

	<tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.lastLogout"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px">
		     <c:if test="${'view' == requestScope.methodName}">

		  <input value="${logoutDate}" name="logoutDate"  class="textNumeric" style="width:145px;" disabled="true" title='<fmt:message key="tooltip.user.lastlogout"/>' />

</c:if>

		  <c:if test="${'update' == requestScope.methodName}">

        <input value="${logoutDate}" name="logoutDate"  class="textNumeric"style="width:145px;" disabled="true" title='<fmt:message key="tooltip.user.lastlogout"/>' />

</c:if>
		 </td>
		 </tr>

	 	<tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="login.notification.lastFailedLogin"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px">
        	<input value="${lastFaileddate}" name="lastFailedLogin"  class="textNumeric"style="width:145px;" disabled="true" title='<fmt:message key="login.notification.lastFailedLogin"/>' />
	 	  </td>
	 	   <td width="160px">
        	<input value="${lastFailedIP}" name="lastFailedLogin"  class="textNumeric"style="width:145px;" disabled="true" title='<fmt:message key="login.notification.tooltip.lastFailedLoginIp"/>' />
	 	  </td>
		 </tr>


		 <tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><fmt:message key="usermaintenance.extAuthId"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px" colspan="3">
		     <c:if test="${'view' == requestScope.methodName}">

		  <input value="${extAuthId}" name="extAuthId"  class="textAlpha" style="width:300px;" disabled="true" title='<fmt:message key="tooltip.extAuthId"/>' />

</c:if>

		  <c:if test="${'update' == requestScope.methodName}">

        <input value="${extAuthId}" name="extAuthId"  class="textAlpha"style="width:300px;" disabled="true" title='<fmt:message key="tooltip.extAuthId"/>' />

</c:if>
		 </td>
		 </tr>

	</table>
	</fieldset>
	</div>
<!---------------------------------end of fourth fieldset-------------------------------->
<!-----------------------------------five fieldset-------------------------------------------->
<div style="left: 8px; top: 4px; height: 70px;">
    <fieldset style="width: 585px; border: 2px groove;">
        <legend>
            <fmt:message key="generalsystem.generalSettings"/>
        </legend>
        <table width="383px" border="0" cellspacing="0" cellpadding="0">
            <tr height="24">
                <td width="163px"><b style="font-size: 9pt;">
                        <fmt:message key="dateFormat"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="115px">
                    <c:if test="${'view' == requestScope.methodName}">

                        <input type="radio" name="useroptions.dateFormat" ${useroptions.dateFormat == '1' ? 'checked="checked"' : ''} value="${'1'}" id="id1" titleKey="tooltip.selectDateFormatDMY" style="width:13px;margin-bottom: 5px;"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                        <label titleKey="tooltip.selectDateFormatDMY" style="height: 19px;">
                            <fmt:message key="dateFormatDDMMYY"/>
                        </label>

</c:if>
                    <c:if test="${'update' == requestScope.methodName}">

                        <input type="radio" name="useroptions.dateFormat" ${useroptions.dateFormat == '1' ? 'checked="checked"' : ''} value="${'1'}" id="id1" titleKey="tooltip.selectDateFormatDMY" style="width:13px;margin-bottom: 5px;"/>
                        <label titleKey="tooltip.selectDateFormatDMY" style="height: 19px;">
                            <fmt:message key="dateFormatDDMMYY"/>
                        </label>

</c:if>
                </td>
                <td width="112px" style="margin-bottom: 8px; padding-bottom: 5px;">
                    <c:if test="${'view' == requestScope.methodName}">

                        <input type="radio" name="useroptions.dateFormat" ${useroptions.dateFormat == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="id2" titleKey="tooltip.selectDateFormatMDY" style="width:13px;"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                        <label titleKey="tooltip.selectDateFormatMDY">
                            <fmt:message key="dateFormatMMDDYY"/>
                        </label>

</c:if>
                    <c:if test="${'update' == requestScope.methodName}">

                        <input type="radio" name="useroptions.dateFormat" ${useroptions.dateFormat == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="id2" titleKey="tooltip.selectDateFormatMDY" style="width:13px;" />
                        <label titleKey="tooltip.selectDateFormatMDY">
                            <fmt:message key="dateFormatMMDDYY"/>
                        </label>

</c:if>
                </td>
            </tr>
            <tr height="24">
                <td width="128px"><b style="font-size: 9pt;">
                        <fmt:message key="amountDelimiter"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="115px">
                    <c:if test="${'view' == requestScope.methodName}">

                        <input type="radio" name="useroptions.amountDelimiter" ${useroptions.amountDelimiter == '1' ? 'checked="checked"' : ''} value="${'1'}" id="1" titleKey="tooltip.selectAmountFormat" style="width:13px;margin-bottom: 5px;"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                        <label titleKey="tooltip.selectAmountFormat" style="height: 19px;">
                            <fmt:message key="amountcomabeforedecimal"/>
                        </label>

</c:if>
                    <c:if test="${'update' == requestScope.methodName}">

                        <input type="radio" name="useroptions.amountDelimiter" ${useroptions.amountDelimiter == '1' ? 'checked="checked"' : ''} value="${'1'}" id="1" titleKey="tooltip.selectAmountFormat" style="width:13px;margin-bottom: 5px;"  />
                        <label titleKey="tooltip.selectAmountFormat" style="height: 19px;">
                            <fmt:message key="amountcomabeforedecimal"/>
                        </label>

</c:if>
                </td>
                <td width="112px" style="margin-bottom: 8px; padding-bottom: 5px;">
                    <c:if test="${'view' == requestScope.methodName}">

                        <input type="radio" name="useroptions.amountDelimiter" ${useroptions.amountDelimiter == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="2" titleKey="tooltip.selectAmountFormat" style="width:13px;"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                        <label titleKey="tooltip.selectAmountFormat">
                            <fmt:message key="amountcomaafterdecimal"/>
                        </label>

</c:if>
                    <c:if test="${'update' == requestScope.methodName}">

                        <input type="radio" name="useroptions.amountDelimiter" ${useroptions.amountDelimiter == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="2" titleKey="tooltip.selectAmountFormat" style="width:13px;"   />
                        <label titleKey="tooltip.selectAmountFormat">
                            <fmt:message key="amountcomaafterdecimal"/>
                        </label>

</c:if>
                </td>
            </tr>
        </table>
    </fieldset>
</div>

				<!---------------------------------end of five fieldset-------------------------------->

</div>
</div>
<!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
<div id="UserOptions" style="position:absolute; left:540; top:609px; width:70; height:25px; z-index:5; visibility:visible;">
    <table >
	  	<div id="exportReport" />
	</table>
</div>

 <div id ="helpIcon" style="position:absolute; left:575; top:607px; width:30; height:39px;">
			<c:if test="${'update' == requestScope.methodName}">

					<a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Change User Detail '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
	   <c:if test="${'update' != requestScope.methodName}">

	            <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','My User Detail '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
</div>
<!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:599px; width:603px; height:39px; visibility:visible;">
  <div id="UserOptions" style="position:absolute; left:8; top:4; width:603px; height:15px; visibility:visible;">
  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70" id="changebutton">
			</td>
			<td width="70" id="savebutton">
			</td>
			<td width="70" id="cancelbutton">
			</td>
			<td width="70" id="closebutton">
			  <a  tabindex="6" title='<fmt:message key="tooltip.close"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitCancel('close')"><fmt:message key="button.close"/></a>

			</td>
		</tr>
		</table>
	</div>
    <div style="position:absolute; left:6; top:4; width:603; height:15px; visibility:hidden;">
    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>


		<td  id="changeenablebutton">
			<a tabindex="3" title='<fmt:message key="tooltip.changeUserDetails"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('change')"><fmt:message key="button.change"/></a>
		</td>
		<td id="changedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
		</td>

        <td  id="saveenablebutton">
		<a  tabindex="4" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('update')"><fmt:message key="button.save"/></a>
		</td>
		<td id="savedisablebutton">
			<a  class="disabled" disabled="disabled" ><fmt:message key="button.save"/></a>
		</td>
		<td id="cancelenablebutton">
			<a tabindex="5" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitFormCancel('display')"><fmt:message key="button.cancel"/></a>
		</td>
		<td id="canceldisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.cancel"/></a>
		</td>

	</tr>
    </table>
  </div>
</div>



</form>
</body>
</html>