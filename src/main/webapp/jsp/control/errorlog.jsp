<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>

<html>
<head>
<title><fmt:message key="ErrorLog.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">



function buildExportErrorsURL(methodName, exportType) {
      var param = 'errorlog.do?method=' + methodName;
      param += '&doExport=no';
      param += '&exportType=' + exportType;
      param += '&screen=Error Log - SMART-Predict';
      return openWindow(param, '', 'left=50,top=190,width=570,height=215,toolbar=0,resizable=yes,scrollbars=yes', 'true');
}

 
</SCRIPT>
<link rel="stylesheet"	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "ErrorLog";
</script>
</head>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>

</html>
