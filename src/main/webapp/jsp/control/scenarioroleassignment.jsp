<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.Scenario"%>
<%@page import="org.swallow.control.model.ScenarioNotify"%>
<html>
<head>
<title><fmt:message key="scenarioRoleAssignment.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

	var menuEntityCurrGrpAccess = "${menuAccessId}";
	mandatoryFieldsArray = "undefined";
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad()
	{
		xl = new XLSheet("scenarioNotificationDetails","table_2", ["String","String", "String", "String","String","String","String","String"],"11111111");
		
		xl.onsort = xl.onfilter = disableButtons;
		
		highlightTableRows("scenarioNotificationDetails");	
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
	
		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";	
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);	
		
		 buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";	
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);	
				
		 buttonStatus = "<%= request.getAttribute(SwtConstants.DEL_BUT_STS) %>";	
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);	
						
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		
	}
	
	/**
	 * Dispatched when the user selects a row in the grid 
	 */
	function selectTableRow(e)
	{
              var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}
		
		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;
	
		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);
		
		if(isRowSel == false)
			rowElement.className = 'selectrow' ;
	
		onSelectTableRow(rowElement,!isRowSel);
	}
	
	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected 
	 */
	function onSelectTableRow(rowElement , isSelected)
	{
		var hiddenElement = rowElement.getElementsByTagName("input")[0];
		var hiddenElement2 = rowElement.getElementsByTagName("input")[1];
		var hiddenElement3 = rowElement.getElementsByTagName("input")[2];
		
		document.forms[0].selectedScenarioID.value = hiddenElement.value;
		document.forms[0].selectedRoleValue.value = hiddenElement2.value;
		document.forms[0].selectedEntityValue.value = hiddenElement3.value;
		document.getElementById("changebutton").innerHTML = (isSelected&&menuEntityCurrGrpAccess == "0"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);
		document.getElementById("deletebutton").innerHTML =(isSelected&&menuEntityCurrGrpAccess == "0"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);
		
	 }
	
	/**
	 * Disable buttons and colors when the user does not select any row in the grid
	 */
	function disableButtons(){
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		updateColors();
	}
	
	/**
	 * delete the selected role after clicking on delete button
	 * @param methodName
	 */
	function submitDeleteForm(methodName){
			document.forms[0].method.value = methodName;	
			var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
			if (yourstate==true) 
				document.forms[0].submit();
	}
	
	/**
	 * Build URL wichi will open the add a new role assignement for a scenario
	 */
	function buildAddRoleURL(methodName){
		var param = 'scenMaintenance.do?method='+methodName;
		param +='&selectedScenarioID='+document.forms[0].selectedScenarioID.value;
		param +='&selectedRoleValue='+document.forms[0].selectedRoleValue.value;
		param +='&selectedEntityValue='+document.forms[0].selectedEntityValue.value;
		return  param;
	}
</SCRIPT>
</head>
<form action="scenMaintenance.do">
	<input name="method" type="hidden" value="display">
	<input name="selectedScenarioID" type="hidden" value="">
	<input name="selectedRoleValue" type="hidden" value="">
	<input name="selectedEntityValue" type="hidden" value="">
	<input name="menuAccessId" type="hidden" >
	<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
		<div id="ScenarioRoleAssignment" style="position:absolute; border:2px outset; left:20px; top:15px; width:1217px; height:300;">
			<div id="ScenarioRoleAssignment" style="position:absolute;z-index:99;left:0px; top:0px; width:1217px; height:10px;">
				<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1217px" border="0" cellspacing="1" cellpadding="0">
					<thead>
						<tr height="22px">
							<td width="175" align="left" title='<fmt:message key="tooltip.scenarioID"/>'><b><fmt:message key="scenario.id"/></b></td>
							<td width="140" align="left" title='<fmt:message key="tooltip.sortRoleId"/>'><b><fmt:message key="scenarioNotification.role"/></b></td>
							<td width="140" align="left"  title='<fmt:message key="tooltip.sortEntityId"/>'><b><fmt:message key="scenarioNotification.entity"/></b></td>
							<td width="90" align="left" title='<fmt:message key="tooltip.accessrequired"/>'><b><fmt:message key="scenarioNotification.access"/></b></td>
							<td width="90" align="left" title='<fmt:message key="tooltip.deliverPopup"/>'><b><fmt:message key="scenarioNotification.popup"/></b></td>
							<td width="90" align="left" title='<fmt:message key="tooltip.flashIcon"/>'><b><fmt:message key="scenarioNotification.flash"/></b></td>
 							<td width="90" align="left" title='<fmt:message key="tooltip.sendMail"/>'><b><fmt:message key="scenarioNotification.email"/></b></td>
 							<td width="180" align="left" title='<fmt:message key="tooltip.fullInstanceAccess"/>'><b><fmt:message key="scenarioNotification.fullInstanceAccess"/></b></td>							
						</tr>
					</thead>
				</table>
			</div>
			<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:1234px; height:292;overflowY:auto">
				<div id="ScenarioRoleAssignment" style="position:absolute;z-index:99;left:1px; top:22px; width:1217px; height:10px;">
					<table class="sort-table" id="scenarioNotificationDetails" width="1217px" border="0" cellspacing="1" cellpadding="0" height="270">

						<tbody>
							<c:set var="count" value="0" />
							<c:forEach var="scenarioNotificationDetails" items="${requestScope.scenarioNotificationDetails}">
								<tr height="20px" class="${count % 2 == 0 ? 'even' : 'odd'}">
									<c:set var="scenNotify" value="${scenarioNotificationDetails}" />
									<input type="hidden" name="id.scenarioId" value="${scenNotify.id.scenarioId}" />
									<input type="hidden" name="id.roleId" value="${scenNotify.id.roleId}" />
									<input type="hidden" name="id.entityId" value="${scenNotify.id.entityId}" />
									<td width="175" align="left">${scenNotify.id.scenarioId}&nbsp;</td>
									<td width="140" align="left">${scenNotify.id.roleId}&nbsp;</td>
									<td width="140" align="center">${scenNotify.id.entityId}&nbsp;</td>
									<td width="90" align="center">${scenNotify.accessrequired}&nbsp;</td>
									<td width="90" align="center">${scenNotify.deliverpopups}&nbsp;</td>
									<td width="90" align="center">${scenNotify.flashicon}&nbsp;</td>
									<td width="90" align="center">${scenNotify.sendemail}&nbsp;</td>
									<td width="180" align="center">${scenNotify.fullinstanceaccess}&nbsp;</td>
								</tr>
								<c:set var="count" value="${count + 1}" />
							</c:forEach>
						</tbody>
						<tfoot><tr><td colspan="7" ></td></tr></tfoot>
					</table>
				</div>
			</div>
			<div id="ScenarioMaintenance" style="position:absolute; left:1130; top:315; width:70px; height:29px; visibility:visible;">
				<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td align="Right">
							<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Role Assignment'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
						</td>
						<td align="right" id="Print">&nbsp;
							<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
						</td>
					</tr>
				</table>
			</div>
		</div>
			<div id="ddimagebuttons" style="position:absolute; border:2px outset; left:20; top:322; width:1234px; height:39px; visibility:visible;">
				<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:1234px; height:15px; visibility:visible;">
					<table width="285" border="0" cellspacing="0" cellpadding="0" height="20">	    
						<tr>
							<td id="addbutton"></td>
							<td id="changebutton"></td>
							<td id="deletebutton"></td>
							<td id="closebutton" width="70px">		
								<a title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>			
							</td>
						</tr>
					</table>
				</div>
				<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden;display: none;">  	
					<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
						<tr>			
				            <td id="addenablebutton">		
								<a title='<fmt:message key="tooltip.addRole"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRoleURL('addRole'),'','left=50,top=190,width=700,height=300,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add"/></a>
							</td>				
							<td id="adddisablebutton">
								<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
							</td>
							<td id="changeenablebutton">		
								<a title='<fmt:message key="tooltip.changeRole"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRoleURL('changeRole'),'alertmessagechange','left=50,top=190,width=700,height=300,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
							</td>		
							<td id="changedisablebutton">
								<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
							</td>
							<td id="deleteenablebutton">		
								<a  title='<fmt:message key="tooltip.deleteRole"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('deleteRole');" ><fmt:message key="button.delete"/></a>
							</td>		
							<td id="deletedisablebutton">
								<a title='<fmt:message key="tooltip.deleteRole"/>' class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
							</td>
						</tr>
					</table>
				</div>
			</div>
		
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</body>
</form>
</html>            