<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<title><fmt:message key="viewUserStatus.title.window"/> </title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
var dateFormat = '${sessionScope.CDM.dateFormat}';
function submitForm(methodName){

		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
function closeWindow(){
	window.close();
}
function bodyOnLoad()
{


}

</SCRIPT>
</head>
<html:form action="userStatus.do">
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();" onunload="call()">


<div id="UserStatusView" style="position:absolute; left:20px; top:20px; width:550px; height:375px; border:2px outset;" color="#7E97AF">
<div id="UserStatusView" style="position:absolute; left:8px; top:4px; width:535px; height:38px;">

<!------------------------------first fieldset------------------------------------------->
<div style="left:8px; top:4px;">
<fieldset style="width:530px;border:2px groove;height:65px;">

<legend>
<fmt:message key="usermaintenance.user"/>
</legend>

<table width="523" border="0" cellpadding="0" cellspacing="1" height="50">

	 <tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.userId"/></b></td>
	 <td width="28px">&nbsp;</td>
	   <td width="330px" height="25">

         <html:text  property="userStatus.users.id.userId" styleClass="htmlTextAlpha" style="width:140px;"  maxlength="12" disabled="${requestScope.screenFieldsStatus}"/>
          </td>
    </tr>
	<tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.userName"/></b></td>
	 <td width="28px">&nbsp;</td>
	  <td width="330px">

         <html:text  property="userStatus.users.userName" styleClass="htmlTextAlpha" style="width:280px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>
    </td>
	 </tr>
	  </table>
  </fieldset>
  </div>
  <!--------------------------------------end of first fieldset---------------------------------->

<!-----------------------------------second fieldset-------------------------------------------->
<div style="left:8px; top:4px;">
<fieldset style="width:530px;border:2px groove;height:80px;">

<legend>
<fmt:message key="usermaintenance.profile"/>
</legend>

<table width="315" border="0" cellpadding="0" cellspacing="1" height="75px">

<tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="alertMessage.roleId"/></b></td>
	<td width="28px">&nbsp;</td>
	  <td width="120px">

			 <html:text  property="userStatus.users.roleId" styleClass="htmlTextAlpha" style="width:120px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>
	</tr>
	<tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.entity"/></b></td>
	<td width="28px">&nbsp;</td>
	  <td width="120px">

        <html:text   property="userStatus.users.currentEntity" styleClass="htmlTextAlpha" style="width:120px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>

		</td>
	</tr>



	<tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.section"/></b></td>
	<td width="28px">&nbsp;</td>
	  <td width="120px">


         <html:text   property="userStatus.users.sectionId" styleClass="htmlTextAlpha" style="width:120px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>



	  </td>
	</tr>
</table>
</fieldset>
</div>
<!-----------------------------------end of second fieldset----------------------------------------------->

<!-----------------------------------third fieldset------------------------------------------------------->
<div style="left:8px; top:4px;">
<fieldset style="width:530px;border:2px groove;height:80px;">

<legend>
<fmt:message key="usermaintenance.Personal"/>
</legend>

<table width="523" border="0" cellpadding="0" cellspacing="1" height="75px">

          <tr height="25">
          <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.language"/></b></td>
		 <td width="28px">&nbsp;</td>
          <td width="330px">
           <html:text  property="userStatus.language" styleClass="htmlTextAlpha" style="width:85px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>


	  </td>
	</tr>


	<tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.phoneNo"/></b></td>
	<td width="28px">&nbsp;</td>
	  <td width="330px">


		       <html:text  property="userStatus.users.phoneNumber" styleClass="htmlTextAlpha" style="width:100px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>


	  </td>
	</tr>


	<tr height="25">
	  <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.emailId"/></b></td>
	 <td width="28px">&nbsp;</td>
	  <td width="330px">
          <html:text  property="userStatus.users.emailId" styleClass="htmlTextAlpha" style="width:320px;" maxlength="30" disabled="${requestScope.screenFieldsStatus}"/>

	  </td>
	</tr>

	</table>
	</fieldset>
</div>
<!-------------------------------------end fieldset----------------------------------------------------------------->

<!------------------------------------fourth fieldset---------------------------------------------------------------->
<div style="left:8px; top:4px;">

<fieldset style="width:530px;border:2px groove;height:80px;">

<legend>
<fmt:message key="usermaintenance.info"/>
</legend>

<table width="510" border="0" cellpadding="0" cellspacing="1" height="75px">



	<tr height="25">
	  <td  width="163px">&nbsp;<b><fmt:message key="usermaintenance.lastLogin"/></b></td>
	  <td width="28px">&nbsp;</td>
	  <td  width="330px">
	    <input value="${loginDate}" name="loginDate" class="textNumeric" style="width:142px;" disabled="true"/>


         </td>
	</tr>


	<tr height="25">
          <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.pwdDate"/></b></td>
		<td width="28px">&nbsp;</td>
          <td width="330px">
          	<input value="${uDate}" name="uDate"  class="textNumeric" style="width:142px;" disabled="true"/>


	  </td>
	</tr>


	<tr height="25">
          <td width="163px">&nbsp;<b><fmt:message key="usermaintenance.lastLogout"/></b></td>
		<td width="28px">&nbsp;</td>
          <td width="330px">


           <input value="${logoutDate}" name="logoutDate"  class="textNumeric" style="width:142px;" disabled="true"/>


	  </td>
	</tr>




</table>
</fieldset>
</div>
<!------------------------end fourth fieldset----------------------------------------------------------------------->


</div>
</div>







<div id="UserStatusView" style="position:absolute; left:8; top:408; width:565; height:15px; visibility:visible;">
	<table width="550" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="right" id="Print">
				<a tabindex="2" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key = "tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:400; width:550; height:39px; visibility:visible;">
  <div id="UserStatusView" style="position:absolute; left:6; top:4; width:550; height:15px; visibility:visible;">
  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
  <tr>
  <td id="closebutton" width="70" title='<fmt:message key ="tooltip.close"/>' >
  <a tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.close"/></a></td>
		</tr>
		</table>
	</div>
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>


<script type="text/javascript">
</script>
</html:form>
</body>
</html>


