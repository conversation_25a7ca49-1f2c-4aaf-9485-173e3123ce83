<!DOCTYPE HTML PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.ScenarioCategory"%>
<%@page import="org.swallow.control.model.Scenario"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<title>
		<c:if test="${requestScope.methodName == 'add'}">
			<fmt:message key="scenario.addScreen.title.window"/>
		</c:if>
		<c:if test="${requestScope.methodName == 'view'}">
			<fmt:message key="scenario.viewScreen.title.window"/>
		</c:if>
		<c:if test="${requestScope.methodName == 'change'}">
			<fmt:message key="scenario.changeScreen.title.window"/>
		</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body leftmargin="0" rightmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()"> 

<script type="text/javascript" src="js/datavalidation.js"></script>
<script language="JAVASCRIPT">

     var screenRoute = "scenarioDetail";
	 <c:if test="${requestScope.parentFormRefresh == 'yes'}">
		window.opener.document.forms[0].method.value="list";
		window.opener.document.forms[0].submit();
		self.close();
	</c:if>
	var lockImage = "images/lock.png";
	var unlockImage = "images/unlock.png";
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
	mandatoryFieldsArray=["*"];
	var methodName = "${methodName}"
	var selectedScenarioID = "${selectedScenarioID}"
	var selectedSystemFlag = "${selectedSystemFlag}"
	var menuAccessId = "${menuAccessId}"
	var fromAdvanced = "${fromAdvanced}"
	var pageSize = <%=SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE)%>;	
	var noneLabel = '<%=SwtConstants.FACILITY_NONE%>';
	function openGenericDisplay(param) {
        openWindow(param,'genericdisplay','left=50,top=190,width=1230,height=480,toolbar=0, resizable=yes, scrollbars=yes','true');

	}
	function refreshParent() {
		window.opener.document.forms[0].method.value="list";
		window.opener.document.forms[0].submit();
		self.close();
	}
	function subEvents(screenName) {
		var param = "scenMaintenance.do?method=subEvents&screenName="+screenName;
		var 	mainWindow = openWindow (param, 'subEvents','left=10,top=230,width=1070,height=590,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}
	
	function guiHighlightSub(screenName) {
		var param = "scenMaintenance.do?method=guiHighlightSub&screenName="+screenName;
		var 	mainWindow = openWindow (param, 'guiHighlightSub','left=10,top=230,width=820,height=550,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}

	function openDefParamScreen(methodName){
		var param = "scenMaintenance.do?method=" + methodName;
		var 	mainWindow = openWindow (param, 'defineParameters','left=10,top=230,width=630,height=420,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}
	
	function openSchedDetailScreen(methodName){
		var param = "scenMaintenance.do?method=" + methodName;
		var 	mainWindow = openWindow (param, 'openSchedule','left=10,top=230,width=630,height=420,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}
	
	function openConfigScreen(methodName){
		var param = "scenMaintenance.do?method=" + methodName;
		var 	mainWindow = openWindow (param, 'ConfigureParams','left=10,top=230,width=630,height=420,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}
	</script>

<%@ include file="/angularscripts.jsp"%>

	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" /> 
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="accountmonitor.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>