<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>
    <c:choose>
        <c:when test="${'add' == requestScope.screenName}">
            Events to launch Add
        </c:when>
        <c:when test="${'change' == requestScope.screenName}">
            Events to launch Change
        </c:when>
        <c:when test="${'view' == requestScope.screenName}">
            Events to launch View
        </c:when>
    </c:choose>
</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "eventsSub";

function openMsgFormatScreen(methodName,scenarioId){
	var param = '/' + appName + '/messageformats.do?method='+methodName;
	param += '&isScenarioFormat='+"Y";
	param += '&from='+"eventsSub";
	param += '&scenarioId='+ scenarioId;
	var 	mainWindow = openWindow (param, 'ScenMsgFormat','left=10,top=230,width=1020,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}

function openConfigScreen(methodName){
	var param = '/' + appName + '/scenMaintenance.do?method='+methodName;
	var 	mainWindow = openWindow (param, 'ScenEmailFormat','left=10,top=230,width=600,height=400,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
	return false;
}

function getMsgFormats(){
Main.updateMsgFormatCombo();
}

 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>