<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><fmt:message key="inputconfig.title.window"/><%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%></title>

		<style>
			body { margin: 0px; overflow:hidden }
		</style>
</head>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">

			window.onunload = call;
			var screenRoute = "inputConfig";
			var fromPCM = '${requestScope.fromPCM}';
		</script>

		<script type="text/javascript">
			var refreshPending = false;
			 /* Varaible declared to get MenuAccessId to
			 * Disable change button if the screen set view acces */
			var menuAccessId = <%=request.getParameter("menuAccessId")%>;
			 /* Varaible declared to get MenuAccessId to
			 * Disable change button if the screen set view acces */
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

			label["text"]["button-rate"] = "<fmt:message key="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.rateButton"/>";

			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

			label["text"]["button-save"] = "<fmt:message key="button.save"/>";
			label["tip"]["button-save"] = "<fmt:message key="tooltip.SaveChanges"/>";

			label["text"]["button-cancel"] = "<fmt:message key="button.cancel"/>";
			label["tip"]["button-cancel"] = "<fmt:message key="tooltip.CancelChanges"/>";

			label["text"]["button-change"] = "<fmt:message key="button.change"/>";
			label["tip"]["button-change"] = "<fmt:message key="inputconfig.tooltip.change"/>";
			/* Start: code added by krishna on 24-Jul-2011 for mantis 1446:GUI changes in Predict for Smart Input v6  */
			label["text"]["alert-save"] = "<fmt:message key="alert.interfaceSettings.save"/>";
			label["text"]["alert-emaillogsto"] = "<fmt:message key="alert.interfaceSettings.emaillogsto"/>";
			label["text"]["alert-number" ] = "<fmt:message key="alert.interfaceSettings.number"/>";
			label["text"]["alert-posnumber"] = "<fmt:message key="alert.interfaceSettings.posnumber"/>";
			label["text"]["alert-emails"] = "<fmt:message key="alert.interfaceSettings.emails"/>";
			label["text"]["alert-emaillog"] = "<fmt:message key="alert.interfaceSettings.emaillog"/>";
			label["text"]["alert-emails"] = "<fmt:message key="alert.interfaceSettings.emails"/>";
			label["text"]["alert-text"] = "<fmt:message key="alert.interfaceSettings.text"/>";
			label["text"]["alert-time"] = "<fmt:message key="alert.interfaceSettings.time"/>";
			label["text"]["alert-sec"] = "<fmt:message key="alert.interfaceSettings.sec"/>";
			label["text"]["alert-directory"] = "<fmt:message key="alert.interfaceSettings.directory"/>";
			label["text"]["alert-url"] = "<fmt:message key="alert.interfaceSettings.url"/>";
			label["text"]["alert-password"] = "<fmt:message key="alert.interfaceSettings.password"/>";
			label["text"]["alert-startendtime"] = "<fmt:message key="alert.interfaceSettings.startendtime"/>";
			label["text"]["alert-savecancelconfirmation"] = "<fmt:message key="alert.interfaceSettings.savecancelconfirmation"/>";
			label["text"]["alert-requiresEmailLogsTo"] = "<fmt:message key="alert.interfaceSettings.requiresEmailLogsTo"/>";
			label["text"]["alert-requiresChannelName"] = "<fmt:message key="alert.interfaceSettings.requiresChannelName"/>";
			label["text"]["alert-requiresHostName"] = "<fmt:message key="alert.interfaceSettings.requiresHostName"/>";
			label["text"]["alert-requiresPortNumber"] = "<fmt:message key="alert.interfaceSettings.requiresPortNumber"/>";
			label["text"]["alert-requiresAlertThreshold"] = "<fmt:message key="alert.interfaceSettings.requiresAlertThreshold"/>";
			label["text"]["alert-alertThresholdRange"] = "<fmt:message key="alert.interfaceSettings.alertThresholdRange"/>";;
			label["text"]["label-buildProgress"] = "<fmt:message key="screen.buildInProgress"/>";
			/* End: code added by krishna on 24-Jul-2011 for mantis 1446:GUI changes in Predict for Smart Input v6  */
                        /*Start : Added by Imed B on 18-02-2014*/
			label["text"]["label-inputConfiguration"] = "<fmt:message key="interfaceSettings.title"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
			label["text"]["label-save"] = "<fmt:message key="interfaceSettings.save"/>";
			label["text"]["label-summaryDetails"] = "<fmt:message key="interfaceSettings.summaryDetails"/>";
			label["text"]["label-bottomDetails"] = "<fmt:message key="interfaceSettings.bottomDetails"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["alert-nothingToSave"] = "<fmt:message key="alert.interfaceSettings.nothingToSave"/>";
			label["text"]["label-dataBuildProgress"] = "<fmt:message key="screen.buildInProgress"/>";
			/*End : Added by Imed B on 18-02-2014*/
			function help(){
					openWindow(buildPrintURL('print','Interface Setting'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
                      }
			/* Added to check the time  */

			function checkTime(strValue){
			    var PatternsDict = new Object();
			    PatternsDict.timePat = /^([01]?[0-9]|[2][0-3])(:[0-5][0-9])?$/;
			    var thePat = PatternsDict["timePat"];
			    var gotIt;
			    if(strValue.length > 0){
			    	gotIt = thePat.exec(strValue);
			    }
			    if(!gotIt) {
			    	return false;
			   }
			   return true;
			}





		</script>
        <%@ include file="/angularscripts.jsp"%>

	    <body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<!-- 		<div id="swf"></div> -->
		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="inputconfig.title.window"/>"/>
		</form>
		<!-- Start : code added by krishna for Mantis 1446 on 14-Jul-2011 for implementing export -->
		<iframe name="tmp" width="0%" height="0%" src="#" />
		<!-- End : code added by krishna for Mantis 1446 on 14-Jul-2011 for implementing export -->
	</body>
	</html>
