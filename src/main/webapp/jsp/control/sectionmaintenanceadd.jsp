   <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:if test="${'add' == requestScope.methodName}">

	<fmt:message key="addSection.title.window"/>


</c:if>
<c:if test="${'add' != requestScope.methodName}">

	<c:if test="${'change' == requestScope.methodName}">

		<fmt:message key="changeSection.title.window"/>

</c:if>

</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>

</head>
<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray = ["*"];
<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();

</c:if>
function submitForm(methodName){
    var sectionId=validateField(document.forms[0].elements['section.id.sectionId'],'section.id.sectionId','alphaNumPat');


	if(sectionId)
	{
	  var sectionName=validateField(document.forms[0].elements['section.sectionName'],'section.sectionName','alphaNumPatWithSpace')
	  if(sectionName)
	  {

    if(validateForm(document.forms[0]) ){
    enableFields();
    document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
	}

	else
	{
	   document.forms[0].elements['section.sectionName'].focus();
	}
	}
	else
	{
	  document.forms[0].elements['section.id.sectionId'].focus();
	}
}
function enableFields(){
	document.forms[0].elements["section.id.sectionId"].disabled = "";
	document.forms[0].elements["section.sectionName"].disabled = "";

}
function validateForm(objForm){
  var elementsRef = new Array(1);

  elementsRef[0] = objForm.elements["section.id.sectionId"];


  return validate(elementsRef);
}

</SCRIPT>
<form action="section.do" method="post" >
<input name="method" type="hidden" value="save">
<input name="oldValue" type="hidden" value= "${oldValue}">
<!--Start Code modified by Chidambaranathan for Mantis_1217 for unused image removed on 26-May-2011-->
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}')" onunload="call()">
<div id="SectionMaintenance" style="position:absolute; left:20px; top:20px; width:383px; height:68px; border:2px outset;" color="#7E97AF">
<div id="SectionMaintenance" style="position:absolute;left:8px; top:4px; width:375px; height:50px;">
      <table width="363" border="0" cellpadding="0" cellspacing="2" class="content">
        <tr width="25px">
		<c:if test="${'change' == requestScope.methodName}">

		  <td width="55"><b><fmt:message key="section.sectionId"/></b></td>

</c:if>
		 <c:if test="${'add' == requestScope.methodName}">

		  <td width="55"><b><fmt:message key="section.sectionId"/></b>*</td>

</c:if>
		  <td width="28">&nbsp;</td>

		  <c:if test="${'true' == requestScope.screenFieldsStatus}">

			<td  title='<fmt:message key="tooltip.enterSectionId"/>' width="120">
		      <input type="text"  name="section.id.sectionId" value="${section.id.sectionId}"  tabindex="1" cssClass="htmlTextAlpha" style="width:120px;" maxlength="12" disabled="true" onchange="return validateField(this,'section.sectionId','alphaNumPat');" />
          	</td>

</c:if>

		  <c:if test="${'true' != requestScope.screenFieldsStatus}">

			 <td  title='<fmt:message key="tooltip.enterSectionId"/>' width="120">
		      <input type="text"  name="section.id.sectionId" value="${section.id.sectionId}"  tabindex="1" cssClass="htmlTextAlpha" style="width:120px;" maxlength="12"  onchange="return validateField(this,'section.sectionId','alphaNumPat');" />
          	  </td>

</c:if>

           </tr>



		  <tr width="25px">
          <td width="55"><b><fmt:message key="section.sectionName"/></b></td>
		   <td width="28">&nbsp;</td>
          <td title='<fmt:message key="tooltip.enterSectionName"/>'width="280">
		  	  <input type="text" tabindex="2" name="section.sectionName" value="${section.sectionName}"  cssClass="htmlTextAlpha" maxlength="30" style="width:280px;"  titleKey="tooltip.enterSectionName" onchange="return validateField(this,'section.sectionName','alphaNumPatWithSpace');" />

		    </tr>
		   </table>
    </td>
</div>
</div>

 <div id="sectionMain" style="position:absolute ;left:330; top:94px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="39px">
		<tr>

		<td align="Right">
			<c:if test="${'add' == requestScope.methodName}">

				<a tabindex="5"  href=# onclick="javascript:openWindow(buildPrintURL('print','Add Section '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>

</c:if>
	   <c:if test="${'change' == requestScope.methodName}">

				<a tabindex="5"  href=# onclick="javascript:openWindow(buildPrintURL('print','Change Section '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>

</c:if>

		</td>
			<td align="right" id="Print">
				<a tabindex="5" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:93; width:383px; height:39px; visibility:visible;">
<div id="SectionMaintenance" style="position:absolute; left:6; top:4; width:370px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
    <tr>
	  <td>
	    <c:if test="${'add' == requestScope.methodName}">

	       <a tabindex="3"title='<fmt:message key="tooltip.save"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitForm('save');"><fmt:message key="button.save"/></a></td>

</c:if>
	      <c:if test="${'change' == requestScope.methodName}">

                <td><a tabindex="3"title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:submitForm('update');"><fmt:message key="button.save"/></a></td>

</c:if>
         </td>

	  <td id="cancelbutton"><a tabindex="4" title='<fmt:message key="tooltip.cancel"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="confirmClose('C');"><fmt:message key="button.cancel"/></a></td>
	</tr>
  </table>
</div>
</div>
<!--End Code modified by Chidambaranathan for Mantis_1217 for unused image removed on 26-May-2011-->
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>