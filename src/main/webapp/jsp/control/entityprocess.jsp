	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
	<%@ include file="/taglib.jsp"%>
	<%@ page import="org.swallow.util.SwtUtil" %>
	<html>
	
	<head>
	<title>	<fmt:message key="entityprocess.title.window"/></title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">	
	<script language="JavaScript" type="text/javascript">	
	mandatoryFieldsArray = ["entityProcessDefaultRunTime"] ;
	var enterPressed=false;
	/* variable to hold spanId for runTime or runOrder value */
	var spanId="";
	/* variable to hold old value  for runTime or runOrder */
	var txtOld="";
	/* variable to hold Select entity for selected row */
	var selectedEntity="";
	/* variable to hold  saveenble for check the status (enable/disable) */
	var saveEnable=false;
	/* variable to hold status forselected row */
	var status=""	
	//THE element which holds the table
	var element = null;
	// 'doubleclick to Edit' mode
	var blnPointToSelect = true;
	//Variable to hold validate Flag
	var valFlag=true;
	//Variable to hold application name
	var appName = "<%=SwtUtil.appName%>";
	//Variable to hold request URL
	var requestURL = new String('<%=request.getRequestURL()%>');	
	//Variable to hold Id Y
	var idy = requestURL.indexOf('/'+appName+'/');	
	requestURL=requestURL.substring(0,idy+1) ;	
	//Variable to hold  x1
	var xl;
	//Variable to hold total count
	var totalCount = 0;	
	//Variable to hold check flag
	var checkFlag = true;
	//Variable to hold check array
	var checkArray = new Array();
	//Variable to hold last selected Object
	var lastselectedObj="";
	//Variable to hold Previous Process Id
	var previousProcessId = "";
	//This buttons name used to not apply the onblur event .
	var cancelcloseElements = new Array(2);
	cancelcloseElements[0]="closebutton1";
	cancelcloseElements[1]="refreshbutton1";	
	//variable to hold Last Refresh Time
	var lastRefTime = "${requestScope.lastRefTime}";
	/**
	* valDefTime()
	* 
	* This method used to validate the default run time
	*/	
	function valDefTime() {
		//variable to hold the time pattern
		var v = "timePat"; 
		var thePat = PatternsDict[v]; 
		if(document.forms[0].elements["entityProcess.defaultRunTime"].value=="")
			return true;
		var gotIt = thePat.exec(document.forms[0].elements["entityProcess.defaultRunTime"].value);       
		return gotIt;
	}
	
	/**
	* checkLastSelect()
	* 
	* This method used to check the editable text box. 
	*/	
	function checkLastSelect(){
		//To check the editable textbox
		if(lastselectedObj!="")
	{	
		//to remove the editable text box 
		lastselectedObj.innerHTML =unescape ( '<input name="'+spanId+'" value="'+lastselectedObj.getElementsByTagName("input")[0].value+'" type="hidden"/>'+lastselectedObj.getElementsByTagName("input")[0].value);
		
	}
	
	}
	/**
	*  defaultRunTime_KeyPress()
	* 
	* This method used to detect  enter KeyPress in defaultRunTime text box. 
	*/	
	function defaultRunTime_KeyPress(){
		return event.keyCode!=13;
	}
	/**
	* onSelectTableRow()
	* @param rowElement
	* @param isSelected
	* Method called when a row in data grid selected, to enable buttons
	*/	
	function onSelectTableRow(rowElement, isSelected){
	//To check the last select row
	checkLastSelect();
	//To get the selected entity id for grid data
	if(rowElement.cells!=null)
		selectedEntity = new String(rowElement.cells[0].innerText).trim()	
	selectElement = rowElement;
	//To get  the Status (Not Running/ToRun/Disabled)
	status = new String(document.getElementById(selectedEntity+"#proStatus").value).trim()	
	if(isSelected && document.forms[0].menuAccessId.value==0){
	//To check the Status for Running or To run
	if(status=="R" || status=="TR"){
		document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
		document.getElementById("Dibutton").innerHTML = document.getElementById("Dienablebutton").innerHTML;  		 
		document.getElementById("setrunbutton").innerHTML=document.getElementById("setrundisablebutton").innerHTML;  		
	}//To check the Status for Not Running
	else if(status=="N"){
		document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
		document.getElementById("Dibutton").innerHTML = document.getElementById("Dienablebutton").innerHTML;  		
		document.getElementById("setrunbutton").innerHTML=document.getElementById("setrunenablebutton").innerHTML;  
		
	}//To check the Status for Disable
	else if(status=="D"){
		document.getElementById("Enbutton").innerHTML = document.getElementById("Enenablebutton").innerHTML;
		document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;  		
		document.getElementById("setrunbutton").innerHTML=document.getElementById("setrunenablebutton").innerHTML;  
		}
	/* Start: Modified for Mantis 1445 [1053 Beta issues] by Arumugam on 07-09-2011 */
	//To check the status value is emplty to disable the recover/enable/diable/set run buttons
	if(status==""){		
		document.getElementById("rebutton").innerHTML = document.getElementById("redisablebutton").innerHTML;
		document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
		document.getElementById("setrunbutton").innerHTML=document.getElementById("setrundisablebutton").innerHTML;
		document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML; 
	}
	//To Check status value is not emplty to enable the recover
	else{
		
		document.getElementById("rebutton").innerHTML = document.getElementById("reenablebutton").innerHTML;
		}
	/* End: Modified for Mantis 1445 [1053 Beta issues] by Arumugam on 07-09-2011 */
	}
	//To check the status value is emplty to disable the recover/enable/diable/set run buttons
	else{
	document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
    document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;   
	document.getElementById("setrunbutton").innerHTML = document.getElementById("setrundisablebutton").innerHTML;  
	document.getElementById("rebutton").innerHTML = document.getElementById("redisablebutton").innerHTML;

	}
	
}
/**
 * init()
 * @param tableName 
 * Method called when onloaded the screen 
* The entry point. This will 
* Attache all the appropriate functions to the table
*/	
 function init (tableName)
  {

    if (document.getElementById)
      element = document.getElementById (tableName);
    else  
		eval (element = "document." + tableName);
    if(isIEunder10){
    	if (blnPointToSelect)
   	     element.attachEvent ('onclick', onClickCell);
   	   else
   	     element.attachEvent ('ondblclick', onClickCell);
    }else {
	   if (blnPointToSelect)
	     element.addEventListener ('click', onClickCell);
	   else
	     element.addEventListener ('dblclick', onClickCell);
    }
   
   }
/**
 * getEventRow() 
 * Method used to Return the TR elem on which an event has fireed
 */	  
   function getEventRow (e)
  {
	var target = (e.srcElement || e.target);
    var srcElem = target;
    //crawl up to find the row
    while (srcElem.tagName != "TR" && srcElem.tagName != "TABLE")
    {
      srcElem = srcElem.parentNode;
    }
    return srcElem;
  }
 /**
 * getEventCell() 
 * Method used to Return the TD elem on which an event has fireed
 */ 
 function getEventCell (e)
  {
	 var target = (e.srcElement || e.target);
    var srcElem = target;
    //crawl up the tree to find the table col
    while (srcElem.tagName != "TD" && srcElem.tagName != "TABLE")
    {
      srcElem = srcElem.parentNode;
    }
    return srcElem;
  }

/**
 * getEventCell() 
 * Method used to set the time format for Runtime (or) Default Run Time
 */ 
function expandTimeValue1(timeVal){
	
 var strField = document.getElementById(timeVal);
 var strValue = strField.value;
 var sign = '+';
 var indexSrt = 0;
 var indexEnd = 0;
 var hours;
 var minutes = 0;
 var defTime ="";

 if(strValue.length > 0){
 indexEnd = strValue.indexOf(':');
 if(indexEnd == -1){
    indexEnd = strValue.length;
	minutes = '00';
 }else{
    minutes = strValue.substring(indexEnd+1,strValue.length);
 }
 hours = strValue.substring(indexSrt,indexEnd);
 hours = hours.replace(/\b(\d)\b/g, '0$1');
  
 strValue = hours + ':' + minutes; 
 strField.value = strValue;
 }
}
/**
 * getEventCell() 
 * Method used to Entry point for all the click events on the table
 */
 function onClickCell (e)
  {
	 var event = (window.event|| e);
    var srcElem = getEventRow (event);
	if (srcElem.tagName != "TR") 
		return;
	else 
		onEdit (event);
  }
/**
 * timeFormat() 
 * Method used to set the time format for Runtime (or) Default Run Time
 */ 
 function timeFormat(){
   var objSrcElm = new String(spanId); 
	if (objSrcElm.indexOf("runOrder")==-1)
	{
		var thePat = PatternsDict['timePat']; 
			if(thePat == PatternsDict.timePat )
				expandTimeValue1("changVal")
	}
  
  }
/**
 * focusLost() 
* This function is called when user clicks on a cell other than  the top row.
* Show the content of the cell in an input box
 */ 
  function focusLost (e){
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	lastselectedObj!=""
	var objSrcElm = target; 	
	if(valFlag==false && !enterPressed){		
		checkLastSelect();
		return false;	
	}
	if(enterPressed){
	  	enterPressed=false;
	  	return false;		
	}
	if(isCancelorCloseButtonPressed())
		objSrcElm.value = txtOld
	if(txtOld!=objSrcElm.value){
		document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
		saveEnable=true;
	}
	timeFormat();    
	objSrcElm.parentNode.innerHTML = unescape ('<input name="'+spanId+'" value="'+objSrcElm.value+'" type="hidden"/>'+objSrcElm.value);
}
 /**
 * checkvalidate() 
* This function is used to validate the time format and Numeric format 
 */  
  function checkvalidate(){
	var objSrcElm = new String(spanId); 
	if (objSrcElm.indexOf("runOrder")!=-1)
	{
		valFlag =validateField(document.getElementById("changVal"),'Number 2','numberPat',99, 1);
		if(!valFlag){
			
			if(txtOld!=document.getElementById("changVal").value)
			{
				document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
				saveEnable=true;
			}
			document.getElementById("changVal").value="";
			
			}
		enterPressed=false;
		return valFlag;
		}
	else{
		if(validateField(document.getElementById("changVal"),'Number 2','timePat')){			
			timeFormat()
			valFlag=true;
			enterPressed=false;
		}
		else{
			if(txtOld!=document.getElementById("changVal").value)
			{
				document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
				saveEnable=true;
		}
			document.getElementById("changVal").value="";
			
			valFlag=false;
			return false;
			}
		}
  }
  
  /**
 * checkForEnter() 
* This will be called on everkeypress event of the input box
* This raises the focuslost event if the user hits enter
 */  
  function checkForEnter (e){
	var event = (window.event|| e);
    if (event.keyCode == 13) {
    	enterPressed=true;
    	checkvalidate();
    	focusLost (e);
    }
 }
 /**
 * onEdit() 
* TThis function is called when user clicks on a cell other than the top row.
* Show the content of the cell in an input box
 */  

 function onEdit (e)
  {
	var target = (e.srcElement || e.target);
	var srcElem1 = getEventRow (e);	
    var srcElem = getEventCell (e);
	
	if(!valDefTime())
	{	alert("<fmt:message key='alert.entityprocess.defTime'/>");
		document.forms[0].elements["entityProcess.defaultRunTime"].focus();
		return;    
	}
	
	
	if(document.getElementById("changVal")!=null)
		return;    
    if (srcElem.tagName != "TD") 
		return;
    if (srcElem && srcElem.tagName == "INPUT" || srcElem=="null") 
		return;
    if(srcElem.cellIndex==1||srcElem.cellIndex==2){	
	if(srcElem.cellIndex==1){
		spanId=selectedEntity+"#runTime";
		if(document.getElementById(spanId).value==null)
			txtOld =""
		else
			txtOld = document.getElementById(spanId).value;
	}
	else{		
		spanId=selectedEntity+"#runOrder";
		if(document.getElementById(spanId).value==null)
			txtOld =""
		else
			txtOld = document.getElementById(spanId).value;
		}	
	
    srcElem.innerHTML = ""; 
    lastselectedObj = target;
	var objInput = document.createElement ("INPUT");
    objInput.style.width = "100%";
    objInput.type = "text";
    objInput.value = "" + txtOld;
	objInput.id= "changVal"	
	objInput.name= "changVal"	
	if(isIEunder10){
		objInput.attachEvent ("onfocusout", focusLost);
		objInput.attachEvent ("onchange", checkvalidate);    
		objInput.attachEvent ("onkeypress", checkForEnter);
	}else{	
			objInput.addEventListener ("focusout", focusLost);
			objInput.addEventListener ("change", checkvalidate);    
	    	objInput.addEventListener ("keypress", checkForEnter);
	 }
    srcElem.insertAdjacentElement ("beforeEnd", objInput);
    objInput.select ();
	}
  }
	
	/**
 * bodyOnLoad()
 * Method called on body onload
 * to load the grid and enable the buttons based on the access 
 */
	function bodyOnLoad()
	{
	var dropBox1 = new SwSelectBox(document.forms[0].elements["entityProcess.id.processName"],document.getElementById("processType"));			
	xl = new XLSheet("EntityProcessDetails1","table_2", ["String","String", "String", "String", "String", "String","String","String","String"],"000000000","false"); 		
	highlightTableRows("EntityProcessDetails1");
	previousProcessId = document.getElementById("entityProcess.id.processName").value;
	defTime = document.forms[0].elements["entityProcess.defaultRunTime"].value
	document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
    document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;  
	document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;  
	document.getElementById("setrunbutton").innerHTML = document.getElementById("setrundisablebutton").innerHTML;  
	document.getElementById("rebutton").innerHTML = document.getElementById("redisablebutton").innerHTML;
	var table = document.getElementById("EntityProcessDetails1");
	totalCount=table.rows.length-1;
	var accessId = '<%=request.getParameter("menuAccessId")%>'
	if(accessId!=""){
		document.forms[0].menuAccessId.value = accessId;
	}
	if(document.forms[0].menuAccessId.value==0){	
		init('EntityProcessDetails1');
	}else{
		document.getElementById("entityProcess.defaultRunTime").disabled=true;
	}
	document.getElementById("lastRefTime").innerText = lastRefTime.replace('Server time','<fmt:message key="alert.serverTime"/>');
}


	
/**
 * changeTimeDisplay
 * Method to change the entity local time or system time 
 */	
function changeTimeDisplay(selectTime){
	
	var disTime ='';
	if(selectTime == 'EntityTime')
		disTime = 'SystemTime'
	else
		disTime = 'EntityTime'
	
	var table = document.getElementById("EntityProcessDetails1");		
	 
	 for(i=0; i < table.rows.length-1; i++)
	 {			
		
		var changeTime = new String(table.rows[i].cells[4].innerHTML)
		
		changeTime=changeTime.replace(document.getElementById(disTime+(i+1)).innerText,document.getElementById(selectTime+(i+1)).innerText)
		table.rows[i].cells[4].innerHTML = changeTime;
		changeTime = new String(table.rows[i].cells[6].innerHTML)
		
		changeTime=changeTime.replace(document.getElementById("lastHeartBeat"+disTime+(i+1)).innerText,document.getElementById("lastHeartBeat"+selectTime+(i+1)).innerText)
		table.rows[i].cells[6].innerHTML = changeTime;
		
		changeTime = new String(table.rows[i].cells[7].innerHTML)
		
		changeTime=changeTime.replace(document.getElementById("lastEnd"+disTime+(i+1)).innerText,document.getElementById("lastEnd"+selectTime+(i+1)).innerText)
		table.rows[i].cells[7].innerHTML = changeTime;
	}
}

/**
 * closetosave()
 *
 * This function check the close button if without save, to rais the alert message
 */	
	function closetosave(){
		//To check the save button status 
		if(saveEnable){
			//To raise the alert message for without save
			var yourstate=window.confirm("<fmt:message key='alert.entityprocess.unSaved'/>");
			if (yourstate==true){				
				//To close the screen
				confirmClose('P');
			}
		}
		else{
				//To close the screen
				confirmClose('P');
			}
	}



/**
 * processChange()
 * This function to used to change the process and submit the data
 */	

	function processChange(){		
		//To check the save button status 
		if(saveEnable){
			var yourstate=window.confirm("<fmt:message key='alert.entityprocess.unSaved'/>");
			if (yourstate==true){	
				document.forms[0].method.value = 'display';	 
				document.forms[0].submit();
			}else{
				document.getElementById("entityProcess.id.processName").value=previousProcessId;
			}
			
		}
		else{
			document.forms[0].method.value = 'display';	 
			document.forms[0].submit();
			}
	
	}
	
/** * validateForm() 
* @param objForm 
* Method to validate the mandatory fields */
function validateForm(objForm){  
	var elementsRef = new Array(1);
	elementsRef[0] = objForm.elements["entityProcess.defaultRunTime"];  
	return validate(elementsRef); 
	
	}	
/**
 * submitForm()
 * @param methodName
 * This function to used to save the grid data
 */	
	function submitForm(methodName)
	{	
		//To set  the empty value for onblur event
		document.forms[0].elements["entityProcess.defaultRunTime"].onblur="";
		checkLastSelect();
		if(validateDefTime(document.forms[0].elements["entityProcess.defaultRunTime"])){		
		if(methodName=="save"){
			if(validateForm(document.forms[0])){
				document.forms[0].method.value = methodName;					
				document.forms[0].submit();	
			}
		}
		else{			
			document.forms[0].method.value = methodName;	 
			document.forms[0].submit();		
		}
		}else
			document.forms[0].elements["entityProcess.defaultRunTime"].onblur="return  validateDefTime(this);";
	}
	


/**
 * validateDefTime()
 * @param obj
 * This function to validate the default run time 
 */
	function validateDefTime(obj){
	
	if(isCancelorCloseButtonPressed())
		return true;
	if(valDefTime())
	{
		
		expandTimeValue1("entityProcess.defaultRunTime")		
		if(defTime!=document.forms[0].elements["entityProcess.defaultRunTime"].value){
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
			saveEnable=true;			
		}
		return true
	}
	else{
		if(document.forms[0].elements["entityProcess.defaultRunTime"].value!="")
		{	alert("<fmt:message key='alert.entityprocess.defTime'/>");
			document.forms[0].elements["entityProcess.defaultRunTime"].focus();
		
		}
		
	
			return false;
		}
	
	
	}

/**
 * changeStatus()
 * @param buttonName
 * This function callled to click the button for enable/disable/set run/recover and change the status 
 */
 function changeStatus(buttonName)
	{
		
		// to Check the buttonName for enable 
		if(buttonName == "enable"){
			//to change the status "Not Running"
			status ="N"	
		}// to Check the buttonName for disable 
		else if(buttonName == "disable"){
			// to Check the status for "Running"
			if(status == "R")
				//to change the status "To Disabled"
				status = "TD"
			// to Check the status for "To Run" or "Not Running"
			else if(status =="TR" || status == "N")
				//to change the status "Disabled"
				status = "D"			
		}// to Check the buttonName for setRun 
		else if(buttonName == "setrun"){
				// to Check the status for "Disabled" or "Not Running"
			if(status =="D" || status == "N")
				//to change the status "To Run"
				status ="TR"
		}// to Check the buttonName for Recover 
		else if(buttonName == "recover"){
			//to raise the alert message and get conformation 
			var yourstate=window.confirm("<fmt:message key='alert.entityProcess.recover'/>");
			if (yourstate==true)	
				//to change the status "Disabled"
				status ="D"
		}
		document.getElementById("entityProcess.processStatus").value = status;
		document.getElementById("entityProcess.id.entityId").value = selectedEntity;
		document.getElementById(selectedEntity+"#proStatus").value = status;		
	}
/**
 * saveStatus()
 * @param buttonName
 * This function callled to click the button for enable/disable/set run/recover and change the status 
 */	
	function saveStatus(buttonName){
		//To check the save button status 
		if(saveEnable)
		{
			
			//to raise the alert message and get conformation 
		    var yourstate=window.confirm("<fmt:message key='alert.entityProcess.save'/>");
			if (yourstate==true){			
				//To set the  status for curren status
				changeStatus(buttonName);
				// to called the save function and save the grid data 
				submitForm("save");
			}else{
			//To set the  status for curren status
			changeStatus(buttonName);
			// to called the save function and save the grid data 
			submitForm("savestatus");
			}
		
		}else{
			
			//To set the  status for curren status
			changeStatus(buttonName);
			// to called the save function and save the grid data 
			submitForm("savestatus");
		}
	}
	/**
	* checkfocusOut()
	* 
	* This function callled to body tag and used to check the focus out event
	*/
	function checkfocusOut()
	{
		
		//To check the editable text box
		if(document.getElementById("changVal")!=null)
		{	document.all("changVal").focus();
			
		
		}
		
	}	
	
	</SCRIPT>
	</head>
	
	<body leftmargin="0" topmargin="0" marginheight="0" onkeydown="if (event.keyCode==13) {event.keyCode=9; return event.keyCode }" onfocusout="checkfocusOut()" onLoad="bodyOnLoad();setParentChildsFocus();setTitleSuffix(document.forms[0]);" onunload="call()">
	<form action="entityprocess.do" target="" method="post">
	
	<input name="method" type="hidden"  >
	<input type="hidden" name="entityProcess.id.entityId" value="${entityProcess.id.entityId}" />
	<input type="hidden" name="entityProcess.processStatus" value="${entityProcess.processStatus}" />
	<input name="menuAccessId" type="hidden" >
	
		<div id="EntityProcess" style="position:absolute; left:10px; top:15px; width:1198px; height:85px; border:2px outset;" color="#7E97AF">
		<div id="EntityProcess" style="position:absolute; left:8px; top:2px; width:573px; height:400;">
		<table width="490" id="table_8" border="0" cellpadding="0" cellspacing="0" height="30">
		<tr height="25px">
	
		
			  <td width="150"><b><fmt:message key="label.entityprocess.process"/></b></td>
			  <td width="28">&nbsp;</td>
			  <td width="330px">					
					<select
						id="entityProcess.id.processName"
						name="entityProcess.id.processName"
						class="htmlTextAlpha"
						style="width:325px"
						title="<fmt:message key='tooltip.process'/>"
						tabindex="1"
						onchange="processChange()"
					>
						<c:forEach items="${requestScope.processNameList}" var="process">
							<option value="${process.value}" ${entityProcess.id.processName == process.value ? 'selected' : ''}>${process.label}</option>
						</c:forEach>
					</select>
			  </td>			   
		 </tr>
		  <tr height="25px">
			  <td width="150"><b><fmt:message key="label.entityprocess.processType"/></b></td>
			  <td width="28">&nbsp;</td>
			  <td width="140px">
				
					<span id="processType" name="processType" class="spantext"/>	  
				
			  </td>
			  
				
				
			   </td>
		 </tr>
		 <tr height="25px">
			  <td width="150"><b><fmt:message key="label.entityprocess.defaultRunTime"/></b>*</td>
			  <td width="28">&nbsp;</td>
			  <td width="325px" id="entityProcessDefaultRunTime">
				  <input type="text"
						 name="entityProcess.defaultRunTime"
						 style="width:45px"
						 onkeypress="return defaultRunTime_KeyPress();"
						 onclick="checkLastSelect();"
						 onchange="return validateDefTime(this);"
						 onfocus="checkLastSelect();"
						 onblur="return validateDefTime(this);"
						 title="<fmt:message key='tooltip.defaultRunTime'/>"
						 tabindex="2"
						 value="${entityProcess.defaultRunTime}" />&nbsp;&nbsp;&nbsp;
					<div style="padding-left: 65px; margin-top: -20px;"><fmt:message key="label.entityprocess.specTimeZone"/></div>
			  </td>			  
		 </tr>		 
	</table>
	
	</div>
	<div id="EntityProcess1" style="position:absolute; left:890px; top:38px; width:3px; height:400;">
		<table width="250" id="table_8" border="0" cellpadding="0" cellspacing="0" height="30">
		<tr height="25px">
		
			  <td width="440px">
					<fieldset style="width:300px;border:2px groove;height:40px;top:20px;">
						<legend><fmt:message key="label.entityprocess.timeDisplay"/></legend>
							<input type="radio" id="3" tabindex="3" style="width:13px;" title="<fmt:message key='tooltip.entityLocalTime'/>" name="entityProcess.timeDisplay" value="1" onclick="changeTimeDisplay('EntityTime')" ${entityProcess.timeDisplay == '1' ? 'checked' : ''} />
							<label for="3" title='<fmt:message key="tooltip.entityLocalTime"/>'><fmt:message key="label.entityprocess.entityLocalTime"/> </label>
							<input type="radio" id="4" tabindex="4" style="width:13px;" name="entityProcess.timeDisplay" value="2" title="<fmt:message key='tooltip.sysTime'/>" onclick="changeTimeDisplay('SystemTime')" ${entityProcess.timeDisplay == '2' ? 'checked' : ''} />
							<label for="4" title="<fmt:message key='tooltip.sysTime'/>"><fmt:message key="label.entityprocess.sysTime"/></label>
					</fieldset>
			  </td>			   
		 </tr>	 
	</table>	
	</div>
	</div>		
	<div style="color:7E97AF; position:absolute; border-left:2px outset; left:10px; top:106px; width:1199px; height:365px;">
		<div id="EntityProcess" style="position:absolute;z-index:99;left:0px; top:0px; width:1197px; height:50px !important;">
			<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1178" border="0" cellspacing="1" cellpadding="0"  height="40">	
				<thead>
					<tr>
						<td width="120" height="50px" align="center" style="padding-bottom: 12px;border-left-width: 0px;"><b><br><fmt:message key="label.entityprocess.entity"/></b></td>
						<td width="100" height="50px" align="center"><b><fmt:message key="label.entityprocess.runTimeEntity"/></b></td>
						<td width="70" height="50px" align="center" style="padding-bottom: 12px"><b style="width: 76px;"><br><fmt:message key="label.entityprocess.runOrder"/></b></td>
						<td width="105" height="50px" align="center" style="padding-bottom: 12px"><br><b><fmt:message key="label.entityprocess.status"/></b></td>
						<td width="140" height="50px" align="center" style="padding-bottom: 12px"><b><br><fmt:message key="label.entityprocess.lastStarted"/></b></td>
						<td width="90" height="50px" align="center"><b><fmt:message key="label.entityprocess.databaseSession"/></b></td>
						<td width="140" height="50px" align="center" style="padding-bottom: 12px"><b><br><fmt:message key="label.entityprocess.lastHeartbeat"/></b></td>
						<td width="140" height="50px" align="center" style="padding-bottom: 12px"><b><br><fmt:message key="label.entityprocess.lastEnded"/></b></td>						
						<td width="90" height="50px" align="center" style="padding-bottom: 12px"><center><b><br>&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="label.entityprocess.lastStatus"/></b></center></td>
						
					</tr>
				</thead>
			</table>
		</div>
		
		<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:1195px; height:360px;overflowY:scroll">
			<div id="EntityProcess" style="position:absolute;z-index:99;left:0px; top:40px; width:1178px; height:10px;" >
				<table class="sort-table" id="EntityProcessDetails1" width="1178" border="0" cellspacing="1" cellpadding="0" height="310" style="margin-top: 10px">
						<tbody>
						<%
						int count = 0;
						%>
						<c:forEach items="${requestScope.EntityProcessList}" var="entityProcess">
							<%
							if(count % 2 == 0){
							%>
								<tr class="even">
							<% } else { %>
								<tr class="odd" height="30">
							<%
							}
							count++;
							%>
							<td width="120" align="left"><c:out value="${entityProcess.id.entityId}"/>&nbsp;</td>
							<td width="100" height="25" align="center">
								<input name="${entityProcess.id.entityId}#runTime" value="${entityProcess.runTime}" type="hidden"/>
								<c:out value="${entityProcess.runTime}"/>
							</td>
							<td width="70" align="center">
								<input name="${entityProcess.id.entityId}#runOrder" value="${entityProcess.runOrder}" type="hidden"/>
								<c:out value="${entityProcess.runOrder}"/>
							</td>
							<td width="105" align="left"><c:out value="${entityProcess.processStatusDetails}"/>&nbsp;
								<input name="${entityProcess.id.entityId}#proStatus" type="hidden" value="${entityProcess.processStatus}"/>
							</td>
							<td width="140" align="left">
								<c:choose>
									<c:when test="${requestScope.timeDisplay == '1'}">
										<c:out value="${entityProcess.lastStartEntityTimeAsString}"/>
										<span id="EntityTime${count}" name="entitylocaltime${count}" style="display: none;"><c:out value="${entityProcess.lastStartEntityTimeAsString}"/></span>
										<span id="SystemTime${count}" name="systemtime${count}" style="display: none;"><c:out value="${entityProcess.lastStartSystemTimeAsString}"/></span>
									</c:when>
									<c:otherwise>
										<c:out value="${entityProcess.lastStartSystemTimeAsString}"/>
										<span id="EntityTime${count}" name="entitylocaltime${count}" style="display: none;"><c:out value="${entityProcess.lastStartEntityTimeAsString}"/></span>
										<span id="SystemTime${count}" name="systemtime${count}" style="display: none;"><c:out value="${entityProcess.lastStartSystemTimeAsString}"/></span>
									</c:otherwise>
								</c:choose>
							</td>
							<td width="90" align="left"><c:out value="${entityProcess.databaseSession}"/>&nbsp;</td>
							<td width="140" align="left">
								<c:choose>
									<c:when test="${requestScope.timeDisplay == '1'}">
										<c:out value="${entityProcess.lastHeartBeatEntityTimeAsString}"/>
										<span id="lastHeartBeatEntityTime${count}" name="entitylocaltime${count}" style="display: none;"><c:out value="${entityProcess.lastHeartBeatEntityTimeAsString}"/></span>
										<span id="lastHeartBeatSystemTime${count}" name="systemtime${count}" style="display: none;"><c:out value="${entityProcess.lastHeartBeatSystemTimeAsString}"/></span>
									</c:when>
									<c:otherwise>
										<c:out value="${entityProcess.lastHeartBeatSystemTimeAsString}"/>
										<span id="lastHeartBeatEntityTime${count}" name="entitylocaltime${count}" style="display: none;"><c:out value="${entityProcess.lastHeartBeatEntityTimeAsString}"/></span>
										<span id="lastHeartBeatSystemTime${count}" name="systemtime${count}" style="display: none;"><c:out value="${entityProcess.lastHeartBeatSystemTimeAsString}"/></span>
									</c:otherwise>
								</c:choose>
							</td>
							<td width="140" align="left">
								<c:choose>
									<c:when test="${requestScope.timeDisplay == '1'}">
										<c:out value="${entityProcess.lastEndEntityTimeAsString}"/>
										<span id="lastEndEntityTime${count}" name="entitylocaltime${count}" style="display: none;"><c:out value="${entityProcess.lastEndEntityTimeAsString}"/></span>
										<span id="lastEndSystemTime${count}" name="systemtime${count}" style="display: none;"><c:out value="${entityProcess.lastEndSystemTimeAsString}"/></span>
									</c:when>
									<c:otherwise>
										<c:out value="${entityProcess.lastEndSystemTimeAsString}"/>
										<span id="lastEndEntityTime${count}" name="entitylocaltime${count}" style="display: none;"><c:out value="${entityProcess.lastEndEntityTimeAsString}"/></span>
										<span id="lastEndSystemTime${count}" name="systemtime${count}" style="display: none;"><c:out value="${entityProcess.lastEndSystemTimeAsString}"/></span>
									</c:otherwise>
								</c:choose>
							</td>
							<td width="90" align="left"><c:out value="${entityProcess.lastRunStatusDetails}"/>&nbsp;</td>
						</tr>
						</c:forEach>
						</tbody>
						<tfoot><tr><td colspan="9"></td></tr></tfoot>
					</table>
				
			</div>
			
		</div>
	</div>
	
<div id="EntityProcess" style="position: absolute; left: 1130; top: 488; width: 70px; height: 39px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right">
				<a tabindex="12" href=# onclick="javascript:openWindow(buildPrintURL('print','Entity Process Information'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
					<img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
				</a>
			</td>
			<td align="Right">
			<a onclick="printPage();" tabindex="13" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
	</div>
	
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:478; width:1199; height:39px; visibility:visible;">
  <div id="EntityProcess" style="position:absolute; left:6; top:4; width:154px; height:15px; visibility:visible;">
	<table width="1140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		
				<td id="refreshbutton" width="70px">        
            <a  name="refreshbutton1" id="refreshbutton1" title='<fmt:message key="tooltip.refreshWindow"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"     onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('display')" onKeyDown="submitEnter(this,event)"  ><fmt:message key="button.Refresh"/></a>  
			</td>     
				
				<td id="Dibutton" width="70px"/> 
				<td id="Enbutton" width="70px"/> 
				<td id="setrunbutton" width="70px"/>		
				<td id="rebutton" width="70px"/>		
				<td id="okbutton" width="70px"/>		
				
				<td title='<fmt:message key="tooltip.close"/>' width="70px" name="closebutton" id="closebutton">		
					<a tabindex="11" onMouseOut="collapsebutton(this)" name="closebutton1" id="closebutton1"onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="closetosave();" onKeyDown="submitEnter(this,event)" ><fmt:message key="button.close"/></a>			
				</td>
				<td width="250">
					&nbsp;
				</td>			
				<td id="lastRefTimeLable" width="100" align="right" style="padding-bottom: 4px;padding-top: 5px;" >
					<b><fmt:message key="label.lastRefTime2"/></b>&nbsp;
				</td>			
				<td id="lastRefTime"  width=250 style="padding-bottom: 4px;padding-top: 5px;">
					<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">	
				</td>			
		</tr>
		</table>
	</div>


 <div id="EntityProcess" style="position:absolute; left:6; top:3; width:154px; height:15px; visibility:hidden;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			
			<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.Refresh"/></a>
		</td>
			
        <td id="Dienablebutton">        
            <a title='<fmt:message key="tooltip.disable"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"  onClick="javascript:saveStatus('disable');"><fmt:message key="button.disable"/></a>
        </td>       
        <td id="Didisablebutton">
            <a  class="disabled" disabled="disabled"><fmt:message key="button.disable"/></a>
        </td>
	<td id="Enenablebutton">        
            <a title='<fmt:message key="tooltip.enable"/>' tabindex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:saveStatus('enable');"><fmt:message key="button.enable"/></a>
        </td>       
        <td id="Endisablebutton">
            <a  class="disabled" disabled="disabled"><fmt:message key="button.enable"/></a>
        </td>
        <td id="setrunenablebutton">        
            <a title='<fmt:message key="tooltip.setRunProcess"/>' tabindex="8" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:saveStatus('setrun');" onKeyDown="submitEnter(this,event)"><fmt:message key="button.setRunProcess"/></a>
        </td>       
        <td id="setrundisablebutton">
            <a  class="disabled" disabled="disabled"><fmt:message key="button.setRunProcess"/></a>
        </td>
        <td id="reenablebutton">        
            <a title='<fmt:message key="tooltip.recoverProcess"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"  onClick="javascript:saveStatus('recover');"><fmt:message key="button.recoverProcess"/></a>
        </td>       
        <td id="redisablebutton">
            <a  class="disabled" disabled="disabled"><fmt:message key="button.recoverProcess"/></a>
        </td>
			
			<td id="okenablebutton" width="70px">	
				<a  title='<fmt:message key= "tooltip.SaveChanges"/>' tabindex="10" onKeyDown="submitEnter(this,event)"    onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitForm('save')" name="savebutton2" id="savebutton2"><fmt:message key= "button.save"/></a>
				
			</td>
			<td id="okdisablebutton"><a  class="disabled"  disabled="disabled"><fmt:message key="button.save"/></td>	
		</tr>
	</table>
</div>  
</div>	
	</form>
	</body>
	</html>
