<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<html>
<head>
<title><fmt:message key="internalMesgsFrame.title.window"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link href="style/Style.css" rel="stylesheet" type="text/css">


<SCRIPT language=JavaScript>

function changetext()
{
    document.getElementById("errMessage").innerHTML = users;
}



</SCRIPT>
</head>
<%-- START: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<body class="bodyModal" onload = "changetext()" onunload="call()">
<%-- END: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<form action="messageinternal.do" method="post" >

<div   style="position:absolute;left:0px;top:40px;height:70px;width:335px" >
    <table  style="position:absolute;left:45px;width:280px">
        <tr>
        <!-- ><td width="10%">&nbsp;</td> -->
        <td width="100%" id='errMessage' style="text-align:left" >
        </td>
        <!-- ><td width="10%">&nbsp;</td> -->
        </tr>
    </table>
</div>

<div id='divOK' style="visibility:visible;position:absolute;left:0px;top:140px">
<table  style="width:335px" cellpadding="0">
  <tr>
    <td >
        <center><input type="button" value="Yes" name="btnYes" class="bolButton" style="width:60px;height:21px;" >
        <input type="button" value="No" name="btnNO" class="bolButton" style="width:60px;height:21px;" onclick="javascript:window.close()"></center>
    </td>
  </tr>
</table>
</div>


</form>



</body>
</html>