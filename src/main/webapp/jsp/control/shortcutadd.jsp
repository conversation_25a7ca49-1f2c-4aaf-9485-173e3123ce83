<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>

<c:if test="${requestScope.methodName == 'add'}">

	<fmt:message key="shortcut.addScreen"/>

</c:if>
<c:if test="${requestScope.methodName != 'add'}">

	<c:if test="${requestScope.methodName == 'change'}">
		<fmt:message key="shortcut.changeScreen"/>

</c:if>
</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT" >
mandatoryFieldsArray = ["*"];
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
function enableFields(){
	document.forms[0].elements["shortcut.id.shortcutId"].disabled = "";
}

function submitForm(methodName){


    var shortcutValue=validateField(document.forms[0].elements['shortcut.shortcutName'],'shortcuts.shortcutNameadd','alphaNumPatWithSpace');
	if(shortcutValue)
	{

     if(validateForm(document.forms[0]) ){
	enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}

	}
	else
	{
	   document.forms[0].elements['shortcut.shortcutName'].focus();
	}


}
/**
* This method is used to validate  the mandatory field the form when click the save button.
*
* @param methodName
* @return validate
**/
function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["shortcut.id.shortcutId"];
  /* code Added  by Nageswara Rao on 03_Jan_2012 for mantis 1580: " Spaces should not be saved to end of inputted values " */
  elementTrim(document.forms[0]);
  elementsRef[1] = objForm.elements["shortcut.shortcutName"];
  return validate(elementsRef);
}


</SCRIPT>

</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
<form action="shortcut.do" method="post">
<input name="method" type="hidden" value="save">
<input name="oldValue" type="hidden" value= "${oldValue}">

<div id="Shortcut" style="position:absolute; left:20px; top:20px; width:468px; height:85px; border:2px outset;" color="#7E97AF">
<div id="Shortcut" style="position:absolute; left:8px; top:4px; width:445; height:69;">
	      <table width="445" border="0" cellpadding="2" cellspacing="0" class="content">

	         <tr height="23">
			    <c:if test="${requestScope.methodName == 'add'}">

			  <td width="120px" ><b><fmt:message key="shortcuts.id.shortcutIdadd"/></b>*</td>
			   <td width="28px" >&nbsp;</td>

			     <td width="100px">
					<div style="width:96px">

		 				<select id="shortcutId" name="shortcut.id.shortcutId" tabindex="1" titleKey="tooltip.shortCutId" class="htmlTextAlpha" style="width:96px">
       		               <c:forEach var="item" items="${requestScope.shortcutAddList}">
       						 <option value="${item.value}">${item.label}</option>
    						</c:forEach>
						</select>


						</div>
		 			</td>

</c:if>

 			      <c:if test="${requestScope.methodName == 'change'}">

				    <td width="90px" ><b><fmt:message key="shortcuts.id.shortcutIdadd"/></b></td>
			   <td width="28px" >&nbsp;</td>


 		 		   <td width="100px">
						<c:if test="${requestScope.screenFieldsStatus == 'true'}">
						<input type="text" style="width:82px" name="shortcut.id.shortcutId" value="${shortcut.id.shortcutId}"   titleKey="tooltip.shortCutId" cssClass="htmlTextAlpha" disabled="true" />

</c:if>
						<c:if test="${requestScope.screenFieldsStatus != 'true'}">
						<input type="text" style="width:82px" name="shortcut.id.shortcutId" value="${shortcut.id.shortcutId}"   titleKey="tooltip.shortCutId" cssClass="htmlTextAlpha" disabled="" />
						</c:if>

		 			</td>
 				   </c:if>

	        </tr>


	  <tr height="23">
			  <td width="120px"><b><fmt:message key="shortcuts.shortcutNameadd"/></b>*</td>
			  <td width="28px" >&nbsp;</td>
			  <td align="left">
				<input type="text"  name="shortcut.shortcutName" value="${shortcut.shortcutName}"   maxlength="20" cssClass="htmlTextAlpha" style="width:180px;"  tabindex="2" titleKey="tooltip.shortCutName" onchange="return validateField(this,'shortcuts.shortcutNameadd','alphaNumPatWithSpace');" />
			  </td>

	        </tr>

		  <tr height="23">
			  <td width="120px"><b><fmt:message key="shortcuts.menuItemId"/></b>*</td>
			   <td width="28px" >&nbsp;</td>
			  <td >
			    <div style="width:300px;">

		 				<select  name="shortcut.menuItemId"  tabindex="1" titleKey="tooltip.screenName" class="htmlTextAlpha" style="width:300px">
       		               <c:forEach var="item" items="${requestScope.screens}">

       						 <option value="${item.value}" <c:if test="${shortcut.menuItemId == item.value}">selected</c:if>>${item.label}</option>

    						</c:forEach>
						</select>

		 	   </div>
			  </td>

	        </tr>


	      </table>
  </div>
 </div>
<div id="Shortcut" style="position:absolute; left:420; top:118; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">

			<c:if test="${requestScope.methodName == 'add'}">

				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Shortcut '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
		<c:if test="${requestScope.methodName == 'change'}">

				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Shortcut '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>


			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:109; width:468px; height:39px; visibility:visible;">
  <div id="Shortcut" style="position:absolute; left:6; top:4; width:368px; height:15px; visibility:visible;">
  	  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <c:if test="${requestScope.methodName == 'add'}">


			  <td width="70px">
				<a title='<fmt:message key="tooltip.save"/>'  tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('save');"><fmt:message key="button.save"/></a>
			  </td>

</c:if>
		  <c:if test="${requestScope.methodName == 'change'}">

			  <td width="70px" >
				<a title='<fmt:message key="tooltip.save"/>'  tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('update');"><fmt:message key="button.save"/></a>
			  </td>

</c:if>
			 <td id="cancelbutton" width="70px">
				<a title='<fmt:message key="tooltip.cancel"/>' tabindex="5"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>
			</td>
			  </tr>
		  </table>
</div>
</div>

</form>
</body>
<script>
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

	getMenuWindow().refreshShortcuts();
	window.opener.document.forms[0].method.value="list";
	window.opener.document.forms[0].submit();
	self.close();

</c:if>
</script>
</html>