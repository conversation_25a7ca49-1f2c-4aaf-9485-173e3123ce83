	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
	<%@ include file="/taglib.jsp" %>
	<%@ page import="org.swallow.util.SwtUtil" %>
	<html>
	<style>
	#iFrame {
   display     : none;
   visibility  : hidden;
   height      : 1px;
 }

	</style>
	<head>
	<title>	<fmt:message key="role.accountaccess.title"/></title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
	<SCRIPT language="JAVASCRIPT">
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	var roleScreen = new String('<%=request.getParameter("selectedScreen")%>');
	var xl;
	var totalCount = 0;
	var checkFlag = true;
	var checkArray = new Array();


	function bodyOnLoad()
	{
		totalCount = 0;

		if(roleScreen=="viewScreen")
		{
			document.title='<fmt:message key="role.accountaccess.viewTitle"/>';
			disableallcheckbox();
			if('<%=request.getParameter("selectedEntityId")%>'!='null')
				document.forms[0].elements["accountAccess.id.entityId"].value='<%=request.getParameter("selectedEntityId")%>';
			else
				document.forms[0].elements["accountAccess.id.entityId"].value="${requestScope.entityId}";
			if('<%=request.getParameter("selectedRoleId")%>'!='null')
				document.forms[0].elements["accountAccess.id.roleId"].value='<%=request.getParameter("selectedRoleId")%>';
			document.forms[0].elements["accountAccess.id.roleId"].disabled = true
			document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;
		}
		else if(roleScreen=="changeScreen")
		{
				document.title='<fmt:message key="role.accountaccess.changeTitle"/>';
				document.forms[0].elements["accountAccess.id.roleId"].disabled = true
				if('<%=request.getParameter("selectedEntityId")%>'!='null')
					document.forms[0].elements["accountAccess.id.entityId"].value='<%=request.getParameter("selectedEntityId")%>';
				else
					document.forms[0].elements["accountAccess.id.entityId"].value="${requestScope.entityId}";
				if('<%=request.getParameter("selectedRoleId")%>'!='null')
					document.forms[0].elements["accountAccess.id.roleId"].value='<%=request.getParameter("selectedRoleId")%>';
				document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

		}
		else if(roleScreen=="addScreen")
		{
			document.title='<fmt:message key="role.accountaccess.addTitle"/>';
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
			if('<%=request.getParameter("selectedEntityId")%>'!='null')
				document.forms[0].elements["accountAccess.id.entityId"].value='<%=request.getParameter("selectedEntityId")%>';
			else
				document.forms[0].elements["accountAccess.id.entityId"].value="${requestScope.entityId}";
			if('<%=request.getParameter("selectedRoleId")%>'!='null')
				document.forms[0].elements["accountAccess.id.roleId"].value='<%=request.getParameter("selectedRoleId")%>';
				document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

		}
		else{
				var rl=document.forms[0].elements["accountAccess.id.roleId"].value
				if(rl.trim()=="")
				{
					alert('<fmt:message key="message.alert.noRolesAssign"/>')
					self.close();
				}
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

			}
		var dropBox1 = new SwSelectBox(document.forms[0].elements["accountAccess.id.entityId"],document.getElementById("entityDesc"));
		var dropBox2 = new SwSelectBox(document.forms[0].elements["accountAccess.id.roleId"],document.getElementById("roleDesc"));
		xl = new XLSheet("AccountAccessDetails","table_2", ["String","String", "String", "String", "String", "String","String","String","String"],"*********","false");
		xl.onsort = xl.onfilter = onFilterandSort;
		var entityAccess = "${requestScope.EntityAccess}";
		var menuAccess = "${requestScope.accessId}"
		if(menuAccess.trim()=="1" || entityAccess.trim()=="1")
		{		disableallcheckbox();
				document.getElementById("okbutton").innerHTML = '<a  class="disabled" disabled="disabled" ><fmt:message key="button.ok"/></a>'
		}
		//alert('<%=request.getAttribute("sweepingAllFlag")%>')
		if('<%=request.getAttribute("sweepingAllFlag")%>'=="true")
			document.getElementsByName("sweepingcheckall")[0].checked=true;
		else
			document.getElementsByName("sweepingcheckall")[0].checked=false;
		if('<%=request.getAttribute("matchingAllFlag")%>'=="true")
			document.getElementsByName("matchingcheckall")[0].checked=true;
		else
			document.getElementsByName("matchingcheckall")[0].checked=false;
		if('<%=request.getAttribute("manualInputAllFlag")%>'=="true")
			document.getElementsByName("inputcheckall")[0].checked=true;
		else
			document.getElementsByName("inputcheckall")[0].checked=false;

	var table = document.getElementById("AccountAccessDetails");
	totalCount = table.rows.length-1;
	if(table.rows.length==1)
	{

		disableallcheckbox();
		document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;
	}
	document.forms[0].target = "";
}

function rowCount()
{
totalCount = 0
	var filterData = new String(xl.getFilterStatus())
	filt = filterData.split(',')
	var data=new Array();
	var count = 0
		for(i=0;i<6;i++)
		{
		if(filt[i].substring(0,5)=="value")
			{
				var value = new String(filt[i])
				value = value.split("_")[1];
				data[count] = i+"_"+value;
				count++
				}
		}
	var table = document.getElementById("AccountAccessDetails");
	for(i=0; i < table.rows.length-1; i++) {
		var flag = false
		var len = parseInt(data.length)
		for(j=0; j<len;j++)
		{
			var dat = data[j].split("_")
			var tab_data = table.rows[i].cells[parseInt(dat[0])].innerText.trim()
			if(dat[1].trim()==tab_data)
				flag = true
			else
				{
					flag = false
					break;
				}


		}
		if (flag)
			totalCount = totalCount+1;
	}
	if (data.length==0)
		totalCount = table.rows.length-1
}
function selectedallInput(ele,val)
	{
	checkFlag = false;
	var filterData = new String(xl.getFilterStatus())
	filt = filterData.split(',')
	var data=new Array();
	var count = 0
		for(i=0;i<6;i++)
		{
		if(filt[i].substring(0,5)=="value")
			{
				var value = new String(filt[i])
				value = value.split("_")[1];
				data[count] = i+"_"+value;
				count++
			}

		}
		var table = document.getElementById("AccountAccessDetails");
	for(i=0; i < table.rows.length-1; i++) {
		var flag = false
		var len = parseInt(data.length)
		for(j=0; j<len;j++)
		{
			var dat = data[j].split("_")
			var tab_data = table.rows[i].cells[parseInt(dat[0])].innerText.trim()
			if(dat[1].trim()==tab_data)
				flag = true
			else
				{
					flag = false
					break;
				}


		}
		if(val=="inputcheck")
			var checks = table.rows[i].cells[6].getElementsByTagName("input")[0];
		else if (val=="matchingcheck")
			var checks = table.rows[i].cells[7].getElementsByTagName("input")[0];
		else
			var checks = table.rows[i].cells[8].getElementsByTagName("input")[0];
		if(flag)
			checks.checked =  ele.checked;

		if(data.length==0)
		{
			checks.checked =  ele.checked;
		}

	}

}

	function onFilterandSort()
{

	updateColors();
	checkall("inputcheck")
	checkall("sweepingcheck")
	checkall("matchingcheck")
	rowCount()
}

	function disableallcheckbox()
	{
		var data = new Array("inputcheckall","matchingcheckall","sweepingcheckall");
		for(j=0;j<data.length;j++)
		{
			var checks = document.getElementsByName(data[j]);
			for ( i=0; i < checks.length; i++ )
			{
				checks[i].disabled = true
			}
		}
		var table = document.getElementById("AccountAccessDetails");
		for(i=0; i < table.rows.length-1; i++)
		{
			var checks = table.rows[i].cells[6].getElementsByTagName("input")[0];
			checks.disabled = true
			var checks = table.rows[i].cells[7].getElementsByTagName("input")[0];
			checks.disabled = true
			var checks = table.rows[i].cells[8].getElementsByTagName("input")[0];
			checks.disabled = true
		}


	}
	function checkall(checkval)
	{
	 var flag="true"
	 var chk = new String(checkval+"all")

	 var checksall = document.getElementsByName(chk);


	 var table = document.getElementById("AccountAccessDetails");

	 if(checkval=="inputcheck")
		var checks_col = 6;
	 else if (checkval=="matchingcheck")
		var checks_col = 7;
	 else
		var checks_col = 8;

	 for(i=0; i < table.rows.length-1; i++)
	 {
			var checks = table.rows[i].cells[checks_col].getElementsByTagName("input")[0];
			if(!checks.checked)
			{
				flag="false"
				break;
			}

	     }
	if(flag=="true")
		checksall[0].checked=true;
	else
		checksall[0].checked=false;
	}
function reload () {
var f = document.getElementById('reportFrame');
f.src = f.src;
}
var isSaveClicked = false;
function enableClose() {
		if(document.forms[0].target != "")
			reload()
		// set the form target
		document.forms[0].target = "";

		if (roleScreen != "changeScreen" && roleScreen!="viewScreen" && roleScreen!="addScreen"){
			document.getElementById("okbutton").innerHTML = '<a  title=\'<fmt:message key="tooltip.SaveChanges"/>\' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitdata();"><fmt:message key="button.save"/></a>';
			document.getElementById("closebutton").innerHTML = '<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitCancel();"><fmt:message key="button.close"/></a>';
		}
	}
function submitdata()
	{

	checkArray = new Array();
	checkFlag = true;
	var checkAccountSize = 1;
	if('<%=request.getParameter("selectedRoleId")%>'!='null')
		document.forms[0].roleId.value='<%=request.getParameter("selectedRoleId")%>'
	else
		document.forms[0].roleId.value=document.forms[0].elements["accountAccess.id.roleId"].value
	document.forms[0].entityId.value=document.forms[0].elements["accountAccess.id.entityId"].value

	if (roleScreen=="changeScreen"|| roleScreen=="addScreen")
		{
			document.forms[0].method.value = 'saveAccountAccess';
			document.forms[0].action = 'role.do';
			isSaveClicked = true;
			window.opener.accountAccessSize = checkAccountSize;
			document.forms[0].submit();
		}
	else
		{
			document.getElementById("okbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>';
			document.forms[0].target = "reportFrame";
			document.forms[0].method.value = 'save';
			document.forms[0].action = 'accountAccess.do';
			document.forms[0].submit();
		}


	}



	function checkboxflag(flagval)
	 {
	 if (flagval)
		return "Y";
	 else
		return "N";
	 }
	 function entitychange()
	{

		if(roleScreen=="viewScreen" || roleScreen=="changeScreen" || roleScreen=="addScreen")
		{
			document.forms[0].method.value = 'viewAccountAccess';
			document.forms[0].selectedEntityId.value=document.forms[0].elements["accountAccess.id.entityId"].value
			document.forms[0].action = 'role.do';
			document.forms[0].submit();

		}
		else{
			document.forms[0].method.value = 'display';
			document.forms[0].submit();
		}
	}
	function changeRole()
	{

				document.forms[0].roleChange.value = "Y";
	}
	function helpWin()
	{

	if(roleScreen=="viewScreen")
		javascript:openWindow(buildPrintURL('print','View Account Access Control'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	else if(roleScreen=="changeScreen")
		javascript:openWindow(buildPrintURL('print','Change Account Access Control'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	else if(roleScreen=="addScreen")
		javascript:openWindow(buildPrintURL('print','Add Account Access Control'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	else
		javascript:openWindow(buildPrintURL('print','Account Access Control'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')


	}

	// This method is called when click the checkbox button
	function selectedRowElement(e){
		var event = (window.event|| e);
		var target = (event.srcElement || event.target);
		var tdElement = target.parentElement;
		var trElement = target.parentElement.parentElement;
		var accountId = new String(trElement.cells[0].innerText).trim();
		removeByElement(checkArray,accountId);
	}

	// This method is called when click the close button
	function submitCancel(){
		if (checkArray.length != 0 || !checkFlag){
			var yourstate=window.confirm('<fmt:message key="accountaccess.closeConfirm"/>');
			if (yourstate==true){
				confirmClose('P');
			}
		}
		else{
			confirmClose('P');
		}
	}

	// This method is called when click the checkbox
	function removeByElement(arrayName,arrayElement)
    {
		var arrayFlag = true;
        for(var i=0; i<arrayName.length;i++ )
           {
              if(arrayName[i]==arrayElement)
				{
					checkArray.remove(arrayName[i]);
					arrayFlag = false;
					break;
				}
           }
		if (arrayFlag){
			checkArray.push(arrayElement);
		}
	}


	function call(){
	if (isSaveClicked == true){
		self.close();
	}
	}

	</SCRIPT>
	</head>

	<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setTitleSuffix(document.forms[0]);" onunload="call()">
	<form action="accountAccess.do" method="post" target="" enctype="multipart/form-data">

	<input name="method" type="hidden"  >
	<input name="selectedEntityId" type="hidden">
	<input name="accountId" type="hidden">
	<input name="selectedRoleId" type="hidden" value='<%=request.getParameter("selectedRoleId")%>'>
	<input name="selectedScreen" type="hidden" value='<%=request.getParameter("selectedScreen")%>'>
	<input name="menuAccessId" type="hidden"  value ='<%=request.getParameter("menuAccessId")%>'>
	<input name="roleChange"  type="hidden" value="N">
	<input name="setData"  type="hidden" value="">
	<input name="roleId"  type="hidden" value="">
    <input name="entityId"  type="hidden" value="">

	<c:if test="${'view' == requestScope.methodName}">

		<div id="AccountAccess" style="position:absolute; left:20px; top:20px; width:968px; height:49px; border:2px outset;" color="#7E97AF">
		<div id="AccountAccess" style="position:absolute; left:8px; top:4px; width:573px; height:400;">
		<table width="500" id="table_8" border="0" cellpadding="0" cellspacing="0" height="30">
		<tr height="25px" style="display:none">

</c:if>
	<c:if test="${'view' != requestScope.methodName}">

		<div id="AccountAccess" style="position:absolute; left:20px; top:15px; width:968px; height:59px; border:2px outset;" color="#7E97AF">
		<div id="AccountAccess" style="position:absolute; left:8px; top:4px; width:573px; height:400;">
		<table width="500" id="table_8" border="0" cellpadding="0" cellspacing="0" height="30">
		<tr height="25px">

</c:if>

			  <td width="38"><b><fmt:message key="alertMessage.roleId"/></b></td>
			  <td width="28">&nbsp;</td>
			  <td width="140px">
					<!--Start:mondified the mantis 1549 by sunil on 10-02-2012.The purpose to display meaningful tooltip. -->
					<select id="accountAccess.id.roleId" name="accountAccess.id.roleId" class="htmlTextAlpha" style="width:140px" titleKey="tooltip.selectRoleId" onchange="changeRole();entitychange()">
						<c:forEach var="role" items="${requestScope.roleIdList}">
							<option value="${role.value}" <c:if test="${role.value == accountAccess.id.roleId}">selected</c:if>>${role.label}</option>
						</c:forEach>
					</select>


			  </td>
			   <td width="20">&nbsp;</td>
			  <td width="280">
				<span id="roleDesc" name="roleDesc" class="spantext">
			   </td>
		 </tr>
		 <tr height="25px">
			  <td width="38"><b><fmt:message key="entity.id"/></b></td>
			  <td width="28">&nbsp;</td>
			  <td width="140px">

					 <select id="accountAccess.id.entityId" name="accountAccess.id.entityId" style="width:140px" titleKey="tooltip.selectEntityId" onchange="entitychange()">
						<c:forEach var="entity" items="${requestScope.entities}">
							<option value="${entity.value}" <c:if test="${entity.value == accountAccess.id.entityId}">selected</c:if>>${entity.label}</option>
						</c:forEach>
					</select>

				<!--End:mondified the mantis 1549 by sunil on 10-02-2012.The purpose to display meaningful tooltip. -->
			  </td>
			   <td width="20">&nbsp;</td>
			  <td width="280">
				<span id="entityDesc" name="entityDesc" class="spantext">

			   </td>
		 </tr>
	</table>
	</div>
	</div>

	<div color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:80px; width:969px; height:365px;">
		<div id="AccountMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:967px; height:10px;">
			<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="948" border="0" cellspacing="1" cellpadding="0"  height="23px">

				<thead>
					<tr>
						<td width="220" height="20px" style="border-left-width: 0px;" title='<fmt:message key="tooltip.sortAccountId"/>'><b><fmt:message key="sweep.accountId"/></b></td>
						<td width="150" height="20px" align="left" title='<fmt:message key="tooltip.sortAccountName"/>'><b><fmt:message key="Jobmaintenance.Name"/></b></td>
						<td width="70" height="20px" title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="acctMaintenance.currcode"/></b></td>
						<td width="105" height="20px" title='<fmt:message key="tooltip.sortAcType"/>'><b><fmt:message key="acctMaintenance.accttype"/></b></td>
						<td width="80" height="20px" title='<fmt:message key="tooltip.sortAcLevel"/>'><b><fmt:message key="acctMaintenance.acctlevel"/></b></td>
						<td width="90" height="20px" title='<fmt:message key="tooltip.sortAcClass"/>'><b><fmt:message key="acctMaintenance.acctClass"/></b></td>
						<td width="75" height="20px" align="center" title='<fmt:message key="tooltip.sortByInput"/>'><b><fmt:message key="role.roleInput"/></b></td>
						<td width="75" height="20px" align="center" title='<fmt:message key="tooltip.sortByMatching"/>'><b><fmt:message key="role.roleMatching"/></b></td>
						<td width="74" height="20px" align="center" title='<fmt:message key="tooltip.sorBySweeping"/>'><b><fmt:message key="role.roleSweeping"/></b></td>

					</tr>
				</thead>
			</table>
		</div>

		<div id="ddscrolltable"
			 style="position:absolute; left:0px; top:0px; width:965px; height:360px;overflow-x:hidden;">
			<div id="AccountMaintenance"
				 style="position:absolute;z-index:99;left:0px; top:22px; width:948px; height:10px;">
				<table class="sort-table" id="AccountAccessDetails" width="950" border="0" cellspacing="1"
					   cellpadding="0" height="338">
					<tfoot>
					<tr>
						<td colspan="9"></td>
					</tr>
					</tfoot>

					<tbody>
					<c:set var="count" value="0"/>
					<c:forEach var="item" items="${requestScope.AccountAccessDetails}">
						<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
							<td width="220" align="left">${item.accountId}&nbsp;</td>
							<td width="150">${item.accountName}&nbsp;</td>
							<td width="70">${item.accountCcy}&nbsp;</td>
							<td width="105" align="left">${item.accountType}&nbsp;</td>
							<td width="80" align="left">${item.accountLevel}&nbsp;</td>
							<td width="90" align="left">${item.accountClass}&nbsp;</td>

							<c:set var="count" value="${count + 1}"/>
							<td width="75" height="20" align="center">
								<c:if test="${'Y' == item.allowManualInput}">
									<input type="checkbox" name="inputcheck#${item.accountId}"
										   onclick="checkall('inputcheck'); selectedRowElement(event);"
										   title="${tooltip.selectInput}" checked>
								</c:if>
								<c:if test="${'Y' != item.allowManualInput}">
									<input type="checkbox" name="inputcheck#${item.accountId}"
										   onclick="checkall('inputcheck'); selectedRowElement(event);"
										   title="${tooltip.selectInput}">
								</c:if>
								&nbsp;
							</td>

							<td width="75" height="20" align="center">
								<c:if test="${'Y' == item.allowMatching}">
									<input type="checkbox" name="matchingcheck#${item.accountId}"
										   onclick="checkall('matchingcheck'); selectedRowElement(event);" checked
										   title="${tooltip.selectMatching}">
								</c:if>
								<c:if test="${'Y' != item.allowMatching}">
									<input type="checkbox" name="matchingcheck#${item.accountId}"
										   onclick="checkall('matchingcheck'); selectedRowElement(event);"
										   title="${tooltip.selectMatching}">
								</c:if>
								&nbsp;
							</td>

							<td width="74" height="20" align="center">
								<c:if test="${'Y' == item.allowSweeping}">
									<input type="checkbox" name="sweepingcheck#${item.accountId}"
										   onclick="checkall('sweepingcheck'); selectedRowElement(event);" checked
										   title="${tooltip.selectSweeping}">
								</c:if>
								<c:if test="${'Y' != item.allowSweeping}">
									<input type="checkbox" name="sweepingcheck#${item.accountId}"
										   onclick="checkall('sweepingcheck'); selectedRowElement(event);"
										   title="${tooltip.selectSweeping}">
								</c:if>
								&nbsp;
							</td>


						</tr>
					</c:forEach>
					</tbody>


				</table>

			</div>

		</div>
	</div>

	<div bgcolor="#E8F3FE" style="position:absolute;  left:20px; top:425px; width:967px; height:25px;">
		<div bgcolor="#E8F3FE" id="AccountMaintenance3" style="position:absolute;z-index:99;left:1px; top:22px; width:965px; height:10px;" >
			<table id="AccountAccessDetails1" width="945" border="0" cellspacing="1" cellpadding="0"  height="20" >
				<tr >
						<td width="120" height="20px"></td>
						<td width="150" height="20px" ></td>
						<td width="70" height="20px" ></td>
						<td width="05" height="20px"></td>
						<td width="75" height="20px" ></td>
						<td width="290" height="20px" align="center"><b><fmt:message key="role.applytoalldisacct"/></b></td>
						<td width="75"  height="20px" align="center"><input type="checkbox" name="inputcheckall"  style="margin-left: 5px;"  onclick ="selectedallInput(this,'inputcheck');" title='<fmt:message key="tooltip.selectAllInput"/>'>&nbsp;</td>
						<td width="74" height="20px" align="center"><input type="checkbox" name="matchingcheckall"  onclick ="selectedallInput(this,'matchingcheck');" title='<fmt:message key="tooltip.selectAllMatching"/>'> </td>
						<td width="73" height="20px" align="center"><input type="checkbox" name="sweepingcheckall" style="margin-left: 5px;"  onclick ="selectedallInput(this,'sweepingcheck');" title='<fmt:message key="tooltip.selectAllSweeping"/>'></td>

					</tr>

			</table>
		</div>
	</div>
<div id="acctAccess" style=" margin-top: -2px; position: absolute; left: 900; top: 488; width: 70px; height: 39px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right">
				<a tabindex="5" href=# onclick="helpWin()" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
					<img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
				</a>
			</td>
			<td align="Right">
			<a onclick="printPage();" tabindex="19" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
	</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:478; width:965; height:39px; visibility:visible;">
  <div id="acctAccess1" style="position:absolute; left:6; top:4; width:154px; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

			<c:if test="${'view' == requestScope.methodName}">

				<td id="okbutton">
				</td>

				<td title='<fmt:message key="tooltip.cancel"/>' width="70px" id="cancelbutton">
					<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>
				</td>

</c:if>
			<c:if test="${'view' != requestScope.methodName}">

				<td id="okbutton">
				</td>

				<td title='<fmt:message key="tooltip.close"/>' width="70px" id="closebutton">
					<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitCancel();"><fmt:message key="button.close"/></a>
				</td>

</c:if>

		</tr>
		</table>
	</div>


 <div id="LocationAccess" style="position:absolute; left:6; top:3; width:154px; height:15px; visibility:hidden;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<c:if test="${'view' == requestScope.methodName}">

			<td id="okenablebutton" width="70px">
				<a  title='<fmt:message key="tooltip.ok"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitdata();"><fmt:message key="button.ok"/></a>
			</td>
			<td id="okdisablebutton">
			</td>

</c:if>
			<c:if test="${'view' != requestScope.methodName}">

			<td id="okenablebutton" width="70px">

				<a  title='<fmt:message key="tooltip.SaveChanges"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitdata();"><fmt:message key="button.save"/></a>

			</td>
			<td id="okdisablebutton">
			</td>




</c:if>

		</tr>
	</table>
</div>


</div>

	<blockquote>&nbsp;</blockquote>
			<p>&nbsp;</p>

	<script type="text/javascript">
	</script>
	</form>
	<iframe name="reportFrame" onload="enableClose()" width="0%" height="0%" src="#"  />
	</body>
	</html>