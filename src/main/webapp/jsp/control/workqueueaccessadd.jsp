<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>

<c:if test="${'save' == requestScope.methodName}">

	<title><fmt:message key="workqueueaccess.addScreen"/></title>

</c:if>

<c:if test="${'update' == requestScope.methodName}">

	<title><fmt:message key="workqueueaccess.changeScreen"/></title>

</c:if>

<c:if test="${'view' == requestScope.methodName}">

	<title><fmt:message key="workqueueaccess.viewScreen"/></title>

</c:if>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="displayDetails";
window.opener.document.forms[0].submit();
self.close();

</c:if>

function submitForm(methodName){
if(methodName == 'displayCurrencyList'){
		document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
 else
 if(validateForm(document.forms[0]) ){
	if(checkQueueStatus())
	{
		enableFields();
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}else
	{
		alert('<fmt:message key="workqueueaccess.alert.access"/>');
	}
 }
}
function enableFields(){
	document.forms[0].elements["workQAccess.id.entityId"].disabled = "";
	document.forms[0].elements["workQAccess.id.currencyCode"].disabled = "";
}

function bodyOnLoad()
{
	//var entitydropBox = new SwSelectBox(document.forms[0].elements["workQAccess.id.entityId"],document.getElementById("entityDesc"));
	<c:if test="${'save' == requestScope.methodName}">

		var currencydropBox = new SwSelectBox(document.forms[0].elements["workQAccess.id.currencyCode"],document.getElementById("currencyDesc"));

</c:if>

	<c:if test="${'save' != requestScope.methodName}">

	document.getElementById("currencyName").innerText = '${selectedCurrencyName}';

</c:if>
	document.getElementById("entityName").innerText = '${entityName}';

	<c:if test="${'yes' == requestScope.queueAccessExiting}">

		alert('<fmt:message key="workqueueaccess.recordPresent"/>');

</c:if>

}

function validateForm(objForm){
  var elementsRef = new Array(2);

  elementsRef[0] = objForm.elements["workQAccess.id.entityId"];
  elementsRef[1] = objForm.elements["workQAccess.id.currencyCode"];
  return validate(elementsRef);
}

function checkQueueStatus()
{
		    var checkBoxArray = new Array(18);
			checkBoxArray[0]= document.forms[0].elements['workQAccess.offerMatchQualA'];
			checkBoxArray[1]= document.forms[0].elements['workQAccess.offerMatchQualB'];
			checkBoxArray[2]= document.forms[0].elements['workQAccess.offerMatchQualC'];
			checkBoxArray[3]= document.forms[0].elements['workQAccess.offerMatchQualD'];
			checkBoxArray[4]= document.forms[0].elements['workQAccess.offerMatchQualE'];

			checkBoxArray[5]= document.forms[0].elements['workQAccess.suspMatchQualA'];
			checkBoxArray[6]= document.forms[0].elements['workQAccess.suspMatchQualB'];
			checkBoxArray[7]= document.forms[0].elements['workQAccess.suspMatchQualC'];
			checkBoxArray[8]= document.forms[0].elements['workQAccess.suspMatchQualD'];
			checkBoxArray[9]= document.forms[0].elements['workQAccess.suspMatchQualE'];
			checkBoxArray[10]= document.forms[0].elements['workQAccess.suspMatchQualZ'];

			checkBoxArray[11]= document.forms[0].elements['workQAccess.confirmMatchQualA'];
			checkBoxArray[12]= document.forms[0].elements['workQAccess.confirmMatchQualB'];
			checkBoxArray[13]= document.forms[0].elements['workQAccess.confirmMatchQualC'];
			checkBoxArray[14]= document.forms[0].elements['workQAccess.confirmMatchQualD'];
			checkBoxArray[15]= document.forms[0].elements['workQAccess.confirmMatchQualE'];
			checkBoxArray[16]= document.forms[0].elements['workQAccess.confirmMatchQualZ'];

			checkBoxArray[17]= document.forms[0].elements['workQAccess.outstanding'];

			for (var i = 0; i <= 17; i++) {
				if(checkBoxArray[i].checked)
				return true;
			 }
	return false;

}
</SCRIPT>
</head>
<form action="workQAccess.do" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad(); ShowErrMsgWindow('${actionError}');" onunload="call()">

<div id="WorkQueueAccessAdd" style="position:absolute; left:20px; top:20px; width:500px; height:59px; border:2px outset;" color="#7E97AF">
	<div id="WorkQueueAccessAdd" style="position:absolute;z-index:99;left:8px; top:4px; width:430px; height:20px;">
		<table width="488px" border="0" cellpadding="0" cellspacing="0" height="48px">
			<tr height="23px">
			  <td width="40px"><b><fmt:message key="role.workQueueAccess.entity1"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				<c:if test="${'save' == requestScope.methodName}">

				   <input type="text"  name="workQAccess.id.entityId" value="${workQAccess.id.entityId}"  cssClass="htmlTextAlpha"  style="width:120px;" disabled="true" /></td>

</c:if>
				<c:if test="${'update' == requestScope.methodName}">

				  <input type="text"  name="workQAccess.id.entityId" value="${workQAccess.id.entityId}"  cssClass="htmlTextAlpha"  style="width:120px;" disabled="true" /></td>

</c:if>
				<c:if test="${'view' == requestScope.methodName}">

				   <input type="text"  name="workQAccess.id.entityId" value="${workQAccess.id.entityId}"  cssClass="htmlTextAlpha"  style="width:120px;" disabled="true" /></td>

</c:if>
			  </td>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">
					<span id="entityName" name="entityName" class="spantext">
			  </td>
		  </tr>
		  <tr>

          			 <c:if test="${'view' == requestScope.methodName}">

						   <td width="40px"><b><fmt:message key="role.workQueueAccess.currency1"/></b></td>

</c:if>
					 <c:if test="${'save' == requestScope.methodName}">

						   <td width="40px"><b><fmt:message key="role.workQueueAccess.currency1"/></b>*</td>

</c:if>
					 <c:if test="${'update' == requestScope.methodName}">

						   <td width="40px"><b><fmt:message key="role.workQueueAccess.currency1"/></b></td>

</c:if>

			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				<c:if test="${'save' == requestScope.methodName}">

					<select id="workQAccess.id.currencyCode" name="workQAccess.id.currencyCode" class="htmlTextAlpha"
							titleKey="tooltip.selectCurrencyId" ${requestScope.screenFieldsStatus ? 'disabled' : ''}
							style="width:55px" tabindex="2">
						<c:forEach items="${requestScope.currencyMaster}" var="item">
							<option value="${item.value}"
									<c:if test="${workQAccess.id.currencyCode == item.value}">selected</c:if> >
									${item.label}
							</option>
						</c:forEach>
					</select>

</c:if>
				  <c:if test="${'update' == requestScope.methodName}">

				   <input type="text"  name="workQAccess.id.currencyCode" value="${workQAccess.id.currencyCode}"  cssClass="htmlTextAlpha" titleKey="tooltip.selectCurrencyId"  style="width:38px;" disabled="true" />


</c:if>
				  <c:if test="${'view' == requestScope.methodName}">

				   <input type="text"  name="workQAccess.id.currencyCode" value="${workQAccess.id.currencyCode}"  cssClass="htmlTextAlpha" titleKey="tooltip.selectCurrencyId" style="width:38px;" disabled="true" />


</c:if>
			  </td>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">

				<c:if test="${'save' == requestScope.methodName}">

					<span id="currencyDesc" name="currencyDesc" class="spantext">

</c:if>


			<c:if test="${'save' != requestScope.methodName}">

				<span id="currencyName" name="currencyName" class="spantext">

</c:if>


			  </td>
			 </tr>
		</table>
	</div>
</div>
<div id="WorkQueueAccessAdd" style="position:absolute; left:20px; top:83px; width:500px; height:110px; border:2px outset;" color="#7E97AF">
<div id="WorkQueueAccessAddCheckBox" style="position:absolute;z-index:99;left:0px; top:0px; width:496px; height:120px;">
<table id = checkBoxTable  bgcolor="#DEDFE0" width="496px" border="0" cellspacing="1" cellpadding="0">
	<thead>
		<tr height="20px">
			<td class="topbar" title="<fmt:message key="tooltip.workQueueAccess"/>"><b><fmt:message key="role.workQueueAccess.matchStatus"/></b></td>
			<td class="topbar" title='<fmt:message key="tooltip.qualityA"/>'><b><fmt:message key="role.workQueueAccess.qualityA"/></b></td>
			<td class="topbar" title='<fmt:message key="tooltip.qualityB"/>'><b><fmt:message key="role.workQueueAccess.qualityB"/></b></td>
			<td class="topbar" title='<fmt:message key="tooltip.qualityC"/>'><b><fmt:message key="role.workQueueAccess.qualityC"/></b></td>
			<td class="topbar" title='<fmt:message key="tooltip.qualityD"/>'><b><fmt:message key="role.workQueueAccess.qualityD"/></b></td>
			<td class="topbar" title='<fmt:message key="tooltip.qualityE"/>'><b><fmt:message key="role.workQueueAccess.qualityE"/></b></td>
			<td class="topbar" title='<fmt:message key="tooltip.qualityZ"/>'><b><fmt:message key="role.workQueueAccess.qualityZ"/></b></td>
			<td class="topbar">&nbsp;</td>
		</tr>
		<tr height="17px" class="even">
			<td align="left"><b><fmt:message key="role.workQueueAccessAdd.offered"/></b></td>
			  <td align="center"><input type="checkbox" tabindex="3" id ="1" name="workQAccess.offerMatchQualA" value="Y" ${requestScope.workQAccess.offerMatchQualA == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.offerMatchQualA == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
			  <td align="center"><input type="checkbox" tabindex="4" id ="2" name="workQAccess.offerMatchQualB" value="Y" ${requestScope.workQAccess.offerMatchQualB == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.offerMatchQualB == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
			  <td align="center"><input type="checkbox" tabindex="5" id="3" name="workQAccess.offerMatchQualC" value="Y" ${requestScope.workQAccess.offerMatchQualC == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.offerMatchQualC == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
			  <td align="center"><input type="checkbox" tabindex="6" id="4" name="workQAccess.offerMatchQualD" value="Y" ${requestScope.workQAccess.offerMatchQualD == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.offerMatchQualD == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
			  <td align="center"><input type="checkbox" tabindex="7" id="5" name="workQAccess.offerMatchQualE" value="Y" ${requestScope.workQAccess.offerMatchQualE == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.offerMatchQualE == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
			  <td align="center">&nbsp;</td>
			  <td>&nbsp;</td>
		</tr>
		<tr height="17px" class="odd">
		  <td ><b><fmt:message key="role.workQueueAccessAdd.suspended"/></b></td>
          <td align="center"><input type="checkbox" tabindex="8" name="workQAccess.suspMatchQualA" value="Y" ${requestScope.workQAccess.suspMatchQualA == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.suspMatchQualA == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td align="center"><input type="checkbox" tabindex="9" name="workQAccess.suspMatchQualB" value="Y" ${requestScope.workQAccess.suspMatchQualB == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.suspMatchQualB == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
		  <td align="center"><input type="checkbox" tabindex="10" name="workQAccess.suspMatchQualC" value="Y" ${requestScope.workQAccess.suspMatchQualC == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.suspMatchQualC == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td align="center"><input type="checkbox" tabindex="11" name="workQAccess.suspMatchQualD" value="Y" ${requestScope.workQAccess.suspMatchQualD == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.suspMatchQualD == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
		  <td align="center"><input type="checkbox" tabindex="12" name="workQAccess.suspMatchQualE" value="Y" ${requestScope.workQAccess.suspMatchQualE == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.suspMatchQualE == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td align="center"><input type="checkbox" tabindex="13" name="workQAccess.suspMatchQualZ" value="Y" ${requestScope.workQAccess.suspMatchQualZ == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.suspMatchQualZ == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td >&nbsp;</td>
	   </tr>

		<tr height="17px" class="even">
		  <td ><b><fmt:message key="role.workQueueAccessAdd.confirmed"/></b></td>
          <td align="center"><input type="checkbox" tabindex="14" name="workQAccess.confirmMatchQualA" value="Y" ${requestScope.workQAccess.confirmMatchQualA == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.confirmMatchQualA == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td align="center"><input type="checkbox" tabindex="15" name="workQAccess.confirmMatchQualB" value="Y" ${requestScope.workQAccess.confirmMatchQualB == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.confirmMatchQualB == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
		  <td align="center"><input type="checkbox" tabindex="16" name="workQAccess.confirmMatchQualC" value="Y" ${requestScope.workQAccess.confirmMatchQualC == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.confirmMatchQualC == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td align="center"><input type="checkbox" tabindex="17" name="workQAccess.confirmMatchQualD" value="Y" ${requestScope.workQAccess.confirmMatchQualD == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.confirmMatchQualD == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
		  <td align="center"><input type="checkbox" tabindex="18" name="workQAccess.confirmMatchQualE" value="Y" ${requestScope.workQAccess.confirmMatchQualE == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.confirmMatchQualE == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td align="center"><input type="checkbox" tabindex="19" name="workQAccess.confirmMatchQualZ" value="Y" ${requestScope.workQAccess.confirmMatchQualZ == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.confirmMatchQualZ == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} /></td>
          <td >&nbsp;</td>
	   </tr>
		<tr height="17px" class="odd">
		  <td ><b><fmt:message key="role.workQueueAccessAdd.outstanding"/></b></td>
          <td align="center">&nbsp;</td>
          <td align="center">&nbsp;</td>
		   <td align="center">&nbsp;</td>
          <td align="center">&nbsp;</td>
		  <td align="center">&nbsp;</td>
		  <td align="center">&nbsp;</td>
          <td align="center">
			<input type="checkbox" tabindex="20" name="workQAccess.outstanding" value="Y" ${requestScope.workQAccess.outstanding == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.workQAccess.outstanding == "Y"}' ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
		</tr>
	</thead>
</table>
</div>
</div>
<div id="WorkQueueAccessAdd" style="position:absolute; left:1px; top:206; width:500px; height:15px; visibility:visible;">
	<table width="505px" border="0" cellspacing="0" cellpadding="0" height="20px">
		<tr>
			<td align="right" id="Print">
				<a tabindex="24" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:197px; width:500px; height:39px; visibility:visible;">
<div id="WorkQueueAccessAdd" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
   <c:if test="${'view' != requestScope.viewButtonStatus}">

	<tr>
	 <!-- Save Button  -->
		  <td title='<fmt:message key="tooltip.ok"/>'>
		  <a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" tabindex="21" onClick="javascript:submitForm('${methodName}');"><fmt:message key="button.ok"/></a>
		  </td>

	 <!-- Cancel Button  -->
		  <td title='<fmt:message key="tooltip.cancel"/>'>
			<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" tabindex="22" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a>
		  </td>
		</tr>


</c:if>
	 <c:if test="${'view' == requestScope.viewButtonStatus}">

	 <tr>
	 <!-- View Button  -->
	<td id="closebutton" title='<fmt:message key="tooltip.close"/>'>
		<a tabindex="23" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.close"/></a></td>
		</tr>

</c:if>

	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>

</form>
</body>
</html>