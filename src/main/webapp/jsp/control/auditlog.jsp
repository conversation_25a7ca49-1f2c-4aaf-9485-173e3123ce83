<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - auditlog.jsp
  -
  - The main purpose of this jsp file is to load the Movement Audit Log && User Audit Log screen.<br>
  -
  - Modified By: Marshal .I
  - Date: 06-September-2011
  -->
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@page import="org.swallow.control.model.AuditLog"%>
<%
    //variable declaration
    String methodName = "";
    String movId = "";
    //get the fromMenu from request attribute
    if (request.getAttribute("methodName") != null) {
        methodName = request.getAttribute("methodName").toString();
    }
    if (request.getAttribute("selectedMovementId") != null) {
        movId = request.getAttribute("selectedMovementId").toString();
    }

%>
<html>
<head>


<title>
<c:if test="${'movementLog' == requestScope.methodName}">

	<fmt:message key="movement.screen"/>

</c:if>
<c:if test="${'movementLog' != requestScope.methodName}">

	<fmt:message key="user.screen"/>

</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">



<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">

</script>
	<link rel="stylesheet"	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
	<script type="text/javascript">
		var appName = "<%=SwtUtil.appName%>";
		var baseURL = new String('<%=request.getRequestURL()%>');
		var screenRoute = "AuditLog";
        var methodName = "<%= methodName %>";
        var movId = "<%= movId %>";
        var archiveId= "${archiveId}";
        var entityId='${requestScope.entityId}';

        function viewDetails(methodName, selectedReferenceId, selectedUserId, selectedDate, selectedTime,
                             selectedReference, selectedAction, archiveId, logList) {
            var param = '/' + appName + '/auditlog.do?method=' + methodName;
            param += '&selectedReferenceId=' + selectedReferenceId;
            param += '&selectedUserId=' + selectedUserId;
            param += '&selectedDate=' + selectedDate;
            param += '&selectedTime=' + selectedTime;
            param += '&selectedReference=' + selectedReference;
            param += '&selectedAction=' + selectedAction;

            param += '&archiveId=' + archiveId;
            param += '&logList=' + logList;

            var mainWindow = openWindow(param, 'userLogView',
                'left=50,top=190,width=843,height=368,toolbar=0, resizable=yes scrollbars=yes', 'true');
            return false;
        }
     
        function help(){
		
            openWindow(buildPrintURL('print','User Log'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	}

	</script>
</head>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>

</html>