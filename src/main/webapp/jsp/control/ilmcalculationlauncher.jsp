
<%@page import="org.swallow.util.SequenceFactory"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>


<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<script language="JAVASCRIPT">

	var entityId = '${entityId}';
	var currencyCode = '${currencyCode}';
	var selectedStartDate  = '${selectedStartDate}';
	var selectedEndDate  = '${selectedEndDate}';
	var processOption  = '${processOption}';
	var singleOrRange  = '${singleOrRange}';
	var sequenceNumber;
	var canceled=false;
	var isSingleDate = true;
	var allProcess = true;
	var currPoints = 0;
	var timePeriod=700;
	var SUCCESS = "SUCCESS";
	var RUNNING = "RUNNING";
	var ccyEntityAccess = "${requestScope.ccyEntityAccess}";
	var ccyIsEmpty = "${requestScope.ccyIsEmpty}";


	var screenTitle = "";
	screenTitle = getMessage("label.ilmcalculation.title.window", null);
	document.title = screenTitle;
	var screenRoute = "ilmCurrencyCalculation";
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/' + appName + '/');
	requestURL = requestURL.substring(0, idy + 1);
	var menuAccessId = '${requestScope.menuAccessId}';

	// This method is called when onload
	window.onload = function () {
		setTitleSuffix(document.forms[0]);
		setParentChildsFocus();
	};


	window.onunload = call;

	var dateFormat = '${sessionScope.CDM.dateFormat}';
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';


	/**
	 * getSequenceNumber
	 * This function get the secuence number
	 * @return sequenceNumber
	 */
	function getSequenceNumber() {
		sequenceNumber = "<%=SequenceFactory.getSequenceFromDb(SwtConstants.ILM_PROCESS_DRIVER_SEQUENCE_NAME)%>";
		return sequenceNumber;
	}

	function checkProcessingResult(processStatus, seqNumber){
		Main.setScreenStatusView(processStatus, seqNumber);
	}



	function stringToDate(_date,_format,_delimiter){
		var formatLowerCase=_format.toLowerCase();
		var formatItems=formatLowerCase.split(_delimiter);
		var dateItems=_date.split(_delimiter);
		var monthIndex=formatItems.indexOf("mm");
		var dayIndex=formatItems.indexOf("dd");
		var yearIndex=formatItems.indexOf("yyyy");
		var month=parseInt(dateItems[monthIndex]);
		month-=1;
		var formatedDate = new Date(dateItems[yearIndex],month,dateItems[dayIndex]);
		return formatedDate;
	}


	function isPotentialCalcul(selectedCurrency,isSingleDate,startDate, endDate ){
		var rangePeriod = 0;

		if(isSingleDate == false){
			rangePeriod = daysBetweenDates(stringToDate(startDate,dateFormatValue,"/"), stringToDate(endDate,dateFormatValue,"/"));
		}

		return (rangePeriod>0 || selectedCurrency =="All");

	}


	function launchCalculation(entityId,currencyCode, selectedStartDate, selectedEndDate,processOption,isSingleDate,singleOrRange, validateReportDates,results) {
		if (validateReportDates) {
			if (!results[2]) {
				alert('<fmt:message key="warn.outsideRange"/>');
				Main.resetOffendingDate(results);
				return;
			}
			if (isPotentialCalcul(currencyCode,isSingleDate,selectedStartDate, selectedEndDate )) {
				var confirmCalculation = window
						.confirm("<fmt:message key="warn.calculationstatus"/>");
				if (confirmCalculation == false) {
					return;
				}
			}
			sequenceNumber = "<%=SequenceFactory.getSequenceFromDb(SwtConstants.ILM_PROCESS_DRIVER_SEQUENCE_NAME)%>";
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ;
			requestURL = requestURL + appName+"/intraDayLiquidity.do?method=runManualCCYProcess";
			requestURL = requestURL +"&entityId="+entityId;
			requestURL = requestURL +"&currencyCode="+currencyCode;
			requestURL = requestURL +"&processOption="+processOption;
			requestURL = requestURL +"&selectedStartDate="+selectedStartDate;
			requestURL = requestURL +"&singleOrRange="+singleOrRange;
			requestURL = requestURL +"&selectedEndDate="+selectedEndDate;
			requestURL = requestURL +"&sequenceNumber="+sequenceNumber;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false);
			oXMLHTTP.send();
			var result = String(oXMLHTTP.responseText);
			var resultsArray = result.split('#');
			var executeResult = resultsArray[0];

			if(executeResult == "C"){//When the current calculation is actually in progress
				Main.setScreenStatusView("RUNNING", null);
				var entityCcyInterference = resultsArray[1]+"/"+resultsArray[2];
				var dateInterference = resultsArray[3];
				alert('<fmt:message key="label.ilmccyprocess.calculationalreadyinprogress"/>'.replace("{0}", entityCcyInterference).replace("{1}", dateInterference));
			}else if( executeResult == "S"){// the processing has been successfully launched
				Main.setScreenStatusView("RUNNING", sequenceNumber);
			}else if( executeResult == "E"){// the processing has been successfully launched
				Main.setScreenStatusView("SUCESS_WITH_ERRORS", sequenceNumber);
			}else  {
				// Error occured when launching the processing, display failure message
				Main.setScreenStatusView("LAUNCH_FAILED", sequenceNumber);
			}

			Main.refreshGrid();
		}
	}

	function cancelCalculation(entityId,currencyCode, selectedStartDate, selectedEndDate,processOption,singleOrRange){
		canceled= true;
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
		requestURL = requestURL + appName+"/intraDayLiquidity.do?method=cancelManualCCYProcess";
		requestURL = requestURL +"&entityId="+entityId;
		requestURL = requestURL +"&currencyCode="+currencyCode;
		requestURL = requestURL +"&processOption="+processOption;
		requestURL = requestURL +"&selectedStartDate="+selectedStartDate;
		requestURL = requestURL +"&singleOrRange="+singleOrRange;
		requestURL = requestURL +"&selectedEndDate="+selectedEndDate;
		requestURL = requestURL +"&sequenceNumber="+sequenceNumber;

		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, true );
		oXMLHTTP.onreadystatechange = function() {
			if (oXMLHTTP.readyState == 4) {
				if (oXMLHTTP.status == 200)
				{
					var result = String(oXMLHTTP.responseText);
					if( result == "S"){
						Main.refreshdetails(null);
						Main.setScreenStatusView("CANCEL", null);
					}

				}
				else
				{
					//request failed
					alert("request failed");
					Main.setScreenStatusView("FAIL", sequenceNumber);
				}
			}
		};
		oXMLHTTP.send();
		Main.refreshGrid();
	}

	function setScreenStatusView(entityId,currencyCode){
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
		requestURL = requestURL + appName+"/intraDayLiquidity.do?method=getUserAccess";
		requestURL = requestURL +"&entityId="+entityId;
		requestURL = requestURL +"&currencyCode="+currencyCode;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false);
		oXMLHTTP.send();
		var result = String(oXMLHTTP.responseText);
		return result;
	}



	function daysBetweenDates( date1, date2 ) {
		var one_day=1000*60*60*24;
		var date1_ms = date1.getTime();
		var date2_ms = date2.getTime();
		var difference_ms = date2_ms - date1_ms;
		return Math.round(difference_ms/one_day);
	}

</script>
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" />
	<input name="method" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="currencyCode" type="hidden" value="">
	<input name="processOption" type="hidden" value="">
	<input name="isRangeOrSingle" type="hidden" value="">
	<input name="selectedStartDate" type="hidden" value="">
	<input name="selectedEndDate" type="hidden" value="">
	<input name="calculationStatus" type="hidden" value="S">
	<input name="menuAccessId" type="hidden" >
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>