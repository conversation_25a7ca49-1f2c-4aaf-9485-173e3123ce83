<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@page import="org.swallow.control.model.AuditLog"%>
<html>

<head>
<title>
<c:if test="${requestScope.viewUserLog == 'viewUserLog'}">

    <fmt:message key="auditLog.viewScreen"/>

</c:if>
<c:if test="${requestScope.viewUserLog != 'viewUserLog'}">
    <fmt:message key="userLog.viewScreen"/>
</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


	<script type="text/javascript" src="js/jquery.exportselect.js"></script>

	<link rel="stylesheet"	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
	<script type="text/javascript">
		var appName = "<%=SwtUtil.appName%>";
		var baseURL = new String('<%=request.getRequestURL()%>');
		var screenRoute = "AuditLogView";
		var selectedReferenceId = "${requestScope.selectedReferenceId}";
		var selectedUserId = "${requestScope.selectedUserId}";
		var selectedDate = "${requestScope.selectedDate}";
		var selectedTime = "${requestScope.selectedTime}";
		var selectedReference = "${requestScope.selectedReference}";
		var selectedAction = "${requestScope.selectedAction}";

		function help(){

			openWindow(buildPrintURL('print','User Log View'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
}
	</script>
</head>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>

</html>
