<?xml version="1.0" encoding="UTF-8"?>
<!--
- The main purpose of this jsp file is to load the Role Based Control List screen.
-
- Author(s): Bala .D
- Date: 08-03-2011
- Updated: JSTL Migration
-->

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<c:set var="recordCount" value="${requestScope.roleBasedControlList.size()}"/>
<rolebasedcontrol>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<singletons>
		<configExistFlag>${requestScope.configExistFlag}</configExistFlag>
		<operation>${requestScope.operation}</operation>
	</singletons>


	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key="label.roleBasedControl.facility"/>"
						draggable="false"
						sort="true"
						type="str"
						dataelement="facilityId"
						width="0"
						sortable = "true"
						visible= "false"
				/>

				<column
						heading="<fmt:message key="label.roleBasedControl.facility"/>"
						draggable="false"
						sort="true"
						type="str"
						headerTooltip="<fmt:message key="roleBasedControl.column.facility.tooltip"/>"
						dataelement="facilityDesc"
						width="350"
						sortable = "true"
				/>

				<column
						heading="<fmt:message key="label.roleBasedControl.ReqAuth"/>"
						draggable="false"
						type="bool"
						headerTooltip="<fmt:message key="roleBasedControl.column.reqAuth.tooltip"/>"
						dataelement="reqAuth"
						width="125"
				/>
				<column
						heading="<fmt:message key="label.roleBasedControl.ReqOthers"/>"
						draggable="false"
						type="bool"
						dataelement="reqOthers"
						headerTooltip="<fmt:message key="roleBasedControl.column.authOther.tooltip"/>"
						width="125"
				/>

			</columns>
		</metadata>

		<rows size="${recordCount}">
			<c:forEach items="${requestScope.roleBasedControlList}" var="rowrecord">
				<row>
					<facilityId aggregate="${rowrecord.facilityId}">${rowrecord.facilityId}</facilityId>
					<facilityDesc aggregate="${rowrecord.facilityDesc}">${rowrecord.facilityDesc}</facilityDesc>
					<reqAuth selected="${rowrecord.reqAuth == 'Y' ? 'true' : 'false'}">${rowrecord.reqAuth == 'Y' ? 'Y' : 'N'}</reqAuth>
					<reqOthers selected="${rowrecord.reqOthers == 'Y' ? 'true' : 'false'}">${rowrecord.reqOthers == 'Y' ? 'Y' : 'N'}</reqOthers>

				</row>
			</c:forEach>
		</rows>

	</grid>
</rolebasedcontrol>