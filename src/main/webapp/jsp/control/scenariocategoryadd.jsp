<!DOCTYPE HTML PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.ScenarioCategory"%>
<%@page import="org.swallow.control.model.Scenario"%>
<html>
<head>
<title>
		<c:if test="${'addCategory'==requestScope.methodName}">

			<fmt:message key="scenarioCategoryAdd.title.window.addScreen"/>

</c:if>
		<c:if test="${'changeCategory'==requestScope.methodName}">

			<fmt:message key="scenarioCategoryAdd.title.window.changeScreen"/>

</c:if>

</title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">



<c:if test="${'yes'==requestScope.parentFormRefresh}">
		window.opener.document.forms[0].method.value="category";
		window.opener.document.forms[0].submit();
		self.close();

</c:if>

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
	mandatoryFieldsArray=["*"];
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad()
	{
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		//Enable || disable save button depending in save button request value
		var saveButton = "<%= request.getAttribute(SwtConstants.SAV_BUT_STS) %>";
		document.getElementById("savebutton").innerHTML = saveButton=='true'?document.getElementById("saveenablebutton").innerHTML:document.getElementById("savedisablebutton").innerHTML;

		if('${methodName}' == 'addCategory')
			document.forms[0].elements["scenarioCategory.displayTab"][0].checked = true;
	}

	/**
	 * Submit the form when clicking on save button
	 */
	function submitForm(methodName){
		var scenario=validateField(document.forms[0].elements['scenarioCategory.id.categoryid'],'scenarioCategory.id.categoryid','alphaNumPatWithoutTilde');
	    enableFields();
	    if (scenario){
	    	if(validateForm(document.forms[0]) ){
		   		document.forms[0].elements['scenarioCategory.systemflag'].value = document.forms[0].elements['scenarioCategory.systemflag']?'Y':'N';
			    document.forms[0].method.value = methodName;
				document.forms[0].submit();
	   		}
		}
	}

	/**
	 *  Enable all fields in the screen that are disabled before submitting
	 */
	function enableFields(){
		document.forms[0].elements["scenarioCategory.systemflag"].disabled = "";
		document.forms[0].elements["scenarioCategory.id.categoryid"].disabled = "";
		document.forms[0].elements["scenarioCategory.description"].disabled = "";
		document.getElementById("displayTab1").disabled = "";
		document.getElementById("displayTab2").disabled = "";
		document.forms[0].elements["scenarioCategory.displayTab"].disabled = "";
		document.forms[0].elements["scenarioCategory.title"].disabled = "";
	}

	/**
	 * validate scenario category && title
	 */
	function validateForm(objForm){
	  var elementsRef = new Array(2);
		  elementsRef[0] = objForm.elements["scenarioCategory.id.categoryid"];
		  elementsRef[1] = objForm.elements["scenarioCategory.title"];
	  return validate(elementsRef);
	}

	/**
	 * This method is used to check the maximum character length
	 *
	 * @param field
	 * @param maxChars
	 * @return none
	 **/
	function maxLengthTextArea (field,maxChars)
	{
		if(field.value.length > maxChars)
			field.value = field.value.substring(0, maxChars);
	}

	/**
	 * Validate the display order, if the user enters a non number value then display an alert && clear the field.
	 **/
	function validateDisplayOrder(){
		var displayOrder=validateField(document.forms[0].elements["scenarioCategory.displayorder"],'scenarioCategory.displayorder','numberPat');

		if(!displayOrder)
			document.forms[0].elements["scenarioCategory.displayorder"].value="";
	}
</SCRIPT>
 </head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
	<form action="scenMaintenance.do" method="post" onsubmit="validate(this);" style="width: 562px;">
		<input name="method" type="hidden" value="display">
		<input name="selectedCategoryId" type="hidden" value="">
		<input name="categorySystemFlag" type="hidden" value="${categorySystemFlag}">
		<input name="oldValue" type="hidden" value= "${oldValue}">
		<div id="scenCategoryAdd" style="position:absolute; left:20px; top:20px; width:640; height:186px; border:2px outset;">
			<div id="scenCategoryAdd" style="position:absolute;left:8px; top:6px; width:600; height:110px;">
				<table width="630" border="0" cellpadding="2" cellspacing="0" height="30">
				    <tr>
				        <td width="108px" ><b><fmt:message key="scenarioCategory.categoryid"/><c:if test="${'addCategory'==requestScope.methodName}">
    *
</c:if></b></td>
						<c:if test="${'changeCategory'==requestScope.methodName}">

							<td style="width: 380px;">
					        	<input type="text" tabindex="2" titleKey="tooltip.enterCategoryId" name="scenarioCategory.id.categoryid" value="${scenarioCategory.id.categoryid}"  disabled="true" class="htmlTextAlpha" style="width: 280px;" maxlength="20" />
					        </td>

</c:if>
				     	<c:if test="${'addCategory'==requestScope.methodName}">

							<td style="width: 380px;">
					        	<input type="text" tabindex="2" titleKey="tooltip.enterCategoryId" name="scenarioCategory.id.categoryid" value="${scenarioCategory.id.categoryid}"  class="htmlTextAlpha" style="width: 280px;" maxlength="20" />
					        </td>

</c:if>
						<td style="width: 80px;">
							<fmt:message key="scenarioCategory.systemflag"/>
				   		</td>
				      	<td>
				      	 	 <input type="checkbox" style="width:13px;margin-left: 9px;" disabled="true" titleKey="tooltip.systemFlag" name="scenarioCategory.systemflag" value="Y" ${requestScope.scenarioCategory.systemflag == 'Y' ? 'checked' : ''}  class="htmlTextAlpha" />
					 	</td>
					</tr>
					</table>
					<table width="630" border="0" cellpadding="2" cellspacing="0" height="75">
					<tr>
				        <td><b><fmt:message key="scenarioCategory.title"/><c:if test="${'Y'!=requestScope.categorySystemFlag}">
    *
</c:if></b></td>
				        <td>
						    <input type="text" tabindex="3" titleKey="tooltip.enterCategoryTitle" name="scenarioCategory.title" value="${scenarioCategory.title}"  <c:if test="${requestScope.categorySystemFlag == 'Y'}">disabled</c:if> class="htmlTextAlpha" style="width: 500px;margin-left: 15px" maxlength="50" />
						</td>
					</tr>

					<tr>
						<td><b><fmt:message key="scenarioCategory.displayTab"/></b></td>
						<td>
						   	<table style="padding-left:15px;">
								<tr>
									<td width="32"><input type="radio"  name="scenarioCategory.displayTab" ${scenarioCategory.displayTab == '1' ? 'checked="checked"' : ''} value="${'1'}" id="displayTab1" <c:if test="${requestScope.categorySystemFlag == 'Y'}">disabled</c:if> style="margin-right:10"   tabindex="13"/></td>

									<td width="115"><label title='<fmt:message key="ilmReport.singleDay" />'
										for="4">${tabName1}</label>&nbsp;</td>
									<td width="32"><input type="radio"  name="scenarioCategory.displayTab" ${scenarioCategory.displayTab == '2' ? 'checked="checked"' : ''} value="${'2'}" id="displayTab2" <c:if test="${requestScope.categorySystemFlag == 'Y'}">disabled</c:if>  tabindex="14"/></td>

									<td width="150">&nbsp<label title='<fmt:message key="ilmReport.dateRange" />'
										for="4">${tabName2}</label>&nbsp;</td>
								</tr>

							</table>
						</td>
					</tr>

				    <tr>
						<td><b><fmt:message key="scenarioCategory.description"/></b></td>
						<td>
							<textarea tabindex="4"  name="scenarioCategory.description" <c:if test="${requestScope.categorySystemFlag == 'Y'}">disabled</c:if>
									  titleKey="tooltip.Description" class="htmlTextAlpha" style="width: 500px;margin-left: 15px;height:60px" onkeydown="maxLengthTextArea(this,250);" onkeyup="maxLengthTextArea(this,250);">${scenarioCategory.description}</textarea>
						</td>
					</tr>
					<tr>
						<td><b><fmt:message key="scenarioCategory.displayorder"/></b></td>
						<c:if test="${'changeCategory'==requestScope.methodName}">

							<td>
								<input type="text" tabindex="5" titleKey="tooltip.enterCategoryDisplayorder" name="scenarioCategory.displayorder" value="${scenarioCategory.displayorder}"   class="htmlTextAlpha" style="width: 50px;margin-left: 15px" onchange="validateDisplayOrder();" />
							</td>

</c:if>
						<c:if test="${'addCategory'==requestScope.methodName}">

							<td>
								<input type="text" tabindex="5" titleKey="tooltip.enterCategoryDisplayorder" name="scenarioCategory.displayorder" value="1"  value="1"  class="htmlTextAlpha" style="width: 50px;margin-left: 15px" onchange="validateDisplayOrder();" />
							</td>

</c:if>
					</tr>
				</table>
			</div>
		</div>
		<div id="scenCategoryAdd" style="position:absolute; left:580; top:222; width:70; height:29px; visibility:visible;">
			<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="10" href=# onclick="javascript:openWindow(buildPrintURL('print','Amend Scenario Category'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
					</td>
					<td align="right" id="Print">&nbsp;
						<a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
					</td>
				</tr>
			</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:212; width:640px; height:39px; visibility:visible;">
			<div id="ScenarioCategory" style="position:absolute; left:2; top:4; width:200px; height:15px; visibility:visible;">
				<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="savebutton"></td>
						<td id="cancelbutton" width="70px">
							<a title='<fmt:message key="tooltip.cancel"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>
						</td>
					</tr>
				</table>
			</div>
			<div style="position:absolute; left:6; top:4; width:554px; height:15px; visibility:hidden;display: none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
					<tr>
						<td id="saveenablebutton" width="70"><a tabindex="8"
											title='<fmt:message key="tooltip.save"/>'
											onMouseOut="collapsebutton(this)"
											onMouseOver="highlightbutton(this)"
											onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
											onclick="javascript:submitForm('${methodName == 'addCategory' ? 'saveCategory': 'updateCategory'}');"><fmt:message key="button.save"/></a></td>
						<td id="savedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</form>
</body>
</html>