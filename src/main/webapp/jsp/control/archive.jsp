
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<title><fmt:message key="title.archive"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<c:if test='${"yes" == requestScope.parentFormRefresh}' >
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</c:if>


var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ; ;

function onFilterandSort()
{
	 
	updateColors();
	disableAllButtons();
}


function disableAllButtons()
{
	 
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function enableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML; 
}


function submitFormDelete(methodName){	               
		
	             document.forms[0].method.value = methodName;   
				 var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
				 if (yourstate== true){ 			      
					document.forms[0].submit();	
				 }
    
	 }
	
/**
* This function is used load the screen
*/
function bodyOnLoad(){   
//Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen

	xl = new XLSheet("archiveColl","table_2",["String","String","String","String","String"],"22222");
	document.getElementById("addenablebutton").innerHTML;
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("archiveColl");
	//disable the change button
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	//disable the delete button
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	//enabling add button based up on the menu access
	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
}

/**
* This function is used react after selecting rows in the datagrid
* @param rowElement
* @param isSelected
*/
function onSelectTableRow(rowElement, isSelected) {	
/*Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen*/
	//deciding button status after selcting tab rows
	if(menuEntityCurrGrpAccess == "0" && isSelected) {
		enableAllButtons();
	} else {
		disableAllButtons();
	}
	console.log("Start:Code Modified for Mantis 12 ",rowElement.getElementsByTagName("input")[0].value)
	document.forms[0].archiveId.value = rowElement.getElementsByTagName("input")[0].value;
	document.forms[0].selectedDefaultDb.value = rowElement.cells[3];
	document.forms[0].module.value = rowElement.cells[1].innerText.trim();
	console.log("All forms:", rowElement.cells[3]);
/*End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen*/
	}

	function buildChangeArchiveURL(methodName){	
		document.forms[0].method.value = methodName;
		 var	param = 'archive.do?method='+methodName;
		  param +='&archiveId='+document.forms[0].archiveId.value;
		 param +='&defaultDb='+document.forms[0].selectedDefaultDb.value;
		return param;
	}	

	function buildAddArchiveURL(methodName){	
		document.forms[0].method.value = methodName;
		 var	param = 'archive.do?method='+methodName;	
		 return param;
	}	

</SCRIPT>
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
	marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');"
	onunload="call()">
<!--Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen-->
<form action="archive.do">
	<input name="method" type="hidden" value="display">
	<input name="archiveId" type="hidden" value="">
	<input name="selectedDefaultDb" type="hidden" value="">
	<input name="menuAccessId" type="hidden">
	<input name="module" type="hidden" value="">
	<div color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10px; top: 10px; width: 1117px; height: 450px;">
	<div id="ArchiveControl"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1095; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1074"
		border="0" cellspacing="1" cellpadding="0" height="20">
		<thead>
			<tr height="20px">
				<td valign="middle"  style="border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortArchiveId"/>' width="145px"><b>
					<fmt:message key="archive.id.archiveId"/></b></td>
				<td valign="middle"  style="border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortModule"/>' width="145px"><b>
					<fmt:message key="archive.module"/></b></td>
				<td valign="middle"
					title='<fmt:message key="tooltip.sortArchiveName"/>' width="216px"><b>
					<fmt:message key="archive.archiveName"/></b></td>
				<td valign="middle"
					title='<fmt:message key="tooltip.sortDatabaseLink"/>' width="300px"><b>
					<fmt:message key="archive.databaseLink"/></b></td>
				<td valign="middle"
					title='<fmt:message key="tooltip.sortCurrentArchive"/>'
					width="140px"><b><fmt:message key="archive.defaultDb"/></b></td>
				<td valign="middle"
					title='<fmt:message key="tooltip.sortTypeArchive"/>'
					width="141px"><b><fmt:message key="archive.type"/></b></td>
			</tr>
		</thead>
	</table>
	</div>

	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 1113px; height: 447px;">
	<div id="ArchiveMaintenance"
		style="position: absolute; left: 0px; top: 21px; width: 1074px; height: 10px;">
<table class="sort-table" id="archiveColl" width="1076" border="0"
       cellspacing="1" cellpadding="0" height="425">
    <tbody>
        <c:set var="count" value="0" />
        <c:forEach var="archive" items="${requestScope.archiveColl}">
            <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
                <c:set var="count" value="${count + 1}" />

                <td width="145px">
                    <input type="hidden" id="archive.id.archiveId" name="archive.id.archiveId" value="${archive.id.archiveId}" />
                    ${archive.id.archiveId}&nbsp;
                </td>
                <td width="145px">
                    <input type="hidden" name="archive.moduleId" value="${archive.moduleId}" />
                    ${archive.moduleId}&nbsp;
                </td>
                <td width="216px" title="${archive.archiveName}">
                    <input type="hidden" name="archive.archiveName" value="${archive.archiveName}" />
                    ${archive.archiveName}&nbsp;
                </td>
                <td width="300px" title="${archive.db_link}">
                    <input type="hidden" name="archive.db_link" value="${archive.db_link}" />
                    ${archive.db_link}&nbsp;
                </td>
                <td width="140px">
                    <input type="hidden" name="archive.defaultDb" value="${archive.defaultDb}" />
                    ${archive.defaultDb}&nbsp;
                </td>
                <td width="141px">
                    <input type="hidden" name="archive.archiveType" value="${archive.archiveType}" />
                    ${archive.archiveType}&nbsp;
                </td>
            </tr>
        </c:forEach>
    </tbody>
    <tfoot>
        <tr>
            <td colspan="4"></td>
        </tr>
    </tfoot>
</table>
	</div>
	</div>
	</div>
	<div id="ArchiveMaintenance"
		style="position: absolute; left: 1060; top: 480; width: 70; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td align="Right"><a
				title='<fmt:message key="tooltip.helpScreen"/>' tabindex="6" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Archive Setup'),'sectionprintdwindow','left=50,top=180,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"></a></td>


			<td align="right" id="Print"><a
				title='<fmt:message key="tooltip.printScreen"/>' tabindex="6"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"> </a></td>
		</tr>
	</table>
	</div>
	
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 472; width: 1117; height: 39px; visibility: visible;">
	
	<div id="ArchiveMaintenance"
		style="position: absolute; left: 6; top: 4; width: 450; height: 10px; visibility: visible;">
	<table width="280" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="addbutton"></td>
			<td id="changebutton"></td>
			<td id="deletebutton"></td>
			<td title='<fmt:message key="tooltip.close"/>' id="closebutton">
			<a tabindex="4" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');">
			<fmt:message key="button.close"/> </a></td>
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 450; height: 10px; visibility: hidden;">
	<table width="280" border="0" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>

			<td id="addenablebutton">
			<a tabindex="1" title='<fmt:message key="tooltip.addArchive"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildAddArchiveURL('add'),'sectionaddwindow','left=50,top=190,width=440,height=260,toolbar=0','true')">
			<fmt:message key="button.add"/> </a> 
			</td>
			<td id="adddisablebutton">
			<a class="disabled" disabled="disabled"> <fmt:message key="button.add"/> </a> 
			</td>


			<td id="changeenablebutton">
			<a tabindex="2"
				title='<fmt:message key="tooltip.changeSelectedArchive"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildChangeArchiveURL('change'),'sectionchangewindow','left=50,top=190,width=440,height=260,toolbar=0,resizable=yes, scrollbars=no','true')">
			<fmt:message key="button.change"/> </a> 

			</td>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled"> <fmt:message key="button.change"/> </a></td>

			<td tabindex="3" id="deleteenablebutton"><a
				title='<fmt:message key="tooltip.deleteSelectedArchive"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitFormDelete('delete')"> <fmt:message key="button.delete"/> </a></td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.delete"/></a></td>

		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>


	<script type="text/javascript">

<!--End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive setup: Remove redundant fields from Archive setup screen-->
</script>
</form>
</body>
</html>

