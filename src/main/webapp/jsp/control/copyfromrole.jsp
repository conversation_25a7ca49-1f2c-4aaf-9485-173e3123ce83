<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<html>
<head>
<title><fmt:message key="rolemaintenance.copyFromScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var roleIdSelected = '${requestScope.roleIdSelected}';
var screenTitle = '${requestScope.screenTitle}';
//This variable stores the roleId that user has filled in add role popup before clicking copy from button
var newRoleId = '${requestScope.newRoleId}';

function submitForm(methodName){
	window.opener.document.forms[0].copiedRoleId.value=document.forms[0].elements['role.roleId'].value;
	window.opener.document.forms[0].method.value = methodName;
	window.opener.document.forms[0].roleIdSelected.value = roleIdSelected;
	window.opener.document.forms[0].screenTitle.value = screenTitle;
	window.opener.document.forms[0].newRoleId.value = newRoleId;
	window.opener.document.forms[0].clearTempRecords.value = "false";
	self.close();
	window.opener.document.forms[0].submit();
}

function bodyOnLoad()
{
 	var roledropBox = new SwSelectBox(document.forms[0].elements["role.roleId"],document.getElementById("roleName"));

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
}


</SCRIPT>
</head>

<form action="role.do" method="post">

<input name="method" type="hidden" value="copy">
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
<div id="role" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:498px; height:40px;">
	<div id="role" style="position:absolute;z-index:99;left:8px; top:4px; width:198px; height:20px;">
		<table width="478px" height="25px" border="0" cellspacing="0" cellpadding="0">
			 <tr>
				  <td width="30px"><b><fmt:message key="role.copyFrom.roleId"/></b>*</td>
				  <td width="28px">&nbsp;</td>
				  <td width="420px">
				  <!--start:Modified the mantis 1549  by sunil on 10-02-2012.The purpose to display the meaningful tooltip -->
					<select id="role.roleId" name="role.roleId" class="htmlTextAlpha"
						  titleKey="tooltip.roleId" style="width:120px" tabindex="1" >
					  <c:forEach items="${requestScope.roleList}" var="entity">
						  <option value="${entity.value}" <c:if test="${role.roleId == entity.value}">selected</c:if> >
								  ${entity.label}
						  </option>
					  </c:forEach>
				  </select>
						<!--End:Modified the mantis 1549  by sunil on 10-02-2012.The purpose to display the meaningful tooltip -->
					&nbsp;&nbsp;&nbsp;
  						<span id="roleName" name="roleName" class="spantext"/>
				  </td>
			 </tr>
		  </table>
	</div>

</div>
<div id="role" style="position:absolute; left:450; top:73px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
					<a  tabindex="5" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Copy Role Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>
		  </td>


			<td align="right" id="Print">
				<a tabindex="4" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:64px; width:498px; height:39px; visibility:visible;">
	<div id="role" style="position:absolute; left:6; top:4; width:207px; height:15px; visibility:visible;">
		<table  width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
			  <td width="70px">
				<a tabindex="2" title='<fmt:message key="tooltip.ok"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('copy')"><fmt:message key="button.ok"/></a>
			  </td>

		 <!-- Cancel Button  -->
			  <td width="70px">
				<a tabindex="3" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();" ><fmt:message key="button.cancel"/></a>
			  </td>
			</tr>
		</table>
	</div>
</div>
</form>
</body>
</html>