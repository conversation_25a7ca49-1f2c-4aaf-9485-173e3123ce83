<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="MaintenanceLog.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="style/displaytag.css" >

<SCRIPT language="JAVASCRIPT">
var dateFlag=true;
mandatoryFieldsArray = ["mainLogFromDate","mainLogToDate"] ;
var totalCount = '${totalCount}';
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';
var lastRefTime = "${requestScope.lastRefTime}";
var dateFormat = '${sessionScope.CDM.dateFormat}';
var filterValues=new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var dateSelected = false;


function disableAllButtons()
{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
function sortDateValidation() {
if(validateForm(document.forms[0]) ){
	if(validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat) && validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
			dateFlag = true;
			return dateFlag;
			} else {
				dateFlag = false;
				return dateFlag;
				}
			}
}

var dateFormat = '${sessionScope.CDM.dateFormat}';

function onDateKeyPress(obj,e){
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'To',dateFormat)){
			submitForm('showDetails');
			}
		else
		{
			return false;
			}
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'To',dateFormat)){
			submitForm('showDetails');
			}
		else
		{
			return false;
			}
		}
}

/*This function validates the To Date fielf when 'Enter || Tab' is been pressed.*/
function onToDateKeyPress(obj,e){
	var event = (window.event|| e);
	dateSelected=false;
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
		if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From','To')){
			submitForm('showDetails');
			} else {
				return false;
				}
			} else {
				return false;
		}
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
			if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From','To')) {
			submitForm('showDetails');
			} else {
				return false;
				}
			} else {
				return false;
			}
		}
}

/*This function validates the From Date fielf when 'Enter || Tab' is been pressed.*/
function onFromDateKeyPress(obj,e){
	dateSelected=false;
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From','To')) {
			submitForm('showDetails');
			} else {
				return false;
				}
			} else {
				return false;
			}
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From','To')) {
			submitForm('showDetails');
			} else {
				return false;
				}
			} else {
				return false;
			}
		}
}

function bodyOnLoad(){
	xl = new XLSheet("maintenanceLogList","table_2", [dateFormat, "String", "String", "IPAddress", "String", "String","String"],"1111111","false",currentFilterValues,sortedValues);

	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("maintenanceLogList");

	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	document.forms[0].totalCount.value = '${requestScope.totalCount}';
	document.getElementById("lastRefTime").innerText = lastRefTime;

	var rows = xl.dataTable.tBody.rows;
	var l = rows.length;
        var maintenanceLogMainSeqNo = getStoredParam('maintenanceLogMainSeqNo');
	for (var i=0; i<l; i++)
	{
				if(rows[i].getElementsByTagName("input")[6].value== maintenanceLogMainSeqNo) {
					highLightTableRow(rows[i]);
					rows[i].className = 'selectrow';
                                        break;
				}
     }
}

function submitForm(methodName){
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	 if(validateForm(document.forms[0]) ){
	   	document.forms[0].method.value = methodName;
	    document.forms[0].submit();
	}
}


function buildTableNameURL(methodName){
	dateSelected = false;
	if(validateDateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)) {
			if(validateDateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)) {
				if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From','To')) {

	var param ='maintenancelog.do?method='+methodName;

	param +='&selectedLogDate=';
	param +=document.forms[0].selectedLogDate.value;
	param+='&selectedUserId=';
	param+=document.forms[0].selectedUserId.value;
	param+='&selectedIpAddress=';
	param+=document.forms[0].selectedIpAddress.value;
	param+='&selectedTableName=';
	param+=document.forms[0].selectedTableName.value;
	param+='&selectedReference=';
	param+=document.forms[0].selectedReference.value;
	param+='&selectedAction=';
	param+=document.forms[0].selectedAction.value;
	window.openWindow(param,'','left=50,top=190,width=1080,height=610,toolbar=0, resizable=yes scrollbars=yes','true');
	} else {
		return false;
		}
		} else {
			return false;
			}
		} else {
			return false;
		}
	}
/*Start:Code modified by Prasenjit Maji for Mantis 1690 on 04/09/2012:View button should be disabled while deselecting a grid record*/

    /**
	 * This function enables/disables buttons based on grid record selected
	 *
	 * @param rowElement
	 * @param isSelected
	 */
function onSelectTableRow(rowElement,isSelected)
	{
	document.forms[0].selectedTableName.value = rowElement.getElementsByTagName("input")[0].value;
	document.forms[0].selectedColumnName.value = rowElement.getElementsByTagName("input")[1].value;
	document.forms[0].selectedDate.value = rowElement.getElementsByTagName("input")[2].value;
	document.forms[0].selectedTime.value = rowElement.getElementsByTagName("input")[3].value;
	var actionColumn = rowElement.getElementsByTagName("input")[4];
	  var rows = xl.dataTable.tBody.rows;
	var l = rows.length;
	if(isSelected){
	 setStoredParam('maintenanceLogMainSeqNo',rowElement.getElementsByTagName("input")[6].value);
	}else
	   setStoredParam('maintenanceLogMainSeqNo','');

    /*Condition for checking the Action column into the grid */
	if((isSelected) && ((actionColumn.value=="Changed" || actionColumn.value=="Added") && (actionColumn.value!="Deleted")))
	{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	}
	else
	{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	}
	document.forms[0].selectedMainSeq.value = rowElement.getElementsByTagName("input")[5].value;
	document.forms[0].selectedLogDate.value = rowElement.getElementsByTagName("input")[6].value;
	document.forms[0].selectedUserId.value = rowElement.getElementsByTagName("input")[7].value;
	document.forms[0].selectedIpAddress.value = rowElement.getElementsByTagName("input")[8].value;
	document.forms[0].selectedReference.value = rowElement.getElementsByTagName("input")[9].value;
	document.forms[0].selectedAction.value = rowElement.getElementsByTagName("input")[10].value;
}
/*End:Code modified by Prasenjit Maji for Mantis 1690 on 04/09/2012:View button should be disabled while deselecting a grid record*/

function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["maintenanceLog.fromDateAsString"];
  elementsRef[1] = objForm.elements["maintenanceLog.toDateAsString"];
  if(validate(elementsRef))
  {
    if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From Date','To Date'))
     {
      return true;
     }
   }

   return false;
}

function clickLink(element) {
	dateSelected = false;
	if (document.forms[0].elements['maintenanceLog.fromDateAsString'].value != "" && document.forms[0].elements['maintenanceLog.toDateAsString'].value != "") {
	if(validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
	if(validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
	element.href +='&method=next';
	element.href +='&selectedSort=';
	element.href +=currentSort;
	element.href +='&selectedFilter=';
	element.href +=currentFilter;
	element.href +='&fromDate='
	element.href +='${fromDate}';
	element.href +='&toDate='
	element.href +='${toDate}';
	} else {
		return false;
	}
	} else {
		return false;
	}
	} else {
		alert('<fmt:message key="alert.enterValidDate"/>');
	}
}
function storeToDate(){
document.forms[0].elements['preMaintenanceToDateAsString'].value = document.forms[0].elements['maintenanceLog.toDateAsString'].value;
}

function onToDateChange(){
	if(dateSelected){

	var preToDate = document.forms[0].elements['preMaintenanceToDateAsString'].value;
	var from_date=document.forms[0].elements['maintenanceLog.fromDateAsString'].value;
	var to_date=document.forms[0].elements['maintenanceLog.toDateAsString'].value;
	if(from_date != "" && to_date != "") {
	if(validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
	cal2.hideCalendar()
	if(validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	if(compare_date == 1)
		{
			document.forms[0].elements['maintenanceLog.toDateAsString'].value=preToDate;
			cal2.hideCalendar()
			alert('<fmt:message key="alert.dateShouldBeGreater"/>');

		}else{
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

			document.forms[0].method.value = 'showDetails';
		    document.forms[0].submit();
		}
		dateSelected = false;
		return true;
	} else {
			dateSelected = false;
			return false;
		}
		} else {
			dateSelected = false;
			return false;
		}
		} else {
				if (from_date == "") {
					document.forms[0].elements['maintenanceLog.fromDateAsString'].focus();
					dateSelected = false;
				}
				if (to_date == "") {
					document.forms[0].elements['maintenanceLog.toDateAsString'].focus();
					dateSelected = false;
				}
			}
	} else {
			return false;
		}
}

function storeFromDate(){
document.forms[0].elements['preMaintenanceFromDateAsString'].value = document.forms[0].elements['maintenanceLog.fromDateAsString'].value;
}

function onFromDateChange(){
	if(dateSelected){

	var preFromDate = document.forms[0].elements['preMaintenanceFromDateAsString'].value;
	var from_date=document.forms[0].elements['maintenanceLog.fromDateAsString'].value;
	var to_date=document.forms[0].elements['maintenanceLog.toDateAsString'].value;
	if(from_date != "" && to_date != "") {
		if(validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
		cal.hideCalendar()
		if(validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	if(compare_date == 1)
		{
			document.forms[0].elements['maintenanceLog.fromDateAsString'].value=preFromDate;
			cal.hideCalendar()
			alert('<fmt:message key="alert.interestCharges.fromDate"/>');


		}else{
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;


			document.forms[0].method.value = 'showDetails';
		    document.forms[0].submit();
		}
		dateSelected = false;
		return true;
			} else {
				dateSelected = false;
				return false;
			}
			} else {
				dateSelected = false;
				return false;
			}
		} else {
				document.forms[0].elements['maintenanceLog.toDateAsString'].focus();
				dateSelected = false;
			}
	} else {
			return false;
		}
}

/*This function validates the Date fields when the refresh button is pressed*/
function refreshWindow() {
	dateSelected = false;
	var dateFormat = '${sessionScope.CDM.dateFormat}';
		if(validateDateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat))
		{
			if(validateDateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat))
			{
				if(comparedates(document.forms[0].elements['maintenanceLog.fromDateAsString'].value,document.forms[0].elements['maintenanceLog.toDateAsString'].value,dateFormat,'From','To'))
				{
					submitForm('showDetails');
				}else{
					return false;
				}
			}else{
				return false;
			}
		}else{
			return false;
		}
}




function optionClick_server_filter_JSP(index,value,action)
{

	if(action == "filter"){
		value = value.trim();
		value = replace(value,'&nbsp;',' ');
		var filterValue ="";
		if(currentFilter =="all" || currentFilter=="undefined"){

			for(var idx = 0 ; idx < xl.numColumns ; ++idx)
			{
				if(idx == index)
					filterValue +=  value + "|";
				else
					filterValue +=  "All" + "|";

			}
		}else{

			var filter=currentFilter.split("|");
			filter[index]=value;
			for(var idx = 0 ; idx < xl.numColumns ; ++idx)
			{
				filterValue+=filter[idx] + "|";
			}
		}
		document.forms[0].selectedSort.value=currentSort;
		document.forms[0].selectedFilter.value =filterValue ;
	}else{
		var sortColum=index;
		var sortDesc=value;
		document.forms[0].selectedSort.value=sortColum + "|" +sortDesc;
		document.forms[0].selectedFilter.value =currentFilter ;
	     }
	document.getElementById('ddscrolltable').innerHTML='';
	document.forms[0].method.value='showDetails';

	document.forms[0].submit();

  }


function validatePageNumber(strObject) {
	dateSelected = false;
	if (document.forms[0].elements['maintenanceLog.fromDateAsString'].value != "" && document.forms[0].elements['maintenanceLog.toDateAsString'].value != "") {
	if(validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
	if(validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
    var re = /^\d+$/;
	if (strObject && (re.test(strObject.value) && strObject.value != 0))
	{
		if(parseInt(strObject.value) > maxPage)
		{
			strObject.value = maxPage;
		}
		goToResultsPage(strObject.value);
	}
	else
	{
		alert('<fmt:message key="alert.enterValidPageNumber"/>');
		strObject.value = currPage;
	}
	} else {
		return false;
	}
	} else {
		return false;
	}
	} else {
		alert('<fmt:message key="alert.enterValidDate"/>');
	}
}

function goToResultsPage(goToPageNo)
{
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	var currPage = "${requestScope.currentPage}";
	document.forms[0].method.value = "next";
	document.forms[0].goToPageNo.value=goToPageNo;
	document.forms[0].currentPage.value=currPage;
	document.forms[0].maxPages.value=maxPage;
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value=currentFilter;
	document.forms[0].fromDate.value='${fromDate}';
	document.forms[0].toDate.value='${toDate}';
	document.forms[0].submit();
}

function clickLink(goToPageNo) {
	dateSelected = false;
	if (document.forms[0].elements['maintenanceLog.fromDateAsString'].value != "" && document.forms[0].elements['maintenanceLog.toDateAsString'].value != "") {
	if(validateField(document.forms[0].elements['maintenanceLog.fromDateAsString'],'auditLog.from',dateFormat)){
	if(validateField(document.forms[0].elements['maintenanceLog.toDateAsString'],'auditLog.to',dateFormat)){
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	/*  START:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
	var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((PageDetails)((ArrayList<PageDetails>)request.getAttribute("pageSummaryList")).get(0), "pageDetails") %>';
	var url='maintenancelog.do?'+baseUrl;
	url +='method=next&goToPageNo='+goToPageNo+'&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&fromDate='+'${fromDate}'+'&toDate='+'${toDate}';
    submitFormFromURL(url,window);
	/*  END:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
	} else {
		return false;
	}
	} else {
		return false;
	}
	} else {
		alert('<fmt:message key="alert.enterValidDate"/>');
	}
}

</SCRIPT>

<script language="JAVASCRIPT">
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
      var cal = new CalendarPopup("caldiv",true);

      cal.offsetX = 22;
      cal.offsetY = 0;
	  var cal2 = new CalendarPopup("caldiv",true);

      cal2.offsetX = 22;
      cal2.offsetY = 0;
  </script>
</head>


<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]);" onunload="call();">
<form action="maintenancelog.do" method="post" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">
<input name="selectedTableName" type="hidden" value="display">
<input name="selectedColumnName" type="hidden" value="display">
<input name="selectedLogDate" type="hidden" value="">
<input name="selectedDate" type="hidden" value="">
<input name="selectedTime" type="hidden" value="">
<input name="selectedMainSeq" type="hidden" value="">
<input name="selectedUserId" type="hidden" value="">
<input name="selectedIpAddress" type="hidden" value="">
<input name="selectedReference" type="hidden" value="">
<input name="selectedAction" type="hidden" value="">
<input name="totalCount" type="hidden" value="">

<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
<input name="selectedSort" type="hidden" value='${selectedSort}'>

<input name="preMaintenanceFromDateAsString" type="hidden" value="">
<input name="preMaintenanceToDateAsString" type="hidden" value="">

<input name="goToPageNo" type="hidden" value="">
<input name="fromDate" type="hidden" value="">
<input name="toDate" type="hidden" value="">
<input name="maxPages" type="hidden" value="">
<input name="currentPage" type="hidden" value="">

<bean:define id="CDM" name="CDM" type="org.swallow.util.CommonDataManager" scope="session"/>

<div id="MaintenanceLog" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:954px; height:37px;">
<div id="MaintenanceLog" style="position:absolute; left:8; top:4; width:920; height:15px; visibility:visible;">
   <table width="450" border="0" cellpadding="0" cellspacing="0" class="content">

  <tr>
		  <td width="15"><b><fmt:message key="auditLog.from"/></b>*</td>
		     <td width="28px" >&nbsp;</td>
		  <td width="80px" id="mainLogFromDate">

	<input type="text"  name="maintenanceLog.fromDateAsString" value="${maintenanceLog.fromDateAsString}"   maxlength="10" titleKey="tooltip.fromDate" tabindex="1" class="htmlTextAlpha" style="width:80px;height: 20px;" onkeydown="onFromDateKeyPress(this,event)" onblur="onFromDateChange();" onmouseout="dateSelected=false;" />
		  </td>
		<td width="28px"> &nbsp;<A  title='<fmt:message key="tooltip.selectFromDate"/>' tabindex="2" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['maintenanceLog.fromDateAsString'],'datelink',dateFormatValue); storeFromDate();dateSelected = true; return false;"><img src="images/calendar-16.gif" style="margin-bottom: -2px !important;"></A></td>

       <td width="28px" >&nbsp;</td>

		  <td width="15"><b><fmt:message key="auditLog.to"/></b>*</td>
		  	  <td width="28px" >&nbsp;</td>
		  <td width="80px" id="mainLogToDate">
		  	<input type="text"  name="maintenanceLog.toDateAsString" value="${maintenanceLog.toDateAsString}"   maxlength="10" titleKey="tooltip.toDate" tabindex="3" cssClass="htmlTextAlpha" style="width:80px; height: 20px;" onkeydown="onToDateKeyPress(this,e)" onblur="onToDateChange();" onmouseout="dateSelected=false;" />
		  </td>
		 <td> &nbsp;<A  title='<fmt:message key="tooltip.selectToDate"/>' tabindex="4" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['maintenanceLog.toDateAsString'],'datelink2',dateFormatValue);storeToDate();dateSelected = true; return false;" ><img src="images/calendar-16.gif" style="margin-bottom: -2px !important;"></A></td>

        </tr>
		</table>
</div>

<div  id="pageSummaryList" style="position:absolute; left:744px; top:1px; width:210px; height:25px;border:2px;">
<c:if test="${'true' != requestScope.hidePagination}">

		<table border="0" cellpadding="0" cellspacing="1" height="25px" style="padding-bottom: 4px;">
		  <tr height="25px" style="padding-bottom: 5px;">
		  <%String currentPageAsString = (String)request.getAttribute("currentPage");%>
		  <%String maxPageAsString = (String)request.getAttribute("maxPage");%>
		  <%int countPageNo = 1;%>
<c:forEach items="${requestScope.pageSummaryList}" var="pageSummaryList">
			<% if( countPageNo <=12) {++countPageNo; %>
			<td height="30"><b>Page</b>&nbsp;&nbsp;
	  		<input class="htmlTextNumeric" id="pageNoText" name="pageNo" size="5" style="height: 22px; padding-top: 2px" align="top" value="<%=currentPageAsString %>" onkeydown="if (event.keyCode == 9 || event.keyCode == 13)validatePageNumber(this);">
	  		</td>
	  		<td >
<c:if test="${'true' == requestScope.nextEnabled}">

		  		<a href="#"  onclick="clickLink(-1);">
				    <img alt="Next page" src="images/page_up.png" align="top" border="0" height="12" width="18" style="padding-top:6px;margin-left: -1px;"></img><br />
			    </a>

</c:if>

<c:if test="${'true' != requestScope.nextEnabled}">

			    	<img alt="Next page" src="images/page_up.png" align="top" border="0" height="12" width="18" style="padding-top:6px;margin-left: -1px; "></img><br />

</c:if>

<c:if test="${'true' == requestScope.prevEnabled}">

	  			<a href="#"  onclick="clickLink(-2);">
				    <img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" border="0" style="padding-bottom:6px;margin-left: -1px;"></img><br />
			    </a>

</c:if>
<c:if test="${'true' != requestScope.prevEnabled}">

			    	<img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" border="0" style="padding-bottom:6px;margin-left: -1px;"></img><br />

</c:if>
	  		</td>
	  		<td style="text-align: center; padding-top: 2px"><fmt:message key="genericDisplayMonitor.labelOf"/>&nbsp;&nbsp;
	    		<input class="textAlpha" style="background:transparent;border: 0;height:17;width:50;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
	  		</td>
			<%}%>
</c:forEach>
		  </tr>
		</table>

</c:if>
	</div>
</div>
 <DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>


 <div id="MaintenanceLog" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:62px; width:954px; height:424px;">
<div id="MaintenanceLog" style="position:absolute;z-index:99;left:0px; top:0px; width:949px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="935" border="0" cellspacing="1" cellpadding="0"  height="23">
	<thead>
		<tr>
	<td title='<fmt:message key="tooltip.sortLogDate"/>' width="80px" height="20px"  style="border-left-width: 0px;" align="center"><b><fmt:message key="maintenanceLog.logDate_Date"/></b></td>
	<td title='<fmt:message key="tooltip.sortLogTime"/>' width="77px" height="20px" align="center"><b><fmt:message key="auditLog.logDate_Time"/></b></td>
	<td title='<fmt:message key="tooltip.sortUserId"/>' width="135px" height="20px" align="center"><b><fmt:message key="auditLog.userId"/></b></td>
	<td title='<fmt:message key="tooltip.sortIpAddress"/>' width="120px" height="20px" align="center"><b><fmt:message key="auditLog.id.ipAddress"/></b></td>
	<td title='<fmt:message key="tooltip.sortFacility"/>' width="150px" height="20px" align="center"><b><fmt:message key="maintenanceLog.tableName"/></b></td>
	<td title='<fmt:message key="tooltip.sortbyReference"/>' width="275px" height="20px" align="center"><b><fmt:message key="maintenanceLog.reference"/></b></td>
	<td title='<fmt:message key="tooltip.sortAction"/>' width="85px" height="20px" align="center"><b><fmt:message key="auditLog.id.action"/></td>
		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:950px; height:418px;overflowY:scroll">
<div id="AuditLog" style="position:absolute;z-index:99;left:1px; top:21px; width:931px; height:10px;">
<table class="sort-table" id="maintenanceLogList" width="933" border="0" cellspacing="1" cellpadding="0" height="409">
	<tbody>
	<%int count = 0; %>
<c:forEach items="${requestScope.maintenanceLogList}" var="maintenanceLogList">
		<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr class="odd"> <%}++count; %>

		<input type="hidden" name="maintenanceLogList.id.tableName" value="${maintenanceLogList.id.tableName}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.columnName" value="${maintenanceLogList.columnName}" disabled="true"/>

		<input type="hidden" name="maintenanceLogList.logDate_Date" value="${maintenanceLogList.logDate_Date}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.logDate_Time" value="${maintenanceLogList.logDate_Time}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.id.dupaction" value="${maintenanceLogList.id.dupaction}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.mainSeqNo" value="${maintenanceLogList.mainSeqNo}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.logDateIso" value="${maintenanceLogList.logDateIso}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.id.userId" value="${maintenanceLogList.id.userId}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.id.ipAddress" value="${maintenanceLogList.id.ipAddress}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.id.reference" value="${maintenanceLogList.id.reference}" disabled="true"/>
		<input type="hidden" name="maintenanceLogList.id.action" value="${maintenanceLogList.id.action}" disabled="true"/>


			<td width="80px" align="left">${maintenanceLogList.logDate_Date}&nbsp;</td>
			<td width="77px" align="left">${maintenanceLogList.logDate_Time}&nbsp;</td>
			<td width="135px" align="left">${maintenanceLogList.id.userId}&nbsp;</td>
			<td width="120px" align="left">${maintenanceLogList.id.ipAddress}&nbsp;</td>
			<td width="150px" align="left">${maintenanceLogList.id.tableName}&nbsp;</td>
			<td width="275px" title="${maintenanceLogList.id.reference}" align="left">${maintenanceLogList.id.reference}&nbsp;</td>
			<td width="85px" align="left">${maintenanceLogList.id.dupaction}&nbsp;</td>

		</tr>
</c:forEach>
	</tbody>
	<tfoot><tr><td colspan="7" ></td></tr></tfoot>
</table>
</div>
</div>

</div>
<div id="MaintenanceLog" style="position:absolute; left:898; top:509px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

			<td align="Right">
					<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Maintenance Log'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
			</td>


			<td align="right" id="Print">
				<a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>' ></a>
			</td>
		</tr>
	</table>
</div>

 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:500px; width:954px; height:39px; visibility:visible;">

  <div id="UserLog" style="position:absolute; left:6; top:4; width:675; height:15px; visibility:visible;">
  	 <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td id="refreshbutton" width="70px">
			<a title='<fmt:message key="tooltip.refreshMainLog"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){refreshWindow();}"><fmt:message key="button.Refresh"/></a>
            </td>

			<td id="viewbutton" width="70px">
			</td>
    		<td id="closebutton" width="70px">
			<a title='<fmt:message key="tooltip.close"/>' tabindex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
	</div>
	<table height="35"><tr>
		<td id="lastRefTimeLable" width="740px" align="right" >
		<b><fmt:message key="label.lastRefTime"/></b>
		</td>
		<td id="lastRefTime" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">
		</td>
		</tr>
	</table>
  <div style="position:absolute; left:6; top:4; width:675 height:15px; visibility:hidden;">
  	 <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.Refresh"/></a>
		</td>
		<td id="viewenablebutton" width="70px">
			<a title='<fmt:message key="tooltip.viewSelectedLog"/>' tabindex="6"  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){buildTableNameURL('viewDetails');}"><fmt:message key="button.view"/></a>
			</td>
			<td id="viewdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td>

		</tr>
		</table>
	</div>

</div>



</form>
</body>
</html>