<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title>
<c:if test="${'R' == requestScope.scheduledJobType}">
	<fmt:message key="reportScheduler.title.window"/>
</c:if>
<c:if test="${'R' != requestScope.scheduledJobType}">
	<c:if test="${'P' == requestScope.scheduledJobType}">
		<fmt:message key="processScheduler.title.window"/>
	</c:if>
	<c:if test="${'P' != requestScope.scheduledJobType}">
		<fmt:message key="batchScheduler.title.window"/>
	</c:if>
</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<SCRIPT language="JAVASCRIPT">
var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var lastRefTime = "${requestScope.lastRefTime}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var scheduledJobType = "${requestScope.scheduledJobType}";
var selectedScheduledJobType = "${requestScope.selectedScheduledJobType}";
var gridScrollTop="${requestScope.gridScrollTop}";
var menuAccessId = "${requestScope.menuAccessId}";
var appName = "<%=SwtUtil.appName%>";

function buildJobMaintenance(methodName, selectedScheduledJobType, scheduledJobType, selectedJobId, selectedScheduleId, selectedJobType) {
	var param = 'scheduler.do?method=' + methodName;
	param += '&selectedjobId=' + selectedJobId;
	param += '&selectedScheduleId=' + selectedScheduleId;
	param += '&selectedJobType=' + selectedJobType;
	param += '&selectedScheduledJobType=' + selectedScheduledJobType;
	param += '&scheduledJobType=' + scheduledJobType;

	openWindow(param, '', 'left=50,top=190,width=650,height=545,toolbar=0,scrollbars=yes,status=yes,resizable=yes');
}

function buildEntityProcess(menuAccessId) {
	var param = 'entityprocess.do?menuAccessId=' + menuAccessId;
	openWindow(param, '', 'left=50,top=190,width=1240,height=533,toolbar=0,status=yes,resizable=yes,scrollbars=yes');
}

function closeWindow() {
	window.close();
}

function doHelp(helpId) {
	openWindow('help.do?helpid=' + helpId, 'helpwindow', 'width=800,height=500,toolbar=0,status=yes,resizable=yes,scrollbars=yes', 'true');
}

function getAppName() {
	return appName;
}
</SCRIPT>
</head>

<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "BatchScheduler";
</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>
