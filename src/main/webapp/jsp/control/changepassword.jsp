<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="changePassword.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">



<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="cancelbutton";
	mandatoryFieldsArray = ["*"];

function clearPasswordChangeStatus() {
	sessionStorage.removeItem('passwordChangeStatus');
	sessionStorage.removeItem('changepassworddays');
	sessionStorage.removeItem('changepasswordadlertsent');
}


<c:if test="${'yes' == requestScope.parentFormRefresh}">
clearPasswordChangeStatus();
window.opener.document.forms[0].method.value="save";
window.opener.document.forms[0].submit();
self.close();

</c:if>
sessionStorage.setItem('changepasswordadlertsent', 'N');

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

function closeHandler() {
	self.close();
}
function validateForm(objForm){
  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["passwordHisory.password"];
  elementsRef[1] = objForm.elements["user.password"];
  elementsRef[2] = objForm.elements["user.password1"];
  return validate(elementsRef);
 }
/* Start:Code modified by Alibasha for Mantis 1608 on 08-03-2012 */
function submitForm(methodName){
	<% session=request.getSession(); %>
	var sessionId='<%=session.getId()%>';
	var userId='<%=SwtUtil.getCurrentUserId(request.getSession())%>';
    if(validateField(document.forms[0].elements['user.password'],'user.password','alphaNumPatPassWord'))
       {

	if(!validateForm(document.forms[0]) ){
		return;
	}

	var pass = document.forms[0].elements['user.password1'].value;
	var pass1 = document.forms[0].elements['user.password'].value;
	if(pass != pass1){
		ShowErrMsgWindowWithBtn('', '<fmt:message key="changePassword.alert.newpass"/>', null);
		return;
	}
	try{
	document.forms[0].elements["encpasswdHisory"].value=encryptPass(userId,document.forms[0].elements["passwordHisory.password"].value);
	document.forms[0].elements["encpasswd"].value=encryptPass(userId,document.forms[0].elements["user.password"].value);
	document.forms[0].elements["encpasswd1"].value=encryptPass(userId,document.forms[0].elements["user.password1"].value);
	document.forms[0].elements["encoldpasswd"].value=encryptPass(userId,document.forms[0].elements["passwordHisory.password"].value);

	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	 }catch(e){
		 ShowErrMsgWindowWithBtn('', '<fmt:message key="alert.htmlInternalErrorOccurred"/>'+' '+e.message, null);
	 }
	}else{
	document.forms[0].elements['user.password'].focus();
	}
}
/* End:Code modified by Alibasha for Mantis 1608 on 08-03-2012 */
function bodyOnLoad() {

	var userId='<%=SwtUtil.getCurrentUserId(request.getSession())%>';

	document.getElementById("userId").value = userId;


	<c:if test="${'close' == requestScope.closescreen}">

	ShowErrMsgWindowWithBtn('', '<fmt:message key="changePassword.alert.changed"/>', null, closeHandler);

</c:if>
	document.getElementById("passwordHisory.password").value="";
	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	<%}%>

	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	}else{
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	}

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

}
</SCRIPT>

</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
<form action="changepassword.do" method="post" >

<input name="method" type="hidden" value="save">

<!-- Mantis 2077: Added by Meftah Bouazizi to pass the hidden history password "encpasswdHisory" , the hidden password "encpasswd" ,
 the hidden password1 "encpasswd1" && the hidden old password "encoldpasswd" -->
<input name="encpasswdHisory" type="hidden" value="">
<input name="encpasswd" type="hidden" value="">
<input name="encpasswd1" type="hidden" value="">
<input name="encoldpasswd" type="hidden" value="">

<input name="menuAccessId" type="hidden" >

<!-- Start:Code modified by Alibasha for Mantis 1608 on 07-Feb-2012 -->
<div id="ChangePassword" style="position:absolute; left:20px; top:20px; width:450px; height:115px; border:2px outset;" color="#7E97AF">


<div id="ChangePassword" style="position:absolute; left:8px; top:4px; width:350; height:30;">
      <table width="413px" border="0" class="content">
          <tr height="10">
          <td width="175px"><b><fmt:message key="changePassword.userId"/></b></td>
		   <td width="26px" ></td>
		  <td >
         <input type="text"  id="userId" class="htmlTextAlpha" style="widht:158px" tabindex="1" title='<fmt:message key="changePassword.tooltip.userId"/>' disabled /></td>
         </tr>
          <tr height="10">
          <td width="175px"><b><fmt:message key="password.old"/></b>*</td>
		   <td width="26px" ></td>
		<!-- Mantis 2077: Change the password tag from html:password into input tag, Reason: If we don't give a name for the input element, it will not be submitted into the form, then the clear password will not be sent to server. START-->
		  <td >
         <input type="password"  id="passwordHisory.password" class="htmlTextAlpha" style="widht:158px" tabindex="1" title='<fmt:message key="tooltip.oldPwd"/>'  /></td>
         </tr>
		   <tr height="10">
          <td width="175px"><b><fmt:message key="password.new"/></b>*</td>
		   <td width="26px" ></td>
		  <td >
         <input type="password"  id="user.password" class="htmlTextAlpha" style="widht:158px;" tabindex="2"    onchange="return validateField(this,'user.password','alphaNumPatPassWord');"  title="Enter password (Acceptable characters:[a-z,A-Z],[0-9],[~!@#$%^&*()-_=+;:'&quot;,<.>/?])" />
          </td>
         </tr>
         <tr height="10">
          <td width="290px"><b><fmt:message key="password.retypeNew"/></b>*</td>
		   <td width="26px" ></td>
		  <td >
          <input type="password"  id="user.password1" class="htmlTextAlpha" style="widht:158px" tabindex="3" title='<fmt:message key="tooltip.confirmNewPwd"/>' /></td>
         </tr>
	    <!-- Mantis 2077: Change the password tag from html:password into input tag, Reason: If we don't give a name for the input element, it will not be submitted into the form, then the clear password will not be sent to server. END-->

      </table>
      </div>
	  	  </div>
	  	  <!-- End:Code modified by Alibasha for Mantis 1608 on 07-Feb-2012 -->
 <div id="ChangePassword" style="position:absolute; left:383; top:150; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
				<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Password '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		</td>


			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:145; width:450px; height:39px; visibility:visible;">
<div id="ChangePassword" style="position:absolute; left:6; top:4; width:370; height:15px; visibility:visible;">
<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>

		 <td width="70px" id="savebutton">
		 </td>


		 <td id="cancelbutton" width="70px">
		 <a title='<fmt:message key="tooltip.cancel"/>' tabindex="5"  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a></td>
		 </td>
	</tr>
</table>
</div>
</div>

<div id="ChangePassword" style="position:absolute; left:6; top:4; width:370; height:15px; visibility:hidden;">
<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>

		 <td id="saveenablebutton">
			<a title='<fmt:message key="tooltip.save"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('save');"><fmt:message key="button.save"/></a></td>
  	  	 </td>
		 <td id="savedisablebutton">
			<a  class="disabled" title='<fmt:message key="tooltip.SaveChanges"/>' disabled="disabled" ><fmt:message key="button.save"/></a>
		 </td>
	</tr>
</table>

</div>
	  <blockquote>&nbsp;</blockquote>
            <p>&nbsp;</p>
</form>
</body>
</html>