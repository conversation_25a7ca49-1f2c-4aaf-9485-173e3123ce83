<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.LabelValueBean"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<title><fmt:message key="title.exportErrors"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
	<script type="text/javascript" src="js/jquery.multicolselect.js"></script>
<script language="JAVASCRIPT">
mandatoryFieldsArray = ["*"];
var dateFormat = '${sessionScope.CDM.dateFormat}';
 var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
      var cal = new CalendarPopup("caldiv");

      cal.offsetX = 22;
      cal.offsetY = 0;
	  var cal2 = new CalendarPopup("caldiv");

      cal2.offsetX = 22;
      cal2.offsetY = 0;

function onDateKeyPress(obj,e){
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'To',dateFormat)){
			submitForm('errorLogDetails');
			}
		else
		{
			return false;
			}
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'To',dateFormat)){
			submitForm('errorLogDetails');
			}
		else
		{
			return false;
			}
		}
}

function validateForm(objForm){
  var elementsRef = new Array(2);
  var validateTime = new Array(2);
  elementsRef[0] = objForm.elements["errorLog.fromDateAsString"];
  elementsRef[1] = objForm.elements["errorLog.toDateAsString"];
  validateTime[0] = objForm.elements["errorLog.fromTime"];
  validateTime[1] = objForm.elements["errorLog.toTime"];


  if(validate(elementsRef))
  {

	//From date validation
  	if (!validateField(objForm.elements["errorLog.fromDateAsString"],'auditLog.from',dateFormat)) {
  		return false;
  	}
  	//From time validation
  	if (!validateField(objForm.elements["errorLog.fromTime"],'Number 2','timePatWithonedigit')) {
  		return false;
  	}
  	//To date validation
  	if (!validateField(objForm.elements["errorLog.toDateAsString"],'auditLog.to',dateFormat)) {
  		return false;
  	}
  	//To time validation
  	if (!validateField(objForm.elements["errorLog.toTime"],'Number 2','timePatWithonedigit')) {
  		return false;
  	}

    if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date'))
     {
		  return true;

     }
   }
   return false;
}

function submitForm(methodName) {
  if(validateForm(document.forms[0]))
  {
	document.forms[0].method.value=methodName;
	document.forms[0].submit();
	setParentChildsFocus();
  }

}

var headers = ["User", "Name"];


var dataprovider = new Array();
function loadUserList()
{
	<%
		ArrayList<LabelValueBean> userList = (ArrayList<LabelValueBean>)request.getAttribute("users");
		Iterator it = userList.iterator();
		while (it.hasNext())
		{
			LabelValueBean lvb = (LabelValueBean) it.next();
	%>
		var newElement = {};
		newElement[headers[0]] = "<%=lvb.getValue()%>";
		newElement[headers[1]] = "<%=lvb.getLabel()%>";
		dataprovider.push(newElement);
	<%
		}

	%>


}

function bodyOnLoad()
{
	loadUserList();

	$("#dropDownUser").multicolselect({
			headerData: headers,
			dataprovider: dataprovider,
			name: 'errorLog.dropDownUserId',
			selectedIndex: 0,
			descriptionElement: $('#userName')
	});

	//var dropBox1 = new SwSelectBox(document.forms[0].elements["error.dropDownUserId"],document.getElementById("userName"));

}
</script>


</head>
<!-- Start:Code Modified For Beta Testing issues defect no 1054_SEL_045 by Nithiyananthan on 29-March-2012 -->
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad();">
<!-- End:Code Modified For Beta Testing issues defect no 1054_SEL_045 by Nithiyananthan on 29-March-2012 -->
<form action="errorlog.do" method="post">

<input name="method" type="hidden" value="display">
<input name="exportType" type="hidden" value="<%=request.getParameter("exportType")%>">
<input name="screen" type="hidden" value="<%=request.getParameter("screen")%>">

<DIV ID="caldiv" STYLE="z-index:101;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>
<div id="Movement" style="position:absolute;z-index:100 ;left:20px; top:20px; width:500px; height:117px; border:2px outset;" color="#7E97AF">
<div id="MovementDisplay" style="position:absolute;left:8px; top:4px; width:500px; height:110px;">

	<table width="500px" border="0" cellpadding="0" cellspacing="1" height="23">
		<tr height="24">
			  <td width="80px"><b><fmt:message key="auditLog.userId"/></b></td>
			  <td width="20px" >&nbsp;</td>
			  <td width="155px">
			  <div id="dropDownUser"/></div>
			  </td>
			   <td width="15px">&nbsp;</td>
			  <td width="188px" style="z-index:10" align="left">
				<span id="userName" name="userName" style="background:transparent; border:thin;width:188px;"/>
			   </td>
		</tr>
	</table>

	<table width="500px" border="0" cellpadding="0" cellspacing="1" class="content">
	 <tr >
		<td width="83px"><b><fmt:message key="auditLog.from"/></b>*</td>
		<td width="27px" >&nbsp;</td>
		<td width="80px">
		<input type="text" titleKey="tooltip.fromDate" tabindex="1" name="errorLog.fromDateAsString" value="${errorLog.fromDateAsString}"  class="htmlTextAlpha" style="width:80px;height:22px" onchange="return validateField(this,'auditLog.from',dateFormat)" />
		<td width="28px"> &nbsp;<A  title='<fmt:message key="tooltip.selectFromDate"/>' tabindex="2" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['errorLog.fromDateAsString'],'datelink',dateFormatValue); return false;"><img src="images/calendar-16.gif"></A></td>
		<td width="28px" >&nbsp;</td>
		<td width="130px"><b>From Time</b></td>
		<td width="27px">&nbsp;</td>
		<td width="117px">
		<input type="text"  name="errorLog.fromTime" value="${errorLog.fromTime}"  style="width:70px;height:22px" onchange="return validateField(this,'Number 2','timePatWithonedigit')" />
		</td>
	</tr>
	</table>
	<table width="500px" border="0" cellpadding="0" cellspacing="1" height="23">
		<tr height="22px">
		<td width="83"><b><fmt:message key="auditLog.to"/></b>*</td>
		<td width="28px" >&nbsp;</td>
	    <td width="80px">
		<input type="text" titleKey="tooltip.toDate" tabindex="3" name="errorLog.toDateAsString" value="${errorLog.toDateAsString}"  cssClass="htmlTextAlpha" style="width:80px;height:22px" onchange="return validateField(this,'auditLog.to',dateFormat)" />
		<td width="28px"> &nbsp;<A  title='<fmt:message key="tooltip.selectToDate"/>' tabindex="4" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['errorLog.toDateAsString'],'datelink2',dateFormatValue); return false;" onblur="document.forms[0].elements['errorLog.toDateAsString'].focus();"><img src="images/calendar-16.gif"></A></td>
		<td width="28px" >&nbsp;</td>
		<td width="130px"><b>To Time</b></td>
		<td width="28px">&nbsp;</td>
		<td width="117px"  >
		<input type="text"  name="errorLog.toTime" value="${errorLog.toTime}"  style="width:70px;height:22px" onchange="return validateField(this,'Number 2','timePatWithonedigit')" />
		</td>
		</tr>
	</table>

</div>
</div>


		<div id="MovementDisplay" style="position:absolute; left:420px; top:156px; width:70px; height:15px; visibility:visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
				 <tr>

				  <td align="Right">
						<a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Pre-advice Input'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
     				</td>

					<td align="right" id="Print">
						<a onclick="printPage();" tabindex="57" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
					</td>
				</tr>
			</table>
		</div>


		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:145px; width:500px; height:45px; visibility:visible;">
		<div id="MovementDisplay" style="position:absolute; left:6; top:8; width:430px; height:15px;visibility:visible;">
			  <table width="150" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
							<!-- Export Button : Always Display-->
							<td>
									<a tabindex="50" title='<fmt:message key="tooltip.exportErrors"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('exportErrors')"><fmt:message key="button.exportErrors"/></a>
							</td>
							<!-- Close Button : Always Display -->
							<td>
									<a tabindex="53" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
							</td>

					</tr>
			</table>
		</div>
		</div>




</form>
</body>
