<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>

<html>
<head>
<title><fmt:message key="jobSetup.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();

</c:if>
function bodyOnLoad(){

	xl = new XLSheet("entities","table_2", ["Number", "String"],"11");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("entities");
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
}



function submitForm(methodName){
	document.forms[0].method.value = methodName;

	document.forms[0].submit();
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){
		var param = 'scheduler.do?method='+methodName+'&jobid=';
        param +=document.forms[0].selectedjobId.value;
		document.forms[0].submit();

	}
}

function buildJobMaintenance(methodName){

	var param = 'scheduler.do?method='+methodName+'&selectedjobId=';
	param += document.forms[0].selectedjobId.value;

	return  param;
}
function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

}

function onSelectTableRow(rowElement,isSelected)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedjobId.value = hiddenElement.value;

  if(isSelected)
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	}

	else
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

	}
}

</SCRIPT>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad(); setParentChildsFocus();ShowErrMsgWindow('${actionError}');" onunload="call()">
<form action="scheduler.do" method="post">
<input name="selectedjobId" type="hidden" value="GA">
<input name="method" type="hidden" value="display">
<input name="selectedJobCode" type="hidden" value="GBP">

<div id="JobMaintenance" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:463px; height:430px;">

<div id="JobMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:444px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="440px" border="0" cellspacing="1" cellpadding="0"  height="20">
	<thead>
		<tr>
			<td  title='<fmt:message key="tooltip.sortJobId"/>' width="120px" align="center"><b><fmt:message key="Jobmaintenance.Job"/></b></td>
			<td  title='<fmt:message key="tooltip.sortJobName"/>' width="320px" align="center"><b><fmt:message key="Jobmaintenance.Name"/></b></td>
		</tr>
	</thead>
</table>
</div>


<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:458px; height:425px;overflowY:scroll">
<div id="JobMaintenance" style="position:absolute;z-index:99;left:1px; top:18px; width:443px; height:10px;">
<table class="sort-table" id="entities" width="442" border="0" cellspacing="1" cellpadding="0" height="405">
	<tbody>
	<%int count = 0; %>
	 <c:forEach items="${requestScope.jobNameList}" var="jobNameList" >
		<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
			<input type="hidden" name="jobId" value="${jobNameList.id.jobId}"/>
			<td width="120" align="center">
			${jobNameList.id.jobId}&nbsp;</td>
			<td width="320" align="left">${jobNameList.job.jobDescription}&nbsp;</td>
		</tr>
	 </c:forEach>
	</tbody>
	<tfoot><tr><td colspan="2" ></td></tr></tfoot>
</table>
</div>
</div>

</div>

<div id="JobMaintenance" style="position:absolute; left:1; top:464; width:469; height:39px; visibility:visible;">
	<table width="462" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:455; width:463; height:39px; visibility:visible;">

  <div id="JobLog" style="position:absolute; left:6; top:4; width:440; height:15px; visibility:visible;">
  	 <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td width="70">
			<a  tabindex="1" title='<fmt:message key="tooltip.addNewJob"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildJobMaintenance('add'),'','left=50,top=190,width=626,height=590,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
            </td>

			<td width="70" id="changebutton">
			</td>
			<td width="70" id="viewbutton">
			</td>
			<td width="70" id="deletebutton">
			</td>
    		<td width="70">
			<a tabindex="5" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
	</div>

  <div  style="position:absolute; left:6; top:4; width:440; height:15px; visibility:hidden;">
  	 <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td id="changeenablebutton">
			<a  tabindex="2" title='<fmt:message key="tooltip.changeSelectedJob"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildJobMaintenance('displaySchedule'),'','left=50,top=190,width=626,height=590,toolbar=0,status=yes, resizable=yes')"><fmt:message key="button.change"/></a>
			</td>
			<td id="changedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
		</td>

		<td id="viewenablebutton">
			<a  tabindex="3" title='<fmt:message key="tooltip.viewSelectedJob"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildJobMaintenance('Schedulerview'),'','left=50,top=190,width=626,height=590,toolbar=0, status=yes,resizable=yes')"><fmt:message key="button.view"/></a>
			</td>
			<td id="viewdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td>

		<td id="deleteenablebutton">
			<a  tabindex="4" title='<fmt:message key="tooltip.deleteSelectedJob"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('SchedulerDelete');"><fmt:message key="button.delete"/></a>
			</td>
			<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>

		</tr>
		</table>
	</div>
 </div>

<blockquote>&nbsp;</blockquote>


</form>
</body>
</html>