<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<html>
<head>
<title><fmt:message key="internalMesgs.title.window"/></title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link href="style/Style.css" rel="stylesheet" type="text/css">



<SCRIPT language="JAVASCRIPT">
var loggedOffUsers = new String("${requestScope.LoggedOffUsers}");


var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";


function bodyonLoad(){
	document.forms[0].elements["messageInternal.userRoleFlag"].checked="true";
	document.forms[0].elements["messageInternalUser.roleId"].disabled="true";

	if(loggedOffUsers != null && loggedOffUsers.length > 0)
		alert('<fmt:message key="internalMesgs.alert.logoffUsers1"/>' + loggedOffUsers + " " +'<fmt:message key="internalMesgs.alert.logoffUsers2"/>' );

	document.forms[0].elements["messageInternal.userRoleFlag"][0].checked = true;


	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("sendMessagebutton").innerHTML = document.getElementById("sendMessageenablebutton").innerHTML;
	}else{
		document.getElementById("sendMessagebutton").innerHTML = document.getElementById("sendMessagedisablebutton").innerHTML;
	}

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

}


function roleFocus(){
	document.forms[0].elements["messageInternalUser.userId"].disabled="";
	document.forms[0].elements["messageInternalUser.roleId"].disabled="true";



}

function userFocus(){
	document.forms[0].elements["messageInternalUser.userId"].disabled="true";
	document.forms[0].elements["messageInternalUser.roleId"].disabled="";

	document.forms[0].elements["messageInternalUser.roleId"].selectedIndex = 0;

}

/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/
function submitForm(methodName){
/* code Added  by Nageswara Rao on 31_Jan_2012 for mantis 1580:"Spaces should not be saved to end of inputted values " */
elementTrim(document.forms[0]);
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}
</SCRIPT>



</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyonLoad();" onunload="call();">
<form action="messageinternal.do" method="post" >
<input name="method" type="hidden" value="save">

<input name="loggedOffusers" id="loggedOffusers" type="hidden" value="">

<input name="menuAccessId" type="hidden" >


<div id="MessageFormats" style="position:absolute; left:20; top:20; border:2px outset; width:335px; height:310px; visibility:visible;">

<div id="MessageFormats" style="position:absolute; left:6px; top:4px; width:345px; height:190px; ">
 <fieldset style="width:317px;border:2px groove;height:188px;">
		<legend><fmt:message key="internalmessage.send"/></legend>

 <div id="MessageFormats" style="position:absolute; left:6px; top:10px; width:330px;  ">


<table width="300px" border="0" cellpadding="2" cellspacing="0" class="content" height="44px">
   <tr height="22px">
	   <td width="144px" height="15px">
	   <input type="radio" name="messageInternal.userRoleFlag" ${messageInternal.userRoleFlag == 'U' ? 'checked="checked"' : ''} value="${'U'}" titleKey="tooltip.selectUsers" id="1" style="width:13;height:15px"   onclick="roleFocus();" />
		<label tabindex="1" title='<fmt:message key="tooltip.selectUsers"/>'  for="1">
		<fmt:message key="internalmessage.user"/></label>
		</td>

			<td width="68px">&nbsp</td>
			 <td width="144px" height="15px">
			 <input type="radio" name="messageInternal.userRoleFlag" ${messageInternal.userRoleFlag == 'R' ? 'checked="checked"' : ''} value="${'R'}" titleKey="tooltip.selectRole"  id="2" style="width:13;height:15px"    onclick="userFocus();" />
		 <label tabindex="2" title='<fmt:message key="tooltip.selectRole"/>' for="2"><fmt:message key="internalmessage.role"/> </label>
		 </td>
  </tr>


    <tr height="22px">

          <td width="144px">
				<select tabindex="3" style="width:144px;height:150px;" id="messageInternalUser.userId" name="messageInternalUser.userId" multiple="true" class="htmlTextAlpha">
					<c:forEach var="user" items="${requestScope.users}">
						<option value="${user.value}"
							<c:if test="${fn:contains(messageInternalUser.userId, user.value)}">selected</c:if>>
							${user.label}
						</option>
					</c:forEach>
				</select>
			</td>

<td width="48px">&nbsp</td>

<td width="144px">
	<select tabindex="4" style="width:144px;height:150px;" id="messageInternalUser.roleId" name="messageInternalUser.roleId" multiple="true" class="htmlTextAlpha">
		<c:forEach var="role" items="${requestScope.roles}">
			<option value="${role.value}"
				<c:if test="${fn:contains(messageInternalUser.roleId, role.value)}">selected</c:if>>
				${role.label}
			</option>
		</c:forEach>
	</select>
</td>

 		  </tr>
        </table>
   </div>

    <div id="MessageFormats" style="position:absolute; left:-3px; top:188px; width:240px;  ">
         <table width="%" border="0" class="content">
         <tr >
         <td >
         <textarea tabindex="5" titleKey="tooltip.typeMessage" rows="7"  style="width:316px;height:105px;" cols="49" cssClass="htmlTextAlpha"  name="messageInternal.messageText"></textarea>
         </td>
          </tr>
          </table>
     </div>
 </fieldset>
	  </div>
	  </div>


<div id="message" style="position:absolute ;left:280; top:345px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
				<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Internal Message '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		  </td>


			<td align="right" id="Print">
				<a title='<fmt:message key="tooltip.printScreen"/>' tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" ></a>
			</td>
		</tr>
	</table>
</div>





<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:338; width:335px; height:39px; visibility:visible;">
<div id="CustodianMaintenance" style="position:absolute; left:6; top:4; width:348; height:39px; visibility:visible;">
         <table width="140" height="20" border="0" cellpadding="0" cellspacing="0" >

                  <tr>

					  <td width="70px" id="sendMessagebutton"><a tabindex=6 title='<fmt:message key="tooltip.sendMessage"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('send');"><fmt:message key="button.send"/></a></td>

					  <td >
					   <a tabindex="7" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>

                   </tr>

            </table>
           </div>
		   </div>

<div id="CustodianMaintenance" style="position:absolute; left:6; top:4; width:348; height:39px; visibility:hidden;">
        <table width="140" height="20" border="0" cellpadding="0" cellspacing="0" >

                  <tr>
                      <td width="70px" id="sendMessageenablebutton"><a tabindex=6 title='<fmt:message key="tooltip.sendMessage"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('send');"><fmt:message key="button.send"/></a>
					  </td>
					  <td width="70px" id="sendMessagedisablebutton">
					  <a  class="disabled" title='<fmt:message key="tooltip.sendMessage"/>'  disabled="disabled"><fmt:message key="button.send"/></a>
					  </td>


                   </tr>

            </table>
           </div>

<SCRIPT language="JAVASCRIPT">
<c:if test="${'yes' == requestScope.showWarning}">

var users=document.forms[0].loggedOffusers.value;

showModalDialog('jsp/control/messageinternalframe.jsp',users,"center:yes;edge:raised;resizable:yes;scroll:yes;help:no;"+"dialogWidth="+345+"px;dialogHeight="+200+"px;status:no");

</c:if>
</SCRIPT>

</form>
</body>
</html>
 