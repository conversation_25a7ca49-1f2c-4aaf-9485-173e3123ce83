<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%
//variable declaration
String logDate = "";
String userId = "";
String ipAddress = "";
String tableName = "";
String reference = "";
String action = "";
//get the all params from request attribute
if (request.getAttribute("logDate") != null) {
	logDate = request.getAttribute("logDate")
			.toString();
}

if (request.getAttribute("userId") != null) {
	userId = request.getAttribute("userId")
			.toString();
}

if (request.getAttribute("ipAddress") != null) {
	ipAddress = request.getAttribute("ipAddress")
			.toString();
}

if (request.getAttribute("tableName") != null) {
	tableName = request.getAttribute("tableName")
			.toString();
}

if (request.getAttribute("reference") != null) {
	reference = request.getAttribute("reference")
			.toString();
}

if (request.getAttribute("action") != null) {
	action = request.getAttribute("action")
			.toString();
}

%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><fmt:message key="MaintenanceLogView.title.window"/></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body  onunload="saveSettings()">
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "AcctMaintViewLog";
var logDate = "<%= logDate %>";
var userId = "<%= userId %>";
var ipAddress ="<%= ipAddress %>";
var tableName = "<%= tableName %>";
var reference = "<%= reference %>";
var action = "<%= action %>";
 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>