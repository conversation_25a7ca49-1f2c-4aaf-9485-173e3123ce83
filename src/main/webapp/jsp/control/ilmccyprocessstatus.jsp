<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<accountattribute>

	<singletons>
		<selectedStartDate>${requestScope.selectedStartDate}</selectedStartDate>
		<selectedEndDate>${requestScope.selectedEndDate}</selectedEndDate>
		<fromDateAsString>${requestScope.fromDateAsString}</fromDateAsString>
		<toDateAsString>${requestScope.toDateAsString}</toDateAsString>
		<lastThresholdDay>${requestScope.lastThresholdDay}</lastThresholdDay>
		<firstThresholdDay>${requestScope.firstThresholdDay}</firstThresholdDay>
		<selecteDefaultDate>${requestScope.selecteDefaultDate}</selecteDefaultDate>
		<allIsAllowed>${allIsAllowed}</allIsAllowed>
	</singletons>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<process_info>
		<process_status>${requestScope.processStatus}</process_status>
		<running_seqnbr>${requestScope.runningSeqNumber}</running_seqnbr>
	</process_info>
	<grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key="entity.id"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="str"
						dataelement="entityid"
						width="100"/>

				<column heading="<fmt:message key="ilmTransactionSet.currency"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="str"
						dataelement="currencycode"
						width="110"/>

				<column heading="<fmt:message key="label.ilmccyprocess.valuedate"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="date"
						dataelement="valuedate"
						width="132"/>

				<column heading="<fmt:message key="label.ilmccyprocess.processid"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="str"
						dataelement="processid"
						width="250"/>

				<column heading="<fmt:message key="label.ilmccyprocess.currentstatus"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="str"
						dataelement="currentstatus"
						width="150"/>

				<column heading="<fmt:message key="label.ilmccyprocess.lastexecutestatus"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="str"
						dataelement="lastexecutestatus"
						width="185"/>

				<column heading="<fmt:message key="label.ilmccyprocess.laststarted"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="date"
						dataelement="laststarted"
						width="150"/>

				<column heading="<fmt:message key="label.ilmccyprocess.lastended"/>"
						clickable="false"
						draggable="true"
						filterable="true"
						type="date"
						dataelement="lastended"
						width="150"/>



			</columns>
		</metadata>
		<rows size="${requestScope.recordCount}">
			<c:forEach items="${requestScope.ccyProcessStatusList}" var="ccyProcessStatusList">
				<row>
					<entityid clickable="false">${ccyProcessStatusList.id.entityId}</entityid>
					<currencycode clickable="false">${ccyProcessStatusList.id.ccyCode}</currencycode>
					<valuedate clickable="false">${ccyProcessStatusList.valueDateAsString}</valuedate>
					<processid clickable="false">${ccyProcessStatusList.id.processId}</processid>
					<hasViewAccessOnly clickable="false">${ccyProcessStatusList.hasViewAccessOnly}</hasViewAccessOnly>
					<currentstatus clickable="false">
						<c:choose>
							<c:when test="${ccyProcessStatusList.currentStatus == 'N'}"><fmt:message key="label.ilmccyprocess.currentstatus.notRunning"/></c:when>
							<c:when test="${ccyProcessStatusList.currentStatus == 'R'}"><fmt:message key="label.ilmccyprocess.currentstatus.running"/></c:when>
							<c:when test="${ccyProcessStatusList.currentStatus == 'TR'}"><fmt:message key="label.ilmccyprocess.currentstatus.toRun"/></c:when>
							<c:when test="${ccyProcessStatusList.currentStatus == 'K'}"><fmt:message key="label.ilmccyprocess.currentstatus.skipped"/></c:when>
						</c:choose>
					</currentstatus>
					<lastexecutestatus clickable="false">
						<c:choose>
							<c:when test="${ccyProcessStatusList.lastExecuteStatus == 'S'}"><fmt:message key="label.ilmccyprocess.lastexecutestatus.successful"/></c:when>
							<c:when test="${ccyProcessStatusList.lastExecuteStatus == 'F'}"><fmt:message key="label.ilmccyprocess.lastexecutestatus.failed"/></c:when>
							<c:when test="${ccyProcessStatusList.lastExecuteStatus == 'C'}"><fmt:message key="label.ilmccyprocess.lastexecutestatus.canceled"/></c:when>
						</c:choose>
					</lastexecutestatus>
					<laststarted clickable="false">${ccyProcessStatusList.lastStartedAsString}</laststarted>
					<lastended clickable="false">${ccyProcessStatusList.lastEndedAsString}</lastended>
					<uniquecolumn clickable="false">${ccyProcessStatusList.id.entityId}${ccyProcessStatusList.id.ccyCode}${ccyProcessStatusList.valueDateAsString}${ccyProcessStatusList.id.processId}</uniquecolumn>
				</row>
			</c:forEach>
		</rows>
	</grid>
	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option value="${entity.value}"
						selected="${requestScope.entityId == entity.value ? '1' : '0'}">${entity.label}</option>
			</c:forEach>
		</select>
		<select id="currency">
			<c:forEach items="${requestScope.currencies}" var="currency">
				<option value="${currency.value}"
						selected="${requestScope.currencyCode == currency.value ? '1' : '0'}">${currency.label}
				</option>
			</c:forEach>
		</select>
	</selects>
</accountattribute>