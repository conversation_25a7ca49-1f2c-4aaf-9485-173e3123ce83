<!DOCTYPE HTML PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.ScenarioCategory"%>
<%@page import="org.swallow.control.model.Scenario"%>

<% 
String scenRcdInstFlag = "";

	//get the screenIdentifier from request attribute 
	if (request.getAttribute("scenRcdInstFlag") != null) {
		scenRcdInstFlag = request.getAttribute("scenRcdInstFlag")
				.toString();
		
	}	
%>
<html>
<head>
<title><fmt:message key="scenarioAmendAssignment.title.window"/></title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">

	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
		window.opener.document.forms[0].method.value="role";
		window.opener.document.forms[0].submit();
		self.close();
	</c:if>
	
	var cancelcloseElements = new Array(1);
	var secentitycol;
	var scenRcdInstFlag ="<%= scenRcdInstFlag %>";
	cancelcloseElements[0] = "cancelbutton";
	
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad()
	{		
		var dropBox1= new SwSelectBox(document.forms[0].elements["scenarioNotification.id.scenarioId"],document.getElementById("scenarioID"));
		var dropBox2= new SwSelectBox(document.forms[0].elements["scenarioNotification.id.roleId"],document.getElementById("RoleID"));
		var dropBox3= new SwSelectBox(document.forms[0].elements["scenarioNotification.id.entityId"],document.getElementById("EntityID"));
		
		document.forms[0].elements['scenarioNotification.fullinstanceaccess'].disabled = scenRcdInstFlag; 
		
		var obj = document.forms[0].elements['scenarioNotification.accessrequired'];
		changePopupHandler();	  
	    if(obj.checked==false){	
			document.forms[0].elements['scenarioNotification.deliverpopups'].disabled = 'true';
			document.forms[0].elements['scenarioNotification.flashicon'].disabled = 'true';
			document.forms[0].elements['scenarioNotification.sendemail'].disabled = 'true';
			document.getElementById("distListButtondiv").innerHTML = document.getElementById("distlistdisablebutton").innerHTML;
	 	}
		document.forms[0].elements['scenarioNotification.sendemail'].value = 'N';

		var objEmail = document.forms[0].elements['scenarioNotification.sendemail'];
		if(objEmail.checked==false){	
			document.getElementById("distListButtondiv").innerHTML = document.getElementById("distlistdisablebutton").innerHTML;
	 	}else{
	 		document.getElementById("distListButtondiv").innerHTML = document.getElementById("distlistenablebutton").innerHTML;
		 	}
	 	
		var methodName = "${methodName}";
		if(methodName == "addRole")
			scenarioChangeHandler('getScenarioIdProperties');
				
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		//Enable or disable save button depending in save button request value
		var saveButton = "<%= request.getAttribute(SwtConstants.SAV_BUT_STS) %>";	
		document.getElementById("savebutton").innerHTML = saveButton=='true'?document.getElementById("saveenablebutton").innerHTML:document.getElementById("savedisablebutton").innerHTML;
	}
	
	/**
	 * Get scenario sec_entity_col and enable or disable role id combobox depending on the value
	 */
	function scenarioChangeHandler(methodName){
	
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ; 
			requestURL = requestURL + appName+"/scenMaintenance.do?method="+methodName;
			requestURL = requestURL + "&scenarioId=" + document.forms[0].elements["scenarioNotification.id.scenarioId"].value;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false );
			oXMLHTTP.send();
			
	 		secentitycol =new String(oXMLHTTP.responseText);
	 		if(secentitycol== "null"){
	 			document.forms[0].elements["scenarioNotification.id.entityId"].disabled="true";
		 	 	document.forms[0].elements["scenarioNotification.id.entityId"].options.selectedIndex = 0;	  	
			}else{
	 			document.forms[0].elements["scenarioNotification.id.roleId"].disabled="";
	 			roleChangeHandler();
			}
	}
	
	function roleChangeHandler(){
		var roleId = document.forms[0].elements["scenarioNotification.id.roleId"].value;
		if(roleId=="All"){
			document.forms[0].elements["scenarioNotification.id.entityId"].value = "All";
			document.forms[0].elements["scenarioNotification.id.entityId"].disabled = "true";
		}else {
			if(secentitycol == "null" && methodName == "addRole"){
				document.forms[0].elements["scenarioNotification.id.entityId"].disabled="true";
		 	 	document.forms[0].elements["scenarioNotification.id.entityId"].options.selectedIndex = 0;	
			} else {
		 		document.forms[0].elements["scenarioNotification.id.entityId"].disabled="";
			}
		}
	}
	
	/**
	 * Change the check of check boxes when checking/uncheking the access required element
	 */
	function changeAccessRequiredHandler(){
		
		var checkedStatus = document.forms[0].elements['scenarioNotification.accessrequired'];
	
		document.forms[0].elements['scenarioNotification.deliverpopups'].disabled =checkedStatus.checked? '':'true';
		document.forms[0].elements['scenarioNotification.flashicon'].disabled = checkedStatus.checked?'':'true';
		document.forms[0].elements['scenarioNotification.sendemail'].disabled = checkedStatus.checked?'':'true';
		document.getElementById("distListButtondiv").innerHTML = document.getElementById("distlistdisablebutton").innerHTML;
		document.forms[0].elements['scenarioNotification.deliverpopups'].checked = '';
  		document.forms[0].elements['scenarioNotification.flashicon'].checked = '';
  		document.forms[0].elements['scenarioNotification.sendemail'].checked = '';
	}
	
	/**
	 * Check of flash icon check box if the deliver popup is checked.
	 */
	function changePopupHandler(){
		
		var checkedStatus = document.forms[0].elements['scenarioNotification.deliverpopups'];
		if (checkedStatus.checked){
			document.forms[0].elements['scenarioNotification.flashicon'].checked = true;
			document.forms[0].elements['scenarioNotification.flashicon'].disabled = "true";
		}else {
			document.forms[0].elements['scenarioNotification.flashicon'].disabled = "";
		}	
	}

	/**
	 * Check of dist list button if the deliver email is checked.
	 */
	function changeEmailHandler(){
		
		var checkedStatus = document.forms[0].elements['scenarioNotification.sendemail'];
		if (checkedStatus.checked){
			document.getElementById("distListButtondiv").innerHTML = document.getElementById("distlistenablebutton").innerHTML;
			
		}else {
			document.getElementById("distListButtondiv").innerHTML = document.getElementById("distlistdisablebutton").innerHTML;
		}	
	}

	/** This function is passes  url base on  screen and display the subscreens 
	* @ param MethodName
	* @ param screenName
	* @ return param
	*/
	function buildDistList(methodName,screenName)
	{
		var param = 'scenMaintenance.do?method='+methodName;
		param+= '&selectedScenarioID='+document.forms[0].elements['scenarioNotification.id.scenarioId'].value;
		param+= '&selectedRoleID='+document.forms[0].elements['scenarioNotification.id.roleId'].value;
		param+= '&selectedEntityID='+document.forms[0].elements['scenarioNotification.id.entityId'].value;
	    return param;  
	}
	
	/**
	 * Submit the form when clicking on save button
	 */
	function submitForm(methodName){
	    enableFields();
   		document.forms[0].elements['scenarioNotification.accessrequired'].value = document.forms[0].elements['scenarioNotification.accessrequired']?'Y':'N';
   		document.forms[0].elements['scenarioNotification.deliverpopups'].value = document.forms[0].elements['scenarioNotification.deliverpopups']?'Y':'N';   	        
   		document.forms[0].elements['scenarioNotification.flashicon'].value =document.forms[0].elements['scenarioNotification.flashicon']? 'Y':'N';
   		document.forms[0].elements['scenarioNotification.sendemail'].value =document.forms[0].elements['scenarioNotification.sendemail']? 'Y':'N';
   		document.forms[0].elements['scenarioNotification.fullinstanceaccess'].value =document.forms[0].elements['scenarioNotification.fullinstanceaccess']? 'Y':'N';
	    document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
	
	/**
	 *  Enable all fields in the screen that are disabled before submitting 
	 */
	function enableFields(){
		document.forms[0].elements["scenarioNotification.id.scenarioId"].disabled = "";
		document.forms[0].elements["scenarioNotification.id.roleId"].disabled = "";
		document.forms[0].elements["scenarioNotification.id.entityId"].disabled = "";	
		document.forms[0].elements["scenarioNotification.sendemail"].disabled = "";
		document.forms[0].elements['scenarioNotification.flashicon'].disabled = "";
	}
	
	/**
	 * validate scenario id and title 
	 */
	function validateForm(objForm){
	  var elementsRef = new Array(2);
		  elementsRef[0] = objForm.elements["scenarioMaintenance.id.scenarioId"];
		  elementsRef[1] = objForm.elements["scenarioMaintenance.title"];
	  return validate(elementsRef);
	}
	
	/**
	* This method is used to check the maximum character length
	* 
	* @param limitField
	* @param limitNum
	 * @return none	 
	*/
	function limitText(limitField, limitNum) {
		//To check the text length for alert message text box
		if(limitField.value.length > limitNum){	
			if(window.event)
				window.event.returnValue = false;
		}
	}
</SCRIPT>
 </head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
	<form action="scenMaintenance.do" onsubmit="validate(this);" style="width: 660px;">
		<input name="method" type="hidden" value="display">
		<input name="selectedScenarioID" type="hidden" value="">
		<input name="oldValue" type="hidden" value= "${oldValue}">
		<input name="emailusers" type="hidden" value="${emailusers}">
		
		<div id="RoleAssignmentAdd" style="position:absolute; left:20px; top:10px; width:640; height:240px; border:2px outset;">
			<div id="ddimagebuttons" color="#7E97AF">
			<div id="RoleAssignmentAdd" style="position:absolute;left:8px; top:6px; width:620; height:110px;">
				<table width="620" border="0" cellpadding="2" cellspacing="0" height="96">
				    <tr height="28px">
				        <td width="145px" ><b><fmt:message key="scenario.scenarioId"/></b></td>
				        <td width="200px">
							<c:if test="${requestScope.methodName == 'changeRole'}">
							<select tabindex="1" id="scenarioNotification.id.scenarioId" name="scenarioNotification.id.scenarioId"
							disabled="true" class="htmlTextAlpha" style="width:190px"
							titleKey="tooltip.selectScenario">
								<c:forEach items="${requestScope.scenarioList}" var="scenario">
								   <option
									  value="${scenario.value}"
									  <c:if test="${scenarioNotification.id.scenarioId == scenario.value}">selected="selected"</c:if>>
									  ${scenario.label}
								   </option>
								</c:forEach></select>
							<td>
								<span id="scenarioID"  name="scenarioID" class="spantext">	
							</td>
							</c:if>
					     	<c:if test="${requestScope.methodName == 'addRole'}">
								<select tabindex="1" id="scenarioNotification.id.scenarioId"
								name="scenarioNotification.id.scenarioId" class="htmlTextAlpha"
								onchange="scenarioChangeHandler('getScenarioIdProperties')" style="width:190px"
								titleKey="tooltip.selectScenario">
								<c:forEach items="${requestScope.scenarioList}" var="scenario">
								   <option
									  value="${scenario.value}"
									  <c:if test="${scenarioNotification.id.scenarioId == scenario.value}">selected="selected"</c:if>>
									  ${scenario.label}
								   </option>
								</c:forEach></select>
								<td>
									<span id="scenarioID"  name="scenarioID" class="spantext">	
								</td>
							</c:if>
						</td>
					</tr>
				    <tr height="28px">
						<td><b><fmt:message key="scenarioNotification.roleID"/></b></td>
						<td>
							<select tabindex="2" id="scenarioNotification.id.roleId" name="scenarioNotification.id.roleId"
							<c:if test="${requestScope.methodName == 'changeRole'? 'true': ''}">disabled</c:if>
							onchange="roleChangeHandler()" class="htmlTextAlpha" style="width:190px"
							titleKey="tooltip.selectRole">
								<c:forEach items="${requestScope.roleIdlist}" var="role">
								   <option
									  value="${role.value}"
									  <c:if test="${scenarioNotification.id.roleId == role.value}">selected="selected"</c:if>>
									  ${role.label}
								   </option>
								</c:forEach></select>
						</td>
						<td>
							<span id="RoleID"  name="RoleID" class="spantext">	
						</td>       
					</tr>
				    <tr height="28px">
				        <td><b><fmt:message key="scenarioNotification.entityID"/></b></td>
				        <td>
							<select tabindex="3" id="scenarioNotification.id.entityId"
							name="scenarioNotification.id.entityId"
							<c:if test="${requestScope.methodName == 'changeRole'? 'true': ''}">disabled</c:if>
							class="htmlTextAlpha" onchange="" style="width:190px"
							titleKey="tooltip.selectEntity">
							<c:forEach items="${requestScope.entities}" var="entity">
								   <option
									  value="${entity.value}"
									  <c:if test="${scenarioNotification.id.entityId == entity.value}">selected="selected"</c:if>>
									  ${entity.label}
								   </option>
							</c:forEach></select>
						</td>       
						<td>
							<span id="EntityID"  name="EntityID" class="spantext">	
						</td>	
					</tr>
					<tr height="28px">
				        <td><b><fmt:message key="scenarioNotification.accessRequired"/></b></td>
				        <td>
							<input type="checkbox" tabindex="4" style="width:13px;" titleKey="tooltip.selectAccessRequired" onclick="changeAccessRequiredHandler()" name="scenarioNotification.accessrequired" value="Y" ${scenarioNotification.accessrequired == 'Y' ? 'checked' : ''} class="htmlTextAlpha"  />
						</td>
					</tr>
				   <tr height="28px">
				   		<td><b><fmt:message key="scenarioNotification.popup"/></b></td>
				   		<td>
							<input type="checkbox" tabindex="5" style="width:13px;" titleKey="tooltip.selectPopUp" name="scenarioNotification.deliverpopups" value="Y"  ${scenarioNotification.deliverpopups == 'Y' ? 'checked' : ''} class="htmlTextAlpha" onclick="changePopupHandler()"  />
						</td>
				   </tr>
				   <tr height="28px">
				        <td><b><fmt:message key="scenarioNotification.flashIcon"/></b></td>
				        <td>
							<input type="checkbox" tabindex="6" style="width:13px;" titleKey="tooltip.selectFlashIcon" name="scenarioNotification.flashicon" value="Y"  ${scenarioNotification.flashicon == 'Y' ? 'checked' : ''} class="htmlTextAlpha"  />
						</td>
				   </tr>
				   <tr height="28px">
				        <td><b><fmt:message key="scenarioNotification.email"/></b></td>
				        <td>
							<input type="checkbox" tabindex="7" style="width:13px;" titleKey="tooltip.selectMail" name="scenarioNotification.sendemail" value="Y"  ${scenarioNotification.sendemail == 'Y' ? 'checked' : ''} class="htmlTextAlpha" onclick="changeEmailHandler()"  />
						</td>
						<td id="distListButtondiv">
						</td>
				   </tr>
				   <tr height="28px">
				        <td><b><fmt:message key="scenarioNotification.fullInstanceAccess"/></b></td>
				        <td>
							<input type="checkbox" tabindex="8" style="width:13px;" titleKey="tooltip.selectFullInstAccess" name="scenarioNotification.fullinstanceaccess" value="Y" ${scenarioNotification.fullinstanceaccess == 'Y' ? 'checked' : ''} class="htmlTextAlpha"  />
						</td>
				   </tr>
				</table>
			</div> 
			</div>
		</div>
		<div id="RoleAssignmentAdd" style="position:absolute; left:580; top:265; width:70; height:29px; visibility:visible;">
			<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="10" href=# onclick="javascript:openWindow(buildPrintURL('print','Amend Role Assignment'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
					</td>
					<td align="right" id="Print">&nbsp;
						<a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
					</td>
				</tr>
			</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:255; width:640px; height:39px; visibility:visible;">
			<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:200px; height:15px; visibility:visible;">
				<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="savebutton"></td>
						<td id="cancelbutton" width="70px">		
							<a title='<fmt:message key="tooltip.cancel"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>			
						</td>
					</tr>
				</table>
			</div>
			<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden;display: none;">  	
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
					<tr>
						<td id="saveenablebutton" width="70"><a tabindex="8"
											title='<fmt:message key="tooltip.save"/>'
											onMouseOut="collapsebutton(this)"
											onMouseOver="highlightbutton(this)"
											onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
											onclick="javascript:submitForm('${methodName eq 'addRole' ? 'saveRole': 'updateRole'}');"><fmt:message key="button.save" /></a></td>
						<td id="savedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>
						</td>
						
						<td id="distlistenablebutton" width="70"><a tabindex="8"
											title='<fmt:message key="tooltip.distributionList"/>'
											onMouseOut="collapsebutton(this)"
											onMouseOver="highlightbutton(this)"
											onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
											onclick="javascript:openWindow(buildDistList('displayDistList','Alert Message'),'alertmessagechange','left=50,top=190,width=775,height=455,toolbar=0, resizable=yes, scrollbars=yes','true')">Dist. List
											</a></td>
						<td id="distlistdisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="scenarioNotification.button.distributionList"/></a>
						</td>
					</tr>
				</table>
			</div>
		</div>
		
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</form>
</body>
</html>
