<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.Shortcut"%>
<html>
<head>
<title><fmt:message key="shortcut.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

mandatoryFieldsArray ="undefined"

<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.document.forms[0].method.value="list";
window.opener.document.forms[0].submit();
self.close();

</c:if>


function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}


function bodyOnLoad()
{

	xl = new XLSheet("shortcutCollTable","table_2", ["String","String", "String"],"211");
	xl.onsort = xl.onfilter = onFilterandSort;

	highlightTableRows("shortcutCollTable");

	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	 <%if (request.getAttribute(SwtConstants.ADD_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
    <%}else{ %>

		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>


	 <%if (request.getAttribute(SwtConstants.CHG_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
    <%}else{ %>

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>


	 <%if (request.getAttribute(SwtConstants.DEL_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
    <%}else{ %>

		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>

}

function buildShortcut(methodName){

   	var param = 'shortcut.do?method='+methodName+'&shortcutId=';
	param += document.forms[0].selectedShorcutId.value;
	param += '&shortcutName=' + document.forms[0].selectedShortcutName.value;
	param += '&screenName=' + document.forms[0].selectedScreenName.value;
	param += '&menuId=' + document.forms[0].selectedMenuId.value;
	return  param;
}

function submitForm(methodName){
 	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}
function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;

	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){

	document.forms[0].submit();

	}
}
function onSelectTableRow(rowElement, isSelected)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedShorcutId.value = hiddenElement.value;
	var hiddenElement = rowElement.getElementsByTagName("input")[2];

	document.forms[0].selectedMenuId.value = hiddenElement.value;

   if(menuEntityCurrGrpAccess == "0" && isSelected)

	{
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}
	else{

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}

}
function populate()
{

	bodyOnLoad();

	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	setParentChildsFocus();

}


</SCRIPT>

</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="populate();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call();">
<form action="shortcut.do" method="post">
<input name="method" type="hidden" value="display">

<input name="selectedShorcutId" type="hidden" value="">
<input name="selectedShortcutName" type="hidden" value="">
<input name="selectedMenuId" type="hidden" value="">
<input name="selectedScreenName" type="hidden" value="">


<input name="menuAccessId" type="hidden" >


<div id="shortcut" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:20px; width:580px; height:206px;">
<div id="shortcut" style="position:absolute;z-index:99;left:0px; top:0px; width:592px; height:23px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="560" border="0" cellspacing="0" cellpadding="0"  height="23">
	<thead>
		<tr>
			<td width="104px" title='<fmt:message key="tooltip.sortShortCutId"/>' height="20px" align="left" style="border-left-width: 0px;padding-top: 2px;"><b><fmt:message key="shortcuts.id.shortcutId"/></b></td>
			<td width="188" title='<fmt:message key="tooltip.sortSortCutName"/>' height="20px" align="left" style="padding-top: 2px;"><b><fmt:message key="shortcuts.shortcutName"/></b></td>
			<td width="266" title='<fmt:message key="tooltip.sortScreenName"/>' height="20px" align="left" style="padding-top: 2px;"><b><fmt:message key="shortcuts.menuItemId"/></b></td>

		</tr>
	</thead>
</table>
</div>

	<div id="ddscrolltable"
		style="position:absolute;left: 0px; top: 0px; width: 577; height: 200px; overflow-y: scroll;overflow-x: hidden">
<div id="shortcut" style="position:absolute;z-index:99;left:1px; top:20px; width:590px; height:10px;">
<table class="sort-table" id="shortcutCollTable" width="560" border="0" cellspacing="1" cellpadding="0" height="180">
	<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="item" items="${requestScope.shortcutColl}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <!-- Start: Code for mantis 1580: "Spaces should not be saved to end of input values." -->
            <td width="104px" align="left">
                <input type="hidden" name="id.shortcutId" value="${item.id.shortcutId}" />
                ${item.id.shortcutId}&nbsp;
            </td>
            <td width="188px" align="left">
                <input type="hidden" name="shortcutName" value="${item.shortcutName}" />
                ${item.shortcutName}&nbsp;
            </td>
            <td width="266px" align="left">
                <input type="hidden" name="menuItemId" value="${item.menuItemId}" />
                ${item.menuName}&nbsp;
            </td>
            <!-- End: Code for mantis 1580 -->
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>

	<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>
</div>
<div id="shortcut" style="position:absolute; left:530; top:240px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Shortcut '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" ></a>
		</td>

			<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:232; width:580px; height:39px; visibility:visible;">
  <div id="shortcut" style="position:absolute; left:6; top:4; width:580px; height:15px; visibility:visible;">
  	  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		 <td id="addbutton" width="70px"></td>


			<td id="changebutton" width="70px"></td>

			<td id="deletebutton" width="70px"></td>
			<td id="closebutton" width="70px">
				<a title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
	</div>
	<div style="position:absolute; left:6; top:4; width:580px; height:15px; visibility:hidden;">
		<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
		<tr>
		   <td id="addenablebutton">
		     <a  title='<fmt:message key="tooltip.addNewShortCut"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildShortcut('add'),'AddShortcutWindow','left=50,top=190,width=517,height=170,toolbar=0, resizable=yes, scrollbars=yes')"><fmt:message key="button.add"/></a>
			</td>
			<td id="adddisablebutton">
			<a  class="disabled" disabled="disabled" ><fmt:message key="button.add"/></a>
			</td>
			<td id="changeenablebutton">
				<a  title='<fmt:message key="tooltip.changeSelectedShortcut"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"onClick="javascript:openWindow(buildShortcut('change'),'ChangeShortcutWindow','left=50,top=190,width=517,height=170,toolbar=0, resizable=yes, scrollbars=yes')"><fmt:message key="button.change"/></a>
			</td>
			<td id="changedisablebutton">
				<a class="disabled" title='<fmt:message key="tooltip.changeSelectedShortcut"/>' disabled="disabled"><fmt:message key="button.change"/></a>
			</td>

			<td id="deleteenablebutton">
				<a title='<fmt:message key="tooltip.delSelShortcut"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')"><fmt:message key="button.delete"/></a>
			</td>
			<td id="deletedisablebutton">
				<a class="disabled" title='<fmt:message key="tooltip.delSelShortcut"/>' disabled="disabled"><fmt:message key="button.delete"/></a>
			</td>
		</tr>
		</table>
	  </div>
	</div>
   <script type="text/javascript">

 </script>
</form>
</body>
<script>


<c:if test="${requestScope.afterDeleteRefresh == 'yes'}">

	  getMenuWindow().refreshShortcuts();
      document.forms[0].method.value="list";
	  document.forms[0].submit();


</c:if>
</script>
</html>