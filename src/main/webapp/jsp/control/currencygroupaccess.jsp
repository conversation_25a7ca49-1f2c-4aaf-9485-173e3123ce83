<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:if test="${'yes' != requestScope.isViewRole}">

	<c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

		<fmt:message key="currencyGroupAccess.addScreen"/>

</c:if>

	<c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

		<fmt:message key="currencyGroupAccess.changeScreen"/>

</c:if>

	<c:if test="${'copyFrom' == sessionScope.roleMaintenanceOperation}">

   		 <c:if test="${'addRole' == requestScope.screenTitle}">
			<fmt:message key="currencyGroupAccess.addScreen"/>

</c:if>

		<c:if test="${'changeRole' == requestScope.screenTitle}">

			<fmt:message key="currencyGroupAccess.changeScreen"/>

</c:if>

</c:if>


</c:if>
<c:if test="${'yes' == requestScope.isViewRole}">

	<fmt:message key="currencyGroupAccess.viewScreen"/>

</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="add";
window.opener.document.forms[0].clearTempRecords.value="false";
/*-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
if ('<%=request.getParameter("accountAccessSize")%>'!='null')
	window.opener.document.forms[0].accountAccessSize1.value = '<%=request.getParameter("accountAccessSize")%>';
/*--End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
window.opener.document.forms[0].submit();
self.close();

</c:if>
var isAnyEntityNoAccess=true; //this variable checks if at  least one entity is selected for full || read only access
var entityAccess = '${requestScope.entAccess}';
var isViewRole = '${requestScope.isViewRole}';

var radiobuttonStatusArr;
var initialEntityId;
var initialEntityName;

function submitFormOk(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function validateForm(){
 var table = document.getElementById("currencyGroupList");
 var tbody = table.getElementsByTagName("tbody")[0];
 var rows = tbody.getElementsByTagName("tr");
	for (i=0; i < rows.length; i++)
	{
		var colls = rows[i].getElementsByTagName("td");
		var radioElement1 =colls[2].getElementsByTagName("input")[0];
		var radioElement2 =colls[3].getElementsByTagName("input")[0];

		if(radioElement1.checked || radioElement2.checked)
		{
			isAnyEntityNoAccess=false;
			break;
		}
	 }
}

function bodyOnLoad(){

	document.forms[0].isViewRole.value = isViewRole;
	window.opener.document.forms[0].limitButtonStatus.value = '${requestScope.limitButtonStatus}';
	/*-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
	if ('<%=request.getParameter("accountAccessSize")%>'!='null')
		window.opener.document.forms[0].accountAccessSize1.value = '<%=request.getParameter("accountAccessSize")%>';
	/*-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
	var cancelcloseElements = new Array(1);
    cancelcloseElements[0] = "closebutton";
	if(entityAccess == "1")
	{
		radioStatusByEntityAccess();
	}

	getExistingDetails();
	var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyGroup.id.entityId"],document.getElementById("entityName"));
	initialEntityId = document.forms[0].elements["currencyGroup.id.entityId"].value;
	initialEntityName = document.getElementById("entityName").innerText;

}

function getExistingDetails()
{
	var table = document.getElementById("currencyGroupList");
	var tbody = table.getElementsByTagName("tbody")[0];
	var rows = tbody.getElementsByTagName("tr");

	radiobuttonStatusArr = new Array(rows.length);
	for (i=0; i < rows.length; i++)
	{
		var colls = rows[i].getElementsByTagName("td");
		var radioElement1 =colls[2].getElementsByTagName("input")[0];
		var radioElement2 =colls[3].getElementsByTagName("input")[0];
		var radioElement3 =colls[4].getElementsByTagName("input")[0];

		if(radioElement1.checked)
		{
			radiobuttonStatusArr[i] = 1;
		}else if(radioElement1.checked){
			radiobuttonStatusArr[i] = 2;
		}else
		{
			radiobuttonStatusArr[i] = 3;
		}
	 }
}


function radioStatusByEntityAccess()
{
 var table = document.getElementById("currencyGroupList");
 var tbody = table.getElementsByTagName("tbody")[0];
 var rows = tbody.getElementsByTagName("tr");

	for (i=0; i < rows.length; i++)
	{
		var colls = rows[i].getElementsByTagName("td");
		var radioElement1 =colls[2].getElementsByTagName("input")[0];
		var radioElement2 =colls[3].getElementsByTagName("input")[0];
		radioElement1.disabled = true;
	 }
}

function onChangeEntityId(methodName){

	var table = document.getElementById("currencyGroupList");
	var tbody = table.getElementsByTagName("tbody")[0];
	var rows = tbody.getElementsByTagName("tr");
	var temp = new Array(rows.length);
	var recordChanged = false;
	for (i=0; i < rows.length; i++) {
		var colls = rows[i].getElementsByTagName("td");
		var radioElement1 =colls[2].getElementsByTagName("input")[0];
		var radioElement2 =colls[3].getElementsByTagName("input")[0];
		var radioElement3 =colls[4].getElementsByTagName("input")[0];

		if(radioElement1.checked)
		{
			temp[i] = 1;
		}else if(radioElement1.checked){
			temp[i] = 2;
		}else
		{
			temp[i] = 3;
		}
	 }

	for (i=0; i < rows.length; i++) {
		if(temp[i] != radiobuttonStatusArr[i]){
			recordChanged = true;
			break;
		}

	}

	if(recordChanged == true){

		var yourstate= window.confirm('<fmt:message key="confirm.changeCcyGroupAccessEntityId"/>');
		if (yourstate==true){
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}else{
			document.forms[0].elements["currencyGroup.id.entityId"].value = initialEntityId;
			document.getElementById("entityName").innerText = initialEntityName;
		}
	}else {
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}


}
</SCRIPT>
</head>

<form action="role.do" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">
<input name="isViewRole" type="hidden" value="">
<!-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 -->
<input name="accountAccessSize" type="hidden" value='<%=request.getParameter("accountAccessSize")%>'>
<!--End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 -->
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">


<div id="CurrencyGroup" style="position:absolute; left:20px; top:20px; width:720px; height:42px; border:2px outset;" color="#7E97AF">
<div id="CurrencyGroup" style="position:absolute; left:8px; top:4px; width:720px; height:400;">
<table width="468" border="0" cellpadding="0" cellspacing="0" height="30">
	<tr color="black" border="0">
	  <td width="38"><b><fmt:message key="bookCode.entity"/></b></td>
	  <td width="28">&nbsp;</td>
	  <td width="140px">
		  <select id="currencyGroup.id.entityId" name="currencyGroup.id.entityId" class="htmlTextAlpha"
				  titleKey="tooltip.selectEntityid" style="width:140px" tabindex="1"  onchange="onChangeEntityId('displayCurrencyGroupAccessDetails')">
			  <c:forEach items="${requestScope.entities}" var="entity">
				  <option value="${entity.value}" <c:if test="${currencyGroup.id.entityId == entity.value}">selected</c:if> >
						  ${entity.label}
				  </option>
			  </c:forEach>
		  </select>

	  </td>
	   <td width="20">&nbsp;</td>
	  <td width="280">
		<span id="entityName" name="entityName" class="spantext">
	   </td>
	 </tr>
</table>
</div>
</div>





<div id="Role" style="position:absolute; left:20px; top:68px; width:720px; height:336px; border-left:0px outset;" color="#7E97AF">
	<div id="Role" style="position:absolute;z-index:99;left:0px; top:0px; width:700px; height:10px;">
		<table class="sort-table"   width="700px" border="0" cellspacing="1" cellpadding="0"  height="20px">
			<thead>
				<tr height="20px" >
					<td width="115px" title='<fmt:message key="tooltip.currencyGroupId"/>' class="topbar" style="border-left-width: 0px;"><b><fmt:message key="currency.group"/></b></td>
					<td width="280px" title='<fmt:message key="tooltip.currencyGroupName"/>' class="topbar"><b><fmt:message key="section.sectionName"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.fullAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.fullAccess"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.viewAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.readOnly"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.noAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.noAccess"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:716px; height:331px; overflow-x:hidden;">
		<div id="Role" style="position:absolute;z-index:99;left:0px; top:22px; width:700px; height:10px;">
			<table id="currencyGroupList" class="sort-table" width="700px" border="0" cellspacing="1" cellpadding="0" height="309px">
				<tbody>
					<%int count = 0; %>
					<c:forEach items="${requestScope.currencyGroupList}" var="currencyGroupList" >
						<% if( count%2 == 0 ) {%><tr class="evencheckbox"><% }else  { %> <tr class="oddcheckbox"> <%}++count; %>
								<input type="hidden" name="entityId" value="${currencyGroupList.entityId}" />
								<td width="115px" align="left">${currencyGroupList.currencyGroupId}&nbsp;</td>
								<td width="280px">${currencyGroupList.currencyGroupName}&nbsp;</td>

							<c:if test="${'yes' == requestScope.isViewRole}">
								<td width="100px" align="center"><input type=radio name="ccyGrpAccessList<%=count%>" value="0" ${currencyGroupList.ccyGrpAccessHTML1 == 'checked' ? 'checked="checked"' : ''} disabled="true"/></td>
								<td width="100px" align="center"><input type=radio name="ccyGrpAccessList<%=count%>" value="1" ${currencyGroupList.ccyGrpAccessHTML2 == 'checked' ? 'checked="checked"' : ''} disabled="true"/></td>
								<td width="100px" align="center"><input type=radio name="ccyGrpAccessList<%=count%>" value="2" ${currencyGroupList.ccyGrpAccessHTML3 == 'checked' ? 'checked="checked"' : ''} disabled="true"/></td>
							</c:if>

							<c:if test="${'yes' != requestScope.isViewRole}">
							    <td width="100px" align="center"><input type=radio name="ccyGrpAccessList<%=count%>" value="0" ${currencyGroupList.ccyGrpAccessHTML1 == 'checked' ? 'checked="checked"' : ''} /></td>
							    <td width="100px" align="center"><input type=radio name="ccyGrpAccessList<%=count%>" value="1" ${currencyGroupList.ccyGrpAccessHTML2 == 'checked' ? 'checked="checked"' : ''} /></td>
							    <td width="100px" align="center"><input type=radio name="ccyGrpAccessList<%=count%>" value="2" ${currencyGroupList.ccyGrpAccessHTML3 == 'checked' ? 'checked="checked"' : ''} /></td>
							</c:if>

						</tr>
					</c:forEach>
					</tbody>
					<tfoot><tr><td colspan="5" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<div id="Role" style="position:absolute; left:660; top:419px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
		  <!--Betcy:12/11/08:Added:To display the help content properly(start)-->
			<c:if test="${'yes' != requestScope.isViewRole}">

				<c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

					<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Currency Group Access '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>
				<c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

					<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Currency Group Access  '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>

</c:if>
		<c:if test="${'yes' == requestScope.isViewRole}">

			<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','View Currency Group Access  '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>
		 <!--Betcy:12/11/08:Added:To display the help content properly(end)-->
		  </td>



			<td align="right" id="Print">
				<a tabindex="3" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:408; width:720px; height:39px; visibility:visible;">
	<div id="Role" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
		<table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
			<c:if test="${'yes' == requestScope.isViewRole}">

				<tr>
					<td title='<fmt:message key="tooltip.close"/>' width="70px" id="closebutton">
						<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
					</td>
				</tr>

</c:if>

			<c:if test="${'yes' != requestScope.isViewRole}">

				<tr>
				<td width="70px" id="saveenablebutton" title='<fmt:message key="tooltip.ok"/>' >
					<a tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitFormOk('saveCurrencyGroupAccessDetails');"><fmt:message key="button.ok"/></a></td>

					<!-- <td width="70px" id="cancelbutton" title='<fmt:message key="tooltip.close"/>'>
						<a title='<fmt:message key="tooltip.cancel"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a> -->

					</td>
					<td title='<fmt:message key="tooltip.close"/>' width="70px" id="closebutton">
						<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
					</td>
				</tr>

</c:if>
		</table>
	</div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>