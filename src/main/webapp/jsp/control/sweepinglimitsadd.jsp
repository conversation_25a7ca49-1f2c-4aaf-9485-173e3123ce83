<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.methodName == 'sweepLimitSave'}">
        <title><fmt:message key="sweepinglimits.addScreen"/></title>
    </c:when>
    <c:when test="${requestScope.methodName == 'update'}">
        <title><fmt:message key="sweepinglimits.changeScreen"/></title>
    </c:when>
    <c:when test="${requestScope.methodName == 'view'}">
        <title><fmt:message key="sweepinglimits.viewScreen"/></title>
    </c:when>
</c:choose>


<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">

var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";

var sweepLimitCurrName = '${requestScope.sweepLimitCurrName}';
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="sweepLimit";
window.opener.document.forms[0].submit();
self.close();
</c:if>

/**
	 * submitForm
	 *
	 * This method is used to submit the form while perform button operation on ok button.
	 **/

function submitForm(methodName){

<!-- start: code modified by nageswararao for mantis 1597 on 20120306-->
var amount=validateCurrency(document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'],'sweepLimits.sweepLimitAsStringAnother',currencyFormat,document.forms[0].elements['sweepLimits.id.currencyCode'].value);
<!--End: code modified by nageswararao for mantis 1597 on 20120306-->

if(amount)
{


  if(validateForm(document.forms[0]) )
  {
  enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
  }
	
  }
  else
  {
     document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'].focus();
  }

}

      /**
 	  * onChangeCurrency
      * 			
      * This method is called while changing the currency code to the collection in list box.  
      */  
<!-- start: code modified by nageswararao for mantis 1597 on 20120306-->	  
	function onChangeCurrency() {

	if(document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'].value !=""){
		    var amount = validateCurrency(document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'], 'movement.amountAsString', currencyFormat,document.forms[0].elements['sweepLimits.id.currencyCode'].value);
		}	
		}
	<!--End: code modified by nageswararao for mantis 1597 on 20120306-->	  
function enableFields(){
	document.forms[0].elements["sweepLimits.id.currencyCode"].disabled = "";
}


function bodyOnLoad()
{
	<c:if test="${requestScope.methodName == 'sweepLimitSave'}">
	var currencydropBox = new SwSelectBox(document.forms[0].elements["sweepLimits.id.currencyCode"], document.getElementById("currencyName"));
	</c:if>

	<c:if test="${requestScope.methodName != 'sweepLimitSave'}">
	document.getElementById("currencyName").innerText = '${requestScope.sweepLimitCurrName}';
	</c:if>

	<c:if test="${requestScope.sweepLimitExiting == 'yes'}">
	alert("<fmt:message key='alert.recordAlreadyExists'/>");
	</c:if>
}

function validateForm(objForm){
  var elementsRef = new Array();
  elementsRef[0] = objForm.elements["sweepLimits.id.currencyCode"]; 
  elementsRef[1] = objForm.elements["sweepLimits.sweepLimitAsStringAnother"]; 

  return validate(elementsRef);
}

var currencyFormat = '${sessionScope.CDM.currencyFormat}';
</SCRIPT>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');" onunload="call()">
<form action="sweepLimits.do"  onsubmit="validate(this);" method="post">
<input name="method" type="hidden" value="display">
<input name="dummy" value="" style="visibility:hidden">

<div id="sweepLimitsAdd" style="position:absolute; left:20px; top:20px; width:559px; height:61px; border:2px outset;" color="#7E97AF">
	<div id="sweepLimitsAdd" style="position:absolute;z-index:99;left:8px; top:4px; width:430px; height:35px;">
		<table width="541px" border="0" cellpadding="0" cellspacing="0" height="50">
		<tr>
			<td width="93px">
				<b>
					<fmt:message key="role.sweepLimits.currencyCode"/>
				</b>
				<c:if test="${requestScope.methodName == 'sweepLimitSave'}">*</c:if>
			</td>

			<td width="28px">&nbsp;</td>
			<td width="420px">
				<c:choose>
					<c:when test="${requestScope.methodName == 'sweepLimitSave'}">
						<select tabindex="1" titleKey="tooltip.selectCurrencyId" id="sweepLimits.id.currencyCode"
								name="sweepLimits.id.currencyCode" onchange="onChangeCurrency()"
								disabled="${attr.screenFieldsStatus}" style="width:55px;">
							<c:forEach var="currency" items="${requestScope.currencyMaster}">
								<option value="${currency.value}" ${currency.value == sweepLimits.id.currencyCode ? 'selected="selected"' : ''}>${currency.label}</option>
							</c:forEach>
						</select>
					</c:when>

					<c:otherwise>
						<input type="text" tabindex="1" name="sweepLimits.id.currencyCode" titleKey="tooltip.selectCurrencyId"
							   class="htmlTextAlpha" style="width:40px;" disabled="true" value="${sweepLimits.id.currencyCode}"/>
					</c:otherwise>
				</c:choose>

				&nbsp;&nbsp;&nbsp;
				<span id="currencyName" name="currencyName" class="spantext"></span>
			</td>
		</tr>

		<tr>
			<td width="93px">
				<b>
					<fmt:message key="role.sweepLimits.sweepLimit"/>
				</b>
				<c:if test="${requestScope.methodName != 'view'}">*</c:if>
			</td>

			<td width="28px">&nbsp;</td>

			<td width="420px">
				<c:choose>
					<c:when test="${requestScope.methodName == 'view'}">
						<input type="text" titleKey="tooltip.enterSwpLimit" name="sweepLimits.sweepLimitAsStringAnother"
							   class="htmlTextNumeric" style="width:195px;" maxlength="28"
							   onchange="return validateCurrency(this,'movement.amountAsString',currencyFormat, document.forms[0].elements['sweepLimits.id.currencyCode'].value)"
							   disabled="${attr.screenFieldStatus}" value="${sweepLimits.sweepLimitAsStringAnother}"/>
					</c:when>
					<c:otherwise>
						<input type="text" tabindex="2" titleKey="tooltip.enterSwpLimit" class="htmlTextNumeric"
							   name="sweepLimits.sweepLimitAsStringAnother" maxlength="28" style="width:195px;"
							   onchange="return validateCurrency(this,'movement.amountAsString',currencyFormat, document.forms[0].elements['sweepLimits.id.currencyCode'].value)"
							   value="${sweepLimits.sweepLimitAsStringAnother}"/>
					</c:otherwise>
				</c:choose>
			</td>
		</tr>

	</table>
</div>
</div>

<div id="sweepLimitsAdd" style="position:absolute; left:510; top:94px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				<c:choose>
					<c:when test="${requestScope.methodName == 'sweepLimitSave'}">
						<a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','Add Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
							<img src="images/help_default.GIF " name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>">
						</a>
					</c:when>
					<c:when test="${requestScope.methodName == 'update'}">
						<a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','Change Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
							<img src="images/help_default.GIF " name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>">
						</a>
					</c:when>
					<c:when test="${requestScope.methodName == 'view'}">
						<a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','View Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
							<img src="images/help_default.GIF " name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>">
						</a>
					</c:when>
				</c:choose>
			</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();"
				   onMouseOut="MM_swapImgRestore()"
				   onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)">
					<img src="images/Print.gif " name="Print" border="0" title="<fmt:message key='tooltip.printScreen'/>">
				</a>
			</td>
		</tr>

	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:85px; width:559px; height:39px; visibility:visible;">
<div id="sweepLimitsAdd" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
  <c:choose>
    <c:when test="${requestScope.viewButtonStatus != 'view'}">
        <tr>
            <!-- Save Button -->
            <td width="70px">
                <a title="<fmt:message key='tooltip.ok'/>" tabindex="3"
                   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                   onClick="javascript:submitForm('${methodName}');">
                    <fmt:message key="button.ok"/>
                </a>
            </td>

            <!-- Cancel Button -->
            <td id="cancelbutton" width="70px">
                <a title="<fmt:message key='tooltip.cancel'/>" tabindex="4"
                   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                   onclick="javascript:window.close();">
                    <fmt:message key="button.cancel"/>
                </a>
            </td>
        </tr>
    </c:when>

    <c:otherwise>
        <tr>
            <!-- View Button -->
            <td id="closebutton" width="70px">
                <a title="<fmt:message key='tooltip.close'/>" tabindex="5"
                   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                   onclick="confirmClose('P');">
                    <fmt:message key="button.close"/>
                </a>
            </td>
        </tr>
    </c:otherwise>
</c:choose>


	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>