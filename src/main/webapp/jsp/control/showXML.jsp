<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><fmt:message key="addJob.title.showXML"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad() {
		var schedulerConfigXML = getMenuWindow().decode64("${requestScope.schedulerConfigXML}");
		var div = $("#DistributionList");
		 var txt2  = $('<textarea style="height:100%;width:100%;border-left:0px outset;"></textarea>').text(schedulerConfigXML);
		 $(txt2).attr('readonly', true);
		$(div).append( txt2);
	}

	function formatXml(xml) {
	    var formatted = '';
	    //var reg = /(>)(<)(\/*)/g;
	    var reg = /(>)(<)([a-z])/g;
	    xml = xml.replace(reg, '$1\r\n$2$3');
	    var pad = 0;
	    jQuery.each(xml.split('\r\n'), function(index, node) {
	        var indent = 0;
	        if (node.match( /.+<\/\w[^>]*>$/ )) {
	            indent = 0;
	        } else if (node.match( /^<\/\w/ )) {
	            if (pad != 0) {
	                pad -= 1;
	            }
	        } else if (node.match( /^<\w[^>]*[^\/]>.*$/ )) {
	            indent = 1;
	        } else {
	            indent = 0;
	        }

	        var padding = '';
	        for (var i = 0; i < pad; i++) {
	            padding += '  ';
	        }
	        formatted += padding + node + '\r\n';
	        pad += indent;
	    });

	    return formatted;
	}

</SCRIPT>
</head>



<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
<form action="scenMaintenance.do" method="post" onsubmit="return validate(this);">
	<input name="method" type="hidden" value="display">
	<div id="DistributionList" style="position:absolute; border:2px outset; left:10px; top:33px; width:523px; height:365px; border-left:0px outset;" color="#7E97AF">
	</div>
<div id="DistributionListHelp" style="left: 450px; top: 418px; width: 70px; height: 29px; visibility: visible; position: absolute; z-index: 150;">
	<table width="60" height="20" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr>
				<td align="Right">
					<a tabindex="10" title="Help screen content" onmouseover="MM_swapImage('Help','','images/help_default.GIF ',1)" onmouseout="MM_swapImgRestore()" onclick="javascript:openWindow(buildPrintURL('print','Distribution List'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" href="#"><img name="Help" src="images/help_default.GIF " border="0"></a>
				</td>
				<td align="right" id="Print">&nbsp;
					<a tabindex="11" onmouseover="MM_swapImage('Print','','images/Print_R.gif ',1)" onmouseout="MM_swapImgRestore()" onclick="printPage();"><img name="Print" title="Print screen content" src="images/Print.gif " border="0"></a>
				</td>
			</tr>
		</tbody>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:410; width:523px; height:39px; visibility:visible;">
	<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:200px; height:15px; visibility:visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td id="savebutton">
					<a title='<fmt:message key="tooltip.ok"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"  onclick="confirmClose('P');"><fmt:message key="button.ok"/></a>
				</td>
			</tr>
		</table>
	</div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>