<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.control.model.Role"%>
<%@page import="org.swallow.mfa.RegisterMFA"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
	<c:if test="${'save' == requestScope.methodName}">

		<title><fmt:message key="rolemaintenance.addScreen"/></title>

</c:if>
	<c:if test="${'update' == requestScope.methodName}">

		<title><fmt:message key="rolemaintenance.changeScreen"/></title>

</c:if>
	<c:if test="${'view' == requestScope.methodName}">

		<title><fmt:message key="rolemaintenance.viewScreen"/></title>

</c:if>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
	<script type="text/javascript" src="js/jquery.exportselect.js"></script>
	<SCRIPT language="JAVASCRIPT">
	<c:if test="${'yes' == requestScope.parentFormRefresh}">

		window.opener.document.forms[0].method.value="display";
		window.opener.document.forms[0].accessAltered.value = '${requestScope.accessAltered}'; // whether access of any menu is altered
		window.opener.document.forms[0].submit();
		self.close();


</c:if>
	mandatoryFieldsArray = ["*"];
		var cancelcloseElements = new Array(1);
		cancelcloseElements[0] = "cancelbutton";
	//This variable stores the selected role Id incase of change
		var roleIdSelected = "";
		var isViewRole = '${requestScope.isViewRole}';
		var screenTitle = '${requestScope.screenTitle}';
		var accountAccessSize = 0;
	/** This function is passes  url base on  screen && display the subscreens
	* @ param MethodName
	* @ param screenName
	* @ return param
	*/
	function buildAddRole(methodName,screenName)
	{
		if(methodName == 'sweepLimit'){
			var limitButtonStatus = document.forms[0].limitButtonStatus.value;
		if(limitButtonStatus == 'enable'){
			var param = 'sweepLimits.do?method='+methodName+'&isViewRole=';
			param += isViewRole;
			javascript:openWindow(param,'sweepingLimitsWindow','left=50,top=190,width=505px,height=458px,toolbar=0, resizable=yes, status=yes, scrollbars=yes')
		}
		else{
			ShowErrMsgWindowWithBtn("",'<fmt:message key="currencyGroup.access"/>', null);
		}
		}
		if(methodName == 'ccyGrpAccess'){
			var param = 'role.do?method='+'displayCurrencyGroupAccessDetails'+'&isViewRole=';
			param += isViewRole;
			param+="&accountAccessSize="+accountAccessSize;
			return param;
		}
		// check the method name && open the add || change screen
		if(methodName == 'accountAccess'){
			var limitButtonStatus = document.forms[0].limitButtonStatus.value;
		if(limitButtonStatus == 'enable'){
		if (screenName=="addScreen")
			javascript:openWindow(buildAccountAccessControl('addScreen'),'restrictLocationWindow','left=50,top=190,width=1030,height=530,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')
		else
			javascript:openWindow(buildAccountAccessControl('changeScreen'),'restrictLocationWindow','left=50,top=190,width=1030,height=530,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')
		}
		else{
			alert('<fmt:message key="currencyGroup.access"/>');
		}
		}
			var param = 'role.do?method='+methodName+'&isViewRole=';
			param += isViewRole;
			param+='&roleIdSelected=';
			param+= roleIdSelected;
			param+='&screenTitle=';
			param+= screenTitle;
			param+='&newRoleId=' + document.forms[0].elements["role.roleId"].value;
			param+="&accountAccessSize="+accountAccessSize;
			return param;
	}
	/**
 	  * submitForm
      *
      * This method is used to submit form while clicking on the save/update button.
	*/
	function submitForm(methodName)
	{
		// Added for mantis 1443, Enhanced Alerting
    	if(document.forms[0].elements['role.roleId'].value.toUpperCase() == 'ALL' && methodName != "cancel"){
			alert('<fmt:message key="alert.error.creationWithAllRole"/>');
    		return;
    	}
 		obj = document.forms[0].elements['role.accountAccessControl'];
 		if(methodName != 'cancel' && obj.checked==true && accountAccessSize > 0)
			alert('<fmt:message key="message.alert.accountAccess"/>');
 			var roleId=validateField(document.forms[0].elements['role.roleId'],'role.roleId','alphaNumPat');
  		if(roleId)
 		{
    		var roleName=validateField(document.forms[0].elements['role.roleName'],'role.roleName','alphaNumPatWithSpace');
    	if(roleName)
		{
    		elementTrim(document.forms[0]);
 		if(methodName != 'cancel')
  		{

		if(validateForm(document.forms[0]))
		{
			enableFields();
		if(!document.forms[0].elements["role.advancedUser"].checked)
		{
			document.forms[0].elements["role.advancedUser"].value="N";
			document.forms[0].elements["role.inputInterruption"].value="N";
		}
		else
		{
	    	document.forms[0].elements["role.advancedUser"].value="Y";
			document.forms[0].elements["role.inputInterruption"].value="Y";
		}

		document.forms[0].elements["role.maintainAnyIlmScenario"].value = document.forms[0].elements["role.maintainAnyIlmScenario"].checked == true?"Y":"N";
		document.forms[0].elements["role.maintainAnyIlmGroup"].value = document.forms[0].elements["role.maintainAnyIlmGroup"].checked == true?"Y":"N";
		document.forms[0].elements["role.maintainAnyReportHist"].value = document.forms[0].elements["role.maintainAnyReportHist"].checked == true?"Y":"N";

		document.forms[0].clearTempRecords.value = "false"
		document.forms[0].method.value = methodName;
		document.forms[0].submit();

		}
  		}
  		else
		{
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
			window.opener.popupOpened=false;
		}
		}
		else
		{
	  		document.forms[0].elements['role.roleName'].focus();
		}
		}
		else
		{
	   		document.forms[0].elements['role.roleId'].focus();
		}
	}


	function enableFields()
	{
			document.forms[0].elements["role.roleId"].disabled = "";
	}
	function disableFields()
	{
			document.forms[0].elements["role.roleId"].disabled = "true";
	}
	function validateForm(objForm)
	{
  			var elementsRef = new Array(3);
  			elementsRef[0] = objForm.elements["role.roleId"];
  			elementsRef[1] = objForm.elements["role.alertType"];
  			elementsRef[2] = objForm.elements["role.roleName"];
  			return validate(elementsRef);
	}


	function checkChangePasswordAcess(){
		var isUseSmartAuthenticator = "<%=RegisterMFA.getInstance().isUseSmartAuthenticator()%>";
		var isAllowInternalAuthentication = "<%=RegisterMFA.getInstance().isAllowInternalAuthentication()%>";
		if(isUseSmartAuthenticator=="true" && isAllowInternalAuthentication =="true"){

<%--     			<%=Collection<MenuAccessOptionsGui> menuAccess = request.getAttribute("itemDescList")%> --%>

// 			itemDescList

			var accessToChangePassword='<%=SwtUtil.getMenuHasFullAccessForRoleFromRequest(""+43,request)%>';
			if(accessToChangePassword != "true"){
				var messageToShow = "<fmt:message key="role.password.noaccess.to.facility"/>";
					Swal.fire({
						  html: messageToShow,
						  showCancelButton: false,
						  width:350,
						  showClass: {
							    popup: '',
							    icon: ''
							  },
							  hideClass: {
							    popup: '',
							  },
						  confirmButtonText: 'OK',
						});


			}
		}else {
			if(isUseSmartAuthenticator=="false"){
				<c:if test="${'update' == requestScope.methodName}">

				var accessToChangePassword='<%=SwtUtil.getMenuHasFullAccessForRoleFromRequest(""+43,request)%>';
				if(accessToChangePassword != "true"){
					var messageToShow = "<fmt:message key="role.password.noaccess.to.facility"/>";
						Swal.fire({
							  html: messageToShow,
							  showCancelButton: false,
							  width:350,
							  showClass: {
								    popup: '',
								    icon: ''
								  },
								  hideClass: {
								    popup: '',
								  },
							  confirmButtonText: 'OK',
							});


				}

</c:if>

					<c:if test="${'view' == requestScope.methodName}">

					var accessToChangePassword='<%=SwtUtil.getMenuHasFullAccessForRoleFromRequest(""+43,request)%>';

				if(accessToChangePassword != "true"){
					var messageToShow = "<fmt:message key="role.password.noaccess.to.facility"/>";
						Swal.fire({
							  html: messageToShow,
							  showCancelButton: false,
							  width:350,
							  showClass: {
								    popup: '',
								    icon: ''
								  },
								  hideClass: {
								    popup: '',
								  },
							  confirmButtonText: 'OK',
							});


				}

</c:if>
			}
		}
	}
	/*Start Code:Modified by Mariappan for Mantis 2076 */
		/**
 	 	 * bodyOnLoad
      	 *
      	 * This method is called when the screen is onloaded to set the status for elements.
	     */
 	function bodyOnLoad()
 	{


 		 	<c:if test="${'yes' != requestScope.parentFormRefresh}">


 		 	<c:if test="${'yes' != requestScope.cancelFlag}">

				var accessToHigherLevel='<%=SwtUtil.checkAccessToHigherMenuItemsFromRequest(request)%>';
				if(accessToHigherLevel != "false"){
					var messageToShow = "<fmt:message key="role.noaccess.higherlevel"/>";

						Swal.fire({
							  html: messageToShow,
							  showCancelButton: true,
							  cancelButtonText: "<fmt:message key="role.button.showList"/>",
							  width:350,
							  showClass: {
								    popup: '',
								    icon: ''
								  },
								  hideClass: {
								    popup: '',
								  },
							  confirmButtonText: 'OK',
	// 						  onClose: function () {
	// 					         alert('eeeee'); // Close the window when the alert is closed
	// 					        }
							}).then(function(result) {
								  if (!result.value) {
									  var accessToHigherLevel='<%=SwtUtil.checkAccessToHigherMenuItemsFromRequestList(request)%>';
										Swal.fire({
											  html: accessToHigherLevel,
											  showCancelButton: false,
											  width:350,
											  showClass: {
												    popup: '',
												    icon: ''
												  },
												  hideClass: {
												    popup: '',
												  },
												  onClose: function () {
													  checkChangePasswordAcess();
											        },
											  confirmButtonText: 'OK',
											});
								  } else {
									  checkChangePasswordAcess();
								  }
								});


				}else{
					 checkChangePasswordAcess();
				}

</c:if>

</c:if>

		// Close Add / Change role screen if cancel button is clicked
		<c:if test="${'yes' == requestScope.cancelFlag}">

			self.close();

</c:if>
		<c:if test="${'yes' != requestScope.cancelFlag}">

			if ('<%=request.getParameter("accountAccessSize1")%>'!='null')
				accountAccessSize = parseInt('<%=request.getParameter("accountAccessSize1")%>')
				document.forms[0].limitButtonStatus.value  = '${requestScope.limitButtonStatus}';
				var obj = document.forms[0].elements['role.restrictLocations'];
			if(obj.checked == true ) {
				document.getElementById("locationbutton").innerHTML = document.getElementById("enablelocationbutton").innerHTML;
			}
			else {
				document.getElementById("locationbutton").innerHTML = document.getElementById("disablelocationbutton").innerHTML;
			}
				obj = document.forms[0].elements['role.accountAccessControl'];
			if(obj.checked==true)
			{
				document.getElementById("accountsbutton").innerHTML = document.getElementById("enableaccountsbutton").innerHTML;
			}
			else
			{
				document.getElementById("accountsbutton").innerHTML = document.getElementById("disableaccountsbutton").innerHTML;
			}
				window.onbeforeunload = beforeBodyUnload;

</c:if>

		    var headerData = [];
			var dataprovider = new Array();

			var newElement1 = {};
			newElement1[headerData[0]] = 'Pdf';
			dataprovider.push(newElement1);

			var newElement2 = {};
			newElement2[headerData[0]] = 'Excel';
			dataprovider.push(newElement2);

			$("#exportReport").exportselect ({
				dataprovider: dataprovider,
				change: exportReport,
				selectedIndex:0
			  });

	}
 	function exportReport(){
 			var type=$("#exportReport").getSelected(0);
 			return generateReport(type.toLowerCase());
	}

		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;

	function beforeBodyUnload()
	{
 		    window.opener.popupOpened=false;
 			if(document.forms[0].elements['clearTempRecords'].value == "true")
  		{
 				window.opener.closeChild();
  		}
  			unloadMyWindow();
	}
	function callMe()
	{
			var oXMLHTTP = new XMLHttpRequest();
			var sURL = requestURL + appName+"/role.do?method=cancel";
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
	}
	function buildLocationAccess(methodName)
	{
		document.forms[0].method.value = methodName;
		var	param = 'role.do?method='+methodName;
		param+='&selectedRoleId=' + document.forms[0].elements["role.roleId"].value;
		return param;
	}
	function buildAccountAccessControl(screenName)
	{
		if (document.forms[0].copiedRoleId.value == "")
		{
			var	param = 'role.do?method=viewAccountAccess';
			param+='&selectedRoleId=' + document.forms[0].elements["role.roleId"].value;
			param+='&selectedScreen='+screenName;
		}
		else
		{
			var	param = 'role.do?method=viewAccountAccess';
			param+='&selectedRoleId=' + document.forms[0].copiedRoleId.value;
			param+='&selectedScreen='+screenName;
		}
		return param;
	}
	function enableDisableLocationButton(value)
	{
		var obj = document.forms[0].elements['role.restrictLocations'];
		if (obj.checked == true)
		{
			document.getElementById("locationbutton").innerHTML = document.getElementById("enablelocationbutton").innerHTML;
		}
		else
		{
			document.getElementById("locationbutton").innerHTML = document.getElementById("disablelocationbutton").innerHTML;
		}
	}
	function enableDisableAccountsButton(value)
	{
		var obj = document.forms[0].elements['role.accountAccessControl'];
		if (obj.checked == true)
		{
			document.getElementById("accountsbutton").innerHTML = document.getElementById("enableaccountsbutton").innerHTML;
		}
		else
		{
			document.getElementById("accountsbutton").innerHTML = document.getElementById("disableaccountsbutton").innerHTML;
		}
	}
	function generateReport(fileType)
	{
			document.forms[0].fileType.value =fileType.trim();
    		document.forms[0].method.value = "getRoleReport";
			document.forms[0].selectedRoleId_Report.value=document.forms[0].elements["role.roleId"].value;
			document.forms[0].clearTempRecords.value = "false";
    		document.forms[0].submit();
			setParentChildsFocus();
	}
	</SCRIPT>
	<form action="role.do" method="post" onsubmit="validate(this);">
		<input name="method" type="hidden" value="save">
		<input name="copiedRoleId" type="hidden" value='${requestScope.copiedRoleId}'>
		<input name="isViewRole" type="hidden" value="">
		<input name="clearSessionOnUnLoad" type="hidden" value="">
		<input name="roleIdSelected" type="hidden" value="">
		<input name="screenTitle" type="hidden" value="">
		<input name="clearTempRecords" type="hidden" value="true">
		<input name="newRoleId" type="hidden" value="">
		<input name="limitButtonStatus" type="hidden" value="">
		<input name="selectedRoleId_Report" type="hidden" value="">
		<input name="fileType" type="hidden" value="">
		<input name="selectedRoleId" type="hidden" >
		<input name="accountAccessSize1" type="hidden" value="">
	<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()" onBeforeUnload="beforeBodyUnload()" >
	<div id="RoleMaintenanceAdd" style="position:absolute; left:20px; top:10px; width:470px; height:510px; border:2px outset;" color="#7E97AF">
	<div id="RoleMaintenanceAdd" style="position:absolute; left:8px; top:4px; width:470px; height:460px;">

<!------------------------------first fieldset------------------------------------------->
	<div style="left:8px; top:18px;height:70px;" >
	<fieldset style="width:445px;border:2px groove;">
	<legend>
	<fmt:message key="role.roleId"/>
	</legend>
	<table width="395" border="0" cellpadding="0" cellspacing="1" height="50">
	 <tr height="25">
			<c:if test="${'save' == requestScope.methodName}">

		 	<c:if test="${'yes' == requestScope.isViewRole}">

			 	<td width="80px">&nbsp;<b><fmt:message key="role.roleId"/></b></td>

</c:if>
		 	<c:if test="${'yes' != requestScope.isViewRole}">

			 	<td width="80px">&nbsp;<b><fmt:message key="role.roleId"/></b>*</td>

</c:if>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

			 	<td width="80px">&nbsp;<b><fmt:message key="role.roleId"/></b></td>

</c:if>
	   	<td width="280px">
			<c:if test="${'save' == requestScope.methodName}">

			<c:if test="${'yes' == requestScope.isViewRole}">

				<input type="text" titleKey="tooltip.enterRoleId" tabindex="1" name="role.roleId" value="${role.roleId}"  maxlength = "12" style="width:120px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'role.roleId','alphaNumPat');" />

</c:if>
			<c:if test="${'yes' != requestScope.isViewRole}">

				<input type="text" titleKey="tooltip.enterRoleId" tabindex="1" name="role.roleId" value="${role.roleId}"  maxlength = "12" style="width:120px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'role.roleId','alphaNumPat');" />

</c:if>

</c:if>
			<c:if test="${'add' == requestScope.methodName}">

			<c:if test="${'yes' == requestScope.isViewRole}">

				<input type="text"  name="role.roleId" value="${role.roleId}"  titleKey="tooltip.enterRoleId" tabindex="1" maxlength = "12" style="width:120px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'role.roleId','alphaNumPat');" />

</c:if>
			<c:if test="${'yes' != requestScope.isViewRole}">

				<input type="text"  name="role.roleId" value="${role.roleId}"  titleKey="tooltip.enterRoleId" tabindex="1" maxlength = "12" style="width:120px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'role.roleId','alphaNumPat');" />

</c:if>

</c:if>
			<c:if test="${'update' == requestScope.methodName}">

				<input type="text"  name="role.roleId" value="${role.roleId}"  style="width:120px;" maxlength = "12" titleKey="tooltip.enterRoleId" tabindex="1" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'role.roleId','alphaNumPat');" />

</c:if>
			<c:if test="${'view' == requestScope.methodName}">

				 <input type="text"  name="role.roleId" value="${role.roleId}"  style="width:120px;" maxlength = "12" titleKey="tooltip.enterRoleId" tabindex="1" disabled="true" onchange="return validateField(this,'role.roleId','alphaNumPat');" />

</c:if>
		  </td>
    </tr>
	<tr height="25">
	  	<td width="80px">&nbsp;<b><fmt:message key="role.roleName"/><c:if test="${'yes' != requestScope.isViewRole}">
    *
</c:if></b></td>
	  	<td width="280px">
		  	<c:if test="${'yes' == requestScope.isViewRole}">

				<input type="text"  name="role.roleName" value="${role.roleName}"  titleKey="tooltip.enterRoleName" maxlength = "30"  tabindex="2" style="width:280px;" disabled="true" onchange="return validateField(this,'role.roleName','alphaNumPatWithSpace');" />

</c:if>
		  <c:if test="${'yes' != requestScope.isViewRole}">

				<input type="text"  name="role.roleName" value="${role.roleName}"  titleKey="tooltip.enterRoleName"  maxlength = "30" tabindex="2" style="width:280px;"  onchange="return validateField(this,'role.roleName','alphaNumPatWithSpace');" />

</c:if>
		</td>
	</tr>
 	</table>
  	</fieldset>
  	</div>
  <!--------------------------------------end of first fieldset---------------------------------->
	<div id="ddimagebuttons" style=" top:70px;height:240px;position: absolute;">
	<fieldset style="width:445px;border:2px groove;height:210px;position: absolute;">
	<legend>
	<fmt:message key="role.entAccessList.Access"/>
	</legend>
	<c:if test="${'yes' != requestScope.isViewRole}">

	<table width="210" border="0" cellpadding="0" cellspacing="1" height="25px">
	<tr>
	</tr>
	<tr  height="25px">
			<!--  Menu Access Option Button  -->
		<td width="70px" title="<fmt:message key="tooltip.menuAccess"/>">
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.MENUAO_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

				<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('menuAccessOptions'),'menuaccessoptionsWindow','left=50,top=190,width=1035,height=438,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.menu"/></a>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('menuAccessOptions'),'menuaccessoptionsWindow','left=50,top=190,width=1035,height=438,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.menu"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.MENUAO_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"><fmt:message key="button.menuAO"/></a>
			<%}%>
			</td>
			<td width="70px" title="<fmt:message key="tooltip.entityAccess"/>">
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ENTITYAL_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

				<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('entityAccessList'),'entityaccesslistWindow','left=50,top=190,width=778,height=420,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.entity"/></a>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('entityAccessList'),'entityaccesslistWindow','left=50,top=190,width=778,height=420,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.entity"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.ENTITYAL_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"><fmt:message key="button.entityAL"/></a>
			<%}%>
			</td>
			<td width="70px" >
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.WORKQA_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

				<c:if test="${'true' == requestScope.saveEnableEntity}">
				<a tabindex="5" title='<fmt:message key="tooltip.ccyGrpAccess"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('ccyGrpAccess'),'ccyGrpAccessWindow','left=50,top=190,width=778,height=466,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.ccyGrp"/></a>

</c:if>
			<c:if test="${'true' != requestScope.saveEnableEntity}">

				<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.ccyGrpAccess"/>'><fmt:message key="button.ccyGrp"/></a>

</c:if>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="5" title='<fmt:message key="tooltip.ccyGrpAccess"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('ccyGrpAccess'),'ccyGrpAccessWindow','left=50,top=190,width=778,height=466,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.ccyGrp"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.WORKQA_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.ccyGrpAccess"/>'><fmt:message key="button.ccyGrp"/></a>
			<%}%>
			</td>

			<td width="70px" title="<fmt:message key="tooltip.4EyesAccess"/>">
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.MENUAO_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

				<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('roleBasedControl'),'fourEyesAccessWindow','left=50,top=190,width=670,height=438,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.4EyesGrp"/></a>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('roleBasedControl'),'fourEyesAccessWindow','left=50,top=190,width=670,height=438,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.4EyesGrp"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.MENUAO_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"><fmt:message key="button.4EyesGrp"/></a>
			<%}%>
			</td>
	</tr>
	<table width="380px" border="0" cellpadding="1" cellspacing="1" height="25px" style="margin-left:11px;">
	<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" name="role.restrictLocations" value="Y" ${requestScope.role.restrictLocations == 'Y' ? 'checked' : ''} titleKey="tooltip.selectRestrictLocation" fieldValue="Y" value='${requestScope.role.restrictLocations == "Y"}' onclick="javascript:enableDisableLocationButton(this.value);" cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="120px"><fmt:message key="role.restrictLocations"/></td>
			<td width="10px">&nbsp;</td>
			<td width="100px" id="locationbutton" ></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" name="role.accountAccessControl" value="Y" ${requestScope.role.accountAccessControl == 'Y' ? 'checked' : ''} titleKey="tooltip.roleAccountsCheck" fieldValue="Y" value='${requestScope.role.accountAccessControl == "Y"}' onclick="javascript:enableDisableAccountsButton(this.value);" cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px"><fmt:message key="role.roleAccountsLabel"/></td>
			<td width="10px">&nbsp;</td>
			 <td width="100px" id="accountsbutton"></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" name="role.allEntityOption" value="Y" ${requestScope.role.allEntityOption == 'Y' ? 'checked' : ''} titleKey="tooltip.allEntityOption"  fieldValue="Y" value='${requestScope.role.allEntityOption == "Y"}' cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.allEntityOption"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.advancedUser" value="Y" ${requestScope.role.advancedUser == 'Y' ? 'checked' : ''} titleKey="tooltip.advancedUser" fieldValue="Y" value='${requestScope.role.advancedUser == "Y"}'  cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.advancedUser"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyIlmScenario" value="Y" ${requestScope.role.maintainAnyIlmScenario == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainAnyIlmScenario" fieldValue="Y" value='${requestScope.role.maintainAnyIlmScenario == "Y"}'  cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainAnyIlmScenario"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyIlmGroup" value="Y" ${requestScope.role.maintainAnyIlmGroup == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainAnyIlmGroup" fieldValue="Y" value='${requestScope.role.maintainAnyIlmGroup == "Y"}'  cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainAnyIlmGroup"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyReportHist" value="Y" ${requestScope.role.maintainAnyReportHist == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainSchedulerReportHist"  fieldValue="Y" value='${requestScope.role.maintainAnyReportHist == "Y"}'  cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainSchedulerReportHist"/></td>
	</tr>
	<!-- <tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyPCFeature" value="Y" ${requestScope.role.maintainAnyPCFeature == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainAnyPCFeature"  fieldValue="Y" value='${requestScope.role.maintainAnyPCFeature == "Y"}'  cssClass="htmlTextAlpha" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainAnyPCFeature"/></td>
	</tr>-->
 	</table>
	<!-- HIDDEN DIV TO ENABLE/DISABLE LOCATIONS BUTTON -->
	<div id="locationdefinition" style="position:absolute; left:8px; top:20px; width:400px; height:15px; visibility:hidden;">
	<table width="260px" border="" cellpadding="1" cellspacing="1" height="25px">
		<tr  height="25px">
			<c:if test="${'save' == requestScope.methodName}">

			<td width="80px" id="enablelocationbutton">
				<a tabindex="11" title='<fmt:message key="tooltip.restrictLocations"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildLocationAccess('addLocationAccessList'),'restrictLocationWindow','left=50,top=190,width=630,height=559,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="role.Locations"/></a>
			</td>
			<td width="80px" id="disablelocationbutton">
				<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.restrictLocations"/>'><fmt:message key="role.Locations"/></a>
			</td>

</c:if>
			<c:if test="${'update' == requestScope.methodName}">

			<td width="80px" id="enablelocationbutton">
				<a tabindex="11" title='<fmt:message key="tooltip.restrictLocations"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildLocationAccess('changeLocationAccessList'),'restrictLocationWindow','left=50,top=190,width=630,height=559,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="role.Locations"/></a>
			</td>
			<td width="80px" id="disablelocationbutton">
				<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.restrictLocations"/>'><fmt:message key="role.Locations"/></a>
			</td>
			</td>

</c:if>
	</tr>
	</table>
	</div>
	<!-- HIDDEN DIV TO ENABLE/DISABLE ACCOUNTS BUTTON -->
	<div id="accountsdefinition" style="position:absolute; left:8px; top:20px; width:400px; height:15px; visibility:hidden;">
	<table width="260px" border="" cellpadding="1" cellspacing="1" height="25px">
		<tr  height="25px">
			<c:if test="${'save' == requestScope.methodName}">

			<td width="90px" id="enableaccountsbutton">
					<a tabindex="11" title='<fmt:message key="tooltip.roleAccountsButton"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildAddRole('accountAccess','addScreen');"><fmt:message key="role.roleAccounts"/></a>
			</td>
			<td width="80px" id="disableaccountsbutton">
					<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.roleAccountsButton"/>'><fmt:message key="role.roleAccounts"/></a>
			</td>

</c:if>
			<c:if test="${'update' == requestScope.methodName}">

			<td width="90px" id="enableaccountsbutton">
					<a tabindex="11" title='<fmt:message key="tooltip.roleAccountsButton"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildAddRole('accountAccess','changeScreen');"><fmt:message key="role.roleAccounts"/></a>
			</td>
			<td width="80px" id="disableaccountsbutton">
					<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.roleAccountsButton"/>'><fmt:message key="role.roleAccounts"/></a>
			</td>
			</td>

</c:if>
	</tr>
	</table>
	</div>
	<!-- HIDDEN DIV TO ENABLE/DISABLE ACCOUNTS BUTTON -->
	</table>

</c:if>
	<c:if test="${'yes' == requestScope.isViewRole}">

	<table width="210" border="0" cellpadding="0" cellspacing="1" height="25px">
	<tr>
	</tr>
		<tr  height="25px">
		<td width="70px" title='<fmt:message key="tooltip.menuAccess"/>'>
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.MENUAO_BUT_STS)) ) {%>
				<c:if test="${'save' == requestScope.methodName}">

					<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('menuAccessOptions',''),'menuaccessoptionsWindow','left=50,top=190,width=1035,height=438,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.menu"/></a>

</c:if>
				<c:if test="${'save' != requestScope.methodName}">

					<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('menuAccessOptions',''),'menuaccessoptionsWindow','left=50,top=190,width=1035,height=438,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.menu"/></a>

</c:if>
			<%}%>
				<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.MENUAO_BUT_STS)) ) {%>
					<a class="disabled" disabled="disabled"><fmt:message key="button.menuAO"/></a>
				<%}%>
		</td>
	<!--  Entity Access List Button  -->
		<td width="70px" title='<fmt:message key="tooltip.entityAccess"/>'>
		<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ENTITYAL_BUT_STS)) ) {%>
		<c:if test="${'save' == requestScope.methodName}">

			<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('entityAccessList',''),'entityaccesslistWindow','left=50,top=190,width=778,height=420,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.entity"/></a>

</c:if>
		<c:if test="${'save' != requestScope.methodName}">

			<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('entityAccessList',''),'entityaccesslistWindow','left=50,top=190,width=778,height=420,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.entity"/></a>

</c:if>
		<%}%>
		<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.ENTITYAL_BUT_STS)) ) {%>
			<a class="disabled" disabled="disabled"><fmt:message key="button.entity"/></a>
		<%}%>
		</td>
	<!--  Currency Group Access Button  -->
		<td width="70px">
		<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.WORKQA_BUT_STS)) ) {%>
		<c:if test="${'save' == requestScope.methodName}">

<c:if test="${'true' == requestScope.saveEnableEntity}">
			<a tabindex="5" title='<fmt:message key="tooltip.ccyGrpAccess"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('ccyGrpAccess',''),'ccyGrpAccessWindow','left=50,top=190,width=778,height=466,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.ccyGrp"/></a>

</c:if>
		<c:if test="${'true' != requestScope.saveEnableEntity}">

			<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.ccyGrpAccess"/>'><fmt:message key="button.ccyGrp"/></a>

</c:if>

</c:if>
		<c:if test="${'save' != requestScope.methodName}">

			<a tabindex="5" title='<fmt:message key="tooltip.ccyGrpAccess"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('ccyGrpAccess',''),'ccyGrpAccessWindow','left=50,top=190,width=778,height=466,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.ccyGrp"/></a>

</c:if>
		<%}%>
		<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.WORKQA_BUT_STS)) ) {%>
			<a class="disabled" disabled="disabled"><fmt:message key="button.ccyGrp"/></a>
		<%}%>
			</td>



			<td width="70px" >
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.WORKQA_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

			<c:if test="${'true' == requestScope.saveEnableEntity}">

				<a tabindex="5" title='<fmt:message key="tooltip.4EyesAccess"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('roleBasedControl',''),'fourEyesAccessWindow','left=50,top=190,width=670,height=466,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.4EyesGrp"/></a>

</c:if>
			<c:if test="${'true' != requestScope.saveEnableEntity}">

				<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.4EyesAccess"/>'><fmt:message key="button.4EyesGrp"/></a>

</c:if>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="5" title='<fmt:message key="tooltip.4EyesAccess"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('roleBasedControl',''),'fourEyesAccessWindow','left=50,top=190,width=670,height=466,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="button.4EyesGrp"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.WORKQA_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.4EyesAccess"/>'><fmt:message key="button.4EyesGrp"/></a>
			<%}%>
			</td>
		</tr>
	<!--  Location Access Button  -->
	<table width="360px" border="0" cellpadding="1" cellspacing="1" height="50px">
		<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" titleKey="tooltip.selectRestrictLocation" name="role.restrictLocations" value="Y" ${requestScope.role.restrictLocations == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.role.restrictLocations == "Y"}' cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="120px"><fmt:message key="role.restrictLocations"/></td>
			<td width="10px">&nbsp;</td>
			<td width="80px" id="locationbutton">
				<a tabindex="11" title='<fmt:message key="tooltip.restrictLocations"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildLocationAccess('viewLocationAccessList'),'restrictLocationWindow','left=50,top=190,width=630,height=559,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="role.Locations"/></a>
			</td>
	</tr>
		<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" name="role.accountAccessControl" value="Y" ${requestScope.role.accountAccessControl == 'Y' ? 'checked' : ''} titleKey="tooltip.roleAccountsCheck"  fieldValue="Y" value='${requestScope.role.accountAccessControl == "Y"}' cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="290px"><fmt:message key="role.roleAccountsLabel"/></td>
			<td width="10px">&nbsp;</td>
			 <td width="150px" id="accountsbutton"></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" name="role.allEntityOption" value="Y" ${requestScope.role.allEntityOption == 'Y' ? 'checked' : ''} titleKey="tooltip.allEntityOption" fieldValue="Y" value='${requestScope.role.allEntityOption == "Y"}' cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.allEntityOption"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
				<input type="checkbox" tabindex="5" style="width:13px;" name="role.advancedUser" value="Y" ${requestScope.role.advancedUser == 'Y' ? 'checked' : ''} titleKey="tooltip.advancedUser" fieldValue="Y" value='${requestScope.role.advancedUser == "Y"}' cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.advancedUser"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyIlmScenario" value="Y" ${requestScope.role.maintainAnyIlmScenario == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainAnyIlmScenario" fieldValue="Y" value='${requestScope.role.maintainAnyIlmScenario == "Y"}'  cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainAnyIlmScenario"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyIlmGroup" value="Y" ${requestScope.role.maintainAnyIlmGroup == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainAnyIlmGroup"  fieldValue="Y" value='${requestScope.role.maintainAnyIlmGroup == "Y"}'  cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainAnyIlmGroup"/></td>
	</tr>
	<tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyReportHist" value="Y" ${requestScope.role.maintainAnyReportHist == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainSchedulerReportHist"  fieldValue="Y" value='${requestScope.role.maintainAnyReportHist == "Y"}'  cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainSchedulerReportHist"/></td>
	</tr>
	<!-- <tr  height="25px">
			<td width="13px">
		 		<input type="checkbox" tabindex="5" style="width:13px;" name="role.maintainAnyPCFeature" value="Y" ${requestScope.role.maintainAnyPCFeature == 'Y' ? 'checked' : ''} titleKey="tooltip.maintainAnyPCFeature"  fieldValue="Y" value='${requestScope.role.maintainAnyPCFeature == "Y"}'  cssClass="htmlTextAlpha" disabled="true" />
			</td>
			<td width="10px">&nbsp;</td>
			<td width="350px" colspan="3"><fmt:message key="role.maintainAnyPCFeature"/></td>
	</tr>-->
	</table>
	<div id="accountsdefinition" style="position:absolute; left:8px; top:20px; width:400px; height:15px; visibility:hidden;">
	<table width="260px" border="0" cellpadding="1" cellspacing="1" height="25px">
		<tr  height="25px">
		<c:if test="${'view' == requestScope.methodName}">

			<td width="83px" id="enableaccountsbutton">
				<a tabindex="11" title='<fmt:message key="tooltip.roleAccountsButton"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAccountAccessControl('viewScreen'),'restrictLocationWindow','left=50,top=190,width=1030,height=530,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="role.roleAccounts"/></a>
			</td>
			<td width="83px" id="disableaccountsbutton">
				<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.roleAccountsButton"/>'><fmt:message key="role.roleAccounts"/></a>
			</td>
			</td>

</c:if>
		</tr>
	</table>
	<div id="locationdefinition" style="position:absolute; left:8px; top:20px; width:400px; height:15px; visibility:hidden;">
	<table width="260px" border="0" cellpadding="1" cellspacing="1" height="25px">
		<tr  height="25px">
		<c:if test="${'view' == requestScope.methodName}">

			<td width="80px" id="enablelocationbutton">
				<a tabindex="11" title='<fmt:message key="tooltip.restrictLocations"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildLocationAccess('viewLocationAccessList'),'restrictLocationWindow','left=50,top=190,width=630,height=559,toolbar=0, status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="role.Locations"/></a>
			</td>
			<td width="80px" id="disablelocationbutton">
				<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.restrictLocations"/>'><fmt:message key="role.Locations"/></a>
			</td>
			</td>

</c:if>
		</tr>
	</table>
	</div>
	</table>

</c:if>
	</fieldset>
	</div>
	<div id="ddimagebuttons" style="top:310px;position: absolute;">
	<fieldset style="width:445px;border:2px groove;height:70px;">
	<legend>
	<fmt:message key="role.sweepLimits.sweep"/>
	</legend>
	<table width="200" border="0" cellpadding="0" cellspacing="1" height="10px">
	<c:if test="${'yes' == requestScope.isViewRole}">

       <tr height="10px">
		 <td width="70px" title='<fmt:message key="tooltip.sweepingLimitsCurr"/>'>
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SWEEPLC_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

				<a tabindex="6" onMouseOut ="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildAddRole('sweepLimit','')"><fmt:message key="button.limits"/></a>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildAddRole('sweepLimit','')"><fmt:message key="button.limits"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.SWEEPLC_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"><fmt:message key="button.sweepLC"/></a>
			<%}%>
			</td>
			</tr>


</c:if>
	<c:if test="${'yes' != requestScope.isViewRole}">

			<tr height="10px">
		 	<td width="70px" title='<fmt:message key="tooltip.sweepingLimitsCurr"/>'>
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SWEEPLC_BUT_STS)) ) {%>
			<c:if test="${'save' == requestScope.methodName}">

				<a tabindex="6" onMouseOut ="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildAddRole('sweepLimit','')"><fmt:message key="button.limits"/></a>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildAddRole('sweepLimit','')"><fmt:message key="button.limits"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.SWEEPLC_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"><fmt:message key="button.sweepLC"/></a>
			<%}%>
			</td>
			</tr>

</c:if>
			</table>
			<div style="left:8px; top:4px;">
			<table width="330" border="0" cellpadding="0" cellspacing="1" height="20px">
		   <tr>
           <td width="85px">&nbsp;<b><fmt:message key="role.alerttype"/></b></td>
		   <td width="20px">&nbsp;</td>
<td width="200px">
<c:if test="${'yes' != requestScope.isViewRole}">
            <input type="radio" id="1" name="role.alertType" ${role.alertType == '1' ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_EMAIL}" tabindex="7" style="width:10;" title="<fmt:message key='tooltip.alertType'/>" ${requestScope.screenFieldsStatus ? 'disabled' : ''}/>
            <label title="<fmt:message key='tooltip.alertType'/>" tabindex="7" for="1">
                <fmt:message key="role.alerttype.email"/>&nbsp;&nbsp;&nbsp;
            </label>

            <input type="radio" id="2" name="role.alertType" ${role.alertType == '2' ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_POPUP}" tabindex="8" style="width:10;" title="<fmt:message key='tooltip.alertType'/>" ${requestScope.screenFieldsStatus ? 'disabled' : ''}/>
            <label title="<fmt:message key='tooltip.alertType'/>" tabindex="8" for="2">
                <fmt:message key="role.alerttype.popup"/>&nbsp;&nbsp;&nbsp;
            </label>

            <input type="radio" id="3" name="role.alertType" ${role.alertType == '0' ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_BOTH}" tabindex="9" style="width:10;" title="<fmt:message key='tooltip.alertType'/>"/>
            <label title="<fmt:message key='tooltip.alertType'/>" tabindex="9" for="3">
                <fmt:message key="role.alerttype.both"/>
            </label>
</c:if>
<c:if test="${'yes' == requestScope.isViewRole}">
            <input type="radio" id="1" name="role.alertType" ${role.alertType == '1' ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_EMAIL}" tabindex="7" style="width:10;" title="<fmt:message key='tooltip.alertType'/>" disabled/>
            <label title="<fmt:message key='tooltip.alertType'/>" for="1">
                <fmt:message key="role.alerttype.email"/>&nbsp;&nbsp;&nbsp;
            </label>

            <input type="radio" id="2" name="role.alertType" ${role.alertType == '2' ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_POPUP}" tabindex="8" style="width:10;" title="<fmt:message key='tooltip.alertType'/>" disabled/>
            <label title="<fmt:message key='tooltip.alertType'/>" for="2">
                <fmt:message key="role.alerttype.popup"/>&nbsp;&nbsp;&nbsp;
            </label>

            <input type="radio" id="3" name="role.alertType" ${role.alertType == '0' ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_BOTH}" tabindex="9" style="width:10;" title="<fmt:message key='tooltip.alertType'/>" disabled/>
            <label title="<fmt:message key='tooltip.alertType'/>" for="3">
                <fmt:message key="role.alerttype.both"/>
            </label>
</c:if>
</td>

 		</tr>
		</table>
		</div>
		</fieldset>
		</div>
	<!--Movement Input/Update-->
	<div id="ddimagebuttons"  style="top:380px;position: absolute;">
		<fieldset style="width:440px;border:2px groove;height:45px;">
		<legend>
		<fmt:message key="role.fieldset.mvtInputUpdate"/>
		</legend>
			<div style="left:8px; top:4px;">
			<table width="440" border="0" cellpadding="0" cellspacing="1" height="20px">
		   <tr height="25px">
		    <td width="25px" align = "right">
			          <c:if test="${'yes' != requestScope.isViewRole}">

					<input type="checkbox"  name="role.authorizeInput" value="Y" ${requestScope.role.authorizeInput == 'Y' ? 'checked' : ''} style="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='${requestScope.role.authorizeInput == "Y"}' titleKey="tooltip.authorizationInput" tabindex="10" />

					  </c:if>
				 <c:if test="${'yes' == requestScope.isViewRole}">

				 <input type="checkbox"  name="role.authorizeInput" value="Y" ${requestScope.role.authorizeInput == 'Y' ? 'checked' : ''} style="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='${requestScope.role.authorizeInput == "Y"}' titleKey="tooltip.authorizationInput" disabled="true" tabindex="10" />

				 </c:if>
			</td>
          <td width="440px">&nbsp;<b><fmt:message key="label.manualInput"/></b></td>
		  <td width="20px">&nbsp;</td>
		  	</tr>

            <tr height="28px">
		    <td width="25px" align = "right">
			      <c:if test="${'yes' != requestScope.isViewRole}">

					<input type="checkbox"  name="role.allowMsdMultiMvtUpdates" value="Y" ${requestScope.role.allowMsdMultiMvtUpdates == 'Y' ? 'checked' : ''} style="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='${requestScope.role.allowMsdMultiMvtUpdates == "Y"}' titleKey="tooltip.allowMultiMvtUpdatesFromMsd" tabindex="10" />

				  </c:if>
				 <c:if test="${'yes' == requestScope.isViewRole}">

				 <input type="checkbox"  name="role.allowMsdMultiMvtUpdates" value="Y" ${requestScope.role.allowMsdMultiMvtUpdates == 'Y' ? 'checked' : ''} style="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='${requestScope.role.allowMsdMultiMvtUpdates == "Y"}' titleKey="tooltip.allowMultiMvtUpdatesFromMsd" disabled="true" tabindex="10" />

				 </c:if>
			</td>
          <td width="440px">&nbsp;<b><fmt:message key="label.allowMultiMvtUpdatesFromMsd"/></b></td>
		  <td width="20px">&nbsp;</td>
		  	</tr>
	</table>
	</div>
	</fieldset>
	<fieldset style="width:445px;border:2px groove;height:45px;">
	<legend>
	<fmt:message key="role.notification.inputNotification"/>
	</legend>
	<div style="left:8px; top:4px;">
	<table width="440" border="0" cellpadding="0" cellspacing="1" height="22px">
	<tr  height="25px">
			<td width="25px" align = "right">
			<c:if test="${'yes' != requestScope.isViewRole}">

				<input type="checkbox" tabindex="11" style="width:13px;" name="role.inputInterruption" value="Y" ${requestScope.role.inputInterruption == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.role.inputInterruption == "Y"}' cssClass="htmlTextAlpha" titleKey="tooltip.inputNotification" />

</c:if>
			<c:if test="${'yes' == requestScope.isViewRole}">

				<input type="checkbox" tabindex="11" style="width:13px;" name="role.inputInterruption" value="Y" ${requestScope.role.inputInterruption == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.role.inputInterruption == "Y"}' cssClass="htmlTextAlpha" disabled="true" titleKey="tooltip.inputNotification" />

</c:if>
			</td>
			<td width="350px" colspan="3">&nbsp;<b><fmt:message key="role.inputInterruption"/></b></td>
			<td width="10px">&nbsp;</td>
			<td width="200px">
    <c:if test="${'yes' != requestScope.isViewRole}">
        <input type="radio" id="1" name="role.inputInterruptionAlertType" ${role.inputInterruptionAlertType == SwtConstants.ALERT_EMAIL ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_EMAIL}" tabindex="12" style="width:10;" titleKey="tooltip.InterfaceInterruptionAlertTypeEmail" ${requestScope.screenFieldsStatus ? 'disabled' : ''}/>
        <label title="<fmt:message key='tooltip.alertType'/>" tabindex="12" for="1">
            <fmt:message key="role.alerttype.email"/>&nbsp;&nbsp;&nbsp;
        </label>

        <input type="radio" id="2" name="role.inputInterruptionAlertType" ${role.inputInterruptionAlertType == SwtConstants.ALERT_POPUP ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_POPUP}" tabindex="13" style="width:10;" titleKey="tooltip.InterfaceInterruptionAlertTypePopUp" ${requestScope.screenFieldsStatus ? 'disabled' : ''}/>
        <label title="<fmt:message key='tooltip.alertType'/>" tabindex="13" for="2">
            <fmt:message key="role.alerttype.popup"/>&nbsp;&nbsp;&nbsp;
        </label>

        <input type="radio" id="3" name="role.inputInterruptionAlertType" ${role.inputInterruptionAlertType == SwtConstants.ALERT_BOTH ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_BOTH}" tabindex="14" style="width:10;" titleKey="tooltip.InterfaceInterruptionAlertType"/>
        <label title="<fmt:message key='tooltip.alertType'/>" tabindex="14" for="3">
            <fmt:message key="role.alerttype.both"/>
        </label>
    </c:if>

    <c:if test="${'yes' == requestScope.isViewRole}">
        <input type="radio" id="1" name="role.inputInterruptionAlertType" ${role.inputInterruptionAlertType == SwtConstants.ALERT_EMAIL ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_EMAIL}" tabindex="12" style="width:10;" titleKey="tooltip.InterfaceInterruptionAlertTypeEmail" disabled/>
        <label title="<fmt:message key='tooltip.alertType'/>" for="1">
            <fmt:message key="role.alerttype.email"/>&nbsp;&nbsp;&nbsp;
        </label>

        <input type="radio" id="2" name="role.inputInterruptionAlertType" ${role.inputInterruptionAlertType == SwtConstants.ALERT_POPUP ? 'checked="checked"' : ''} value="${SwtConstants.ALERT_POPUP}" tabindex="13" style="width:10;" titleKey="tooltip.InterfaceInterruptionAlertTypePopUp" disabled/>
        <label title="<fmt:message key='tooltip.alertType'/>" for="2">
            <fmt:message key="role.alerttype.popup"/>&nbsp;&nbsp;&nbsp;
        </label>

        <input type="radio" id="3" name="role.inputInterruptionAlertType" ${role.inputInterruptionAlertType == SwtConstants.ALERT_BOTH ? 'checked="checked"' : ''}  value="${SwtConstants.ALERT_BOTH}" tabindex="14" style="width:10;" titleKey="tooltip.InterfaceInterruptionAlertType" disabled/>

        <label title="<fmt:message key='tooltip.alertType'/>" for="3">
            <fmt:message key="role.alerttype.both"/>
        </label>
    </c:if>
</td>

			</tr>
	</table>
	</div>
	</fieldset>
</div>
<!---AddButton----------------------- -->
</div>
</div>

<!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
<div id="RoleMaintenanceAdd" style="position:absolute; left:400; top:532px; width:70px; height:15px;z-index:5; visibility:visible;margin-top: 6px;">

			<c:if test="${'save' == requestScope.methodName}">

			 <div  style="float: left;margin-top: 3px;margin-right: -20px">
				<a  tabindex="19" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Role '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</div>

</c:if>

			<c:if test="${'update' == requestScope.methodName}">

			 <div  style="float: left;margin-top: 3px;margin-right: -20px">
				<a  tabindex="19" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Role '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			 </div>

</c:if>

			<c:if test="${'view' == requestScope.methodName}">

			     <table style="float: left;" >
					<div id="exportReport"  />
				 </table>

</c:if>

			<c:if test="${'view' != requestScope.methodName}">

			<table  style="float: left; margin-left:30;">
				<tr>
			<td align="right" id="Print">
				<a onclick="printPage();" tabindex="19" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>

</c:if>
     </div>
     <c:if test="${'view' == requestScope.methodName}">

    	 <div style="position:absolute; left:440; top:535px; width:30px; height:15px;">
     		<a  tabindex="19" href=# onclick="javascript:openWindow(buildPrintURL('print','View Role '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
     			<img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
</div>

</c:if>
     <!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->

<c:if test="${'yes' != requestScope.isViewRole}">

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:530px; width:470px; height:39px; visibility:visible;">
  <div id="RoleMaintenanceAdd" style="position:absolute; left:6; top:4; width:360px; height:10px; visibility:visible;">
  	  <table width="210px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr height="25">
			 <!-- Copy From Button  -->
			<td width="70px" title='<fmt:message key="tooltip.copyFrom"/>'>
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.COPYFROM_BUT_STS)) ) {%>
				<c:if test="${'save' == requestScope.methodName}">

						<a tabindex="11" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('copyFrom',''),'copyfromroleWindow','left=50,top=190,width=558px,height=124,toolbar=0, resizable=yes, status=yes, scrollbars=yes')"><fmt:message key="button.cpyFrom"/></a>

</c:if>
				<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="11" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('copyFrom',''),'copyfromroleWindow','left=50,top=190,width=558px,height=124,toolbar=0, resizable=yes, status=yes, scrollbars=yes')"><fmt:message key="button.cpyFrom"/></a>

</c:if>
			<%}%>
			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.COPYFROM_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"><fmt:message key="button.cpyFrom"/></a>
			<%}%>
			</td>
	<!-- Save Button -->
		<td width="70px" >
			<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
				<c:if test="${'save' == requestScope.methodName}">
					<c:if test="${'true' == requestScope.saveEnableMenu}">

					   <c:if test="${'true' == requestScope.saveEnableEntity}">
							<a tabindex="12" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('${methodName}');"><fmt:message key="button.save"/></a>

</c:if>
						<c:if test="${'true' != requestScope.saveEnableEntity}">

						<a class="disabled" disabled="disabled"title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save"/></a>

</c:if>

</c:if>
					<c:if test="${'true' != requestScope.saveEnableMenu}">

					<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save"/></a>

</c:if>

</c:if>
			<c:if test="${'save' != requestScope.methodName}">

				<a tabindex="12" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('${methodName}');"><fmt:message key="button.save"/></a>

</c:if>
			<%}%>

			<%if ( (SwtConstants.STR_FALSE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
				<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save"/></a>
			<%}%>
			</td>
		 <!-- Cancel Button  -->
		<td id ="cancelbutton" width="70px" title='<fmt:message key="tooltip.cancel"/>'>
			<a tabindex="13" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('cancel');"><fmt:message key="button.cancel"/></a>
		</td>
		</tr>
	 </table>
	</div>
</div>
</c:if>
<!-- Buttons incase of view role-->
<c:if test="${'yes' == requestScope.isViewRole}">

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:530; width:470px; height:39px; visibility:visible;">
  <div id="RoleMaintenanceAdd" style="position:absolute; left:10; top:4; width:380px; height:10px; visibility:visible;">
  	  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20px">
		<tr>
			 <!-- Close Button  -->
			 <td width="70px" title='<fmt:message key="tooltip.close"/>' id="closebutton">
				<a tabindex="18" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('cancel');"><fmt:message key="button.close"/></a>
			</td>
			</tr>
		</table>
	</div>
</div>

</c:if>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<Script language = "javascript">
	<c:if test="${'update' == requestScope.methodName}">

		roleIdSelected = document.forms[0].elements["role.roleId"].value;

</c:if>
</Script>
</form>
</body>
</html>