<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.Role"%>
<html>
<head>
<title><fmt:message key="rolemaintenance.mainScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">

var entityAccess = "${requestScope.EntityAccess}";

var popupOpened=false;

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
}

function onFilterandSort(){
	updateColors();
	disableAllButtons();
}

function bodyOnLoad()
{
	xl = new XLSheet("RoleDetailsList","table_2", ["String","String",],"21");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("RoleDetailsList");

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";

	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;


	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>


	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>


	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	document.forms[0].accessAltered.value = '${requestScope.accessAltered}';
	if (document.forms[0].accessAltered.value == 'Y')
	{

		alert('<fmt:message key="confirm.refreshMainWindow"/>');
	}
	var headerData = [];
	var dataprovider = new Array();

	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);

	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);

	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });

}
function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return generateReport('Print',type.toLowerCase());
}

function parameteronBodyLoad(){
bodyOnLoad();
setParentChildsFocus();
setFocus(document.forms[0]);
ShowErrMsgWindow('${actionError}');

}
function submitForm(methodName){
var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ //Boolean variable. Sets to true if user pressed "OK" versus "Cancel."
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
}

function buildAddRole(methodName){

	if(popupOpened==true){
		alert('<fmt:message key="alert.closeScreen"/>');
		return
	}
	var param = 'role.do?method='+methodName+'&selectedRoleId=';
	param += document.forms[0].selectedRoleId.value;
	popupOpened=true;
		return  param;
}

function onSelectTableRow(rowElement, isSelected)
{
	document.forms[0].selectedRoleId.value = rowElement.cells[0].innerText.trim();

	var count = getCountRowsSelected(rowElement);
	if(count == 1)

	{

			document.forms[0].selectedRow.value ='yes';
			document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;

			if(menuEntityCurrGrpAccess == "0") {

			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
			} else {
		        document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		        document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			}
	}else{

		document.forms[0].selectedRow.value ='no';
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}

}


function generateReport(button,fileType){
    document.forms[0].fileType.value = fileType.trim();

	document.forms[0].method.value = "getRoleReport";
  if(document.forms[0].selectedRow.value=="yes")
		document.forms[0].selectedRoleId_Report.value=document.forms[0].selectedRoleId.value.trim();
   else
     document.forms[0].selectedRoleId_Report.value="";

   document.forms[0].submit();
	setParentChildsFocus();
}


function closeChild()
{
	document.forms[0].method.value = "cancelFromParent";
	document.forms[0].submit();
}

</SCRIPT>
</head>


<body leftmargin="0" topmargin="0" marginheight="0" onLoad="parameteronBodyLoad();" onunload="call()">
<form action="role.do" method="post">

<input name="method" type="hidden" value="display">
<input name="selectedRoleId" type="hidden" value="GBP">

<input name="selectedRoleId_Report" type="hidden" value="">


<input name="fileType" type="hidden" value="">
<input name="selectedRow" type="hidden" value="">

<input name="menuAccessId" type="hidden" >
<input name="accessAltered" type="hidden" value="">
<div id="Role" style="position:absolute; left:10px; top:10px; width:606px; height:369px; border:0px outset;" color="#7E97AF">

	<div id="Role" style="position:absolute;z-index:99;left:0px; top:0px; width:586px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="580px" border="0" cellspacing="1" cellpadding="0"  height="23px" style="margin-top: 0px;">

			<thead>
				<tr>
					<td width="260px" height="23px" title='<fmt:message key="tooltip.sortRoleId"/>' align="left"style="border-left-width: 0px;"><b><fmt:message key="role.roleId"/></b></td>
					<td width="320px" height="23px"  title='<fmt:message key="tooltip.sortname"/>' align="left"><b><fmt:message key="role.roleName"/></b></td>
				</tr>
			</thead>
		</table>
	</div>

	<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:601px; height:365px;overflowY:scroll">

		<div id="Role" style="position:absolute;z-index:99;left:1px; top:22px; width:580px; height:10px;">
			<table class="sort-table" id="RoleDetailsList" width="580px" border="0" cellspacing="1" cellpadding="0" height="343px" >
				<tbody>
    <c:set var="count" value="0"/>
    <c:forEach items="${requestScope.RoleDetailsList}" var="role">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <input type="hidden" name="roleId" value="${role.roleId}"/>
            <td width="260px" align="left">${role.roleId}&nbsp;</td>
            <td width="320px">${role.roleName != null ? role.roleName.replaceAll(' ', '&nbsp;') : ''}&nbsp;</td>
        </tr>
        <c:set var="count" value="${count + 1}"/>
    </c:forEach>
</tbody>

					<tfoot><tr><td colspan="2"></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<div id="Role" style="position:absolute; left:520; top:395; width:70px; height:15px; z-index:5;visibility:visible;">
	<table>
	     <div id="exportReport" />
	</table>
</div>
 <div id="helpIcon" style="position:absolute; left:565; top:394px; width:30px; height:15px;">
    <a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Role Setup '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
        <img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:383px; width:600px; height:39px; visibility:visible;">

<div id="Role" style="position:absolute; left:6; top:4; width:350px; height:15px; visibility:visible;">
    <table width="350px" border="0" cellspacing="0" cellpadding="0" height="20">

	<tr>

		<td width="70px" id="addbutton" >
	    </td>

		<td id="changebutton"  width="70px"></td>
		<td id="viewbutton" width="70px"></td>
		<td id="deletebutton" width="70px"></td>
		<td id="closebutton" width="70px" title='<fmt:message key="tooltip.close"/>'>
			<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
		</td>
	</tr>
	</table>
</div>
<!-- Start:modified the mantis 1549 by sunil on 10-02-2012.The purpose to display the meaningful tooltip -->
	<div style="position:absolute; left:6; top:4; width:424px; height:15px; visibility:hidden;">
	<table width="350px" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;display:none;">
	<tr>

	    <td id="addenablebutton">
	        <a  tabindex="1"  title='<fmt:message key="tooltip.addNewRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('add'),'rolemaintenanceaddWindow','left=50,top=190,width=520,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
		</td>


		<td id="adddisablebutton">
			<a  class="disabled" disabled="disabled" title='<fmt:message key="tooltip.addNewRole"/>' ><fmt:message key="button.add"/></a>
		</td>

		<td id="changeenablebutton" >

			<a tabindex="2" title='<fmt:message key="tooltip.changeSelectedRole"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('change'),'rolemaintenancechangeWindow','left=50,top=190,width=520,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>


		</td>
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.changeSelectedRole"/>'><fmt:message key="button.change"/></a>
		</td>
		<td id="viewenablebutton" >
			<a tabindex="3" title='<fmt:message key="tooltip.viewSelectedRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('view'),'rolemaintenancechangeWindow','left=50,top=190,width=520,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>

		</td>
		<td id="viewdisablebutton">
			<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.viewSelectedRole"/>'><fmt:message key="button.view"/></a>
		</td>

		<td id="deleteenablebutton" >
			<a tabindex="4" title='<fmt:message key="tooltip.deleteSelectedRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('delete')"><fmt:message key="button.delete"/></a>
		</td>
		<td id="deletedisablebutton">
			<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.deleteSelectedRole"/>'><fmt:message key="button.delete"/></a>
		</td>

			</tr>
			<tr>
			<!-- End:modified the mantis 1549 by sunil on 10-02-2012.The purpose to display the meaningful tooltip -->

	</tr>
	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
  	
  <iframe name="tmp" width="0%" height="0%" src="#" />
  
</body>
</html>