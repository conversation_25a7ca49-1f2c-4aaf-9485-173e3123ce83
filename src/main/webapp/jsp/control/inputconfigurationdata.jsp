<?xml version="1.0" encoding="UTF-8"?>
<%@page import="org.swallow.util.SwtUtil"%>
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>






<inputconfiguration>
<request_reply>
	<status_ok>${requestScope.reply_status_ok}</status_ok>
	<message>${requestScope.reply_message}</message>
	<location />

	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>
</request_reply>

<grid>
<metadata>
	<columns>
		<column
				heading=""
				draggable="false"
				tooltip=""
				filterable="false"
				type="str"
				format=""
				dataelement="${requestScope.flexFieldMapping.mapping.expand}"
				width="10"
				editable="false"
				resizable="false"
				visible="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.interface'/>"
				draggable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.interfaceId'/>"
				filterable="true"
				type="str"
				dataelement="${requestScope.flexFieldMapping.mapping.interface_id}"
				width="${requestScope.column_width.interface_id}"
				editable="false"
				clickable="false"
				primarykey="1"
				sort="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.emaillogs'/>"
				draggable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.emaillogs'/>"
				filterable="true"
				type="combo"
				dataprovider="type"
				dataelement="${requestScope.flexFieldMapping.mapping.emaillogs}"
				width="${requestScope.column_width.emaillogs}"
				clickable="true"
				sort="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.emaillogsto'/>"
				draggable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.emaillogsto'/>"
				filterable="true"
				type="input"
				maxChars="250"
				dataelement="${requestScope.flexFieldMapping.mapping.emaillogsto}"
				width="${requestScope.column_width.emaillogsto}"
				clickable="true"
				editable="true"
				sort="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.enabled'/>"
				draggable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.enabled'/>"
				filterable="true"
				type="bool"
				dataelement="${requestScope.flexFieldMapping.mapping.engine_active}"
				width="${requestScope.column_width.engine_active}"
				editable="true"
				sort="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.beginTime'/>"
				draggable="true"
				filterable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.beginTime'/>"
				type="input"
				maxChars="5"
				dataelement="${requestScope.flexFieldMapping.mapping.begin_alert}"
				width="${requestScope.column_width.begin_alert}"
				clickable="true"
				editable="true"
				sort="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.endTime'/>"
				draggable="true"
				filterable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.endTime'/>"
				type="input"
				maxChars="5"
				dataelement="${requestScope.flexFieldMapping.mapping.end_alert}"
				width="${requestScope.column_width.end_alert}"
				clickable="true"
				editable="true"
				sort="true"
		/>
		<column
				heading="<fmt:message key='inputconfig.header.threshold'/>"
				draggable="true"
				filterable="true"
				tooltip="<fmt:message key='tooltip.interfaceSettings.threshold'/>"
				type="num"
				maxChars="6"
				dataelement="${requestScope.flexFieldMapping.mapping.threshold}"
				width="${requestScope.column_width.threshold}"
				editable="true"
				sort="true"
		/>
	</columns>
</metadata>

<rows size="${requestScope.data.size()}">
<c:forEach items="${requestScope.data}" var="record">
	<row
		${record.expand == '' ? 'interfaceId="' + record.interfaceId + '" hidden="false" isFiltrable="false"' : ''}
	>
		<${requestScope.flexFieldMapping.mapping.expand} clickable="true" ${record.expand == 'Y' ? 'opened="true"' : ''}>
		<c:out value="${record.expand}" />
	</${requestScope.flexFieldMapping.mapping.expand}>

	<${requestScope.flexFieldMapping.mapping.interface_id} clickable="false">
	${record.expand == '' ? '<![CDATA[]]>' : ''}
	<c:out value="${record.messageType}" />
	</${requestScope.flexFieldMapping.mapping.interface_id}>

	<${requestScope.flexFieldMapping.mapping.emaillogs} clickable="true">
	${record.expand == 'Y' || record.expand == 'N' ? '<c:out value="${record.emailLogs}" />' : ''}
	</${requestScope.flexFieldMapping.mapping.emaillogs}>

	<${requestScope.flexFieldMapping.mapping.emaillogsto} clickable="true">
	<c:out value="${record.emailLogsTo}" />
	</${requestScope.flexFieldMapping.mapping.emaillogsto}>

		<${requestScope.flexFieldMapping.mapping.engine_active}
		selected="${record.engineActive == '1' ? 'true' : 'false'}">
	${record.engineActive == '1' ? 'on' : (record.engineActive == '0' ? 'off' : '')}
	</${requestScope.flexFieldMapping.mapping.engine_active}>

		<${requestScope.flexFieldMapping.mapping.begin_alert} clickable="false">
	<c:out value="${record.startAlertTime}" />
	</${requestScope.flexFieldMapping.mapping.begin_alert}>

		<${requestScope.flexFieldMapping.mapping.end_alert} clickable="false">
	<c:out value="${record.endAlertTime}" />
	</${requestScope.flexFieldMapping.mapping.end_alert}>

		<${requestScope.flexFieldMapping.mapping.threshold} clickable="false">
	<c:out value="${record.threshold}" />
	</${requestScope.flexFieldMapping.mapping.threshold}>
	</row>
</c:forEach>
</rows>
	<selects>
	<select id="type">
		<c:forEach items="${requestScope.elogs}" var="emailLogs">
			<option value="${emailLogs.label}">${emailLogs.label}</option>
		</c:forEach>
	</select>
</selects>
</grid>


</inputconfiguration>