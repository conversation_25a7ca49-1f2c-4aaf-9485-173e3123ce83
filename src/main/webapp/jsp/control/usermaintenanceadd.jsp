<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@page import="org.swallow.util.SwtUtil"%>
<html>

<head>
    <title>
        <c:if test="${'save' == requestScope.methodName}">

            <fmt:message key="userSetup.addScreen"/>

</c:if>
        <c:if test="${'add' != requestScope.methodName}">

            <c:if test="${'update' == requestScope.methodName}">

                <fmt:message key="userSetup.changeScreen"/>

</c:if>

</c:if>
        <c:if test="${'add' != requestScope.methodName}">

            <c:if test="${'change' != requestScope.methodName}">

                <c:if test="${'view' == requestScope.methodName}">
                    <fmt:message key="userSetup.viewScreen"/>

</c:if>

</c:if>

</c:if>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <script type="text/javascript" src="js/jquery.exportselect.js"></script>
    <SCRIPT language="JAVASCRIPT">
        var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray = ["*"];
function populateDropDown(){
ShowErrMsgWindow('${actionError}');

bodyOnLoad();

}
 /* Added parameter fileType to generate report for excel || PDF */
function submitPrint(methodName,fileType){
	document.forms[0].method.value = methodName;
	/* Assigning fileType to generate report for excel || PDF */
	document.forms[0].fileType.value = fileType.trim();
	document.forms[0].selectedUserCodeId.value=document.forms[0].elements["usermaintenance.id.userId"].value;
	document.forms[0].submit();
	/*Close the window when parent window is closed || application is log off */
	setParentChildsFocus();
}
/**
function bodyOnLoad

Draws && loads the role,entity,curreny group && selection dropdown boxes
Changes the status radio button disabled mode value.

**/
function bodyOnLoad() {
	// loads the role,entity,curreny group && selection dropdown boxes
    var roleDropBoxElement = new SwSelectBox(document.forms[0].elements["usermaintenance.roleId"], document.getElementById("roleName"));
    var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["usermaintenance.currententity"], document.getElementById("entityDesc"));
    var currGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["usermaintenance.currentCcyGrpId"], document.getElementById("currGrpName"));
    var sectionDropBoxElement = new SwSelectBox(document.forms[0].elements["usermaintenance.sectionid"], document.getElementById("sectionDesc"));

    var selectedUserID = document.forms[0].elements["usermaintenance.id.userId"].value;
    // Gets the currently logged in user ID
    var currentUserID = "<%=SwtUtil.getCurrentUserId(session)%>";
    // Checks the user id && method name. if user id is currently logged in user || admin then disabled the status radio button
    if ((selectedUserID == currentUserID || selectedUserID == "<%=SwtConstants.ADMIN%>") && '<%=request.getAttribute("methodName")%>' == "update") {
        document.forms[0].elements["usermaintenance.status"][0].disabled = true;
        document.forms[0].elements["usermaintenance.status"][1].disabled = true;
    }

    document.forms[0].elements["usermaintenance.status"].checked = "";
    changeIcon(true);

    var headerData = [];
	var dataprovider = new Array();

	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);

	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);

	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });
}

function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return submitPrint('report',type.toLowerCase());
}
<c:if test="${'yes' == requestScope.parentFormRefresh}">


window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();

</c:if>

function submitRoleForm(methodName){
    document.forms[0].lockedExternalAuthId.value = lockedExternalAuthId;
	enableFields();
	document.forms[0].method.value = methodName;

	document.forms[0].submit();

}

<!-- Mantis 2077: Added by Meftah Bouazizi to convert the password field value of form to dummy password that should not be seen in network -->
function dummyPass(pwd){
	var password='';
		for(var i=0;i<pwd.length;i++){
			password+="#";
		}
	return password;
}

 /*Start:Code Modified by Alibasha For TPR_531 related Mantis_1557  on 19-Mar-2012 */
/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/

function submitForm(methodName){
	<% session=request.getSession(); %>
	var sessionId='<%=session.getId()%>';
	if(validateField(document.forms[0].elements['usermaintenance.id.userId'],'usermaintenance.id.userId','alphaNumPat'))
	{

		if(validateField(document.forms[0].elements['usermaintenance.username'],'usermaintenance.username','printable'))
		{
		<c:if test="${'update' == requestScope.methodName}">

		if(validateField(document.forms[0].elements['usermaintenance.newPassword'],'usermaintenance.newPassword','alphaNumPatPassWord'))
			{

</c:if>
 		<c:if test="${'save' == requestScope.methodName}">

 		if(validateField(document.forms[0].elements['usermaintenance.newPassword'],'usermaintenance.newPassword','alphaNumPatPassWord'))
			{

</c:if>
 		<c:if test="${'view' == requestScope.methodName}">

 		if(validateField(document.forms[0].elements['usermaintenance.newPassword'],'usermaintenance.newPassword','alphaNumPatPassWord'))
			{

</c:if>

				if(validateField(document.forms[0].elements['usermaintenance.phonenumber'],'usermaintenance.phonenumber','numberPat'))
				{
					var emailValue=validateField(document.forms[0].elements['usermaintenance.emailId'],'usermaintenance.emailId','emailPat');
					if(emailValue)
					{
						if(validateForm(document.forms[0]) ){
						document.forms[0].method.value = methodName;
						enableFields();
						document.forms[0].userId.value = document.forms[0].elements["usermaintenance.id.userId"].value;
						try{
						document.forms[0].elements["encnewpasswd"].value=encryptPass(document.forms[0].elements["usermaintenance.id.userId"].value,document.forms[0].elements["usermaintenance.newPassword"].value)
 					    document.forms[0].elements["usermaintenance.newPassword"].value=dummyPass(document.forms[0].elements["usermaintenance.newPassword"].value);
					    document.forms[0].elements["encconfirmpasswd"].value=encryptPass(document.forms[0].elements['usermaintenance.id.userId'].value,document.forms[0].elements["usermaintenance.confirmPassword"].value);
					    document.forms[0].elements["usermaintenance.confirmPassword"].value=dummyPass(document.forms[0].elements["usermaintenance.confirmPassword"].value);
						document.forms[0].submit();
						}catch(e){
							    alert("Html internal error occurred: "+e.message);
						 }
					}
					} else {
						document.forms[0].elements['usermaintenance.emailId'].focus();
					}
				} else {
					document.forms[0].elements['usermaintenance.phonenumber'].focus();
				}
			} else {
				document.forms[0].elements['usermaintenance.newPassword'].focus();
			}
		} else {
			document.forms[0].elements['usermaintenance.username'].focus();
		}
	} else {
		document.forms[0].elements['usermaintenance.id.userId'].focus();
	}
}
/*End:Code Modified by Alibasha For TPR_531 related Mantis_1557  on 19-Mar-2012 */
function enableFields(){
	document.forms[0].elements["usermaintenance.id.userId"].disabled = "";
	document.forms[0].elements["usermaintenance.username"].disabled = "";
	document.forms[0].elements["usermaintenance.sectionid"].disabled = "";
	document.forms[0].elements["usermaintenance.language"].disabled = "";
	document.forms[0].elements["usermaintenance.phonenumber"].disabled = "";
	document.forms[0].elements["usermaintenance.emailId"].disabled = "";
	document.forms[0].elements["usermaintenance.roleId"].disabled = "";
	document.forms[0].elements["usermaintenance.currententity"].disabled = "";
	document.forms[0].elements["usermaintenance.currentCcyGrpId"].disabled = "";
	document.forms[0].elements["usermaintenance.dateFormat"].disabled = "";
	document.forms[0].elements["usermaintenance.amountDelimiter"].disabled = "";
<c:if test="${'update' == requestScope.methodName || 'save' == requestScope.methodName}">

	document.getElementById("logoutDate").disabled = "";
	document.getElementById("loginDate").disabled = "";
	document.getElementById("uDate").disabled = "";
	document.getElementById("extAuthId").disabled = "";


</c:if>
}
var lockedExternalAuthIdFromServer = '${lockedExternalAuthId}'
var lockedExternalAuthId = lockedExternalAuthIdFromServer !== '' && lockedExternalAuthIdFromServer != null && lockedExternalAuthIdFromServer  !== undefined ? lockedExternalAuthIdFromServer==false ||  lockedExternalAuthIdFromServer == 'false'?false:true:true;
function changeIcon(fromBodyOnLoad) {

        try {
        if(fromBodyOnLoad){
            if (!lockedExternalAuthId) {
                document.getElementById("lockUnlockImgExtAuth").src = "images/unlock.png";
                document.getElementById("extAuthId").disabled = false;
                document.getElementById("extAuthId").style.borderWidth = "1px";
                document.getElementById("extAuthId").style.borderStyle = "solid";
                document.getElementById("extAuthId").style.borderColor = "#a9a9a9";
                document.getElementById("extAuthId").style.backgroundColor = "white";
            } else {
                document.getElementById("lockUnlockImgExtAuth").src = "images/lock.png";
                document.getElementById("extAuthId").disabled = true;
                document.getElementById("extAuthId").style.backgroundColor = "#EBEFF8";
                document.getElementById("extAuthId").style.borderWidth = "1px";
                document.getElementById("extAuthId").style.borderStyle = "solid";
                document.getElementById("extAuthId").style.borderColor = "#a9a9a9";
            }
        }else {
            if (lockedExternalAuthId) {
                document.getElementById("lockUnlockImgExtAuth").src = "images/unlock.png";
                lockedExternalAuthId = false;
                document.getElementById("extAuthId").disabled = false;
                document.getElementById("extAuthId").style.borderWidth = "1px";
                document.getElementById("extAuthId").style.borderStyle = "solid";
                document.getElementById("extAuthId").style.borderColor = "#a9a9a9";
                document.getElementById("extAuthId").style.backgroundColor = "white";
            } else {
                document.getElementById("lockUnlockImgExtAuth").src = "images/lock.png";
                lockedExternalAuthId = true;
                document.getElementById("extAuthId").disabled = true;
                document.getElementById("extAuthId").style.backgroundColor = "#EBEFF8";
                document.getElementById("extAuthId").style.borderWidth = "1px";
                document.getElementById("extAuthId").style.borderStyle = "solid";
                document.getElementById("extAuthId").style.borderColor = "#a9a9a9";
            }
            document.forms[0].lockedExternalAuthId.value = lockedExternalAuthId;
        }
        }catch (e) {
            console.log(e);
        }
    }


function validateForm(objForm){
  var elementsRef = new Array(6);
  elementsRef[0] = objForm.elements["usermaintenance.id.userId"];
   elementTrim(document.forms[0]);
  elementsRef[1] = objForm.elements["usermaintenance.username"];
  elementsRef[2] = objForm.elements["usermaintenance.language"];

  elementsRef[3] = objForm.elements["usermaintenance.newPassword"];
  elementsRef[4] = objForm.elements["usermaintenance.currententity"];
 elementsRef[5] = objForm.elements["usermaintenance.currentCcyGrpId"];
 var passwordFlag='true';//Variable to check whether entered passwords are correct
 	if (objForm.elements["usermaintenance.confirmPassword"].value != objForm.elements["usermaintenance.newPassword"].value){
 		alert('<fmt:message key="changePassword.alert.confirmPass"/>');
 		passwordFlag='false';
 	}
 	//If both the passwords are same then it will either add the new user || update the existing user
 	if (passwordFlag == 'true'){
		return validate(elementsRef);
	}
 }



</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropDown();" onunload="call()">
    <form action="usermaintenance.do" method="post" method="post" onsubmit="return validate(this);">
        <input type="hidden" name="usermaintenance.invPassAttempt" value="${usermaintenance.invPassAttempt}" />
        <input name="method" type="hidden" value="">
        <input name="lockedExternalAuthId" type="hidden" value="">
        <input name="userId" type="hidden" value="">
        <input name="oldValue" type="hidden" value="${oldValue}">
        <input name="method1" type="hidden" value="add">
        <input name="selectedUserCodeId" type="hidden" value="">
        <input name="fileType" type="hidden" value="">
        <!-- Added by Meftah Bouazizi to pass the hidden confirm password "encconfirmpasswd" -->
        <input name="encconfirmpasswd" type="hidden" value="">
        <!-- Added by Meftah Bouazizi to pass the hidden new password "encnewpasswd" -->
        <input name="encnewpasswd" type="hidden" value="">
        <div id="usermaintenance" style="position: absolute; left: 15px; top: 10px; width: 609px; height: 589px; border: 2px outset;" color="#7E97AF">
            <div id="usermaintenance" style="position: absolute; left: 8px; top: 4px; width: 590px; height: 38px;">
                <!------------------------------first fieldset------------------------------------------->
                <div style="left: 8px; top: 4px;height: 142px;">
                    <fieldset style="width: 589px; border: 2px groove; ">
                        <legend>
                            <fmt:message key="usermaintenance.user"/>
                        </legend>
                        <table width="522" border="0" cellpadding="0" cellspacing="1" height="100">
                            <tr height="24">
                                <c:if test="${'save' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.userId*"/></b>*</td>
                                    <td width="28px">&nbsp;</td>
                                    <td width="330px" height="25">
                                        <input type="text" titleKey="tooltip.enterNewUserId" tabindex="1" cssClass="htmlTextAlpha" name="usermaintenance.id.userId" value="${usermaintenance.id.userId}"  style="width:140px;" onchange="return validateField(this,'usermaintenance.id.userId','alphaNumPat');" maxlength="12" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                                    </td>

</c:if>
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.userId*"/></b></td>
                                    <td width="28px">&nbsp;</td>
                                    <td width="330px" height="25">
                                        <input type="text" titleKey="tooltip.enterNewUserId" name="usermaintenance.id.userId" value="${usermaintenance.id.userId}"  style="width:140px;" cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                                    </td>

</c:if>
                                <c:if test="${'update' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.userId*"/></b></td>
                                    <td width="28px">&nbsp;</td>
                                    <td width="330px" height="25">
                                        <input type="text" titleKey="tooltip.enterNewUserId" name="usermaintenance.id.userId" value="${usermaintenance.id.userId}"  style="width:140px;" cssClass="htmlTextAlpha" maxlength="15" onchange="return validateField(this,'usermaintenance.id.userId','alphaNumPat');" disabled="true" />
                                    </td>

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.userName"/></b></td>

</c:if>
                                <c:if test="${'view' != requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.userName"/></b>*</td>

</c:if>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.enterUserName" tabindex="2" cssClass="htmlTextAlpha" name="usermaintenance.username" value="${usermaintenance.username}"  style="width:280px;" maxlength="30" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'usermaintenance.username','printable');" />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.enterUserName" name="usermaintenance.username" value="${usermaintenance.username}"  cssClass="htmlTextAlpha" style="width:280px;" maxlength="30" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.enterUserName" tabindex="2" cssClass="htmlTextAlpha" name="usermaintenance.username" value="${usermaintenance.username}"  style="width:280px;" maxlength="30" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'usermaintenance.username','printable');" />

</c:if>
                                </td>
                            </tr>
                            <!-- Start:Code Modified by Alibasha for Mantis 1608 to show the tooltip  -->
                            <tr height="24">
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.password"/></b></td>

</c:if>
                                <c:if test="${'view' != requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.password"/></b>*</td>

</c:if>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input type="password" titleKey="tooltip.userPassword" tabindex="3" cssClass="htmlTextAlpha" name="usermaintenance.newPassword" value="${usermaintenance.newPassword}"  style="width:140px;" maxlength="35" ${requestScope.screenFieldsStatus ? 'disabled' : ''} onchange="return validateField(this,'usermaintenance.username','alphaNumPatPassWord');" />
</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="password" titleKey="tooltip.userPassword" name="usermaintenance.newPassword" value="${usermaintenance.newPassword}"  style="width:140px;" cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="password" titleKey="tooltip.userPassword" tabindex="3" cssClass="htmlTextAlpha" name="usermaintenance.newPassword" value="${usermaintenance.newPassword}"  style="width:140px;" maxlength="35" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>
                                </td>
                            </tr>
                            <!--End:Code Modified by Alibasha for Mantis 1608 to show the tooltip  -->
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="password.confirmPassword"/></b>
                                    <c:if test="${'view' != requestScope.methodName}">
    *
</c:if>
                                </td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input type="password" titleKey="tooltip.confirmPassword" tabindex="3" cssClass="htmlTextAlpha" name="usermaintenance.confirmPassword" value="${usermaintenance.confirmPassword}"  style="width:140px;" maxlength="35" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="password" titleKey="tooltip.confirmPassword" name="usermaintenance.confirmPassword" value="${usermaintenance.confirmPassword}"  style="width:140px;" cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="password" titleKey="tooltip.confirmPassword" tabindex="3" cssClass="htmlTextAlpha" name="usermaintenance.confirmPassword" value="${usermaintenance.confirmPassword}"  style="width:140px;" maxlength="35" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.status"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="315px">
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.status" ${usermaintenance.status == '1' ? 'checked="checked"' : ''} value="${'1'}" titleKey="tooltip.selectUserStatus" id="1" style="width:13;" cssClass="htmlTextAlpha"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

                                        <label tabindex="4" for="1">
                                            <fmt:message key="userSetup.Label.Enabled"/> </label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <input type="radio" name="usermaintenance.status" ${usermaintenance.status == '2' ? 'checked="checked"' : ''} value="${'2'}" titleKey="tooltip.selectUserStatus" style="width:13;" cssClass="htmlTextAlpha" id="2"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} />

                                        <label tabindex="5" for="2">
                                            <fmt:message key="userSetup.Label.Disabled"/> </label>

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.status" ${usermaintenance.status == '1' ? 'checked="checked"' : ''} value="${'1'}" titleKey="tooltip.selectUserStatus" id="1"  style="width:13;"  cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                                        <label tabindex="4" for="1">
                                            <fmt:message key="userSetup.Label.Enabled"/> </label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <input type="radio" name="usermaintenance.status" ${usermaintenance.status == '2' ? 'checked="checked"' : ''} value="${'2'}" titleKey="tooltip.selectUserStatus" id="2"  style="width:13;"  cssClass="htmlTextAlpha" ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                                        <label tabindex="5" for="2">
                                            <fmt:message key="userSetup.Label.Disabled"/> </label>

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.status" ${usermaintenance.status == '1' ? 'checked="checked"' : ''} value="${'1'}" titleKey="tooltip.selectUserStatus" style="width:13;" cssClass="htmlTextAlpha" id="1"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                                        <label tabindex="4" for="1">
                                            <fmt:message key="userSetup.Label.Enabled"/> </label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <input type="radio" name="usermaintenance.status" ${usermaintenance.status == '2' ? 'checked="checked"' : ''} value="${'2'}" titleKey="tooltip.selectUserStatus" style="width:13;" cssClass="htmlTextAlpha" id="2"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} />
                                        <label tabindex="5" for="2">
                                            <fmt:message key="userSetup.Label.Disabled"/> </label>

                                    </c:if>
                                </td>
                            </tr>
                        </table>
                    </fieldset>
                </div>
                <!--------------------------------------end of first fieldset---------------------------------->
                <!-----------------------------------second fieldset-------------------------------------------->
                <div style="left: 8px; top: 4px;height: 118px;">
                    <fieldset style="width: 590px; border: 2px groove; ">
                        <legend>
                            <fmt:message key="usermaintenance.profile"/>
                        </legend>
                        <table width="585" border="0" cellpadding="0" cellspacing="1" height="75px">
                            <tr height="24">
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="alertMessage.roleId"/></b></td>

</c:if>
                                <c:if test="${'view' != requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="alertMessage.roleId"/></b></td>

</c:if>
                                <td width="28px">&nbsp;</td>
                                <td width="395px">
                                    <c:if test="${'save' == requestScope.methodName}">

<select id="usermaintenance.roleId" name="usermaintenance.roleId" class="htmlTextAlpha"
		titleKey="tooltip.selectRoleId" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="6"  onchange="submitRoleForm('roleEntityAccess');">
	<c:forEach items="${requestScope.roleIdList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.roleId == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>


</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                      <select id="usermaintenance.roleId" name="usermaintenance.roleId" class="htmlTextAlpha"
		titleKey="tooltip.selectRoleId" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="6"  onchange="submitRoleForm('roleEntityAccess');">
	<c:forEach items="${requestScope.roleIdList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.roleId == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>


</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                     <select id="usermaintenance.roleId" name="usermaintenance.roleId" class="htmlTextAlpha"
		titleKey="tooltip.selectRoleId" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="6"  onchange="submitRoleForm('roleEntityAccess');">
	<c:forEach items="${requestScope.roleIdList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.roleId == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>


</c:if>&nbsp;&nbsp;&nbsp; <span id="roleName" class="spantext">
                                </td>
                            </tr>
                            <tr height="24">
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.entity"/></b></td>

</c:if>
                                <c:if test="${'view' != requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.entity"/></b>*</td>

</c:if>
                                <td width="28px">&nbsp;</td>
                                <td width="395px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                      <select id="usermaintenance.currententity" name="usermaintenance.currententity" class="htmlTextAlpha"
		titleKey="tooltip.selectEntityid" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="7"  onchange="submitRoleForm('displayCcyGrpListByEntity');">
	<c:forEach items="${requestScope.roleEntityList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.currententity == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>


</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                     <select id="usermaintenance.currententity" name="usermaintenance.currententity" class="htmlTextAlpha"
		titleKey="tooltip.selectEntityid" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="7" >
	<c:forEach items="${requestScope.roleEntityList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.currententity == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>


</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                      <select id="usermaintenance.currententity" name="usermaintenance.currententity" class="htmlTextAlpha"
		titleKey="tooltip.selectEntityid" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="7"  onchange="submitRoleForm('displayCcyGrpListByEntity');">
	<c:forEach items="${requestScope.roleEntityList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.currententity == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>


</c:if> &nbsp;&nbsp;&nbsp; <span id="entityDesc" name="entityDesc" class="spantext">
                                </td>
                            </tr>
                            <tr height="24">
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.currGrp"/></b></td>

</c:if>
                                <c:if test="${'view' != requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.currGrp"/></b>*</td>

</c:if>
                                <td width="28px">&nbsp;</td>
                                <td width="395px">
                                    <c:if test="${'save' == requestScope.methodName}">
<select id="usermaintenance.currentCcyGrpId" name="usermaintenance.currentCcyGrpId" class="htmlTextAlpha"
		titleKey="tooltip.defaultCcyGrp" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="7">
	<c:forEach items="${requestScope.currencyGroupList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.currentCcyGrpId == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">
<select id="usermaintenance.currentCcyGrpId" name="usermaintenance.currentCcyGrpId" class="htmlTextAlpha"
		titleKey="tooltip.defaultCcyGrp" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="7">
	<c:forEach items="${requestScope.currencyGroupList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.currentCcyGrpId == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">
<select id="usermaintenance.currentCcyGrpId" name="usermaintenance.currentCcyGrpId" class="htmlTextAlpha"
		titleKey="tooltip.defaultCcyGrp" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="7">
	<c:forEach items="${requestScope.currencyGroupList}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.currentCcyGrpId == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if> &nbsp;&nbsp;&nbsp; <span id="currGrpName" name="currGrpName" class="spantext">
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.section"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="395px">
                                    <c:if test="${'view' == requestScope.methodName}">
<select id="usermaintenance.sectionid" name="usermaintenance.sectionid" class="htmlTextAlpha"
		titleKey="tooltip.selectSectionId" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px">
	<c:forEach items="${requestScope.sectiondetails}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.sectionid == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">
<select id="usermaintenance.sectionid" name="usermaintenance.sectionid" class="htmlTextAlpha"
		titleKey="tooltip.selectSectionId" style="width:140px">
	<c:forEach items="${requestScope.sectiondetails}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.sectionid == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">
<select id="usermaintenance.sectionid" name="usermaintenance.sectionid" class="htmlTextAlpha"
		titleKey="tooltip.selectSectionId" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px">
	<c:forEach items="${requestScope.sectiondetails}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.sectionid == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if> &nbsp;&nbsp;&nbsp; <span id="sectionDesc" name="sectionDesc" class="spantext">
                                </td>
                            </tr>
                        </table>
                    </fieldset>
                </div>
                <!-----------------------------------end of second fieldset----------------------------------------------->
                <!-----------------------------------third fieldset------------------------------------------------------->
                <div style="left: 8px; top: 4px;height: 94px;">
                    <fieldset style="width: 589px; border: 2px groove; ">
                        <legend>
                            <fmt:message key="usermaintenance.Personal"/>
                        </legend>
                        <table width="523" border="0" cellpadding="0" cellspacing="1" height="77px">
                            <tr height="24">
                                <c:if test="${'view' == requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.language"/></b></td>

</c:if>
                                <c:if test="${'view' != requestScope.methodName}">

                                    <td width="163px">&nbsp;<b>
                                            <fmt:message key="usermaintenance.language"/></b>*</td>

</c:if>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'update' == requestScope.methodName}">
<select id="usermaintenance.language" name="usermaintenance.language" class="htmlTextAlpha"
		titleKey="tooltip.selectLanguage" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="9">
	<c:forEach items="${requestScope.languagedetails}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.language == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">
<select id="usermaintenance.language" name="usermaintenance.language" class="htmlTextAlpha"
		titleKey="tooltip.selectLanguage" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="9">
	<c:forEach items="${requestScope.languagedetails}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.language == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">
<select id="usermaintenance.language" name="usermaintenance.language" class="htmlTextAlpha"
		titleKey="tooltip.selectLanguage" ${requestScope.screenFieldsStatus ? 'disabled' : ''} style="width:140px" tabindex="9">
	<c:forEach items="${requestScope.languagedetails}" var="item">
		<option value="${item.value}"
			<c:if test="${usermaintenance.language == item.value}">selected</c:if> >
				${item.label}
		</option>
	</c:forEach>
</select>

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.phoneNo"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.enterPhNo" name="usermaintenance.phonenumber" value="${usermaintenance.phonenumber}"  style="width:140px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} cssClass="htmlTextAlpha" />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.enterPhNo" tabindex="10" name="usermaintenance.phonenumber" value="${usermaintenance.phonenumber}"  style="width:140px;" maxlength="20" onchange="return validateField(this,'usermaintenance.phonenumber','numberPat');" ${requestScope.screenFieldsStatus ? 'disabled' : ''} cssClass="htmlTextAlpha" />

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.enterPhNo" tabindex="10" name="usermaintenance.phonenumber" value="${usermaintenance.phonenumber}"  style="width:140px;" maxlength="20" onchange="return validateField(this,'usermaintenance.phonenumber','numberPat');" ${requestScope.screenFieldsStatus ? 'disabled' : ''} cssClass="htmlTextAlpha" />

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.emailId"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.emailId" tabindex="11" name="usermaintenance.emailId" value="${usermaintenance.emailId}"  style="width:300px;" onchange="return validateField(this,'usermaintenance.emailId','emailPat');" maxlength="50" ${requestScope.screenFieldsStatus ? 'disabled' : ''} cssClass="htmlTextAlpha" />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.emailId" name="usermaintenance.emailId" value="${usermaintenance.emailId}"  style="width:300px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} cssClass="htmlTextAlpha" />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input type="text" titleKey="tooltip.emailId" tabindex="11" name="usermaintenance.emailId" value="${usermaintenance.emailId}"  style="width:300px;" maxlength="50" onchange="return validateField(this,'usermaintenance.emailId','emailPat');" ${requestScope.screenFieldsStatus ? 'disabled' : ''} cssClass="htmlTextAlpha" />

</c:if>
                                </td>
                            </tr>
                        </table>
                    </fieldset>
                </div>
                <!-------------------------------------end fieldset----------------------------------------------------------------->
                <!------------------------------------fourth fieldset---------------------------------------------------------------->
                <div style="left: 8px; top: 4px;height: 143px;">
                    <fieldset style="width: 589px; border: 2px groove; ">
                        <legend>
                            <fmt:message key="usermaintenance.info"/>
                        </legend>
                        <table width="522" border="0" cellpadding="0" cellspacing="1" height="109px">
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.lastLogin"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input value="${loginDate}" name="loginDate" title='<fmt:message key="tooltip.lastlog"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="12" class="htmlTextAlpha" />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input value="${loginDate}" name="loginDate" title='<fmt:message key="tooltip.lastlog"/>' style="width: 142px;" class="textNumeric" disabled="${requestScope.screenFieldsStatus}" tabindex="12" />
                                        <input value="${lastLoginIP}" name="lastLoginIP" id="lastLoginIP" title='<fmt:message key="login.notification.tooltip.lastLoginIp"/>' style="width: 152px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input value="${loginDate}" name="loginDate" id="loginDate" title='<fmt:message key="tooltip.lastlog"/>' style="width: 142px;" class="textNumeric" disabled="${requestScope.screenFieldsStatus}" tabindex="12" />
                                        <input value="${lastLoginIP}" name="lastLoginIP" id="lastLoginIP" title='<fmt:message key="login.notification.tooltip.lastLoginIp"/>' style="width: 152px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.pwdDate"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input value="${uDate}" name="uDate" id="uDate" class="textNumeric" title='<fmt:message key="tooltip.lastpass"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="13" />

</c:if>
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input value="${uDate}" name="uDate" title='<fmt:message key="tooltip.lastpass"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="13" />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input value="${uDate}" name="uDate" title='<fmt:message key="tooltip.lastpass"/>' style="width: 142px;" class="textNumeric" disabled="${requestScope.screenFieldsStatus}" tabindex="13" />

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.lastLogout"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input value="${logoutDate}" name="logoutDate" id="logoutDate" title='<fmt:message key="tooltip.lastlogout"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input value="${logoutDate}" name="logoutDate" title='<fmt:message key="tooltip.lastlogout"/>' style="width: 142px;" class="textNumeric" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input value="${logoutDate}" name="logoutDate" id="logoutDate" class="textNumeric" title='<fmt:message key="tooltip.lastlogout"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="login.notification.lastFailedLogin"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="330px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input value="${lastFaileddate}" name="lastFailedLogin" id="lastFailedLogin" title='<fmt:message key="login.notification.lastFailedLogin"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input value="${lastFaileddate}" name="lastFailedLogin" title='<fmt:message key="login.notification.lastFailedLogin"/>' style="width: 142px;" class="textNumeric" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />
                                        <input value="${lastFailedIP}" name="lastFailedIP" id="lastFailedIP" title='<fmt:message key="login.notification.lastFailedLogin"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <input value="${lastFaileddate}" name="lastFailedLogin" id="lastFailedLogin" class="textNumeric" title='<fmt:message key="login.notification.lastFailedLogin"/>' style="width: 142px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />
                                        <input value="${lastFailedIP}" name="lastFailedIP" id="lastFailedIP" title='<fmt:message key="login.notification.tooltip.lastFailedLoginIp"/>' style="width: 152px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="163px">&nbsp;<b>
                                        <fmt:message key="usermaintenance.extAuthId"/></b></td>
                                <td width="28px">&nbsp;
                                </td>
                                <td colspan="3" width="330px">
                                    <c:if test="${'save' == requestScope.methodName}">

                                        <input type="text" value="${extAuthId}" name="extAuthId" id="extAuthId" onchange="return validateField(this,'extAuthId','printable');"  maxlength="50"title='<fmt:message key="tooltip.extAuthId"/>' class="htmlTextAlpha" style="width: 300px;" ${requestScope.screenFieldsStatus ? 'disabled' : ''} tabindex="14" />
                                        <img  id="lockUnlockImgExtAuth" onclick="changeIcon(false)" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 472px; left: 140px;"		border="0" title="">

</c:if>
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="text" value="${extAuthId}" name="extAuthId" title='<fmt:message key="tooltip.extAuthId"/>' style="width: 300px;" class="htmlTextAlpha" maxlength="50" ${requestScope.screenFieldsStatus ? 'disabled' : ''} tabindex="14" />

</c:if>
                                    <c:if test="${'update' == requestScope.methodName}">

                                        <img  id="lockUnlockImgExtAuth" onclick="changeIcon(false)" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 472px; left: 140px;"		border="0" title="">
                                        <input value="${extAuthId}" type="text" name="extAuthId" id="extAuthId" onchange="return validateField(this,'extAuthId','printable');" maxlength="50" class="htmlTextAlpha" title='<fmt:message key="tooltip.extAuthId"/>' style="width: 300px;" disabled="${requestScope.screenFieldsStatus}" tabindex="14" />

</c:if>
                                </td>
                            </tr>
                        </table>
                    </fieldset>
                </div>
                <!------------------------end fourth fieldset----------------------------------------------------------------------->
                <!-----------------------------------five fieldset-------------------------------------------->
                <div style="left: 8px; top: 4px; height: 70px;">
                    <fieldset style="width: 585px; border: 2px groove;">
                        <legend>
                            <fmt:message key="generalsystem.generalSettings"/>
                        </legend>
                        <table width="383px" border="0" cellspacing="0" cellpadding="0">
                            <tr height="24">
                                <td width="163px"><b style="font-size: 9pt;">
                                        <fmt:message key="dateFormat"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="115px">
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.dateFormat" ${usermaintenance.dateFormat == '1' ? 'checked="checked"' : ''} value="${'1'}" id="1" tabindex="1" titleKey="tooltip.selectDateFormatDMY" style="width:13px;margin-bottom: 5px;"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectDateFormatDMY" style="height: 19px;">
                                            <fmt:message key="dateFormatDDMMYY"/>
                                        </label>

</c:if>
                                    <c:if test="${'view' != requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.dateFormat" ${usermaintenance.dateFormat == '1' ? 'checked="checked"' : ''} value="${'1'}" id="1" tabindex="1" titleKey="tooltip.selectDateFormatDMY" style="width:13px;margin-bottom: 5px;"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectDateFormatDMY" style="height: 19px;">
                                            <fmt:message key="dateFormatDDMMYY"/>
                                        </label>

</c:if>
                                </td>
                                <td width="112px" style="margin-bottom: 8px; padding-bottom: 5px;">
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.dateFormat" ${usermaintenance.dateFormat == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="2" tabindex="2" titleKey="tooltip.selectDateFormatMDY" style="width:13px;"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectDateFormatMDY">
                                            <fmt:message key="dateFormatMMDDYY"/>
                                        </label>

</c:if>
                                    <c:if test="${'view' != requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.dateFormat" ${usermaintenance.dateFormat == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="2" tabindex="2" titleKey="tooltip.selectDateFormatMDY" style="width:13px;"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectDateFormatMDY">
                                            <fmt:message key="dateFormatMMDDYY"/>
                                        </label>

</c:if>
                                </td>
                            </tr>
                            <tr height="24">
                                <td width="128px"><b style="font-size: 9pt;">
                                        <fmt:message key="amountDelimiter"/></b></td>
                                <td width="28px">&nbsp;</td>
                                <td width="115px">
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.amountDelimiter" ${usermaintenance.amountDelimiter == '1' ? 'checked="checked"' : ''} value="${'1'}" id="1" tabindex="1" titleKey="tooltip.selectAmountFormat" style="width:13px;margin-bottom: 5px;"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectAmountFormat" style="height: 19px;">
                                            <fmt:message key="amountcomabeforedecimal"/>
                                        </label>

</c:if>
                                    <c:if test="${'view' != requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.amountDelimiter" ${usermaintenance.amountDelimiter == '1' ? 'checked="checked"' : ''} value="${'1'}" id="1" tabindex="1" titleKey="tooltip.selectAmountFormat" style="width:13px;margin-bottom: 5px;"   ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectAmountFormat" style="height: 19px;">
                                            <fmt:message key="amountcomabeforedecimal"/>
                                        </label>

</c:if>
                                </td>
                                <td width="112px" style="margin-bottom: 8px; padding-bottom: 5px;">
                                    <c:if test="${'view' == requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.amountDelimiter" ${usermaintenance.amountDelimiter == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="2" tabindex="2" titleKey="tooltip.selectAmountFormat" style="width:13px;"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectAmountFormat">
                                            <fmt:message key="amountcomaafterdecimal"/>
                                        </label>

</c:if>
                                    <c:if test="${'view' != requestScope.methodName}">

                                        <input type="radio" name="usermaintenance.amountDelimiter" ${usermaintenance.amountDelimiter == '2' ? 'checked="checked"' : ''} value="${'2'}"   id="2" tabindex="2" titleKey="tooltip.selectAmountFormat" style="width:13px;"  ${requestScope.screenFieldsStatus ? 'disabled' : ''} /> <label titleKey="tooltip.selectAmountFormat">
                                            <fmt:message key="amountcomaafterdecimal"/>
                                        </label>

</c:if>
                                </td>
                            </tr>
                        </table>
                    </fieldset>
                </div>
                <!---------------------------------end of five fieldset-------------------------------->
            </div>
        </div>
        <!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
        <div id="UserMaintenanceAdd" style="position: absolute; left: 535; top: 609px; width: 100; height: 29px;z-index:5;visibility: visible;  ">
            <c:if test="${'save' == requestScope.methodName}">

                <div style="float: left;margin-top: 7px;">
                    <a tabindex="17" href=# onclick="javascript:openWindow(buildPrintURL('print','Add User '),'sectionprintdwindow','left=50,top=200,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
                </div>

</c:if>
            <c:if test="${'update' == requestScope.methodName}">

                <div style="float: left;margin-top: 5px;">
                    <a tabindex="17" href=# onclick="javascript:openWindow(buildPrintURL('print','Change User '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
                </div>

</c:if>
            <c:if test="${'view' == requestScope.methodName}">

                <div style="float: left;margin-top: 9px;">
                    <table style="float: left;">
                        <div id="exportReport" />
                    </table>
                </div>

</c:if>
            <c:if test="${'view' != requestScope.methodName}">

                <div style="float: left; margin-left:20;margin-bottom:10;margin-top: 5px;top: 1;">
                    <table>
                        <tr>
                            <td align="right" id="Print">
                                <a tabindex="17" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print" border="0" title='<fmt:message key="tooltip.printScreen"/>'>
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>

</c:if>
        </div>
        <c:if test="${'view' == requestScope.methodName}">

            <div style="position: absolute; left: 590; top: 612px; width: 30; height: 29px;margin-top: 5px;">
                <a tabindex="17" href=# onclick="javascript:openWindow(buildPrintURL('print','View User '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
            </div>

</c:if>
        <!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
        <div id="ddimagebuttons" color="#7E97AF" style="position: absolute; border: 2px outset; left: 15; top: 609px; width: 609px; height: 39px; visibility: visible;">
            <div id="UserMaintenance" style="position: absolute; left: 8; top: 4; width: 591px; height: 15px; visibility: visible;">
                <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
                    <tr>
                        <c:if test="${'save' == requestScope.methodName}">

                            <td width="70" title='<fmt:message key="tooltip.save"/>' id="savebutton"><a tabindex="15" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('save')">
                                    <fmt:message key="button.save"/></a></td>

</c:if>
                        <c:if test="${'update' == requestScope.methodName}">

                            <td width="70" title='<fmt:message key="tooltip.save"/>' id="savebutton"><a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" tabindex="15" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('update')">
                                    <fmt:message key="button.save"/></a></td>

</c:if>
                        <c:if test="${'update' == requestScope.methodName}">

                            <td width="70" id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'><a tabindex="16" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
                                    <fmt:message key="button.cancel"/></a>
                            </td>

</c:if>
                        <c:if test="${'save' == requestScope.methodName}">

                            <td width="70" id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'><a tabindex="16" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
                                    <fmt:message key="button.cancel"/></a>
                             </td>

</c:if>
                        <c:if test="${'view' == requestScope.methodName}">

                            <td width="70" title='<fmt:message key="tooltip.close"/>' id="closebutton"><a tabindex="15" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
                                    <fmt:message key="button.close"/></a>
                            </td>

</c:if>
                    </tr>
                </table>
            </div>
        </div>
    </form>
</body>

</html>