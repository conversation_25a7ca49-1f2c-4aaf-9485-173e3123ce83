<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:if test="${'yes' != requestScope.isViewRole}">

	<c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

		<fmt:message key="entityaccesslist.addScreen"/>

</c:if>

	<c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

		<fmt:message key="entityaccesslist.changeScreen"/>

</c:if>

	<c:if test="${'copyFrom' == sessionScope.roleMaintenanceOperation}">

   		<c:if test="${'addRole' == requestScope.screenTitle}">
			<fmt:message key="entityaccesslist.addScreen"/>

</c:if>

		<c:if test="${'changeRole' == requestScope.screenTitle}">

			<fmt:message key="entityaccesslist.changeScreen"/>

</c:if>

</c:if>


</c:if>
<c:if test="${'yes' == requestScope.isViewRole}">

	<fmt:message key="entityaccesslist.viewScreen"/>

</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="add";
window.opener.document.forms[0].clearTempRecords.value="false";
/*-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
if ('<%=request.getParameter("accountAccessSize")%>'!='null')
	window.opener.document.forms[0].accountAccessSize1.value = '<%=request.getParameter("accountAccessSize")%>';
/*-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
window.opener.document.forms[0].submit();
self.close();

</c:if>
var isAnyEntityNoAccess=true; //this variable checks if at  least one entity is selected for full || read only access
mandatoryFieldsArray ="undefined";
function submitForm(methodName){
	document.forms[0].method.value = methodName;
	validateForm();
	if(!isAnyEntityNoAccess)
	document.forms[0].submit();
	else{
	alert('<fmt:message key="copyFromRole.alert.oneEntity"/>');
	isAnyEntityNoAccess =true;
	}
}

function validateForm(){
 var table = document.getElementById("roleEntityAccessList");
 var tbody = table.getElementsByTagName("tbody")[0];
 var rows = tbody.getElementsByTagName("tr");

	for (i=0; i < rows.length; i++)
	{
		var colls = rows[i].getElementsByTagName("td");
		var radioElement1 =colls[2].getElementsByTagName("input")[0];
		var radioElement2 =colls[3].getElementsByTagName("input")[0];

		if(radioElement1.checked || radioElement2.checked)
		{
			isAnyEntityNoAccess=false;
			break;
		}
	 }
}

function bodyOnLoad(){
	/*-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
	if ('<%=request.getParameter("accountAccessSize")%>'!='null')
	window.opener.document.forms[0].accountAccessSize1.value = '<%=request.getParameter("accountAccessSize")%>';
	/*-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 --*/
	var cancelcloseElements = new Array(1);
    cancelcloseElements[0] = "closebutton";
}

</SCRIPT>
</head>

<form action="role.do" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">
<!-- Start : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 -->
<input name="accountAccessSize" type="hidden" value='<%=request.getParameter("accountAccessSize")%>'>
<!-- End : Mantis Issue 1029 : Code added for Account Access Control  by Arumugam on 23-Nov-2009 -->
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<div id="Role" style="position:absolute; left:20px; top:20px; width:720px; height:336px; border:2px outset;" color="#7E97AF">
	<div id="Role" style="position:absolute;z-index:99;left:0px; top:0px; width:700px; height:10px;">
		<table  class="sort-table" width="700px" border="0" cellspacing="1" cellpadding="0"  height="20px">
			<thead>
				<tr height="20px" >
					<td width="110px" title='<fmt:message key="tooltip.entityId"/>' class="topbar"><b><fmt:message key="entity.id.entityId"/></b></td>
					<td width="280px" title='<fmt:message key="tooltip.entityName"/>' class="topbar"><b><fmt:message key="role.entAccessList.name"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.fullAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.fullAccess"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.viewAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.readOnly"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.noAccess"/>' class="topbar"><b><fmt:message key="role.entAccessList.noAccess"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:717px; height:331px; overflowY:scroll">
		<div id="Role" style="position:absolute;z-index:99;left:0px; top:22px; width:700px; height:10px;">
			<table id="roleEntityAccessList" class="sort-table" width="700px" border="0" cellspacing="1" cellpadding="0" height="309px">
				<tbody>
					<%int count = 0; %>
					<c:forEach  items="${requestScope.roleEntityAccessList}" var="roleEntityAccessList" >
						<% if( count%2 == 0 ) {%><tr class="evencheckbox"><% }else  { %> <tr class="oddcheckbox"> <%}++count; %>
								<input type="hidden" name="entityId" value="${roleEntityAccessList.entityId}" />
								<td width="110px" align="left">${roleEntityAccessList.entityId}&nbsp;</td>
								<td width="280px">${roleEntityAccessList.entityName}&nbsp;</td>

							<c:if test="${'yes' == requestScope.isViewRole}">

							<td width="100px" align="center"><input type="radio" name="entityAccessList<%=count%>" value="0" ${roleEntityAccessList.entityAccessHTML1 == 'checked' ? 'checked="checked"' : ''} disabled="true"/></td>
							<td width="100px" align="center"><input type="radio" name="entityAccessList<%=count%>" value="1" ${roleEntityAccessList.entityAccessHTML2 == 'checked' ? 'checked="checked"' : ''} disabled="true"/></td>
							<td width="100px" align="center"><input type="radio" name="entityAccessList<%=count%>" value="2" ${roleEntityAccessList.entityAccessHTML3 == 'checked' ? 'checked="checked"' : ''} disabled="true"/></td>
							</c:if>

							<c:if test="${'yes' != requestScope.isViewRole}">

							<td width="100px" align="center"><input type="radio" name="entityAccessList<%=count%>" value="0" ${roleEntityAccessList.entityAccessHTML1 == 'checked' ? 'checked="checked"' : ''} /></td>
							<td width="100px" align="center"><input type="radio" name="entityAccessList<%=count%>" value="1" ${roleEntityAccessList.entityAccessHTML2 == 'checked' ? 'checked="checked"' : ''} /></td>
							<td width="100px" align="center"><input type="radio" name="entityAccessList<%=count%>" value="2" ${roleEntityAccessList.entityAccessHTML3 == 'checked' ? 'checked="checked"' : ''} /></td>

							</c:if>
						</tr>
					</c:forEach>
					</tbody>
					<tfoot><tr><td colspan="5" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<div id="Role" style="position:absolute; left:660; top:369px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
	<!-- Betcy:12/11/08:To display the help content properly(start) -->
	 <c:if test="${'yes' != requestScope.isViewRole}">

		  <c:if test="${'add' == sessionScope.roleMaintenanceOperation}">

					<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Entity Access '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>
		<c:if test="${'change' == sessionScope.roleMaintenanceOperation}">

			<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Entity Access '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>

</c:if>
	<c:if test="${'yes' == requestScope.isViewRole}">

		<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','View Entity Access '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>

</c:if>
	<!-- Betcy:12/11/08:To display the help content properly(end) -->
		</td>
   		<td align="right" id="Print">
				<a tabindex="3" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:360; width:720px; height:39px; visibility:visible;">
	<div id="Role" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
		<table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
			<c:if test="${'yes' == requestScope.isViewRole}">

				<tr>
					<td title='<fmt:message key="tooltip.close"/>' width="70px" id="closebutton">
						<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
					</td>
				</tr>

</c:if>


			<c:if test="${'yes' != requestScope.isViewRole}">

				<tr>
				<td width="70px" id="saveenablebutton" title='<fmt:message key="tooltip.ok"/>' >
					<a tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveEntityAccessList');"><fmt:message key="button.ok"/></a></td>
					<td width="70px" id="cancelbutton" title='<fmt:message key="tooltip.close"/>'>
						<a title='<fmt:message key="tooltip.cancel"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>
					</td>
				</tr>

</c:if>
		</table>
	</div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
<c:if test="${'yes' == requestScope.workQAccessRecordPresent}">

 alert('<fmt:message key="copyFromRole.alert.deleteWorkQ1"/>'+'${requestScope.workQAccessEntity}'+'<fmt:message key="copyFromRole.alert.deleteWorkQ2"/>');

</c:if>
</script>
</form>
</body>
</html>