<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>


<inputconfiguration>

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />

		<timing>
			<c:forEach items="${requestScope.opTimes}" var="opTime">
				<operation id="${opTime.key}">${opTime.value}</operation>
			</c:forEach>
		</timing>
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key='inputconfig.header.class'/>"
						draggable="true"
						filterable="true"
						tooltip="<fmt:message key='tooltip.interfaceSettings.class'/>"
						type="str"
						dataelement="${requestScope.flexFieldMapping.mapping.classs}"
						width="250"
						clickable="false"
						gridtype="bottom"
				/>
				<column
						heading="<fmt:message key='inputconfig.header.property'/>"
						draggable="true"
						filterable="true"
						tooltip="<fmt:message key='tooltip.interfaceSettings.property'/>"
						type="str"
						dataelement="${requestScope.flexFieldMapping.mapping.property}"
						width="350"
						clickable="true"
				/>
				<column
						heading="<fmt:message key='inputconfig.header.value'/>"
						draggable="true"
						filterable="true"
						tooltip="<fmt:message key='tooltip.interfaceSettings.value'/>"
						type="input"
						maxChars="4000"
						dataelement="${requestScope.flexFieldMapping.mapping.value}"
						width="550"
						clickable="true"
						editable="true"
				/>
			</columns>
		</metadata>

		<rows size="${requestScope.data.size()}">
			<c:forEach items="${requestScope.data}" var="record">
				<row>
					<classs clickable="false">
						<c:out value="${record.classs}"/>
					</classs>
					<property clickable="false">
						<c:out value="${record.property}"/>
					</property>
					<value clickable="false" ispassword="${record.isPassword}">
						<c:out value="${record.value}"/>
					</value>
					<beanId clickable="false">
						<c:out value="${record.beanId}"/>
					</beanId>
					<name clickable="false">
						<c:out value="${record.name}"/>
					</name>
				</row>
			</c:forEach>
		</rows>
	</grid>
</inputconfiguration>
