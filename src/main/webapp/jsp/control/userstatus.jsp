<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="userStatus.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();

</c:if>
var lastRefTime = "${requestScope.lastRefTime}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

//Start : Nithiyana<PERSON>han 02/01/2012 Mantis 1659
/**
	function disableAllButtons
	Disable the kill && view buttons
**/
function disableAllButtons()
{
	document.getElementById("killbutton").innerHTML = document.getElementById("killdisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
}
//End : Nithiyananthan 02/01/2012 Mantis 1659
function onFilterandSort()
{
	updateColors();
	disableAllButtons();
}

function bodyOnLoad()
{

	 xl = new XLSheet("UserStatusColl","table_2", ["String","CaseInsensitiveString","String","dateFormat","IPAddress"],"221122","false");

	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("UserStatusColl");

	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	document.getElementById("killbutton").innerHTML = document.getElementById("killdisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.REFRESH_BUT_STS)) ) {%>
		document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshenablebutton").innerHTML;
		<%}%>
 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.REFRESH_BUT_STS)) ) {%>
		document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	<%}%>


	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.KILL_BUT_STS)) ) {%>
		document.getElementById("killbutton").innerHTML = document.getElementById("killenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.KILL_BUT_STS)) ) {%>
		document.getElementById("killbutton").innerHTML = document.getElementById("killdisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	<%}%>

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.getElementById("lastRefTime").innerText = lastRefTime;

	var rows = xl.dataTable.tBody.rows;
	var l = rows.length;
	var userStatusSelectedRow = getStoredParam('userStatusSelectedRow');
	for (var i=0; i<l; i++)
	{
				if(rows[i].getElementsByTagName("input")[0].value== userStatusSelectedRow) {
					highLightTableRow(rows[i]);
					rows[i].className = 'selectrow';
                                        break;
				}
     }
}
function closeWindow(){
	window.close();
}

function submitFormKill(methodName) {

	var currentUserId = '${requestScope.currentUserId}';

	if(currentUserId == document.forms[0].selectedUserId.value){
		alert('<fmt:message key="cannotkill.message"/>');
	}else{
		submitForm(methodName);
	}


}

	function submitForm(methodName){

	if(methodName == "kill")
	{
		 var msg = 'You have chosen to kill the selected user.Are you sure?';
		 if (!confirm(msg))
		{
			  return;
		}
	}
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}

function buildViewURL(methodName){
	var param = 'usermaintenance.do?method='+methodName+'&selectedUserCode=';
	param += document.forms[0].selectedUserId.value;

	return  param;
	}
function onSelectTableRow(rowElement, isSelected)
{

	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedUserId.value = hiddenElement.value;
	if(isSelected)
	{
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		setStoredParam('userStatusSelectedRow',rowElement.getElementsByTagName("input")[0].value);
		if(menuEntityCurrGrpAccess == "0") {
		document.getElementById("killbutton").innerHTML = document.getElementById("killenablebutton").innerHTML;
		}
	}
	else{
		document.getElementById("killbutton").innerHTML = document.getElementById("killdisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		setStoredParam('userStatusSelectedRow','');
	}



	}

</SCRIPT>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setTitleSuffix(document.forms[0]);" onunload="call()">
<form action="userStatus.do" method="post">
<input name="method" type="hidden" value="displayList">
<input name="selectedUserId" type="hidden" >
<input name="menuAccessId" type="hidden" >
<div id="UserStatus" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:885; height:443px;">
<div id="UserStatus" style="position:absolute;z-index:99;left:0px; top:0px; width:900; height:10px;">
 <table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="858" border="0" cellspacing="1" cellpadding="0"  height="23">
	<thead>
		<tr>
			<td width="144px" height="20px" align="left" title='<fmt:message key="tooltip.sortUserId"/>'><b><fmt:message key="userStatus.header.user"/> </b></td>
			<td width="280px" height="20px" align="left" title='<fmt:message key="tooltip.sortUserName"/>'><b><fmt:message key="userStatus.header.name"/></b></td>
			<td width="120px" height="20px" align="left" title='<fmt:message key="tooltip.sortRoleId"/>'><b><fmt:message key="userStatus.header.role"/></b></td>
			<td width="170px" height="20px" align="middle" title="<fmt:message key="tooltip.sortLogonTime"/>"><b><fmt:message key="userStatus.header.logonDtTime"/></b></td>
			<td width="144px" height="20px" align="left" title='<fmt:message key="tooltip.sortIpAddress"/>'><b><fmt:message key="userStatus.header.IPAddress"/></b></td>


		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:880px; height:435;overflow-x:hidden;">
<div id="UserStatus" style="position:absolute;z-index:99;left:0px; top:20px; width:608px; height:10px;">
<table class="sort-table" id="UserStatusColl" width="858" border="0" cellspacing="1" cellpadding="0" height="415">
	<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="UserStatusColl" items="${requestScope.UserStatusColl}">
		<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
        <td width="144px" align="left">
            <input type="hidden" name="${UserStatusColl.id.userId}" value="${UserStatusColl.id.userId}" />
            ${UserStatusColl.id.userId}&nbsp;
        </td>
        <td width="280px" align="left">
            <input type="hidden" name="${UserStatusColl.users.userName}" value="${UserStatusColl.users.userName}" />
            ${UserStatusColl.users.userName}&nbsp;
        </td>
        <td width="120px" align="left">
            <input type="hidden" name="${UserStatusColl.users.roleId}" value="${UserStatusColl.users.roleId}" />
            ${UserStatusColl.users.roleId}&nbsp;
        </td>
        <td width="170px" align="middle">
            <input type="hidden" name="${UserStatusColl.id.logDate_Time}" value="${UserStatusColl.id.logDate_Time}" />
            ${UserStatusColl.displayDateTime}&nbsp;
        </td>
        <td width="144px" align="left">
            <input type="hidden" name="${UserStatusColl.ipAddress}" value="${UserStatusColl.ipAddress}" />
            ${UserStatusColl.ipAddress}
        </td>
    </tr>
     <c:set var="count" value="${count + 1}"/>
    </c:forEach>
</tbody>



<tfoot><tr><td colspan="5" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="UserStatus" style="position:absolute; left:828; top:477px; width:70; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','User Status '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		</td>

			<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:469; width:885; height:39px; visibility:visible;">

 <div id="UserStatus" style="position:absolute; left:4; top:4; width:810; height:15px; visibility:visible;">
  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70px" id="refreshbutton">
			</td>
			<td width="70px" id="killbutton">
			</td>
			<td width="70px" id="viewbutton">
			</td>
			<td id="closebutton" width="70px">
				<a title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
</div>
	<table height="30"><tr>
		<td id="lastRefTimeLable" width="690px" align="right" >
		<b><fmt:message key="label.lastRefTime"/></b>
		</td>
		<td id="lastRefTime" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">
		</td>
		</tr>
	</table>
<div id="UserStatus" style="position:absolute; left:6; top:4; width:280; height:15px; visibility:hidden;">
    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" >
	<tr>
		<td id="refreshenablebutton">
		<a  title='<fmt:message key="tooltip.refreshUserStatus"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('displayList')"><fmt:message key="button.Refresh"/></a>
		</td>
		<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.Refresh"/></a>
		</td>


		<td id="killenablebutton">
			<a  title='<fmt:message key="tooltip.killSelectedUser"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="submitFormKill('kill')"><fmt:message key="button.kill"/></a>
		</td>
		<td id="killdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.kill"/></a>
		</td>

		<td id="viewenablebutton">
			<a title='<fmt:message key="tooltip.viewDetailsSelUser"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildViewURL('view'),'userstatusviewWindow','left=50,top=190,width=659,height=610,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
		</td>
		<td id="viewdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td>

	</tr>
    </table>
  </div>
</div>
</form>
</body>
</html>