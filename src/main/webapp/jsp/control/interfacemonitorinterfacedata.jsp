<?xml version="1.0" encoding="UTF-8"?>
<%@page import="org.swallow.util.SwtUtil"%>
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>




<interfacemonitor>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
		<timing>
			<c:forEach items="${requestScope.opTimes}" var="opTime">
				<operation id="${opTime.key}">${opTime.value}</operation>
			</c:forEach>
		</timing>
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<c:forEach items="${requestScope.column_order}" var="order">
					<c:choose>
						<c:when test="${order == requestScope.flexFieldMapping.mapping.name}">
							<column
									heading="<fmt:message key='interfacemonitor.sub.threadname'/>"
									draggable="false"
									filterable="false"
									type="str"
									dataelement="${requestScope.flexFieldMapping.mapping.name}"
									width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.name]}"
							/>
						</c:when>
						<c:when test="${order == requestScope.flexFieldMapping.mapping.heartbeat}">
							<column
									heading="<fmt:message key='interfacemonitor.sub.threadhb'/>"
									draggable="false"
									filterable="false"
									type="bool"
									dataelement="${requestScope.flexFieldMapping.mapping.heartbeat}"
									width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.heartbeat]}"
							/>
						</c:when>
						<c:when test="${order == requestScope.flexFieldMapping.mapping.last_mess}">
							<column
									heading="<fmt:message key='interfacemonitor.header.last'/>"
									draggable="false"
									filterable="false"
									type="num"
									dataelement="${requestScope.flexFieldMapping.mapping.last_mess}"
									width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.last_mess]}"
							/>
						</c:when>
						<c:when test="${order == requestScope.flexFieldMapping.mapping.cph}">
							<column
									heading="<fmt:message key='interfacemonitor.header.commits'/>"
									draggable="false"
									filterable="false"
									type="num"
									dataelement="${requestScope.flexFieldMapping.mapping.cph}"
									width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.cph]}"
							/>
						</c:when>
					</c:choose>
				</c:forEach>
			</columns>
		</metadata>

		<rows size="${requestScope.fileManagerThreads.size}">
			<c:forEach items="${requestScope.fileManagerThreads}" var="bean">
				<row>
					<${requestScope.flexFieldMapping.mapping.name}>
						<c:out value="${bean.thread_name}"/>
					</${requestScope.flexFieldMapping.mapping.name}>
					<${requestScope.flexFieldMapping.mapping.heartbeat}>
						<c:out value="${bean.thread_heartbeat}"/>
					</${requestScope.flexFieldMapping.mapping.heartbeat}>
					<${requestScope.flexFieldMapping.mapping.last_mess}>
						<c:out value="${bean.fm_lastMessageCommitted}"/>
					</${requestScope.flexFieldMapping.mapping.last_mess}>
					<${requestScope.flexFieldMapping.mapping.cph}>
						<c:out value="${bean.fm_commitsPerHour}"/>
					</${requestScope.flexFieldMapping.mapping.cph}>
				</row>
			</c:forEach>
		</rows>


		<totals />
	</grid>

	<singletons>
		<c:if test="${not empty requestScope.procedureThread}">
			<singleton id="procedureHeartbeat">
				<c:out value="${requestScope.procedureThread.thread_heartbeat}"/>
			</singleton>
			<singleton id="procedureIsRunning">
				<c:out value="${requestScope.procedureThread.proc_running}"/>
			</singleton>
			<singleton id="procedureLastStarted">
				<c:out value="${requestScope.procedureThread.proc_lastStarted}"/>
			</singleton>
			<singleton id="procedureLastExec">
				<c:out value="${requestScope.procedureThread.proc_lastExecutionTime}"/>
			</singleton>
			<singleton id="procedureLastResult">
				<c:out value="${requestScope.procedureThread.proc_lastResult}"/>
			</singleton>
			<singleton id="procedureLastException">
				<c:out value="${requestScope.procedureThread.proc_lastException}"/>
			</singleton>
			<singleton id="interfacemanagerHeartbeat">
				<c:out value="${requestScope.engineThread.thread_heartbeat}"/>
			</singleton>
			<singleton id="dirmanagerHeartbeat">
				<c:out value="${requestScope.dirManagerThread.thread_heartbeat}"/>
			</singleton>
		</c:if>
	</singletons>

	<labelled_singletons>
		<c:forEach items="${requestScope.generalThreads}" var="generalThread">
			<singleton label="${generalThread.thread_name}">
				<c:out value="${generalThread.thread_heartbeat}"/>
			</singleton>
		</c:forEach>
	</labelled_singletons>
</interfacemonitor>
