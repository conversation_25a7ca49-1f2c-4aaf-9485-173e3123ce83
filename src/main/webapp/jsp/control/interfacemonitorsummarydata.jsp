<?xml version="1.0" encoding="UTF-8"?>
<!--
- Migrated Interface Monitor XML file.
- Provides data grids (Top Grid and Bottom Grid) with dynamic fields and formatting.
- Migration ensures compatibility with modern frameworks and standards.
-->

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page import="org.swallow.util.SwtConstants"%>



<c:if test="${requestScope.hideForNotInternal != 'true'}">

<interfacemonitor
refresh="${requestScope.refresh_period}"
lastRefTime="${requestScope.lastRefTime}"
from="${requestScope.fromDate}"
to="${requestScope.fromDate}"
dateformat="${requestScope.session.CDM.dateFormatValue}"
dateComparing="${requestScope.dateComparing == 'Y'}"
sysDateFrmSession="${requestScope.sysDateFrmSession}"
sessionToDate="${requestScope.sessionToDate}">

<request_reply>
    <status_ok>${requestScope.reply_status_ok}</status_ok>
    <message>${requestScope.reply_message}</message>
    <location />
    <timing>
        <c:forEach items="${requestScope.opTimes}" var="opTime">
            <operation id="${opTime.key}">${opTime.value}</operation>
        </c:forEach>
    </timing>
</request_reply>

<grid>
<metadata>
    <columns>
        <c:forEach items="${requestScope.column_order}" var="order">
            <c:choose>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.interfaceId}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.interfaceId"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.interfaceId"/>"
                            draggable="false"
                            filterable="true"
                            type="str"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.interfaceId}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.interfaceId]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.enabled}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.activeInterface"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.active"/>"
                            draggable="true"
                            filterable="true"
                            type="bool"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.enabled}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.enabled]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.enginestatus}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.interfaceStatus"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.status"/>"
                            draggable="true"
                            filterable="true"
                            type="str"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.enginestatus}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.enginestatus]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.lastMessage}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.lastMessageRecieved"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.lastMessage"/>"
                            draggable="true"
                            filterable="true"
                            type="num"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.lastMessage}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.lastMessage]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.total}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.totalMessages"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.total"/>"
                            draggable="true"
                            filterable="true"
                            type="num"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.total}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.total]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.processed}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.totalMessagesProcessed"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.processed"/>"
                            draggable="true"
                            filterable="true"
                            type="num"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.processed}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.processed]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.awaiting}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.totalMessagesAwaiting"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.awaiting"/>"
                            draggable="true"
                            filterable="true"
                            type="num"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.awaiting}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.awaiting]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.filtered}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.totalFilteredMessages"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.filtered"/>"
                            draggable="true"
                            filterable="true"
                            type="num"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.filtered}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.filtered]}" />
                </c:when>
                <c:when test="${order == requestScope.flexFieldMapping.mapping.bad}">
                    <column
                            heading="<fmt:message key="label.interfaceMonitor.header.totalBadMessages"/>"
                            tooltip = "<fmt:message key="tooltip.interfaceMonitor.bad"/>"
                            draggable="true"
                            filterable="true"
                            type="num"
                            sort="true"
                            dataelement="${requestScope.flexFieldMapping.mapping.bad}"
                            width="${requestScope.column_width[requestScope.flexFieldMapping.mapping.bad]}" />
                </c:when>
            </c:choose>
        </c:forEach>
    </columns>
</metadata>

<rows size="${requestScope.result.size()}">
<c:forEach items="${requestScope.result}" var="bean">
    <row>
        <${requestScope.flexFieldMapping.mapping.interfaceId} clickable="false">
        <c:out value="${bean.key}" />
    </${requestScope.flexFieldMapping.mapping.interfaceId}>

    <${requestScope.flexFieldMapping.mapping.enabled} clickable="false">
    ${bean.value[SwtConstants.INTERFACE_ACTIVE] == 1 ? 'ENABLED' : 'DISABLED'}
    </${requestScope.flexFieldMapping.mapping.enabled}>

    <${requestScope.flexFieldMapping.mapping.enginestatus} clickable="false">
    ${bean.value[SwtConstants.INTERFACE_STATUS] != null ? bean.value[SwtConstants.INTERFACE_STATUS] : ''}
    </${requestScope.flexFieldMapping.mapping.enginestatus}>

    <${requestScope.flexFieldMapping.mapping.lastMessage} clickable="false">
    ${bean.value[SwtConstants.INTERFACE_LAST_MESSAGE] != null ? bean.value[SwtConstants.INTERFACE_LAST_MESSAGE] : ''}
    </${requestScope.flexFieldMapping.mapping.lastMessage}>

        <${requestScope.flexFieldMapping.mapping.total} clickable="false">
    ${bean.value[SwtConstants.INTERFACE_TOTAL_COUNT] < 1 ? '0' : bean.value[SwtConstants.INTERFACE_TOTAL_COUNT]}
    </${requestScope.flexFieldMapping.mapping.total}>

        <${requestScope.flexFieldMapping.mapping.processed} clickable="false">
    ${bean.value[SwtConstants.INTERFACE_PROCESSED] < 1 ? '0' : bean.value[SwtConstants.INTERFACE_PROCESSED]}
    </${requestScope.flexFieldMapping.mapping.processed}>

        <${requestScope.flexFieldMapping.mapping.awaiting} clickable="false">
    ${bean.value[SwtConstants.INTERFACE_AWAITING] < 1 ? '0' : bean.value[SwtConstants.INTERFACE_AWAITING]}
    </${requestScope.flexFieldMapping.mapping.awaiting}>

        <${requestScope.flexFieldMapping.mapping.filtered} status="1" clickable="${bean.value[SwtConstants.INTERFACE_FILTERED] < 1 ? 'false' : 'true'}">
    ${bean.value[SwtConstants.INTERFACE_FILTERED] < 1 ? '0' : bean.value[SwtConstants.INTERFACE_FILTERED]}
    </${requestScope.flexFieldMapping.mapping.filtered}>

        <${requestScope.flexFieldMapping.mapping.bad} status="2" clickable="${bean.value[SwtConstants.INTERFACE_BAD] < 1 ? 'false' : 'true'}">
    ${bean.value[SwtConstants.INTERFACE_BAD] < 1 ? '0' : bean.value[SwtConstants.INTERFACE_BAD]}
    </${requestScope.flexFieldMapping.mapping.bad}>
    </row>
</c:forEach>
</rows>

    <totals />
</grid>
</c:if>
<!-- Start: Added for Mantis 1928 by KaisBS: Interface Monitor: Add 'Show XML - Internal' menu option on context menu -->
<c:if test="${requestScope.internal == 'true'}">
    ${requestScope.heartbeat}
</c:if>
<c:if test="${requestScope.hideForNotInternal != 'true'}">
    <!-- End -->
    </interfacemonitor>
    <!-- Added for Mantis 1928 by KaisBS: Interface Monitor: Add 'Show XML - Internal' menu option on context menu -->
</c:if>