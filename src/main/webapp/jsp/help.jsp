<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<html>
<head>
<title><fmt:message key="shortcut.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

</head>
<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="setParentChildsFocus();" onunload="call()">
 <div id="Help" style="position:absolute; left:20; top:20; width:380; height:15px; visibility:visible;">
<table width="380" border="0" cellpadding="0" cellspacing="0" height="50">
<tr width="200px" height="50" >
  <textarea style="width:382;height:250" rows="18" cols="60" disabled="true">${screenText}</textarea>
</tr>
</table>
</div>
<!--Start Code modified by <PERSON><PERSON><PERSON><PERSON><PERSON> for Mantis 1347 for tab navigation on 12-May-2011-->
<div id="ManualSweep" style="position:absolute;  top:290; width:0; height:5px; visibility:visible;z-index: 150;padding-left: 330px;">
	<table width="50px" border="0" cellspacing="0" cellpadding="0" height="20" width="50px">
		<tr>
			<td align="right" id="Print">
				<a tabindex="11" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:280; width:380; height:45px; visibility:visible;">
  <div id="Help" style="position:absolute; left:6; top:8; width:370; height:15px; visibility:visible;">
  <table width="70" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td id="closebutton">
                                                      <a tabindex="5" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
       </tr>
</table>
</div>
</div>
<!--End Code modified by Chidambaranathan for Mantis 1347 for tab navigation on 12-May-2011-->
<script type="text/javascript">
</script>

</body>
</html>