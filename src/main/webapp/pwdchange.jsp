<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ taglib uri="/WEB-INF/tags-swallow" prefix="swallow" %>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<title>Change Password &nbsp;-&nbsp;Smart Predict </title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var screenRoute = "jsAngularBridge";


function clearPasswordChangeStatus() {
	sessionStorage.removeItem('passwordChangeStatus');
	sessionStorage.removeItem('changepassworddays');
	sessionStorage.removeItem('changepasswordadlertsent');
}


function bodyOnLoad() {
	sessionStorage.setItem('changepasswordadlertsent', 'N');
     var userId='<%=SwtUtil.getCurrentUserId(request.getSession())%>';
	 document.getElementById("userId").value = userId;
}
	
	
	

function logout(){
	document.forms[0].action = "logout.do";
	document.forms[0].method.value = "logout";
	document.forms[0].submit();
}
function validateForm(objForm){
  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["passwordHisory.password"];
  elementsRef[1] = objForm.elements["user.password"];
  elementsRef[2] = objForm.elements["user.password1"];
  return validate(elementsRef);
 }
/* Start:Code modified by Alibasha for Mantis 1608 on 10-Feb-2012 */
function submitForm(methodName){

	<% session=request.getSession(); %>
	var sessionId='<%=session.getId()%>';	
	var userId='<%=SwtUtil.getCurrentUserId(request.getSession())%>';
if(validateField(document.forms[0].elements['user.password'],'user.password','alphaNumPatPassWord')) {

	if (!validateForm(document.forms[0])) {
		return;
	}
	var pass = document.forms[0].elements['user.password1'].value;
	var pass1 = document.forms[0].elements['user.password'].value;
	if (pass != pass1) {
		alert('<fmt:message key="pwdchange.password.bothBoxes"/>');
		return;
	}
	document.forms[0].method.value = methodName;
	<c:if test="${requestScope.screen == 'logon'}">
		document.forms[0].action = "changepassword.do?screen=logon";
	</c:if>

	/* Mantis 2077: encrypt the password and set it in the hidden variable*/
	try {
		clearPasswordChangeStatus();
		document.forms[0].elements["encpasswd"].value = instanceElement.encrypt(sessionId, userId, document.forms[0].elements["user.password"].value);
		document.forms[0].elements["encpasswd1"].value = instanceElement.encrypt(sessionId, userId, document.forms[0].elements["user.password1"].value);
		document.forms[0].elements["encoldpasswd"].value = instanceElement.encrypt(sessionId, userId, document.forms[0].elements["passwordHisory.password"].value);
		document.forms[0].submit();
	} catch (e) {
		alert('<fmt:message key="pwdchange.html.internalerror"/>' + e.message);
	}
} else {
	document.forms[0].elements['user.password'].focus();
}
}

/* End:Code modified by Alibasha for Mantis 1608 on 10-Feb-2012 */


</SCRIPT>

</head>
<form action="changepassword.do" method="post">

<%@ include file="/angularscripts.jsp"%>

<!-- Mantis 2077: Added by Meftah Bouazizi to call encrypt function from Flex side to java script  jsflexbridge.swf file -->
<input name="method" type="hidden" value="save">

<!-- if 'deletePwdHst' == 'y' then the record with seq_no = 0 is deleted from  s_password_history -->
<input name="deletePwdHst" type="hidden" value= "${requestScope.deletePwdHst}">
<!-- Added by Meftah Bouazizi to pass the hidden password "encpasswd" -->
    <input name="encpasswd" type="hidden" value="">
    <input name="encpasswd1" type="hidden" value="">
    <input name="encoldpasswd" type="hidden" value="">
<body class="login" onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">
  <table width="420" cellpadding="0" cellspacing="0" border="0" style="position:absolute; top:20px;">

	<tr >
	  <td align="left" width="30%" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="images/swallow2.gif" ></td>
	  <td align="center" width="40%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	  <td align="right" width="30%" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
    </tr>
  	</table>
	<div  id="Login" border="1" style="position:absolute; background:#E8F3FE; left:280px; top:167px; width:445px; height:252px;border: 2px outset;" color="#BAE7FE" >
	<div  id="Login" style="position:absolute; left:15px; top:15px; width:405px; height:245px;">
	 <table class="content" width="410" height="20" bgcolor="#1F63AA" border="0">
			<tr align="center" >
				<td  width="210px" colspan=3>
			 <b><font color="#FFFFFF" size=2>&nbsp;</font></b></td>
			</tr>
			<tr   align="left" border="white">
				<td colspan=3>
			 <b><font color="#FFFFFF" size=4>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="pwdchange.welcomePredict"/> </font></b><br><br><br>
				</td>
			</tr>
			<tr>
			 <td align="left" width="20px">&nbsp;</td>
             <td align="left" width="210px"><font color="#FFFFFF" size=2><b><fmt:message key="pwdchange.userId"/></font></td>		
		     <td width="200px" align="left">
               <input type="text"  id="userId" class="htmlTextAlpha" style="widht:158px" tabindex="1" title='<fmt:message key = "pwdchange.tooltip.userId"/>' disabled /></td>
            </tr>
			<tr>
			<td align="left" width="20px">&nbsp;</td>
			<td align="left" width="210px"><font color="#FFFFFF" size=2><b><fmt:message key="password.old"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font></td>
				<td width="200px" align="left"><input type="password" id="passwordHisory.password" title='<fmt:message key="pwdchange.enterOldPassword"/>' tabindex="1"/></td>
			</tr>
			<!-- Start:Code modified by Alibasha for Mantis 1608 on 10-Feb-2012 -->
			<tr>
			<td align="left" width="20">&nbsp;</td>
			<td align="left" width="210px"><font color="#FFFFFF" size=2><b><label for="user.password"><fmt:message key="password.new"/></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font></td>
			<td width="200"><input type="password" id="user.password" title="<fmt:message key='pwdchange.enterAcceptableCharacter'/> :[a-z,A-Z],[0-9],[~!@#$%^&*()-_=+[]:;'&ldquo;,<.>/?])" tabindex="2" 
			    onchange="return validateField(this,'user.password','alphaNumPatPassWord');" /></td>
		</tr >
		<!-- End:Code modified by Alibasha for Mantis 1608 on 10-Feb-2012 -->
			<tr>
			<td align="left" width="20px">&nbsp;</td>
			<td align="left" width="235px">
			<font color="#FFFFFF" size=2><b><label for="password.retypeNew"><fmt:message key="password.retypeNew"/></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font></td>
			<td width="200px"><input type="password" id="user.password1" title='<fmt:message key="pwdchange.reenterNewPassword"/>' tabindex="2" /></td>
		</tr >
	<!--	<tr height="40">
			<td align="right">&nbsp;</td><br>
		</tr > -->
		<tr/><tr/>
		<tr/><tr/><tr/><tr/>
		<tr/>
		</table>
	</div>
</div>
<!--	<div  id="Login" border="1" style="position:absolute; background:#E8F3FE; left:250px; top:135px; width:400px; height:290px;border: 2px outset;" color="#BAE7FE" >
	<div  id="Login" style="position:absolute; left:15px; top:3px; width:385px; height:275px;">
	 <table class="content" width="363" height="20" bgcolor="#1F63AA" border="0">
			<tr align="center" >
				<td  width="100%" colspan=2>
			 <b><font color="#FFFFFF" size=4>Welcome to Smart Predict 2.0</font></b></td>
			</tr>
			<tr   align="center" border="white">
				<td colspan=2>
			 <b><font color="#FFFFFF" size=2>Please Enter Your New Password</font></b><br><br><br>
				</td>
			</tr>

			<tr>
				<td align="right" width="40%"> <b><font color="#FFFFFF" size=2><fmt:message key="password.old"/></font></b></td>
				<td width="60%" align="left">
					<html:password property="passwordHisory.password" style="widht:100px"/>
				</td>
			</tr>
			<tr>
		
				<td align="right" width="40%">
				<b><font color="#FFFFFF" size=2><fmt:message key="password.new"/></font></b>
				<td width="60%" align="left">
					<html:password property="user.password" style="widht:100px"/> 
				 </td>
			</tr>
			<tr>
		
				<td align="right" width="50%">
				<b><font color="#FFFFFF" size=2><fmt:message key="password.retypeNew"/></font></b>
				
				<td width="60%" align="left">
					<html:password property="user.password1" style="widht:100px"/>
				 </td>
			</tr>
		<tr height="40">
			<td align="right">&nbsp;</td><br>
		</tr >
		</table>
	</div>
</div> -->
<div id="ddimagebuttons" style="position:absolute; left:420px; top:379px; width:450px; height:10px;">
	<table border="0" cellspacing="0"  cellpadding="0"  width="80%">
	<tr align="center" >
		<td width="20%"><a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="submitForm('save');"><fmt:message key="button.save"/></a></td>
		<td width="70%">
		
		<!--<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();">Cancel</a>-->
			<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="logout()"><fmt:message key="button.cancel"/></a>
		</td>
	</tr>
 </table>
</div>

 <blockquote>&nbsp;</blockquote>
            <p>&nbsp;</p>
</form>
</body>
</html>
