<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="java.text.SimpleDateFormat"%>
<%@ include file="/taglib.jsp"  %>
<fmt:setBundle basename="dictionary" />
<fmt:setLocale value="en" />
<html>
<head>
<title>Error &nbsp;-&nbsp;Smart Predict</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
$(document).ready(function(){
	$("#errorContent").text(function( index, value ) {
		  return value.replace(/\\u0027/g, '\'').replace(/\\u0022/g, '"').replace(/\\u0028/g, '(').replace(/\\u0029/g, ')').replace(/\\u003C/g, '<').replace(/\\u003E/g, '>');
	});
});
</SCRIPT>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();" onUnload="call()">
<BR><BR><BR>
	<h3><fmt:message key="error.unexpectedError"/><BR><fmt:message key="error.contactAdmin"/></h3>
</p>
<BR>
<div id="errorContent" style="margin-left: 5px;">
<fmt:message key="error.errorDateTime"/>
<%=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())%>

</div>
</body>
</html>



