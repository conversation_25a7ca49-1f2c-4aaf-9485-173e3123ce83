<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>

<html>
<head>
<title>Error &nbsp;-&nbsp;Smart Predict</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">


var message = '${ErrorMessage}';
function onBodyload(){
	alert(message);			
	getMenuWindow().onCloseMenuWindow();

}

function onclosemenuwindow()
{
	//call();
	//window.clearInterval(internalMsgCheckObj);
	//window.clearInterval(sweepMsgCheckObj);
	var mainWindow=getMenuWindow();
	var Container = mainWindow.windowContainer;
	
	//alert(windowContainer);
	Container.closeAllWindows();
	mainWindow.document.forms[0].action = "logout.do?method=logout";
	mainWindow.document.forms[0].submit();
}
</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();onBodyload();" onunload="call()">
<table> <BR><BR><BR><H4> &nbsp;&nbsp;&nbsp;&nbsp;${ErrorMessage}.<BR>&nbsp;&nbsp;&nbsp;<h4>
</body>
</html>



