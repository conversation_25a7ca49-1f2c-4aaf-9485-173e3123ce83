/*
 * @(#)ReportAction.java 1.0 25/06/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.report.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.SwtDynamicReportImpl;
import org.swallow.model.ExportObject;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.pcm.report.service.ReportManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.pcm.work.service.DashboardManager;
import org.swallow.reports.web.TurnoverReportAction;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.XmlEncoder;
import org.swallow.work.model.ColumnMetadata;
import org.swallow.work.model.QueryResult;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/reportPCM", "/reportPCM.do"})
public class ReportAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/report/report");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();


	@Autowired
	private ReportManager reportManager = null;

	public static String PDF_EXPORT = "pdf";
	public static String CSV_EXPORT = "csv";
	public static String XLS_EXPORT = "excel";

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(ReportAction.class);

	public void setReportManager(ReportManager reportManager) {
		this.reportManager = reportManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "report":
				return report();
			case "dashboardReport":
				return dashboardReport();
			case "dashbordBreakDownReport":
				return dashbordBreakDownReport();
			case "reportDisplay":
				return reportDisplay();
		}


		return unspecified();
	}

	protected String unspecified() throws SwtException {
		return getView("success");
	}

	public String report() throws SwtException {

		String entityId = null;
		String currencyCode = null;
		String currencyName = null;
		String reportFromDate = null;
		String reportToDate = null;
		String entityName = null;
		String exportType = null;
		String useCcyMultiplier = null;
		String reportType = null;
		String accountGroup = null;
		String accountGroupName = null;
		String source = null;
		String sourceName = null;
		String singleOrRange = null;
		JasperPrint jasperPrint = null;
		JRPdfExporter pdfexporter;
		JRXlsExporter xlsxporter;
		JRCsvExporter csvexporter;
		String sheetName[] = new String[1];
		ServletOutputStream out;

		try {

			log.debug(this.getClass().getName() + "- [report] - Entering ");

			exportType = request.getParameter("exportType");
			reportType = request.getParameter("reportType");
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityName");
			currencyCode = request.getParameter("currencyCode");
			currencyName = request.getParameter("ccyCodeName");
			accountGroup = request.getParameter("accountGroup");
			accountGroupName = request.getParameter("accountGroupName");
			source = request.getParameter("source");
			sourceName = request.getParameter("sourceName");
			reportFromDate = request.getParameter("fromDate");
			reportToDate = request.getParameter("toDate");
			useCcyMultiplier = request.getParameter("useCcy");
			singleOrRange = request.getParameter("singleOrRange");

			out = response.getOutputStream();

			// If single day is selected then to date = from date
			if (singleOrRange.equals(SwtConstants.TURNOVER_SINGLE_DAY)) {
				reportToDate = reportFromDate;
			}

			PCReport pcReport = new PCReport();
			pcReport.setReportType(reportType);
			pcReport.setCurrencyCode(currencyCode);
			pcReport.setCurrencyName(currencyName);
			pcReport.setAccountGroup(accountGroup);
			pcReport.setAccountGroupName(accountGroupName);
			pcReport.setEntityId(entityId);
			pcReport.setEntityName(entityName);
			pcReport.setSource(source);
			pcReport.setSourceName(sourceName);
			pcReport.setUseCurrencyMultiplier(useCcyMultiplier);
			pcReport.setSingleDate("S".equals(singleOrRange));
			pcReport.setFromDate(SwtUtil.parseDate(reportFromDate, SwtUtil.getCurrentDateFormat(request.getSession())));

			if ("S".equals(singleOrRange)) {
				pcReport.setToDate(null);
			} else {
				pcReport.setToDate(SwtUtil.parseDate(reportToDate, SwtUtil.getCurrentDateFormat(request.getSession())));
			}
			pcReport.setRequest(request);

			jasperPrint = reportManager.getBlockPaymentReport(pcReport);

			if (PDF_EXPORT.equals(exportType)) {

				/* Initializing the JRDFExporter */
				pdfexporter = new JRPdfExporter();
				/* To set the output type as PDF file */
				response.setContentType("application/pdf");
				/* To set the content as attachment */
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
				/* To pass the filled report */
				pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				/* Providing the output stream */
				pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				/* Exporting as UTF-8 */
				pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				/* Export Report */
				pdfexporter.exportReport();
			} else if (XLS_EXPORT.equals(exportType)) {
				xlsxporter = new JRXlsExporter();
				response.setContentType("application/xls");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xls");

				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
//				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.FALSE);
//				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.FALSE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				/* Set the sheet name as userId */
				sheetName[0] = SwtUtil.getMessage("PCMReport.BlockedPayments", request);
				xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES, sheetName);
				// Export Report to Excel
				xlsxporter.exportReport();
			} else if (CSV_EXPORT.equals(exportType)) {
				csvexporter = new JRCsvExporter();
				response.setContentType("application/csv");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".csv");

				csvexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				csvexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				csvexporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_GRAPHICS, Boolean.FALSE);

				csvexporter.exportReport();
			}

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [report] method - " + swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [report] method - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "report", TurnoverReportAction.class), request,
					"");
			return getView("fail");
		}

		log.debug(this.getClass().getName() + "- [report] - Exiting ");
		return null;
	}

	public String dashboardReport() throws SwtException {

		log.debug(this.getClass().getName() + "- [report] - Entering ");

		/* Method's local variable declaration. */
		String entityName = null;
		String entityId = null;
		String defaultEntityId = null;
		String listEntities = null;
		String applyCurrencyThreshold = null;
		String applyCurrencyMultiplier = null;
		String spreadOnly = null;
		String value = null;
		String outputFormat = null;
		JasperPrint jasperPrint = null;
		JRPdfExporter pdfexporter;
		JRXlsExporter xlsxporter;
		JRCsvExporter csvexporter;
		String sheetName[] = new String[1];
		ServletOutputStream out;
		Date systemDate = null;
		SystemFormats sysformat = null;
		String systemDateAsString = null;
		String selectedDate = null;
		Date selectedDateIso = null;
		String formatDate = null;
		Date systemDBDate = null;
		List<String> sheetNamesList = null;
		String userId = null;
		try {
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			out = response.getOutputStream();
			outputFormat = request.getParameter("exportType");
			if (SwtUtil.isEmptyOrNull(outputFormat)) {
				outputFormat = PDF_EXPORT;
			}
			entityName = request.getParameter("entityName");
			entityId = request.getParameter("entityId");
			if (!SwtUtil.isEmptyOrNull(entityId) && !entityId.equals("All")) {
				listEntities = convertListToInParameterForQuery(entityId);
			} else {
				listEntities = "All";
				entityId = "All";
			}
			applyCurrencyThreshold = request.getParameter("useThreshold");
			if (SwtUtil.isEmptyOrNull(applyCurrencyThreshold)) {
				applyCurrencyThreshold = SwtConstants.NO;
			}
			applyCurrencyMultiplier = request.getParameter("useCcyMultiplier");
			if (SwtUtil.isEmptyOrNull(applyCurrencyMultiplier)) {
				applyCurrencyMultiplier = SwtConstants.NO;
			}

			/* Reading the spreadOnly value from the request */
			spreadOnly = request.getParameter("spreadOnly");
			if (spreadOnly == null || spreadOnly.isEmpty()) {
				spreadOnly = SwtConstants.NO;
			}

			/* Reading the Volume/Value from the request */
			value = request.getParameter("valueVolume");
			if (value == null || value.isEmpty()) {
				value = SwtConstants.YES;
			}
			defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(defaultEntityId);
			systemDateAsString = request.getParameter("fromDate");
			formatDate = request.getParameter("dateformat");
			if (systemDateAsString == null) {
				systemDateAsString = SwtUtil.getSysDateWithFmt(systemDBDate);
			}

			systemDate = SwtUtil.getDBSysDatewithTime(systemDBDate);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			formatDate = SwtUtil.getCurrentDateFormat(request.getSession());
			if (!SwtUtil.isEmptyOrNull(systemDateAsString)) {
				Date date = SwtUtil.parseDate(systemDateAsString, formatDate);
				selectedDate = sdf.format(date);
				selectedDateIso = SwtUtil.parseDate(selectedDate, "yyyy-MM-dd");
			}
			PCReport pcReport = new PCReport();
			pcReport.setFromDate(selectedDateIso);
			// pcReport.setToDate(SwtUtil.getSysParamDate());
			pcReport.setRequest(request);
			pcReport.setShowValueOrVolume(value);
			pcReport.setSpreadOnly(spreadOnly);
			pcReport.setUseCurrencyMultiplier(applyCurrencyMultiplier);
			pcReport.setApplyCcyThreshold(applyCurrencyThreshold);
			pcReport.setEntityId(listEntities);
			pcReport.setEntityName(entityName);
			pcReport.setUserId(userId);

			jasperPrint = reportManager.getPCMDashbordMonitorReport(pcReport);

			if (PDF_EXPORT.equals(outputFormat)) {

				pdfexporter = new JRPdfExporter();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");

				pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				pdfexporter.exportReport();

			} else if (XLS_EXPORT.equals(outputFormat)) {
				xlsxporter = new JRXlsExporter();
				response.setContentType("application/xls");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xls");

				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.FALSE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.FALSE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				/* Set the sheet name as userId */
				sheetName[0] = userId;
				xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES, sheetName);
				// Export Report to Excel
				xlsxporter.exportReport();

			} else if (CSV_EXPORT.equals(outputFormat)) {
				csvexporter = new JRCsvExporter();
				response.setContentType("application/csv");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".csv");

				csvexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				csvexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				csvexporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_GRAPHICS, Boolean.FALSE);

				csvexporter.exportReport();
			}

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [report] method - " + swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [report] method - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "report", TurnoverReportAction.class), request,
					"");
			return getView("fail");

		}

		log.debug(this.getClass().getName() + "- [report] - Exiting ");
		return null;
	}

	public String dashbordBreakDownReport() throws SwtException {
		String currentFilter = null;
		String currentSort = null;
		String currPageStr = null;
		String exportType = null;
		ArrayList<FilterDTO> filterData = null;
		FilterDTO fDTO = null;
		String fileName = null;
		String titleSuffix = "";
		String baseQuery = "";
		//String facilityID = "";
		ColumnMetadata metadata;
		QueryResult queryResult;
		ArrayList<ColumnDTO> columnData = null;
		ColumnDTO cDTO = null;
		XmlEncoder xmlEncoder = new XmlEncoder();
		SwtDynamicReportImpl dynamicReport = null;
		// Token for download corresponding file
		String tokenForDownload = null;
		String filter = null;
		String roleId = null;
		ActionMessages errors = null;
		CommonDataManager CDM = null;
		// hold the cancelExport value in CDM
		String cancelExport = "";
		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		String selectedEntity = null;
		String selectedEntityName = null;
		String selectedAccount = null;
		String selectedDate = null;
		String selectedStatus= null;
		String fromScreen= null;
		String applyCcyThreshold = null;
		String useCcyMultiplier = null;
		String systemDateAsString = null;
		String selectedDateAsISO = null;
		String blockedReason = null;
		String sodBalance = null;
		String confirmedCredit = null;
		String creditLine = null;
		String releasePay = null;
		String exCreditLine = null;
		String incCreditLine = null;
		String reserve = null;
		String ascDesc = null;
		String order = null;
		String userFilter = null;
		String initialFilter = null;
		String refFilter = null;
		String spreadOnly = null;
		int currentPage = 0;
		int pageSize = 0;
		Number refreshRate = 0;
		String rowBegin = null;
		String rowEnd = null;
		Date selectedDateIso = null;
		String formatDate = null;
		Date systemDBDate = null;
		String defaultEntityId = null;
		String userId = null;
		SystemFormats sysformat = null;
		String archive = null;
		String pagesToExport = null;
		String listEntities = null;
		String ccyTime = null;
		String inputSince = null;

		try {
			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
			errors = new ActionMessages();
			// Get currentUserId from session
			userId =  "'" +SwtUtil.getCurrentUser(request.getSession()).getId().getUserId()+  "'" ;
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			log.debug(this.getClass().getName() + " - [exportGenericDetails] - " + "Entry");

			selectedCurrencyCode = !SwtUtil.isEmptyOrNull(request.getParameter("ccyCode")) ? "'" +request.getParameter("ccyCode") +"'" : null;
			selectedEntity = request.getParameter("entityId");
			if (!SwtUtil.isEmptyOrNull(selectedEntity) && !selectedEntity.equals("All")) {
				listEntities = entityListFormatter(selectedEntity) ;
			} else {
				listEntities = "'" +"All" +"'";

			}
			selectedEntityName = !SwtUtil.isEmptyOrNull(request.getParameter("entityName")) ? request.getParameter("entityName") : null;
			selectedAccountGroup = !SwtUtil.isEmptyOrNull(request.getParameter("accountGroup")) ? "'" +request.getParameter("accountGroup")+"'" : null;
			selectedAccount = !SwtUtil.isEmptyOrNull(request.getParameter("account")) ? "'" +request.getParameter("account")+"'" : "All";
			selectedStatus = !SwtUtil.isEmptyOrNull(request.getParameter("status")) ? "'" +request.getParameter("status")+"'" : "All";
			selectedDate = !SwtUtil.isEmptyOrNull(request.getParameter("valueDate")) ? request.getParameter("valueDate") : null;
			applyCcyThreshold =  !SwtUtil.isEmptyOrNull(request.getParameter("useThreshold")) ? "'" +request.getParameter("useThreshold")+"'" : "N" ;
			fromScreen =  !SwtUtil.isEmptyOrNull(request.getParameter("fromScreen")) ? request.getParameter("fromScreen") : null;
			useCcyMultiplier =  !SwtUtil.isEmptyOrNull(request.getParameter("useCcyMultiplier")) ? "'" +request.getParameter("useCcyMultiplier")+"'" : "N";
			blockedReason =  !SwtUtil.isEmptyOrNull(request.getParameter("blockedReason")) ? "'" +request.getParameter("blockedReason")+"'" : null;
			userFilter = !SwtUtil.isEmptyOrNull(request.getParameter("userFilter")) ? "'" +replaceQuotes(request.getParameter("userFilter"))+"'" : null ;
			initialFilter = !SwtUtil.isEmptyOrNull(request.getParameter("initialFilter")) ? "'" +replaceQuotes(request.getParameter("initialFilter"))+"'" : null;
			refFilter = !SwtUtil.isEmptyOrNull(request.getParameter("refFilter")) ? "'" +request.getParameter("refFilter")+"'" : null ;
			spreadOnly = !SwtUtil.isEmptyOrNull(request.getParameter("spreadOnly")) ? "'" +request.getParameter("spreadOnly")+"'" : null;
			order = !SwtUtil.isEmptyOrNull(request.getParameter("order")) ? "'" +request.getParameter("order")+"'" : null;
			ascDesc = !SwtUtil.isEmptyOrNull(request.getParameter("ascDesc")) ? "'" +request.getParameter("ascDesc")+"'" : null;
			rowBegin = !SwtUtil.isEmptyOrNull(request.getParameter("rowBegin")) ? request.getParameter("rowBegin"): null;
			rowEnd = !SwtUtil.isEmptyOrNull(request.getParameter("rowEnd")) ?  request.getParameter("rowEnd") : null;
			archive = !SwtUtil.isEmptyOrNull(request.getParameter("archive")) ?  "'" +archiveManager.getDBlink(request.getParameter("archive"))+"'" : null;
			sodBalance = !SwtUtil.isEmptyOrNull(request.getParameter("sod")) ?  request.getParameter("sod") : null;
			confirmedCredit = !SwtUtil.isEmptyOrNull(request.getParameter("confirmedCredit")) ? request.getParameter("confirmedCredit") : null;
			creditLine = !SwtUtil.isEmptyOrNull(request.getParameter("creditLine")) ? request.getParameter("creditLine") : null;
			releasePay = !SwtUtil.isEmptyOrNull(request.getParameter("releasedPayments")) ?  request.getParameter("releasedPayments"): null;
			exCreditLine = !SwtUtil.isEmptyOrNull(request.getParameter("exCreditLine")) ?  request.getParameter("exCreditLine") : null;
			incCreditLine = !SwtUtil.isEmptyOrNull(request.getParameter("inCreditLine")) ?  request.getParameter("inCreditLine") : null;
			reserve = !SwtUtil.isEmptyOrNull(request.getParameter("reserve")) ? request.getParameter("reserve") : null;
			pagesToExport = !SwtUtil.isEmptyOrNull(request.getParameter("pagesToExport")) ? request.getParameter("pagesToExport") : "current";
			ccyTime = !SwtUtil.isEmptyOrNull(request.getParameter("ccyTime")) ?  "'" +request.getParameter("ccyTime") + "'"  : "E";
			inputSince = !SwtUtil.isEmptyOrNull(request.getParameter("inputSince")) ?  "'" + SwtUtil.formatDateToIso(request.getParameter("inputSince") , formatDate) + "'"  : null;

			// get exportType from request parameter.
			exportType = request.getParameter("exportType");

			//PageSize
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.DEFAULT_SYSTEM_SCREEN_PAGE_SIZE);
			if(SwtUtil.isEmptyOrNull(rowBegin) || SwtUtil.isEmptyOrNull(rowEnd)) {
				rowBegin = "0";
				rowEnd = String.valueOf(pageSize);
			}
			if(!pagesToExport.equals("current")) {
				rowBegin = "0";
				rowEnd = "0";
			}
			defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(defaultEntityId);
			systemDateAsString = request.getParameter("fromDate");
			formatDate = request.getParameter("dateformat");

			if(SwtUtil.isEmptyOrNull(selectedDate) && !SwtUtil.isEmptyOrNull(inputSince)) {
				selectedDate = systemDateAsString;

			} else if(SwtUtil.isEmptyOrNull(selectedDate) && !SwtUtil.isEmptyOrNull(fromScreen)&& fromScreen.equals("search")) {
				selectedDate = "";
			}
			/******iso convert***/

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			if(!SwtUtil.isEmptyOrNull(selectedDate)) {
				Date date = SwtUtil.parseDate(selectedDate, formatDate );
				selectedDateAsISO = "'" + sdf.format(date) + "'";
			}
			if (baseQuery != null)
				baseQuery = SwtUtil.decode64(baseQuery);

			tokenForDownload = request.getParameter("tokenForDownload");
			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|OK"));

			if (request.getParameter("filter") != null)
				filter = SwtUtil.decode64(request.getParameter("filter"));
			else
				filter = "";

			// initialize current page
			currentPage = 1;
			// get page Size from properties file.
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE);
			// check currentFilter whether it is null or empty, set as all.
			currentFilter = (request.getParameter("selectedFilter") == null
					|| request.getParameter("selectedFilter").trim().length() == 0) ? "all"
					: SwtUtil.decode64(request.getParameter("selectedFilter"));
			/*
			 * check currentSort whether it is null or empty, set as default value.
			 */
			currentSort = (request.getParameter("selectedSort") == null
					|| request.getParameter("selectedSort").trim().length() == 0) ? "1"
					: SwtUtil.decode64(request.getParameter("selectedSort"));
			// get currentPage from request parameter.
			currPageStr = request.getParameter("currentPage");

			// check currPageStr whether not null, convert string to int.
			if (currPageStr != null) {
				currentPage = Integer.parseInt(currPageStr);
			}
			// check pageCount whether not null, calculate page size.
			if (request.getParameter("pageCount") != null) {
				pageSize = pageSize * Integer.parseInt(request.getParameter("pageCount"));
			}
			// Create an object 'page'. It conserves all the properties of pagination,
			// filtering and sorting
			PCReport page = new PCReport();
			page.setEntityId(listEntities);
			page.setDateAsIso(selectedDateAsISO);
			page.setApplyCcyThreshold(applyCcyThreshold);
			page.setUseCurrencyMultiplier(useCcyMultiplier);
			page.setStatus(selectedStatus);
			page.setBlockReason(blockedReason);
			page.setCurrencyCode(selectedCurrencyCode);
			page.setAccountGroup(selectedAccountGroup);
			page.setAccount(selectedAccount);
			page.setUserId(userId);
			page.setInitialFilter(initialFilter);
			page.setUserFilter(userFilter);
			page.setRefFilter(refFilter);
			page.setSpreadOnly(spreadOnly);
			page.setOrder(order);
			page.setAscDesc(ascDesc);
			page.setRowBegin(rowBegin);
			page.setRowEnd(rowEnd);
			page.setArchive(archive);
			page.setSelectedFilter(currentFilter.concat("|" + filter));
			page.setSelectedSort(currentSort);
			page.setCurrentPage(currentPage);
			page.setCcyTime(ccyTime);
			page.setInputSince(inputSince);


			// Set recordsPerPage in 'page' object. It is a constant set in
			// default_predict.properties
			page.setRecordsPerPage(SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE));

			if (!"1".equals(request.getParameter("pageCount")))
				page.setPrintAllPages(true);
			else
				page.setPrintAllPages(false);



			queryResult = reportManager.getQueryResultPage(page);

			cancelExport = CDM.getCancelExport();
			if (SwtUtil.isEmptyOrNull(cancelExport) || (!cancelExport.equals("true")))
				cancelExport = "false";
			if (cancelExport.equals("true")) {
				response.setContentType("text/html");
				response.setHeader("Content-disposition", "inline");
				return null;
			}
			columnData = new ArrayList<ColumnDTO>();
			// column for date
			for (int i = 0; i < queryResult.getMetadataDetails().size(); i++) {
				cDTO = new ColumnDTO();
				metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
				cDTO.setHeading(metadata.getColumnLabel());
				cDTO.setType(xmlEncoder.getDataType(metadata.getColumnType()));
				cDTO.setDataElement(metadata.getColumnName());
				columnData.add(cDTO);
			}

			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();

			fDTO.setName("Currency");
			fDTO.setValue(request.getParameter("ccyCode"));
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Entity");
			fDTO.setValue(selectedEntity);
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Account Group");
			fDTO.setValue(request.getParameter("accountGroup"));
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Account");
			fDTO.setValue(request.getParameter("account"));
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Status");
			fDTO.setValue(decodeStatus(request.getParameter("status")));
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Date");
			fDTO.setValue(selectedDate);
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Time-frame");
			if(request.getParameter("ccyTime").equals("E")) {
				fDTO.setValue("Entity");
			} else if(request.getParameter("ccyTime").equals("C")) {
				fDTO.setValue("Currency");
			} else {
				fDTO.setValue("System");

			}

			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Apply currency threshold");
			fDTO.setValue(request.getParameter("useThreshold"));
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Apply currency multiplier");
			fDTO.setValue(request.getParameter("useCcyMultiplier"));
			filterData.add(fDTO);
			if(!SwtUtil.isEmptyOrNull(sodBalance) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Start of Day Balance");
				fDTO.setValue(sodBalance);
				filterData.add(fDTO);
			}
			if(!SwtUtil.isEmptyOrNull(confirmedCredit) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Confirmed Credits");
				fDTO.setValue(confirmedCredit);
				filterData.add(fDTO);
			}
			if(!SwtUtil.isEmptyOrNull(creditLine) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Credit Line");
				fDTO.setValue(creditLine);
				filterData.add(fDTO);
			}
			if(!SwtUtil.isEmptyOrNull(releasePay) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Released Payments");
				fDTO.setValue(releasePay);
				filterData.add(fDTO);
			}
			if(!SwtUtil.isEmptyOrNull(exCreditLine) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Available Liquidity (ex Credit Line)");
				fDTO.setValue(exCreditLine);
				filterData.add(fDTO);
			}
			if(!SwtUtil.isEmptyOrNull(incCreditLine) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Available Liquidity (inc Credit Line)");
				fDTO.setValue(incCreditLine);
				filterData.add(fDTO);
			}
			if(!SwtUtil.isEmptyOrNull(reserve) && !request.getParameter("ccyCode").equals("All")) {
				fDTO = new FilterDTO();
				fDTO.setName("Reserve");
				fDTO.setValue(reserve);
				filterData.add(fDTO);
			}

			dynamicReport = new SwtDynamicReportImpl();
			ArrayList<ArrayList<ExportObject>> data = new ArrayList<ArrayList<ExportObject>>();
			if (cancelExport.equals("true")) {
				response.setContentType("text/html");
				response.setHeader("Content-disposition", "inline");
				return null;
			}
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance().getPropertiesValue("windows.title.suffix");

			// Check whether titleSuffix is null and set empty string
			if (titleSuffix == null)
				titleSuffix = "";
			// Export type for csv
			if (exportType.equalsIgnoreCase(XLS_EXPORT)) {
				// Content for excel
				response.setContentType("application/vnd.ms-excel");
				// Column Heading
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_" + SwtUtil.FormatCurrentDate() + ".xls");
				// Object Conversion to XLS
				// excelObjGen.convertObject(request, response, columnData, filterData, data,
				// null, null, fileName);
				dynamicReport.convertObject(request, response, columnData, filterData, baseQuery, queryResult, null,
						fileName, SwtDynamicReportImpl.XLS_EXPORT, null);

				// Export type for pdf
			} else if (exportType.equalsIgnoreCase("pdf")) {
				// Content for pdf
				response.setContentType("application/pdf");
				// Column Heading
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_" + SwtUtil.FormatCurrentDate() + ".pdf");
				// Object Conversion to PDF
				dynamicReport.convertObject(request, response, columnData, filterData, baseQuery, queryResult, null,
						fileName, SwtDynamicReportImpl.PDF_EXPORT, null);
				// Export type for csv
			} else if (exportType.equalsIgnoreCase("csv")) {
				// Content for csv
				response.setContentType("application/vnd.ms-excel");
				// Column Heading
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_" + SwtUtil.FormatCurrentDate() + ".csv");
				// Object Conversion to CSV
				/*
				 * If csvResponse is true then we have to cancel the export of the SCV file. It
				 * becomes true only when the user exports more than 300 pages (as default
				 * property in Predict) and then cancels it
				 */
				dynamicReport.convertObject(request, response, columnData, filterData, baseQuery, queryResult, null,
						fileName, SwtDynamicReportImpl.CSV_EXPORT, null);
			}


			log.debug(this.getClass().getName() + "- [exportGenericDetails] - Exit");
			return null;
		} catch (OutOfMemoryError exp) {
			if (errors != null) {
				errors.add("", new ActionMessage(exp.getMessage()));
			}
			saveErrors(request, errors);
			// Source scan tools may report a "HTTP response splitting" vulnerability, a
			// generic solution works by ignoring text after CRLFs is implemented XSSFilter
			// class.
			tokenForDownload = request.getParameter("tokenForDownload");
			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|KO"));
			response.setContentType("text/html");
			response.setHeader("Content-disposition", "inline");
			return null;
		} catch (SwtException swtexp) {
			if (swtexp.getErrorCode().contains("errors.OutOfMemoryError")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
				// Source scan tools may report a "HTTP response splitting" vulnerability, a
				// generic solution works by ignoring text after CRLFs is implemented XSSFilter
				// class.
				tokenForDownload = request.getParameter("tokenForDownload");
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|KO"));
				response.setContentType("text/html");
				response.setHeader("Content-disposition", "inline");
				return null;
			} else {
				log.error(this.getClass().getName() + " - Exception Catched in [exportGenericDetails] method : - "
						+ swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");
				return getView("fail");
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [exportGenericDetails] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(e, "exportErrors", ReportAction.class),
					request, "");
			return getView("fail");

		} finally {
			if (CDM != null) {
				// CDM.setCancelExport("false");
			}
			// Nullifying the objects for already created objects
			currentFilter = null;
			currentSort = null;
			currPageStr = null;
			exportType = null;
			filterData = null;
			fDTO = null;
			fileName = null;
			titleSuffix = null;
			baseQuery = null;
			metadata = null;
			queryResult = null;
			columnData = null;
			cDTO = null;
			xmlEncoder = new XmlEncoder();
		}
	}

	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 *
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String reportDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		ArrayList<LabelValueBean> allEntity = null;
		PCMCurrencyDetailsVO currencyList;
		Collection sourcesList;
		Collection accountGroupList;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;

		String selectedReportType = null;
		String selectedEntity = null;
		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		String selectedSource = null;
		String singleOrRange = null;
		String selectedFromDate = null;
		String selectedToDate = null;
		String useCcyMultiplier = null;
		boolean entityExistInList = false;
		Collection<LabelValueBean> entitiesLblValue = null;
		String defaultEntityId = null;
		Date dateInCcyTimeframe = null;
		boolean isCurrencyChanged = false;
		String firstDateOfPreviousMonth = null;
		String lastDateOfPreviousMonth = null;

		try {

			log.debug(this.getClass().getName() + " - [display] - " + "Entry");
			entitiesLblValue = new ArrayList<LabelValueBean>();
			entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			allEntity = (ArrayList<LabelValueBean>) SwtUtil.convertEntityAcessCollectionLVL(
					SwtUtil.getUserEntityAccessList(request.getSession()), request.getSession());
			entitiesLblValue.addAll(allEntity);
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			selectedReportType = request.getParameter("reportType");
			selectedEntity = request.getParameter("entity");
			selectedCurrencyCode = request.getParameter("currencyCode");
			selectedAccountGroup = request.getParameter("accountGroup");
			selectedSource = request.getParameter("source");
			singleOrRange = request.getParameter("singleOrRange");
			selectedFromDate = request.getParameter("selectedFromDate");
			selectedToDate = request.getParameter("selectedToDate");
			useCcyMultiplier = request.getParameter("useCcyMultiplier");
			isCurrencyChanged = "true".equals(request.getParameter("isCurrencyChanged"));

			// Set the date in currency timeframe
			dateInCcyTimeframe = SwtUtil.getSysParamDateWithEntityOffset(selectedEntity);
			if (SwtUtil.isEmptyOrNull(selectedEntity)) {
				selectedEntity = "All";
			}
			if (SwtUtil.isEmptyOrNull(selectedAccountGroup)) {
				selectedAccountGroup = "All";
			}
			if (SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				selectedCurrencyCode = "All";
			}

			defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
			for (LabelValueBean o : entitiesLblValue) {
				if (o.getValue().equals(defaultEntityId)) {
					entityExistInList = true;
					break;
				}
			}
			if (!entityExistInList) {
				if (entitiesLblValue.size() > 0)
					defaultEntityId = entitiesLblValue.iterator().next().getValue();
				else
					defaultEntityId = null;
			}

			Date testDate = SwtUtil.getSysParamDateWithEntityOffset(defaultEntityId);
			Calendar cal = Calendar.getInstance();
			cal.setTime(testDate);
			// set from date when selecting single day, it will be day before
			String today = SwtUtil.formatDate(cal.getTime(),
					SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());
			// Set from date when selecting date range, it will first date in the previous
			// month
			cal.setTime(testDate);
			//This line is hidden as When the current date is mid-Febraury 2020, the date range defaults to 1st to 31st December 2019, when it should actually be January.
//			if (cal.get(Calendar.MONTH) == 1)
//				cal.set(cal.get(Calendar.YEAR) - 1, 11, 1);
//			else
			cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) - 1, 1);

			firstDateOfPreviousMonth = SwtUtil.formatDate(cal.getTime(),
					SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());

			// Set to date when selecting date range, it will last date in the previous
			// month
			cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.getActualMaximum(Calendar.DATE));
			lastDateOfPreviousMonth = SwtUtil.formatDate(cal.getTime(),
					SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());

			/*
			 * request.setAttribute("dateInCcyTimeframe",
			 * SwtUtil.formatDate(dateInCcyTimeframe, "yyyy-MM-dd")); cal =
			 * Calendar.getInstance(); cal.setTime(dateInCcyTimeframe);
			 *
			 * request.setAttribute("dateInCcyTimeframeAsString",
			 * SwtUtil.formatDate(cal.getTime(), SwtUtil
			 * .getCurrentSystemFormats(request.getSession()) .getDateFormatValue()));
			 */

			// Set single or range value
			if (SwtUtil.isEmptyOrNull(singleOrRange)) {
				singleOrRange = "S";
			}
			if (SwtUtil.isEmptyOrNull(useCcyMultiplier)) {
				useCcyMultiplier = "N";
			}
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "report";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);

			xmlWriter.startElement(PCMConstant.PCM_REPORT_ROOT_TAG);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.DATE_FORMAT,
					SwtUtil.getCurrentDateFormat(request.getSession()));

			responseConstructor.createElement(PCMConstant.REPORT_TYPE, selectedCurrencyCode);
			responseConstructor.createElement(PCMConstant.CURRENCYCODE, selectedCurrencyCode);
			responseConstructor.createElement(PCMConstant.ACC_GP_ID, selectedAccountGroup);
			responseConstructor.createElement(PCMConstant.ENTITY, selectedEntity);
			responseConstructor.createElement(PCMConstant.SINGLE_OR_RANGE, singleOrRange);
			if (SwtUtil.isEmptyOrNull(selectedFromDate) || isCurrencyChanged) {
				responseConstructor.createElement(PCMConstant.FROM_DATE, today);
			} else {
				responseConstructor.createElement(PCMConstant.FROM_DATE, selectedFromDate);

			}
			responseConstructor.createElement(PCMConstant.FIRST_DATE_OF_PREVIOUS_MONTH, firstDateOfPreviousMonth);
			if (SwtUtil.isEmptyOrNull(selectedToDate)) {
				responseConstructor.createElement(PCMConstant.LAST_DATE_OF_PREVIOUS_MONTH, lastDateOfPreviousMonth);
			} else {
				responseConstructor.createElement(PCMConstant.LAST_DATE_OF_PREVIOUS_MONTH, selectedToDate);

			}
			responseConstructor.createElement(PCMConstant.USE_CCY_MULTIPLIER, useCcyMultiplier);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("All", "All", false));

			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedCurrencyCode))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/***** Report type Combo ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			accountGroupList = getAccountGroupList(request);
			j = accountGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedSource))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.REPORT_TYPE_LIST, lstOptions));

			/***** Source Combo ***********/

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("All", "All", false));

			sourcesList = getSourceList();
			j = sourcesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedSource))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));

			/***** Account Group Combo ***********/

			lstOptions = new ArrayList<OptionInfo>();
			accountGroupList = getAccountGroupCombo(selectedCurrencyCode);
			j = accountGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedAccountGroup))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));

			/**** entity Combo ***********/

			allEntity.add(0, new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			lstOptions = new ArrayList<OptionInfo>();
			j = allEntity.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedEntity))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));

			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.PCM_REPORT_ROOT_TAG);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [display] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [display] method : - " + exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "display", this.getClass()),
					request, "");

			return getView("fail");
		}
	}

	private Collection getAccountGroupList(HttpServletRequest request) {
		Collection accountGroupColl = new ArrayList();
		LabelValueBean L1 = new LabelValueBean(PCMConstant.PCM_REPORT_BLOCKED_PAYMENTS,
				SwtUtil.getMessage("PCMReport.BlockedPayments", request));
		AccountGroup row = null;
		accountGroupColl.add(L1);
		return accountGroupColl;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getSourceList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getSourceList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = PCMUtil.getSourceList();

		log.debug(this.getClass().getName() + " - [getSourceList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the account group list
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	private Collection getAccountGroupCombo(String currency) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - " + "Entry");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection accountColl;
		Iterator accountItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		/* Collect the multiplier list from the Cache manager */
		DashboardManager dashboardManager = (DashboardManager) SwtUtil.getBean("dashboardManager");
		if (SwtUtil.isEmptyOrNull(currency))
			currency = "All";
		accountColl = (Collection) dashboardManager.getAccountGrpDetails(currency);

		/* Iterate the multiplierlist */
		accountItr = accountColl.iterator();
		accountColl = new ArrayList();
		L1 = new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE);
		AccountGroup row = null;
		accountColl.add(L1);
		while (accountItr.hasNext()) {
			row = (AccountGroup) accountItr.next();
			L2 = new LabelValueBean(row.getDescription(), row.getId().getAccGrpId());
			accountColl.add(L2); // None
			index++;

		}
		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - " + "Exit");
		return accountColl;
	}

	private static String convertListToInParameterForQuery(String listAsString) {
		String result = "";
		if (!SwtUtil.isEmptyOrNull(listAsString)) {
			if (listAsString.substring(listAsString.length() - 1).equals(",")) {
				listAsString = listAsString.substring(0, (listAsString.length() - 1));
			}
			String[] listArray = listAsString.split(",(\\s)?");
			result = StringUtils.join(listArray, "','");
			result = "'" + result + "'";

		}

		return result;
	}
	private static String entityListFormatter(String listAsString) {
		String result = "";
		if (!SwtUtil.isEmptyOrNull(listAsString)) {
			if (listAsString.substring(listAsString.length() - 1).equals(",")) {
				listAsString = listAsString.substring(0, (listAsString.length() - 1));
			}
			String[] listArray = listAsString.split(",(\\s)?");
			result = StringUtils.join(listArray, "'',''");
			result = "'''" + result + "'''";

		}

		return result;
	}
	public String decodeStatus(String status) {
		String statusCode = null ;
		switch (status) {
			case "S":
				statusCode = "Stopped";
				break;
			case "W":
				statusCode = "Waiting";
				break;
			case "B":
				statusCode = "Blocked";
				break;
			case "R":
				statusCode = "Released";
				break;
			case "C":
				statusCode = "Cancelled";
				break;

			default:
				statusCode = "All";
				break;
		}

		return statusCode;

	}
	public String replaceQuotes(String str) {
		return str.replaceAll("\'", "\''");
	}
}