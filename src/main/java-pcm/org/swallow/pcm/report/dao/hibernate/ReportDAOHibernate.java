/*
 * @(#)ReportDAOHibernate.java 1.0 25/06/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.report.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.report.dao.ReportDAO;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.reports.dao.hibernate.ReportsDAOHibernate;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.ColumnMetadata;
import org.swallow.work.model.GenericDisplayPageDTO;
import org.swallow.work.model.QueryResult;


import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Session;

@Repository ("reportDAO")
@Transactional
public class ReportDAOHibernate extends CustomHibernateDaoSupport implements ReportDAO {
	public ReportDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(ReportDAOHibernate.class);

	public JasperPrint getBlockPaymentReport(PCReport pcReport) throws SwtException {

		Connection connection = null;
		DataSource ds = null;
		JasperReport jasperReport = null;
		JasperPrint jasperPrint = null;
		List pagesize = null;
		SystemFormats sysformat = null;
		String currencyFormat = null;
		boolean showCuttOffSubReport = false;
		Session session = null;
		try {
			sysformat = SwtUtil.getCurrentSystemFormats(pcReport.getRequest().getSession());
			currencyFormat = sysformat.getCurrencyFormat();
			Map parms = new HashMap();

			/* Jasper file will be compiled */
			try {
			jasperReport = JasperCompileManager.compileReport(pcReport.getRequest()
			        .getServletContext().getRealPath("/")
					+ PCMConstant.PCM_BLOCK_PAYMENT_REPORTS_FILE);
			}catch(JRException exp) {
				exp.printStackTrace();
			}
			log.debug("report complied");
			HashMap parmsLabel = new HashMap();
			parmsLabel.put("pgrandTotal", "Grand-Total");
			parmsLabel.put("pReportTitleBlockedPayment", "Blocked Payments (End of Day)");
			parmsLabel.put("subTotal", "Sub-Total");
			parmsLabel.put("pValueDate", "Value Date");
			parmsLabel.put("pCurrency", "Currency");
			parmsLabel.put("pAccount Grp", "Account Grp" );
			parmsLabel.put("pEntity", "Entity");
			parmsLabel.put("pSource", "Source");
			parmsLabel.put("valueDate", "Value Date");
			parmsLabel.put("pReportDateTime", "Report Date/Time");
			parmsLabel.put("pCuttOffNotReached", "See final page for currencies where cut-off has not yet been reached");
			parmsLabel.put("pcurrencyNotReachedCutOff", "Currencies where cut-off has not yet been reached for value: "+SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())) );
			parmsLabel.put("pAccount", "Account");
			parmsLabel.put("pAmount", "Amount");
			parmsLabel.put("pSourceRef", "Source Ref");
			parmsLabel.put("pPRID", "PR ID");
			parmsLabel.put("pMsgType", "MsgType");
			parmsLabel.put("pStopBlockRule", "Stop/Block Rule");
			
			String toDateAsString = SwtUtil.formatDate(pcReport.getToDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
			String sysDateAsString = SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
			/* Get the datasource object from the bean and get the connection */
			session = SwtUtil.pcSessionFactory.openSession();
			// Establishes the Connection
			connection =  SwtUtil.connection(session);
			/* Preparing the parameters to be passed to Jasper Engine. */
			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
			parms.put("p_selectedCurrency", pcReport.getCurrencyCode());
			parms.put("p_selectedCurrencyName", pcReport.getCurrencyName());
			parms.put("p_selectedAccountGroup", pcReport.getAccountGroup());
			parms.put("p_selectedAccountGrpName", pcReport.getAccountGroupName());
			parms.put("p_UseCcyMultiplier",  pcReport.getUseCurrencyMultiplier());
			parms.put("p_selectedEntity", pcReport.getEntityId());
			parms.put("p_selectedSource", pcReport.getSource());
			parms.put("p_selectedSourceName", pcReport.getSourceName());
			parms.put("p_selectedEntityName", pcReport.getEntityName());
			parms.put("p_ReportDateTime", SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()) +" HH:mm"));
			
			parms.put("p_selectedFromDate", SwtUtil.truncateDateTime(pcReport.getFromDate()));
			if (pcReport.getToDate() == null) {
				parms.put("p_selectedToDateAsString",  SwtUtil.formatDate(pcReport.getFromDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
			} else {
				parms.put("p_selectedToDateAsString", toDateAsString);
			}
			parms.put("p_selectedToDate",SwtUtil.truncateDateTime(pcReport.getToDate()));
			parms.put("p_selectedFromDateAsString", SwtUtil.formatDate(pcReport.getFromDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
			parms.put("p_dateFormat", SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
			parms.put("SUBREPORT_DIR",JasperCompileManager.compileReport(SwtUtil.contextRealPath	+ PCMConstant.PCM_BLOCK_PAYMENT_SUB_REPORTS_FILE));

			if(pcReport.getToDate() == null) {
				Calendar cal = Calendar.getInstance();
				cal.setTime(pcReport.getFromDate());
				
				Calendar cal2 = Calendar.getInstance();
				cal2.setTime(SwtUtil.getSystemDatewithoutTime());
				if(cal.equals(cal2)) {
					showCuttOffSubReport = true;
				}
				
			}else {
				Calendar cal2 = Calendar.getInstance();
				cal2.setTime(pcReport.getToDate());
				
				Calendar cal3 = Calendar.getInstance();
				cal3.setTime(SwtUtil.getSystemDatewithoutTime());
				
				
				if(cal2.equals(cal3) ||  cal3.before(cal2) ) {
					showCuttOffSubReport = true;
				}
			}
			
			if(showCuttOffSubReport)
				parms.put("p_showRemainingCutoff", "true");
			else 
				parms.put("p_showRemainingCutoff", "false");
			
			parms.put("p_Dictionary_Data",parmsLabel);
			parms.put("pCurrencyPattern",currencyFormat);

			/* Compiled Opportunity cost Report will be filled here. */
			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
					connection);
			log.debug("Report Filled");
			/* To get the page size */
			pagesize = jasperPrint.getPages();
			/*
			 * If the page size is Zero Empty datasource will be passed to avoid
			 * the blank report.
			 */
			if (pagesize.size() == 0) {
				jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
						new JREmptyDataSource(1));
			}

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getBlockPaymentReport] method - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getBlockPaymentReport] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, pcReport.getRequest(), "");

		} catch (Exception exp) {
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getBlockPaymentReport",
							ReportsDAOHibernate.class), pcReport.getRequest(), "");
			throw new SwtException(exp.getMessage());
		} finally {
			JDBCCloser.close(null, null, connection, session);
		}

		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Exiting ");
		return jasperPrint;
	}

	public JasperPrint getPCMDashbordMonitorReport(PCReport pcReport) throws SwtException {

		Connection connection = null;
		DataSource ds = null;
		JasperReport jasperReport = null;
		JasperPrint jasperPrint = null;
		List pagesize = null;
		SystemFormats sysformat = null;
		String currencyFormat = null;
		Session session = null;
		
		
		try {
			sysformat = SwtUtil.getCurrentSystemFormats(pcReport.getRequest().getSession());
			currencyFormat = sysformat.getCurrencyFormat();
			
			/* Declare Map to send the parameters to the Japser Engine */
			Map parms = new HashMap();
			
			/* Jasper file will be compiled */
			try {
				jasperReport = JasperCompileManager.compileReport(pcReport.getRequest()
						.getServletContext().getRealPath("/")
						+ PCMConstant.PCM_CURRENCY_DASHBORD_REPORTS_FILE);
			}catch(JRException exp) {
				exp.printStackTrace();
			}
			log.debug("report complied");
			HashMap parmsLabel = new HashMap();
			parmsLabel.put("pReportTitle", "PCM Monitor Report");
			parmsLabel.put("pValueDate", "Value Date");
			parmsLabel.put("pValueVolume", "Value/Volume");
			parmsLabel.put("pEntity", "Entity");
			parmsLabel.put("pApplyCcyThreshold", "Apply Currency Threshold");
			parmsLabel.put("pApplyCcyMultiplier", "Apply Currency Multiplier");
			parmsLabel.put("pSpreadOnly", "Spread Only");
			
			parmsLabel.put("valueDate", "Value Date");
			parmsLabel.put("pReportDateTime", "Report Date/Time");
			
			
			parmsLabel.put("pCurrency", "Currency");
			parmsLabel.put("pTotalPay", "Total Pay");
			parmsLabel.put("pAvailableBal", "Available Bal");
			parmsLabel.put("pWaiting", "Waiting");
			parmsLabel.put("pStopped", "Stopped");
			parmsLabel.put("pBlocked", "Blocked");
			parmsLabel.put("pBackValue", "Back Value");
			parmsLabel.put("pCancelled", "Cancelled");
			parmsLabel.put("pReleased", "Released");
			parmsLabel.put("pRepair", "Repair");
			parmsLabel.put("pSuppressed", "Suppressed");
			parmsLabel.put("pRejected", "Rejected");
			
			String toDateAsString = SwtUtil.formatDate(pcReport.getToDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
			String sysDateAsString = SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
			/* Get the datasource object from the bean and get the connection */
			session = SwtUtil.pcSessionFactory.openSession();
			// Establishes the Connection
			connection =  SwtUtil.connection(session);
			/* Preparing the parameters to be passed to Jasper Engine. */
			
						
			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
			
			parms.put("p_ApplyCcyThreshold", pcReport.getApplyCcyThreshold());
			parms.put("p_ApplyCcyThresholdAsString", "Y".equals(pcReport.getApplyCcyThreshold())?"Yes":"No");
			parms.put("p_ApplyCcyMultiplier", pcReport.getUseCurrencyMultiplier());
			parms.put("p_ApplyCcyMultiplierAsString", "Y".equals(pcReport.getUseCurrencyMultiplier())?"Yes":"No");
			parms.put("p_selectedValueVolume", pcReport.getShowValueOrVolume());
			parms.put("p_selectedValueVolumeAsString", "Y".equals(pcReport.getShowValueOrVolume())?"Value":"Volume");
			parms.put("p_SpreadOnly", pcReport.getSpreadOnly());
			parms.put("p_SpreadOnlyAsString", "Y".equals(pcReport.getSpreadOnly())?"Yes":"No");
			
			parms.put("p_selectedEntity", pcReport.getEntityId());
			parms.put("p_selectedEntityAsString", pcReport.getEntityId().replace("'", ""));
			parms.put("p_selectedEntityName", pcReport.getEntityName());
			
			parms.put("p_ReportDateTime", SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()) +" HH:mm"));
			
			parms.put("p_selectedFromDateAsISO",SwtUtil.formatDate(pcReport.getFromDate(), "yyyy-MM-dd"));
			parms.put("p_selectedFromDate", SwtUtil.formatDate(pcReport.getFromDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
			parms.put("p_selectedToDateAsString", toDateAsString);
			parms.put("p_dateFormat", SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
			
			parms.put("p_selectedUserId", pcReport.getUserId());
			
			
			
			
			parms.put("p_Dictionary_Data",parmsLabel);
			parms.put("pCurrencyPattern",currencyFormat);
			
			/* Compiled Opportunity cost Report will be filled here. */
			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
					connection);
			log.debug("Report Filled");
			/* To get the page size */
			pagesize = jasperPrint.getPages();
			/*
			 * If the page size is Zero Empty datasource will be passed to avoid
			 * the blank report.
			 */
			if (pagesize.size() == 0) {
				jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
						new JREmptyDataSource(1));
				
			}
			
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getPCMDashbordMonitorReport] method - "
					+ swtexp.getMessage());
			
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getPCMDashbordMonitorReport] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, pcReport.getRequest(), "");
			
		} catch (Exception exp) {
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getPCMDashbordMonitorReport",
							ReportsDAOHibernate.class), pcReport.getRequest(), "");
			throw new SwtException(exp.getMessage());
		} finally {
			JDBCCloser.close(null, null, connection, session);
		}
		
		log.debug(this.getClass().getName()
				+ "- [getPCMDashbordMonitorReport] - Exiting ");
		return jasperPrint;
	}
	
	
	public QueryResult getQueryResultPage(PCReport pcReport) throws SwtException{
		
		
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		QueryResult queryResult = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		ArrayList<ColumnMetadata> metadataList= null;
		ColumnMetadata columnMetadata;
		int numberOfColumns = 0;
		int fromValue= 0;
		int toValue= 0;
		int count = 0;
		String orderBy  = "";
		String sortDirection = "";
		String[] sort;
		CommonDataManager CDM = null;
		String cancelExport = ""; 
		String queryText = "";

		try {
			log.debug(this.getClass().getName() + " - [getQueryResultPage] - "
					+ "Entry");
			if (!pcReport.getSelectedSort().equals("1")) {
				sort = pcReport.getSelectedSort().split("\\|");
				orderBy = sort[0];
				sortDirection = sort[1];
			}
			if(!pcReport.isPrintAllPages())
			{
				fromValue = (pcReport.getCurrentPage() - 1) * pcReport.getRecordsPerPage();
				toValue = pcReport.getCurrentPage() * pcReport.getRecordsPerPage();
			}else {
				fromValue=0;
				toValue= SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE);
			}
			
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
		

			CDM = (CommonDataManager)	UserThreadLocalHolder.getUserSession().getAttribute("CDM");
			conn = SwtUtil.connection(session);
			
			queryText = PCMUtil.getDashbordBreakDownQuery(pcReport);
			
			  // create the java statement
				stmt = conn.createStatement();
		      
		      // execute the query, and get a java resultset
		      rs = stmt.executeQuery(queryText);
		      
			queryResult = new QueryResult();
			metadataList = new ArrayList<ColumnMetadata>();
			if(rs!=null){
				
				ResultSetMetaData rsMetaData = rs.getMetaData();
				
				numberOfColumns = rsMetaData.getColumnCount();		
				
				for (int i = 1; i < numberOfColumns+1; i++) {
					columnMetadata = new ColumnMetadata();
					//set metadata attributes
					columnMetadata.setColumnDisplaySize(rsMetaData.getColumnDisplaySize(i));
					columnMetadata.setColumnClassName(rsMetaData.getColumnClassName(i));
					columnMetadata.setColumnLabel(rsMetaData.getColumnLabel(i));
					columnMetadata.setColumnName("column"+(i-1));
					columnMetadata.setColumnType(rsMetaData.getColumnType(i));
					columnMetadata.setColumnTypeName(rsMetaData.getColumnTypeName(i));
					columnMetadata.setSize(rsMetaData.getColumnDisplaySize(i));
					columnMetadata.setScale(rsMetaData.getScale(i));
					//add metadata object to the list
					metadataList.add(columnMetadata);
					
				}
			
				queryResult.setMetadataDetails(metadataList) ;
				
				ArrayList<HashMap<String, Object>> recordsList= new ArrayList<HashMap<String,Object>>();
				rs.next();
					// Cancel Export
					
					/*if(fromExport!=null && fromExport.length>0 && fromExport[0]==true){
						cancelExport = CDM.getCancelExport() == null ? cancelExport
								: CDM.getCancelExport();
						if (cancelExport.equals("true")) {
							break;
						}
					}*/
					int cols = rs.getMetaData().getColumnCount();
					LinkedHashMap<String, Object> arr = new LinkedHashMap<String, Object>();
					for (int i = 1; i < cols; i++) {
						arr.put(metadataList.get(i - 1).getColumnLabel(),rs.getObject(i + 1)!=null?rs.getObject(i + 1):"");
					}
					recordsList.add(arr);
				//}
				
				GenericDisplayPageDTO page = new GenericDisplayPageDTO();
				if (queryResult != null) {
					page.setTotalSize(recordsList.size());
					queryResult.setPage(page);
					
					queryResult.setExecutedQuery(queryText);
					queryResult.setQueryResult(recordsList);
					queryResult.setQueryException("");
					queryResult.setPCMQuery(true);
				}
				
			}else {
				throw new SwtException("Error occurred in the underlying treatment layer, please investigate database logs...");
			}
			log.debug(this.getClass().getName() + " - [getQueryResultPage] - "
					+ "Exit");
		} catch (SQLException exp) {
			log.error(this.getClass().getName()
					+ " - SQLException Catched in [getQueryResultPage] method : - "
					+ exp.getMessage());
			if (queryResult != null) {
			queryResult.setQueryException(exp.getMessage());
			}
		}catch(OutOfMemoryError exp){
			throw new SwtException("errors.OutOfMemoryError");
		}
		catch (Exception swtEx){
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getQueryResultPage] method : - "
					+ swtEx.getMessage());
		}finally {
			SwtException thrownException = null;
			stmt = null;
			rs = null;
			// CallableStatement object
			cstmt = null;
			metadataList= null;
			columnMetadata = null;
			orderBy  = null;
			sortDirection = null;
			sort = null;
			JDBCCloser.close(rs, stmt, conn, session);
//			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);
//			
//			if (exceptions[0] != null)
//				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getQueryResultPage",GenericDisplayMonitorDAOHibernate.class);
//			
//			if (thrownException == null && exceptions[1] !=null) 
//				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getQueryResultPage",GenericDisplayMonitorDAOHibernate.class);
//			
//			if (thrownException != null)
//				throw thrownException;
		}		
		return queryResult;
	}
}