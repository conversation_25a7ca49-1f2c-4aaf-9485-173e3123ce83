/*
 * @(#)ReportManagerImpl.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.report.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.report.dao.ReportDAO;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.pcm.report.service.ReportManager;
import org.swallow.reports.service.impl.TurnoverReportManagerImpl;
import org.swallow.work.model.QueryResult;

import net.sf.jasperreports.engine.JasperPrint;

@Component ("reportManager")
public class ReportManagerImpl implements ReportManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(ReportManagerImpl.class);
	@Autowired
	private ReportDAO reportDAO = null;

	public void setReportDAO(ReportDAO reportDAO) {
		this.reportDAO = reportDAO;
	}
	
	
	public JasperPrint getBlockPaymentReport(PCReport pcReport) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Entering ");
		try {
		/* To call the getTurnoverReport in TurnoverReportDAOHibernate.*/
		JasperPrint jasperPrint = reportDAO.getBlockPaymentReport(pcReport);
		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Exiting ");
		
		return jasperPrint;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getBlockPaymentReport] method : - "
							+ exp.getMessage());
			log	.error(this.getClass().getName()
							+ " - Exception Catched in [getBlockPaymentReport] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBlockPaymentReport", TurnoverReportManagerImpl.class);
		}
	}
	public JasperPrint getPCMDashbordMonitorReport(PCReport pcReport) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Entering ");
		try {
			/* To call the getTurnoverReport in TurnoverReportDAOHibernate.*/
			JasperPrint jasperPrint = reportDAO.getPCMDashbordMonitorReport(pcReport);
			log.debug(this.getClass().getName()
					+ "- [getTurnoverReport] - Exiting ");
			
			return jasperPrint;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getBlockPaymentReport] method : - "
					+ exp.getMessage());
			log	.error(this.getClass().getName()
					+ " - Exception Catched in [getBlockPaymentReport] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBlockPaymentReport", TurnoverReportManagerImpl.class);
		}
	}
	
	public QueryResult getQueryResultPage(PCReport pcReport) throws SwtException{
		log.debug(this.getClass().getName()
				+ "- [getQueryResultPage] - Entering ");
		try {
			/* To call the getTurnoverReport in TurnoverReportDAOHibernate.*/
			QueryResult queryResult = reportDAO.getQueryResultPage(pcReport);
			log.debug(this.getClass().getName()
					+ "- [getQueryResultPage] - Exiting ");
			
			return queryResult;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getQueryResultPage] method : - "
					+ exp.getMessage());
			log	.error(this.getClass().getName()
					+ " - Exception Catched in [getQueryResultPage] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getQueryResultPage", TurnoverReportManagerImpl.class);
		}
	}

}
