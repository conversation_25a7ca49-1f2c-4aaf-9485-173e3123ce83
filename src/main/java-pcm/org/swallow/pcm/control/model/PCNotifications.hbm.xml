<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.control.model.PCNotifications" table="PC_NOTIFICATIONS">
		<id name="notificationId" column = "NOTIFICATION_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_NOTIFICATIONS</param>
			<param name="increment_size">1</param>
			</generator>
		</id>	
		
		<property name="hostId" column="HOST_ID"  type = "java.lang.String" not-null="false"/>
		<property name="entityId" column="ENTITY_ID"  type = "java.lang.String" not-null="false"/>
		<property name="relationId" column="RELATION_ID"  type = "java.lang.String" not-null="false"/>	
		<property name="notificationType" column="NOTIFICATION_TYPE" type = "java.lang.String" not-null="false"/>	
		<property name="priority" column="PRIORITY" type = "int" not-null="false"/>
		<property name="notificationMessage" column="NOTIFICATION_MESSAGE" type = "java.lang.String" not-null="false"/>
	</class>
</hibernate-mapping>
