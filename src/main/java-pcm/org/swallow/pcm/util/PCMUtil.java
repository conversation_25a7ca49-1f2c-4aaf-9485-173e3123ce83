package org.swallow.pcm.util;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import jakarta.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.Notifications;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.control.model.PCNotifications;
import org.swallow.pcm.maintenance.model.TypeValues;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.service.LogonManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.HibernateException;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.Type;
import org.swallow.util.pcm.PCMConstant;

public final class PCMUtil {
	// Log object
	private static final Log log = LogFactory.getLog(PCMUtil.class);
	
	
	/**
	 * This method is used to get the list of label, code and other columns from
	 * S_CFG_TYPE_VALUES, S_DICTIONARY
	 * 
	 * @param typeId
	 * @param languageId
	 * @param showHidden
	 * @param extraValues
	 * @return List<TypeValues>
	 * @throws SwtException
	 */
	public static List<TypeValues> getListTypeValuesAllFlds(int typeId, String languageId, boolean showHidden,String... extraValues) throws SwtException
	{
		// Initializing MessagesManager instance
		LogonManager logonManager = null;
		// Get type values list
		List<TypeValues> allTypeValuesList = new ArrayList<TypeValues>();
		// Variable String to hold languageId
		String langId = null;

		try {
			// log debug message
			log.debug("SwtUtil - [ getListTypeValuesAllFlds ] - Entry");
		
			logonManager = (LogonManager) SwtUtil.getBean("logonManager");
		
			// Set EN default languageId
			if (SwtUtil.isEmptyOrNull(languageId)) {
				langId = "EN";
			} else {
				langId = languageId;
			}
			// get list of values from type values and dictionary (get all fields)
			allTypeValuesList = getListTypeValuesAllFldsFromDB(typeId, langId, showHidden,extraValues);

		} catch (Exception e) {
			// log error message
			log.error("SwtUtil"
					+ " - Exception Caught in [getListTypeValuesAllFlds] method : - "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// log debug message
			log.debug("SwtUtil" + " - [getListTypeValuesAllFlds] - " + "Exit");
			// Nullify Objects
			logonManager = null;
			langId = null;
		}
		// return labels from dictionary
		return allTypeValuesList;
	}
	
	
	/**
	 * This method is used to get the list of label, code and other columns from
	 * S_CFG_TYPE_VALUES, S_DICTIONARY
	 *
	 * @param typeId
	 * @param languageId
	 * @param showHidden
	 * @param extraValues
	 * @return List<TypeValues>
	 * @throws SwtException
	 */
	public static List<TypeValues> getListTypeValuesAllFldsFromDB(int typeId, String languageId, boolean showHidden,String... extraValues) throws SwtException
	{
		// variable for connection
		Connection conn = null;
		// variable for CallableStatement
		CallableStatement cstmt = null;
		// variable for result set
		ResultSet rs = null;
		// String to hold the default order parameter - 'ID' means order by value_id
		String defaultOrder="ID";
		// String to hold hidden parameter
		String hidden=null;
		//List to hold list label
		List<TypeValues> mapLabelList = new ArrayList<TypeValues>();
		if(1 == 1)
			return mapLabelList;
		try {
			// log debug message
			log.debug("PCMUtil - [getListTypeValuesAllFlds] - Entry ");
			// set hidden parameter - 'N' means do not show hidden values
			hidden = showHidden ? "Y" : "N";
			//check if there is extra arguments passed to method
			if (extraValues.length > 0) {
				if (extraValues[0].equals(PCMConstant.SORT_ALPHA))
					defaultOrder = null;
			}
			// Getting the session
			conn = ((DataSource) SwtUtil.getBean("dataSource")).getConnection();
			// Preparing the statement for call
			cstmt = conn
					.prepareCall("{call PKG_S_UTILITY.get_type_values(?,?,?,?,?,?)}");
			// Setting parameter for type Id
			cstmt.setInt(1, typeId);
			// Setting parameter for language Id
			cstmt.setString(2, languageId);
			// Setting parameter for hidden flag
			cstmt.setString(3, hidden);
			// Setting parameter for order
			cstmt.setString(4, defaultOrder);
			// Output parameter for result collection
			cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
			// Setting parameter for order
			cstmt.setString(6, "Y");
			// executing the statement
			cstmt.execute();
			// getting the result object
			rs = (ResultSet) cstmt.getObject(5);

			if (rs != null) {
				while (rs.next()) {
					TypeValues type = new TypeValues();
					type.setCode(rs.getString(1));
					type.setLabelFromDict(rs.getString(2));
					type.setTypeCode(rs.getString(3));
					type.setTypeEnum(rs.getString(4));
					type.setSquery(rs.getString(5));
					type.setTableName(rs.getString(6));
					type.setColumnValueUpper(rs.getString(7));
					type.getId().setValueId(rs.getInt(8));
					mapLabelList.add(type);
				}
			}

		} catch (Exception exp) {
			exp.printStackTrace();
			// log error message
			log.error("PCMUtil - Exception Caught in [getListTypeValuesAllFlds] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getListTypeValuesAllFlds", PCMUtil.class);
		} finally {
			// closing the result set
			try {
				if (rs != null) {
					rs.close();
					rs = null;
				}
			} catch (SQLException ex) {
				log.error("PCMUtil - Exception Catched in [getListTypeValuesAllFlds] method :-"
						+ ex.getMessage());
			}
			try { // closing the statement
				if (cstmt != null) {
					cstmt.close();
					cstmt = null;
				}
			} catch (SQLException ex) {
				log.error("PCMUtil - Exception Catched in [getListTypeValuesAllFlds] method :-"
						+ ex.getMessage());
			}
			try { // closing the connection
				if (conn != null) {
					conn.close();
					conn = null;
				}
			} catch (SQLException ex) {
				log.error(" PCMUtil - Exception Catched in [getListTypeValuesAllFlds] method :-"
						+ ex.getMessage());
			}
		}
			// log debug message
			log.debug("PCMUtil - [getListTypeValuesAllFlds] - Exit ");

		return mapLabelList;
	}
	
	/**
	 * Method to know if the current column is indexed
	 * 
	 * @param tableName
	 * @param function
	 * 
	 * @return {@link Boolean}
	 * @throws 
	 */
	public static boolean isIndexFunctionBased(String tableName, String function) throws SwtException {
		if(1 == 1)
			return false;
		log.debug(PCMUtil.class.getName() + "- [ isIndexFunctionBased ] - Entry");
		// variable for connection
		Connection conn = null;
		// variable for CallableStatement
		CallableStatement cstmt = null;
		boolean isIndexFunction = false;
		try {
			conn = ((DataSource) SwtUtil.getBean("dataSource")).getConnection();
			// Preparing the callable statement
			cstmt = conn.prepareCall("{ ? = call pkg_s_utility.fn_is_index_function_based(?,?)}");
			cstmt.setString(2, tableName);
			cstmt.setString(3, function);
			// output parameter for total no of records
			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.VARCHAR);

			cstmt.execute();
			// getting the result object
			if (cstmt.getString(1).equals("Y"))
				isIndexFunction = true;

			cstmt.getConnection().commit();

			return isIndexFunction;

		} catch (Exception ex) {
			// log error message
			log
					.error(PCMUtil.class.getName()
							+ " - Exception Catched in [isIndexFunctionBased] method : - "
							+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"isIndexFunctionBased", PCMUtil.class.getClass());
		} finally {
			try {
				// Close the callable statement
				if (cstmt != null) {
					cstmt.close();
					cstmt = null;
				}
			} catch (SQLException sqlExp) {
				log.error("SQLException Catched in "
						+ PCMUtil.class.getName()
						+ " - [isIndexFunctionBased] method : -"
						+ sqlExp.getMessage());
			}
			try {
				// Close the connection
				if (conn != null) {
					conn.close();
					conn = null;
				}
			} catch (SQLException sqlExp) {
				log.error("SQLException Catched in "
						+ PCMUtil.class.getName()
						+ " - [isIndexFunctionBased] method : -"
						+ sqlExp.getMessage());
			}
			// log debug message
			log.debug(PCMUtil.class.getName()
					+ " - [isIndexFunctionBased] - Exit");
		}
	}
	
	
	
	/**
	 * executeSelectHibernateQuery() Method to execute sQuery parameter as query
	 * 
	 * @param squery
	 * @return List<Object[]>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public static List<Object[]> executeSelectHibernateQuery(String squery)
			throws SwtException {
		/* Method's local variable declaration */
		List<Object[]> list = null;
		try {
			// log debug message
			log.debug(PCMUtil.class.getName()
					+ "- [executeSelectHibernateQuery] - Entering ");
			// get the list details
			
			Session session = SwtUtil.sessionFactory.openSession();
			Transaction tx = session.beginTransaction();
			list = session.createQuery(squery).getResultList();
			session.close();
			return list;
		} catch (Exception exp) {
			// log error message
			log.error(PCMUtil.class.getName()
					+ " - Exception Catched in [executeSelectHibernateQuery] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"executeSelectHibernateQuery", PCMUtil.class);
		} finally {
			// nullify object(s)
			list = null;
			// log debug message
			log.debug(PCMUtil.class.getName()
					+ "- [executeSelectHibernateQuery] - Exiting ");
		}
	}
	
	
	public static ArrayList<LabelValueBean> getSourceList() throws SwtException {
		ArrayList<LabelValueBean> sourceList = new ArrayList();
		log.debug("Enter into WorkflowMonitorDAOHibernate.getAllEntityOption method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		boolean allEntityOption = false;
		String value = null;
		String label = null;
		String hostId = SwtUtil.getCurrentHostId();
		LabelValueBean bean = null;
		try {
			String query = "select SOURCE_ID, DESCRIPTION from PC_SOURCE_SYSTEM ORDER BY SOURCE_ID ASC";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();

			if (resultSet != null) {
				while (resultSet.next()) {
					value = resultSet.getString(1);
					label = resultSet.getString(2);
					bean = new LabelValueBean(label, value);
					sourceList.add(bean);
				}
			}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getAllEntityOption",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getAllEntityOption",
					PCMUtil.class);
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getAllEntityOption", PCMUtil.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getAllEntityOption", PCMUtil.class);

			if (thrownException != null)
				throw thrownException;

		}

		log.debug("Exiting WorkflowMonitorDAOHibernate.getWorkflowMonitorDetailsFromProc()");
		return sourceList;

	}
	public static ArrayList<LabelValueBean> getMessageTypeList() throws SwtException {
		ArrayList<LabelValueBean> sourceList = new ArrayList();
		log.debug("Enter into WorkflowMonitorDAOHibernate.getAllEntityOption method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		boolean allEntityOption = false;
		String value = null;
		String label = null;
		String hostId = SwtUtil.getCurrentHostId();
		LabelValueBean bean = null;
		try {
			String query = "SELECT CODE, CODE FROM S_CFG_TYPE_VALUES WHERE  TYPE_ID = 11";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();
			
			if (resultSet != null) {
				while (resultSet.next()) {
					value = resultSet.getString(1);
					label = resultSet.getString(2);
					bean = new LabelValueBean(label, value);
					sourceList.add(bean);
				}
			}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getAllEntityOption",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getAllEntityOption",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getAllEntityOption", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getAllEntityOption", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting WorkflowMonitorDAOHibernate.getWorkflowMonitorDetailsFromProc()");
		return sourceList;
		
	}
	public static ArrayList<LabelValueBean> getChanelList() throws SwtException {
		ArrayList<LabelValueBean> sourceList = new ArrayList();
		log.debug("Enter into WorkflowMonitorDAOHibernate.getAllEntityOption method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		boolean allEntityOption = false;
		String value = null;
		String label = null;
		String hostId = SwtUtil.getCurrentHostId();
		LabelValueBean bean = null;
		try {
			String query = "select MESSAGEID, MESSAGELABEL from I_MESSAGE";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();
			
			if (resultSet != null) {
				while (resultSet.next()) {
					value = resultSet.getString(1);
					label = resultSet.getString(2);
					bean = new LabelValueBean(label, value);
					sourceList.add(bean);
				}
			}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getAllEntityOption",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getAllEntityOption",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getAllEntityOption", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getAllEntityOption", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting WorkflowMonitorDAOHibernate.getWorkflowMonitorDetailsFromProc()");
		return sourceList;
		
	}
	public static ArrayList<LabelValueBean> getArchiveList() throws SwtException {
		ArrayList<LabelValueBean> sourceList = new ArrayList();
		log.debug("Enter into getArchiveList method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String value = null;
		String label = null;
		LabelValueBean bean = null;
		try {
			String query = "select ARCHIVE_ID, ARCHIVE_NAME from P_ARCHIVE where MODULE_ID = 'PCM' ORDER BY DEFAULT_DB DESC";
			session = SwtUtil.sessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();
			
			if (resultSet != null) {
				while (resultSet.next()) {
					value = resultSet.getString(1);
					label = resultSet.getString(2);
					bean = new LabelValueBean(label, value);
					sourceList.add(bean);
				}
			}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getArchiveList",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getArchiveList",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getArchiveList", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getArchiveList", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting WorkflowMonitorDAOHibernate.getArchiveList()");
		return sourceList;
		
	}
	public static String getCounterPartySubQuery(String text, String type) throws SwtException {
		log.debug("Enter into WorkflowMonitorDAOHibernate.getAllEntityOption method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String value = null;
		try {
			String query = "SELECT pkg_pc_tools.fn_party_lookup (?, ?) expression FROM DUAL";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.setString(1, text);
			statement.setString(2, type);
			statement.execute();
			resultSet = statement.getResultSet();
			
			if (resultSet != null) {
				resultSet.next();
				value = resultSet.getString(1);
			}
			
			
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getAllEntityOption",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getAllEntityOption",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getAllEntityOption", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getAllEntityOption", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting WorkflowMonitorDAOHibernate.getWorkflowMonitorDetailsFromProc()");
		return value;
		
	}
	
	
	public static boolean getIsDeactivatedRule(String ruleId) throws SwtException {
		log.debug("Enter into getIsDeactivatedRule method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String value = null;
		try {
			String query = "SELECT PKG_PC_CHECK_RULES.is_stop_rule_deactivated (?) expression FROM DUAL";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.setString(1, ruleId);
			statement.execute();
			resultSet = statement.getResultSet();
			
			if (resultSet != null) {
				resultSet.next();
				value = resultSet.getString(1);
			}
			
			
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getIsDeactivatedRule",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getIsDeactivatedRule",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getIsDeactivatedRule", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getIsDeactivatedRule", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting getIsDeactivatedRule()");
		return "Y".equals(value);
		
	}
	
	public static Date getSysDateWithMinCcyTimeFrame() throws SwtException {
		log.debug("Enter into getSysDateWithMinCcyTimeFrame method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		Date value = null;
		try {
			String query = "SELECT PKG_PC_Tools.FN_GET_SYS_IN_MIN_CCY_TIME () expression FROM DUAL";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();
			if (resultSet != null) {
				resultSet.next();
				value = resultSet.getDate(1);
			}
			
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getSysDateWithMinCcyTimeFrame",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getSysDateWithMinCcyTimeFrame",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getSysDateWithMinCcyTimeFrame", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getSysDateWithMinCcyTimeFrame", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting getSysDateWithMinCcyTimeFrame()");
		return value;
		
	}
	
	public static Date getSysDateWithMaxCcyTimeFrame() throws SwtException {
		log.debug("Enter into getSysDateWithMaxCcyTimeFrame method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		Date value = null;
		try {
			String query = "SELECT PKG_PC_Tools.FN_GET_SYS_IN_MAX_CCY_TIME() expression FROM DUAL";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();
			if (resultSet != null) {
				resultSet.next();
				value = resultSet.getDate(1);
			}
			
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getSysDateWithMaxCcyTimeFrame",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getSysDateWithMaxCcyTimeFrame",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getSysDateWithMaxCcyTimeFrame", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getSysDateWithMaxCcyTimeFrame", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting getSysDateWithMaxCcyTimeFrame()");
		return value;
		
	}
	public static String getDashbordBreakDownQuery(PCReport pcReport) throws SwtException {
		log.debug("Enter into getSysDateWithMaxCcyTimeFrame method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String value = null;
		String listEntities = null;
		try {
			String query = "select pkg_pc_dashboard.fn_pr_report(" + pcReport.getEntityId() + "," + pcReport.getDateAsIso() + ","  + pcReport.getApplyCcyThreshold() + ","+ pcReport.getUseCurrencyMultiplier() + ","+ pcReport.getStatus() + ","+ pcReport.getBlockReason() + ","+ pcReport.getCurrencyCode() + ","+ pcReport.getAccountGroup() + ","+ pcReport.getAccount() + ","+ pcReport.getUserId()+ ","+ pcReport.getInitialFilter()+ ","+ pcReport.getUserFilter()+ ","+ pcReport.getRefFilter()+ ","+ pcReport.getSpreadOnly()+ ","+ pcReport.getOrder()+ ","+ pcReport.getAscDesc()+ ","+ pcReport.getRowBegin()+ ","+ pcReport.getRowEnd()+ ","+ pcReport.getArchive()+ ","+ pcReport.getCcyTime()+ ","+ pcReport.getInputSince()  +")from dual";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.execute();
			resultSet = statement.getResultSet();
			if (resultSet != null) {
				resultSet.next();
				value = resultSet.getString(1);
			}
			
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getSysDateWithMaxCcyTimeFrame",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getSysDateWithMaxCcyTimeFrame",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getSysDateWithMaxCcyTimeFrame", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getSysDateWithMaxCcyTimeFrame", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting getSysDateWithMaxCcyTimeFrame()");
		return value;
		
	}
	
	public static String getDictionaryMessageValue(String code, String lang , String params) throws SwtException {
		log.debug("Enter into getDictionaryMessageValue method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String value = null;
		try {
			String query = "SELECT pkg_pc_tools.GetDictText (?, ?, ?) expression FROM DUAL";
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.setString(1, code);
			statement.setString(2, lang);
			statement.setString(3, params);
			statement.execute();
			resultSet = statement.getResultSet();
			
			if (resultSet != null) {
				resultSet.next();
				value = resultSet.getString(1);
			}
			
			
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getAllEntityOption",
					PCMUtil.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getAllEntityOption",
					PCMUtil.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getDictionaryMessageValue", PCMUtil.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getDictionaryMessageValue", PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting getDictionaryMessageValue");
		return value;
		
	}

	
	public static LinkedList<HashMap<String, Object>> executeNamedSelectQuery(String squeryName, Object...parameters) throws Exception{
		String squery  = SwtUtil.getNamedQuery(squeryName);
		long startTime = System.currentTimeMillis();
		LinkedList<HashMap<String, Object>> result = new LinkedList<HashMap<String, Object>>();

		result =  executeSelectQuery(squery, parameters);
		if(SwtUtil.isDBMonitorEnabled()){
			log.fatal("Executed NamedSelectQuery='"+squeryName+"', Took="+(System.currentTimeMillis()  - startTime)+" Millis, using parameters=["+SwtUtil.arrayToString(parameters, ", ")+"].");
		}
		return result;
	}
	
	/**
	 * General reasons execute SELECT SQL query
	 * @param squery
	 * @param parameters
	 * @return
	 * @throws Exception
	 */
	public static LinkedList<HashMap<String, Object>> executeSelectQuery(String squery, Object...parameters) throws Exception{
		LinkedList<HashMap<String, Object>> result = new LinkedList<HashMap<String, Object>>();
		HashMap<String, Object> rowMap = null;
		
		Connection conn = null;
		ResultSet rs = null;
		PreparedStatement  pstmt = null;
		int columnCount = 0;
		int paramsCount = parameters.length;
		long startTime = System.currentTimeMillis();
		try {
			// Initialize connection and the prepared statement
			//conn  = ConnectionManager.getInstance().databaseCon();
			conn = ((DataSource) SwtUtil.getBean("dataSourceDb-PCM")).getConnection();
			pstmt = conn.prepareStatement(squery);
			
		
			// Note: pstmt.getParameterMetaData(); ==> throws error "Unsupported feature" as jdbc is provided by Oracle
			// Set bind variables
			for (int i=0; i<paramsCount; i++){
				Object paramValue = parameters[i];	
				if(paramValue==null)
					pstmt.setNull(i+1, Types.VARCHAR);
				else if(paramValue instanceof Integer)
					pstmt.setInt(i+1, (Integer)paramValue);
				else if (paramValue instanceof String)
					pstmt.setString(i+1, (String)paramValue);
				else if (paramValue instanceof java.util.Date)
					pstmt.setDate(i+1, SwtUtil.truncateDateTime(((java.util.Date)paramValue)));
				else
					pstmt.setObject(i+1, paramValue);
			}
			
			// Execute the statement and fill results
			rs = pstmt.executeQuery();
			
			ResultSetMetaData metadata = rs.getMetaData();
			int colCount = metadata.getColumnCount();
			int idx = 1;
			
			ArrayList<String> colNames = new ArrayList<String>();
			ArrayList<Integer> colTypes= new ArrayList<Integer>();
			for (int i = idx; i <= colCount; i++) {
				colNames.add(metadata.getColumnName(i));
				colTypes.add(metadata.getColumnType(i));
			}
			if (rs != null) {
				while (rs.next()) {
					rowMap = new HashMap<String, Object>();
					for (int i = idx; i <= colCount; i++) {
						// check if CLOB type 
						if (colTypes.get(i -idx) == 2005)
						{
							rowMap.put(colNames.get(i - idx).toLowerCase(),
									rs.getString(i));
						}else
						{
							Object colValue = rs.getObject(i);
							if(colValue!=null){
								if(colValue.getClass().getCanonicalName().equals(BigDecimal.class.getName())){
									rowMap.put(colNames.get(i - idx).toLowerCase(), ((BigDecimal)colValue).doubleValue());
								}
								else if (colValue.getClass().getCanonicalName().equals(Timestamp.class.getName())){
									rowMap.put(colNames.get(i - idx).toLowerCase(), new Date(((Timestamp)colValue).getTime()));
								}
								else{
									rowMap.put(colNames.get(i - idx).toLowerCase(),
											rs.getObject(i));
								}
							}else {
								rowMap.put(colNames.get(i - idx).toLowerCase(),
										rs.getObject(i));
							}
						}

					}
					result.add(rowMap);
				}
			}
			return result;
		} finally {
			try{if(rs!=null)rs.close();}catch(SQLException e){}
			try{if(pstmt!=null)pstmt.close();}catch(SQLException e){}
			try{if(conn!=null)conn.close();}catch(SQLException e){}
		}
	}
	
	/**
	 * Retrieve all PCM notifications from PC_NOTIFICATIONS table
	 * @param notificationsType1
	 * @param notificationsType2
	 * @return Collection
	 * @throws Exception
	 */
	public static Collection getAllPCMNotifications(String notificationsType1, String notificationsType2) throws Exception{
		
		Collection fetchedNotificationsPCM = null;
		Collection newfetchedNotificationsPCM = null;
		Session session = null;
		try {
			newfetchedNotificationsPCM = new ArrayList();
			session = SwtUtil.pcSessionFactory.openSession();
			fetchedNotificationsPCM = session.createQuery(
					"from PCNotifications n where n.notificationType in (?1, ?2)", PCNotifications.class)
			        .setParameter(1, notificationsType1, StandardBasicTypes.STRING)
			        .setParameter(2, notificationsType2, StandardBasicTypes.STRING).getResultList();

			for (Iterator iterator = fetchedNotificationsPCM.iterator(); iterator.hasNext();) {
				PCNotifications oldNotif = (PCNotifications) iterator.next();
				Notifications newNotif = new Notifications();
				newNotif.setHostId(oldNotif.getHostId());
				newNotif.setEntityId(oldNotif.getEntityId());
				newNotif.setNotificationId(oldNotif.getNotificationId());
				newNotif.setRelationId(oldNotif.getRelationId());
				newNotif.setNotificationType(oldNotif.getNotificationType());
				newNotif.setPriority(oldNotif.getPriority());
				newNotif.setNotificationMessage(oldNotif.getNotificationMessage());
				newfetchedNotificationsPCM.add(newNotif);
			}
		} catch (Exception e) {
			log.error( " PCMUtil - [getAllPCMNotifications] - Exception - "+ e.getMessage());
		} finally {
			
			JDBCCloser.close(session);
		}
		return newfetchedNotificationsPCM;
	}
	
	
	
	public static Date getDateInCcyTimeframe(String entityId, String currencyId, Date selectedDate, boolean reverse) throws SwtException {
		
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		Date date = null;

		try {
			log.debug(PCMUtil.class.getName() + " - [ getDateInCcyTimeframe ]- Entry");

			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("select PKG_PC_TOOLS.FN_GET_DATE_IN_CCY_TIME(?,?,?,?) from dual");
			cstmt.setString(1, entityId);
			cstmt.setString(2, currencyId);
			cstmt.setTimestamp(3, new java.sql.Timestamp(selectedDate.getTime()));
			if(reverse) {
				cstmt.setString(4, "REV");
			}else {
				cstmt.setString(4, "N");
			}
			
			rs= cstmt.executeQuery();
			rs.next();
			
			date = (Date) rs.getTimestamp(1);
			log.debug(PCMUtil.class.getName()  + " - [ getDateInCcyTimeframe ]- Exit");
		} catch (SQLException sqlException) {
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getDateInCcyTimeframe", PCMUtil.class);
		} catch (Exception exp) {
			log.error(PCMUtil.class.getName() 
					+ " - Exception Catched in [ getDateInCcyTimeframe ] method : - "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getDateInCcyTimeframe",PCMUtil.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getDateInCcyTimeframe",PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		
		return date;
	}
	public static Date convertEntityToCcyTimeframe(String entityId, String currencyId, Date selectedDate, boolean reverse) throws SwtException {
		
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		Date date = null;
		
		try {
			log.debug(PCMUtil.class.getName() + " - [ getDateInCcyTimeframe ]- Entry");
			
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("select PKG_PC_TOOLS.FN_ENTITY_TO_CCY_TIMEFRAME(?,?,?,?) from dual");
			cstmt.setTimestamp(1, new java.sql.Timestamp(selectedDate.getTime()));
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyId);
			if(reverse) {
				cstmt.setString(4, "REV");
			}else {
				cstmt.setString(4, "N");
			}
			
			rs= cstmt.executeQuery();
			rs.next();
			
			date = (Date) rs.getTimestamp(1);
			log.debug(PCMUtil.class.getName()  + " - [ getDateInCcyTimeframe ]- Exit");
		} catch (SQLException sqlException) {
			sqlException.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getDateInCcyTimeframe", PCMUtil.class);
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(PCMUtil.class.getName() 
					+ " - Exception Catched in [ getDateInCcyTimeframe ] method : - "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getDateInCcyTimeframe",PCMUtil.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getDateInCcyTimeframe",PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		
		return date;
	}
	
	
	
	/**
	 * This function is used to format the system date with given date
	 * 
	 * @param sysDate
	 * @return String
	 * @throws SwtException
	 */
	public static String getDefaultDateForPCMMonitor(HttpSession httpsession, String entity) throws SwtException {
		// system date in string
		String strDate = null;
		// To hold current dateformat
		String dateFormat = null;
		String userId = null;
		
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		Date date;
		
		try {
			userId = SwtUtil.getCurrentUserId(httpsession);
			// get the date format for current session
			dateFormat = SwtUtil.getCurrentDateFormat(httpsession);
			
			
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("select pkg_pc_dashboard.fn_get_value_date(?,?) from dual");
			cstmt.setString(1, entity);
			cstmt.setString(2, userId);
			rs= cstmt.executeQuery();
			
			rs.next();
			date = (Date) rs.getDate(1);
			
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
			strDate = sdf.format(date);
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getSysDateWithFmt] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getDateInCcyTimeframe",PCMUtil.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getDateInCcyTimeframe",PCMUtil.class);

			if (thrownException != null)
				throw thrownException;
			// nullify objects
			session = null;
			dateFormat = null;
		}
		return strDate;
	}

	public static Date getDateInEntityOffset(String entityId, Date selectedDate) throws SwtException {
		
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		Date date = null;
		
		try {
			log.debug(PCMUtil.class.getName() + " - [ getDateInCcyTimeframe ]- Entry");
			
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("select PKG_PC_Tools.Fn_Get_Offset_Date_Ent(?, ?) from dual");
			cstmt.setTimestamp(1, new java.sql.Timestamp(selectedDate.getTime()));
			cstmt.setString(2, entityId);
			
			rs= cstmt.executeQuery();
			rs.next();
			
			date = (Date) rs.getTimestamp(1);
			log.debug(PCMUtil.class.getName()  + " - [ getDateInCcyTimeframe ]- Exit");
		} catch (SQLException sqlException) {
			sqlException.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getDateInCcyTimeframe", PCMUtil.class);
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(PCMUtil.class.getName() 
					+ " - Exception Catched in [ getDateInCcyTimeframe ] method : - "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getDateInCcyTimeframe",PCMUtil.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getDateInCcyTimeframe",PCMUtil.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		
		return date;
	}

	
	/**
	 * This method is used to refresh views related to payment request details for 
	 * VW_PC_MESSAGES_OUTPUT_ALL , VW_PC_PAYMENT_MESSAGE_ALL ,VW_PC_PAYMENT_REQUEST_INFO_ALL ,VW_PC_PAYMENT_REQUEST_ALL
	 * and VW_PC_PAYMENT_STOP_ALL
	 * @throws SwtException
	 */
	public static void updateViews() throws SwtException {
	    /* Method's local variable declaration */

	    Session session = null;
	    Connection conn=null;
	    CallableStatement pstmt=null;
	    
	    try {
	      log.debug(PCMUtil.class.getName()
	          + " - [updateViews] - " + "Entry");
		
	      /*
	       * Returns the HibernateTemplate and session factory for this DAO,
	       * and opening a Hibernate Session
	       */
	      session = SwtUtil.pcSessionFactory.openSession();
	      conn = SwtUtil.connection(session);
	      /* Using Callable statement to execute the Stored Procedure */
	      pstmt=conn.prepareCall("{call pkg_pc_screen_utility.sp_create_payment_views()}");
	      pstmt.execute();
	     
	  
	    } catch (Exception e) {
			log
					.error(PCMUtil.class.getName()
							+ " - Exception Catched in [updateViews] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateViews", PCMUtil.class.getClass());
	    } finally {
	    	JDBCCloser.close(null, pstmt, conn, session);
	      
	    }
	  }

}
