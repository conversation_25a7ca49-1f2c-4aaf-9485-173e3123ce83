/*
 * @(#)CategoryRulesMaintenanceAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.CategoryRulesMaintenanceManager;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;



/**
 * <AUTHOR>
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/categoryRulesPCM", "/categoryRulesPCM.do"})
public class CategoryRulesMaintenanceAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/pc/maintenance/categoryruleadd");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "categoryRuleAdd":
				return categoryRuleAdd();
			case "add":
				return add();
			case "view":
				return view();
		}


		return unspecified();
	}





	@Autowired
	private CategoryRulesMaintenanceManager categoryRulesMaintenanceManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(CategoryRulesMaintenanceAction.class);

	public void setCategoryRulesMaintenanceManager(CategoryRulesMaintenanceManager categoryRulesMaintenanceManager) {
		this.categoryRulesMaintenanceManager = categoryRulesMaintenanceManager;
	}


	protected String unspecified() throws SwtException {
		return getView("success");
	}

	public String categoryRuleAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "categoryRuleAdd");
		return getView("add");
	}

	/**
	 * Method to display the add category screen with its loaded values
	 * collected from parent screen and data base
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;

		PCMCurrencyDetailsVO currencyList;
		Collection entityList;
		Collection sourcesList;

		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;

		Iterator j = null;
		LabelValueBean row;

		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - " + "Entry");


			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "categoryRuleMaintenance";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.CATEGORY_RULE);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),SwtConstants.DATA_FETCH_OK);

			// Singletons
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.HELP_URL,"");
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			/*
			 * the sources Dropdown
			 */
			lstSelect = new ArrayList<SelectInfo>();
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", true));
			sourcesList = PCMUtil.getSourceList();
			j = sourcesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));

			/*
			 * the currency Dropdown
			 */

			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", true));
			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*
			 * the entity Dropdown
			 */

			entityList = getEntityList(request);
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", true));
			j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));


			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.CATEGORY_RULE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [add] method : - "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",swtexp.getMessage());
			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [add] method : - "
					  + exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", CategoryMaintenanceAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",exp.getMessage());
			return getView("fail");
		}
	}




	/**
	 * Method to display the view category screen with its loaded values
	 * collected from parent screen and data base
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;


		Collection currencyList;
		Collection entityList;
		Collection sourcesList;

		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;

		Iterator j = null;
		LabelValueBean row;


		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ view ] - " + "Entry");


			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "categoryRuleMaintenance";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.CATEGORY_RULE);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean
							.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			/*
			 * the sources Dropdown
			 */
			lstSelect = new ArrayList<SelectInfo>();
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", true));
			sourcesList = PCMUtil.getSourceList();
			j = sourcesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));

			/*
			 * the currency Dropdown
			 */

			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", true));
			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = (Collection) currencyMaintenanceManager.getCurrencyDetailList(false).getCurrencyList();
			j = currencyList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*
			 * the entity Dropdown
			 */

			entityList = getEntityList(request);
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", true));
			j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));


			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.CATEGORY_RULE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [view] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [view] method : - "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",swtexp.getMessage());
			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [view] method : - "
					  + exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", CategoryMaintenanceAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",exp.getMessage());
			return getView("fail");
		}
	}


	/**
	 * Method to get the list of entities from SwtUtil that are accessed by the
	 * user and set them in to the request attribute
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */

	private Collection getEntityList(HttpServletRequest request) throws SwtException {

		log.debug(this.getClass().getName() + " - [getEntityList] - "
				  + "Entry");

		/* Method's local variable declaration */
		HttpSession session;
		Collection coll;
		session = request.getSession();
		/* Collects the list of entity for the user */
		coll = SwtUtil.getUserEntityAccessList(session);
		/* Collects the label value bean for entity name and entity id */
		coll = SwtUtil.convertEntityAcessCollectionLVLFullName(coll, session);
		log.debug(this.getClass().getName() + " - [getEntityList] - "
				  + "Exit");
		return coll;

	}
}