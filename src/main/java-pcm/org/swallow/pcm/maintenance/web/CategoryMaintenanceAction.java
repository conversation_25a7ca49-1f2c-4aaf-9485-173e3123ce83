/*
 * @(#)CategoryMaintenanceAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;
import org.swallow.pcm.maintenance.model.ListChangesCategory;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.kv.KVType;
import org.swallow.pcm.maintenance.model.core.kv.TabKVType;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;
import org.swallow.pcm.maintenance.service.CategoryRulesMaintenanceManager;
import org.swallow.pcm.maintenance.service.SpreadProfilesMaintenanceManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
/**
 * <AUTHOR>
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/categoryPCM", "/categoryPCM.do"})
public class CategoryMaintenanceAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/pc/maintenance/categorymaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/maintenance/categorymaintenance");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("expressionBuilder", "jsp/pc/maintenance/expressionbuilder");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();


	@Autowired
	private CategoryMaintenanceManager categoryMaintenanceManager = null;


	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(CategoryMaintenanceAction.class);

	public void setCategoryMaintenanceManager(
			CategoryMaintenanceManager categoryMaintenanceManager) {
		this.categoryMaintenanceManager = categoryMaintenanceManager;
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "categoryAdd":
				return categoryAdd();
			case "categoryView":
				return categoryView();
			case "display":
				return display();
			case "add":
				return add();
			case "view":
				return view();
			case "save":
				return save();
			case "update":
				return update();
			case "delete":
				return delete();
		}


		return unspecified();
	}
	protected String unspecified() throws Exception {

		// log debug message
		log.debug(this.getClass().getName()
				+ " - [unspecified] - Enter/Exit - Call to "
				+ this.getClass().getName()
				+ " - [unspecified]");

		return getView("success");
	}


	public String categoryAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		request.setAttribute("method", "categoryAdd");
		return getView("add");
	}

	public String categoryView() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "categoryView");
		return getView("add");
	}


	/**
	 * This method gets Category maintenance screen
	 * display it.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String errorMessage = null;
		String languageId = null;
		SystemFormats systemFormats = null;
		List<Category> categoryListDetails = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			categoryListDetails = new ArrayList<Category>();

			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			categoryListDetails= (ArrayList<Category>) categoryMaintenanceManager.getCategoryDetailList();

			// build XML response
			return sendDisplayResponse(categoryListDetails, languageId, systemFormats);

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "."
					+ ex.getStackTrace()[0].getMethodName() + ":"
					+ ex.getStackTrace()[0].getLineNumber() + " "
					+ ex.getMessage();

			// log error message
			log.error(this.getClass().getName()
					+ " - [display] - SwtException -"
					+ errorMessage);


		}finally {
			errorMessage = null;
			languageId = null;
			systemFormats = null;
			categoryListDetails = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [display] - Exit");
		}

		return null;
	}


	/**
	 * Method to display the add currency screen with its loaded values
	 * collected from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;

		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;

		try {

			log.debug(this.getClass().getName() + " - [add] - " + "Entry");
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();


			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "categoryMaintenance";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.CATEGORY_RULE);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean
							.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// Grid Spread
			responseConstructor.formGridStart("gridSpread");
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getSpreadGridColumns(width, columnOrder, hiddenColumns));
			responseConstructor.formRowsStart(0);
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridSpread");

			// Grid category Rule
			responseConstructor.formGridStart("gridRules");
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getCategoryRuleGridColumns(width, columnOrder, hiddenColumns));
			responseConstructor.formRowsStart(0);
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridRules");
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.HELPURL,	"swf/system/help/Help.swf?message=");
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			xmlWriter.endElement(PCMConstant.CATEGORY_RULE);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",swtexp.getMessage());
			return getExceptionXmlData(swtexp.getMessage());

		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", CategoryMaintenanceAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",exp.getMessage());
			return getExceptionXmlData(exp.getMessage());
		}finally{
			responseConstructor = null;
			xmlWriter.destroyWriter();
			userId = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;

			log.debug(this.getClass().getName() + "- [add] - Exit");
		}

	}


	/**
	 * Method to display the view category screen with its loaded values
	 * collected from parent screen and data base
	 *
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		Category category = null;
		Collection spreadProfileList = new ArrayList<SpreadProfile>();
		Collection categoryRulesList = new ArrayList<CategoryRule>();
		SpreadProfilesMaintenanceManager spreadProfileManager = null;
		ArrayList<RuleConditions> arrayTabCondition = new ArrayList();
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		Gson gson = null;
		Integer maxOrderCR = null;
		String listOrdreCR = null;
		Integer ordre;
		ArrayList<Integer> listOrdinalCategoryRule= new ArrayList<Integer>() ;
		CategoryRulesMaintenanceManager categoryRulesManager = null;
		String categoryId;
		Boolean existPayReq  = false;
		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ view ] - " + "Entry");


			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "categoryMaintenance";
			categoryId=request.getParameter("categoryId");

			if(categoryId != null) {
				// Get category details
				category= categoryMaintenanceManager.getCategoryDetailById(categoryId);
				existPayReq = categoryMaintenanceManager.existPayRequest(categoryId);
			}

			if(!category.getCategoryRules().isEmpty()) {
				categoryRulesManager = (CategoryRulesMaintenanceManager) SwtUtil.getBean("categoryRulesMaintenanceManager");
				maxOrderCR = categoryRulesManager.getMaxOrder(categoryId);
				maxOrderCR++;
			}

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.CATEGORY_RULE);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean
							.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			// Grid Spread
			spreadProfileManager = (SpreadProfilesMaintenanceManager) SwtUtil.getBean("spreadProfilesMaintenanceManager");
			spreadProfileList = spreadProfileManager.getSpreadProfilesByCategoryId(categoryId);
			responseConstructor.formGridStart("gridSpread");
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getSpreadGridColumns(width, columnOrder, hiddenColumns));
			responseConstructor.formRowsStart(spreadProfileList.size());
			for (Iterator<SpreadProfile> it = spreadProfileList.iterator(); it.hasNext();) {
				SpreadProfile spread = (SpreadProfile) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.SPREAD_ID, spread.getId().getSpreadProfileId()+"");
				responseConstructor.createRowElement(PCMConstant.SPREAD_NAME, spread.getSpreadProfileName());
				responseConstructor.createRowElement(PCMConstant.CURRENCY, spread.getCurrencyCode());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridSpread");

			// Grid rules
			categoryRulesList = category.getCategoryRules();
			responseConstructor.formGridStart("gridRules");
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getCategoryRuleGridColumns(width, columnOrder, hiddenColumns));
			responseConstructor.formRowsStart(categoryRulesList.size());
			gson = new Gson();
			for (Iterator<CategoryRule> it = categoryRulesList.iterator(); it.hasNext();) {
				CategoryRule categoryRule = (CategoryRule) it.next();
				ordre = (Integer)( categoryRule.getOrdinal() == null ? 0 : categoryRule.getOrdinal());
				if(!listOrdinalCategoryRule.contains(ordre)  &&  ordre != 0) {
					listOrdinalCategoryRule.add(ordre);
				}
				arrayTabCondition = new ArrayList();
				if(categoryRule.getRulesDefinition()!= null) {
					if(categoryRule.getRulesDefinition().getRuleConditions()!= null && categoryRule.getRulesDefinition().getRuleConditions().size() > 0) {
						for (int i = 0; i < categoryRule.getRulesDefinition().getRuleConditions().size(); i++) {
							arrayTabCondition.add(categoryRule.getRulesDefinition().getRuleConditions().get(i));
						}
					}
				}

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.CATEGORY_RULE_ID, categoryRule.getCategoryRuleId()+"");
				responseConstructor.createRowElement(PCMConstant.ORDINAL,  categoryRule.getOrdinal()== null ? "" :categoryRule.getOrdinal()+"");
				responseConstructor.createRowElement(PCMConstant.CATEGORY_RULE_NAME, categoryRule.getCategoryRuleName());
				responseConstructor.createRowElement(PCMConstant.APPLY_ONLY_TO_SOURCE, SwtUtil.isEmptyOrNull(categoryRule.getApplyOnlyToSource())? "" : categoryRule.getApplyOnlyToSource());
				responseConstructor.createRowElement(PCMConstant.APPLY_ONLY_TO_CCY,  SwtUtil.isEmptyOrNull(categoryRule.getApplyOnlyToCcy()) ? "": categoryRule.getApplyOnlyToCcy());
				responseConstructor.createRowElement(PCMConstant.APPLY_ONLY_TO_ENTITY, SwtUtil.isEmptyOrNull(categoryRule.getApplyOnlyToEntity()) ? "":  categoryRule.getApplyOnlyToEntity());
				if(categoryRule.getRulesDefinition()!= null) {
					responseConstructor.createRowElement(PCMConstant.RULETEXT, categoryRule.getRulesDefinition().getRuleText());
					responseConstructor.createRowElement(PCMConstant.RULEQUERY, categoryRule.getRulesDefinition().getRuleQuery());
					responseConstructor.createRowElement(PCMConstant.RULE_CONDITIONS, gson.toJson(arrayTabCondition));
				}

				responseConstructor.createRowElement(PCMConstant.RULE_ID, categoryRule.getRuleId());
				responseConstructor.formRowEnd();
			}
			listOrdreCR= listOrdinalCategoryRule.toString();
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridRules");

			// Singletons
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.CATEGORY_ID, category.getId().getCategoryId()+"");
			responseConstructor.createElement(PCMConstant.CATEGORY_NAME, category.getCategoryName());
			responseConstructor.createElement(PCMConstant.ORDINAL, category.getOrdinal()!= null ? ""+ category.getOrdinal() : "");
			responseConstructor.createElement(PCMConstant.RULE_ASSIGN_PRIORITY, category.getRuleAssignPriority()!= null ? ""+ category.getRuleAssignPriority() : "1");
			responseConstructor.createElement(PCMConstant.IS_ACTIVE,  category.getIsActive());
			responseConstructor.createElement(PCMConstant.URGENT_SPREAD_IND,category.getUrgentSpreadInd());
			responseConstructor.createElement(PCMConstant.ASSIGN_METHOD, category.getAssignmentMethod() );
			responseConstructor.createElement(PCMConstant.USE_LIQ_CHECK, category.getUseLiqCheck()== null? "N":category.getUseLiqCheck());
			responseConstructor.createElement(PCMConstant.INCL_TARGET_PAY_RELEASED, category.getInclTargetPayReleased()== null? "N":category.getInclTargetPayReleased());
			responseConstructor.createElement(PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC, category.getInclInAvailableLiqCalc()== null? "N": category.getInclInAvailableLiqCalc());
			responseConstructor.createElement(PCMConstant.SET_RELEASE_TIME, category.getSetReleaseTime()== null? "":category.getSetReleaseTime());
			responseConstructor.createElement(PCMConstant.SPECIFIED_RELEASE_TIME, category.getSpecifiedReleaseTime()== null? "":category.getSpecifiedReleaseTime());
			responseConstructor.createElement(PCMConstant.EXIST_PAY_REQ, Boolean.toString(existPayReq));
			responseConstructor.createElement(PCMConstant.ORDINAL_LIST_CR, listOrdreCR);
			responseConstructor.createElement(PCMConstant.MAX_ORDINAL_CR, maxOrderCR == null? "": maxOrderCR.toString());
			responseConstructor.createElement(PCMConstant.OFFSET_DAYS, category.getReleaseValueDateOffset() != null? "" +category.getReleaseValueDateOffset() : "");

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.CATEGORY_RULE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [view] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",swtexp.getMessage());
			return getExceptionXmlData(swtexp.getMessage());
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", CategoryMaintenanceAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",exp.getMessage());
			return getExceptionXmlData(exp.getMessage());

		}finally{
			responseConstructor = null;
			xmlWriter.destroyWriter();
			userId = null;
			componentId = null;
			category = null;
			spreadProfileList = null;
			categoryRulesList = null;
			spreadProfileManager = null;
			arrayTabCondition = null;
			categoryId= null;
			maxOrderCR = null;
			listOrdreCR = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			gson = null;
			ordre = null;
			listOrdinalCategoryRule = null ;
			categoryRulesManager = null;
			log.debug(this.getClass().getName()
					+ "- [view] - Exit");
		}

	}



	/**
	 * Action method to save Category Rule details
	 *
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		Integer ordinal= null;
		Category category;
		String currentUserId = null;
		String xmlData= null;
		ArrayList<TabKVType> crudResult =null;
		ArrayList<CategoryRule> listCategoryRuleAdd =null;
		ArrayList<CategoryRule> listCategoryRuleDelete = null;
		ArrayList<CategoryRule> listCategoryRuleUpdate = null;
		String categoryId;
		String categoryName;
		String urgentSpreadInd;
		String assignMethod;
		String useLiqCheck;
		String inclTargetPayReleased;
		String inclInAvailableLiqCalc;
		String specifiedTime;
		String releaseTime;
		String active;
		String offsetDays;
		Integer offsetDaysAsNumber = null;
		Integer ruleAssignmentPriority= null;
		try {
			currentUserId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			category = new Category();
			listCategoryRuleAdd = new ArrayList<CategoryRule>();
			listCategoryRuleDelete = new ArrayList<CategoryRule>();
			listCategoryRuleUpdate = new ArrayList<CategoryRule>();


			log.debug(this.getClass().getName() + " - [save] - "
					+ "Entry");

			xmlData = request.getParameter("xmlData");
			categoryId=(String) request.getParameter("categoryId");
			categoryName=(String) request.getParameter("categoryName");
			String order=request.getParameter("ordinal") == null ? null : (String) request.getParameter("ordinal") ;
			if(order != null && ! order.equals("")) {
				ordinal=Integer.parseInt(order) ;
				ordinal=Integer.valueOf(order);
				category.setOrdinal(ordinal);
			}

			String ruleAssignPriority=request.getParameter("ruleAssignPriority") == null ? null : (String) request.getParameter("ruleAssignPriority") ;
			if(ruleAssignPriority != null && ! ruleAssignPriority.equals("")) {
				ruleAssignmentPriority=Integer.parseInt(ruleAssignPriority) ;
				ruleAssignmentPriority=Integer.valueOf(ruleAssignPriority);
				category.setRuleAssignPriority(ruleAssignmentPriority);
			}

			assignMethod= (String) request.getParameter("assignMethod");
			urgentSpreadInd= (String) request.getParameter("instantRelease");
			releaseTime = (String) request.getParameter("releaseTime");
			specifiedTime=request.getParameter("specifiedTime");
			inclTargetPayReleased=(String) request.getParameter("includeInTarget");
			inclInAvailableLiqCalc=(String) request.getParameter("includeInLiquidity");
			useLiqCheck = (String) request.getParameter("useLiquidity");
			active = (String) request.getParameter("isActive");
			offsetDays = request.getParameter("offsetDays") == null ? null : (String)request.getParameter("offsetDays");

			if(!SwtUtil.isEmptyOrNull(offsetDays)) {
				offsetDaysAsNumber=Integer.parseInt(offsetDays) ;
				offsetDaysAsNumber=Integer.valueOf(offsetDays);
				category.setReleaseValueDateOffset(offsetDaysAsNumber);
			}
			category.getId().setCategoryId(categoryId);
			category.setCategoryName(categoryName);
			category.setAssignmentMethod(assignMethod);
			category.setInclTargetPayReleased(inclTargetPayReleased);
			category.setUrgentSpreadInd(urgentSpreadInd);
			category.setInclInAvailableLiqCalc(inclInAvailableLiqCalc);
			category.setUseLiqCheck(useLiqCheck);
			category.setIsActive(active);
			category.setSetReleaseTime(releaseTime);
			category.setSpecifiedReleaseTime(specifiedTime);


			//ReOrder ordinal of Category
//			String value_from =(String) request.getParameter("order");
//			String reOrder =(String) request.getParameter("reOrder");
//			if(reOrder!= null && reOrder.equals("true")) {
//				categoryMaintenanceManager.reOrder(Integer.valueOf(value_from), ordinal);
//			}


			crudResult = doCrudOperation(currentUserId, xmlData, null);
			ListChangesCategory changes = getListChangesCategory(crudResult, categoryId);
			if(changes != null) {
				listCategoryRuleAdd=changes.getListCategoryRuleAdd();
				listCategoryRuleUpdate=changes.getListCategoryRuleUpdate();
				listCategoryRuleDelete=changes.getListCategoryRuleDelete();
			}

			categoryMaintenanceManager.crudCategoryRule(category, listCategoryRuleAdd, listCategoryRuleUpdate,listCategoryRuleDelete,"add");


			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			display();
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveCategory] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"RECORD_EXIST");
				//}else if(swtExp.getErrorCode().equals("errors.DataIntegrityExceptioninName")) {
				//request.setAttribute("reply_message",	"ERROR_NAME");
			}else if(swtExp.getErrorCode().equals("errors.DataIntegrityExceptioninRuleName")) {
				request.setAttribute("reply_message",
						"ERROR_NAME_RULE");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}
		}catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveCategory] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message","ERROR_SAVE");

		}finally {
			xmlData= null;
			crudResult =null;
			listCategoryRuleAdd =null;
			listCategoryRuleDelete = null;
			listCategoryRuleUpdate = null;
			categoryId = null;
			categoryName = null;
			urgentSpreadInd = null;
			assignMethod = null;
			useLiqCheck = null;
			inclTargetPayReleased = null;
			inclInAvailableLiqCalc = null;
			specifiedTime = null;
			releaseTime = null;
			active = null;
			ordinal= null;
			log.debug(this.getClass().getName()
					+ "- [save] - Exit");
		}
		return getView("statechange");
	}



	public ListChangesCategory getListChangesCategory(ArrayList<TabKVType> crudResult, String categoryId) {
		CategoryRule category = null;
		RulesDefinition rule = null;
		ListChangesCategory listChangesCategory = null;
		ArrayList<CategoryRule> listCategoryRuleDelete = new ArrayList<CategoryRule>() ;
		ArrayList<CategoryRule> listCategoryRuleUpdate = new ArrayList<CategoryRule>() ;
		ArrayList<CategoryRule> listCategoryRuleAdd = new ArrayList<CategoryRule>();
		RuleConditions[] ruleConditions = null;
		ArrayList<RuleConditions> ruleConditionsList = null;
		RuleConditions ruleCondition = null;
		String tableToJoinQuery = null;
		try{
			log.debug(this.getClass().getName()
					+ "- [getListChangesCategory] - Entry");
			listChangesCategory = new ListChangesCategory();
			for (Iterator iterator = crudResult.iterator(); iterator.hasNext();) {
				TabKVType object = (TabKVType) iterator.next();
				if (object.getTableName().equals("PC_CATEGORY_RULE")) {
					category = new CategoryRule();
					rule = new RulesDefinition();
					category.setCategoryId(categoryId);

					for(int k =0; k<object.getElementList().size();k++){
						KVType colmunValue = object.getElementList().get(k);
						if (colmunValue.getKey().equals("TAB_CONDITION")) {
							Gson gson = new Gson();
							ruleConditions = gson.fromJson(colmunValue.getValue(), RuleConditions[].class);
							if(ruleConditions!= null && ruleConditions.length>0) {
								ruleConditionsList = new ArrayList<RuleConditions>(Arrays.asList(ruleConditions));
								for (int i = 0; i < ruleConditionsList.size(); i++) {
									if(ruleConditionsList.get(i).getTableName().equals("PC_PAYMENT_REQUEST_INFO")) {
										tableToJoinQuery = "select count(*) from PC_PAYMENT_REQUEST P INNER JOIN pc_payment_request_info f ON p.payreq_id = f.payreq_id ";

									}
								}
							}
						}
					}


					for(int k =0; k<object.getElementList().size();k++){
						KVType colmunValue = object.getElementList().get(k);
						if (colmunValue.getKey().equals("CATEGORY_RULE_ID")) {
							String categoryRuleId = colmunValue.getValue();
							if(categoryRuleId!= null && !categoryRuleId.isEmpty() ) {
								category.setCategoryRuleId(Long.valueOf(categoryRuleId));
							}
						} else if (colmunValue.getKey().equals("NAME")) {
							category.setCategoryRuleName(colmunValue.getValue());
						} else if (colmunValue.getKey().equals("ORDINAL")) {
							String order = colmunValue.getValue();
							if(order!= null && !order.isEmpty() ) {
								category.setOrdinal(Integer.valueOf(order));
							}
						} else if (colmunValue.getKey().equals("APPLY_ONLY_TO_SOURCE")) {
							category.setApplyOnlyToSource(colmunValue.getValue());
						} else if (colmunValue.getKey().equals("APPLY_ONLY_TO_CCY")) {
							category.setApplyOnlyToCcy(colmunValue.getValue());
						} else if (colmunValue.getKey().equals("APPLY_ONLY_TO_ENTITY")) {
							category.setApplyOnlyToEntity(colmunValue.getValue());
						} else if (colmunValue.getKey().equals("RULE_ID")) {
							String ruleId = colmunValue.getValue();
							if(ruleId!= null && !ruleId.isEmpty() ) {
								rule.setRuleId(Integer.valueOf(ruleId));
								category.setRuleId(Integer.valueOf(ruleId));
							}
						} else if (colmunValue.getKey().equals("RULE_TYPE")) {
							rule.setRuleType(colmunValue.getValue());
						}else if (colmunValue.getKey().equals("RULE_TEXT")) {
							rule.setRuleText(colmunValue.getValue());

						}else if (colmunValue.getKey().equals("RULE_QUERY")) {
							String querySQL = colmunValue.getValue();
							if(!SwtUtil.isEmptyOrNull(tableToJoinQuery)) {
								int indexOfWhere = querySQL.indexOf("where");
								querySQL = tableToJoinQuery + querySQL.substring(indexOfWhere,querySQL.length());
							}
							rule.setRuleQuery(querySQL);

						}

					}
					if (object.getOperation().equals("I")) {
						rule.setRuleConditions(ruleConditionsList);
						category.setRulesDefinition(rule);
						listCategoryRuleAdd.add(category);
					} else if (object.getOperation().equals("U")) {
						rule.setRuleConditions(ruleConditionsList);
						category.setRulesDefinition(rule);
						listCategoryRuleUpdate.add(category);
					} else {
						rule.setRuleConditions(ruleConditionsList);
						category.setRulesDefinition(rule);
						listCategoryRuleDelete.add(category);
					}

				}

			}
			listChangesCategory.setListCategoryRuleAdd(listCategoryRuleAdd);
			listChangesCategory.setListCategoryRuleDelete(listCategoryRuleDelete);
			listChangesCategory.setListCategoryRuleUpdate(listCategoryRuleUpdate);
			return listChangesCategory;
		}catch(Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getListChangesCategory] method : - "
					+ exp.getMessage());
		}finally {
			category = null;
			rule = null;
			listChangesCategory = null;
			listCategoryRuleDelete = null ;
			listCategoryRuleUpdate = null ;
			listCategoryRuleAdd = null;
			ruleConditions = null;
			ruleConditionsList = null;
			ruleCondition = new RuleConditions();
			tableToJoinQuery = null;

			log.debug(this.getClass().getName()
					+ "- [getListChangesCategory] - Exit");
		}
		return listChangesCategory;
	}
	/**
	 * Method to display the change category screen with its loaded values
	 * collected from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		Category category;
		String xmlData= null;
		String currentUserId = null;
		ArrayList<TabKVType> crudResult =null;
		ArrayList<CategoryRule> listCategoryRuleAdd =null;
		ArrayList<CategoryRule> listCategoryRuleDelete = null;
		ArrayList<CategoryRule> listCategoryRuleUpdate = null;
		String categoryId;
		String categoryName;
		Integer ordinal = null;
		String urgentSpreadInd;
		String assignMethod;
		String useLiqCheck;
		String inclTargetPayReleased;
		String inclInAvailableLiqCalc;
		String specifiedTime;
		String releaseTime;
		String active;
		String offsetDays = null;
		Integer offsetDaysAsNumber = null;
		Integer ruleAssignmentPriority = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [update] - Entry");
			currentUserId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// Instantiate Category
			category = new Category();
			listCategoryRuleAdd = new ArrayList<CategoryRule>();
			listCategoryRuleDelete = new ArrayList<CategoryRule>();
			listCategoryRuleUpdate = new ArrayList<CategoryRule>();
			xmlData = request.getParameter("xmlData");
			categoryId=(String) request.getParameter("categoryId");
			categoryName=(String) request.getParameter("categoryName");
			String order= request.getParameter("ordinal") == null ? null : (String) request.getParameter("ordinal") ;
			if(order != null && ! order.equals("")) {
				ordinal=Integer.parseInt(order) ;
				ordinal=Integer.valueOf(order);
				category.setOrdinal(ordinal);
			}

			String ruleAssignPriority=request.getParameter("ruleAssignPriority") == null ? null : (String) request.getParameter("ruleAssignPriority") ;
			if(ruleAssignPriority != null && ! ruleAssignPriority.equals("")) {
				ruleAssignmentPriority=Integer.parseInt(ruleAssignPriority) ;
				ruleAssignmentPriority=Integer.valueOf(ruleAssignPriority);
				category.setRuleAssignPriority(ruleAssignmentPriority);
			}


			assignMethod= (String) request.getParameter("assignMethod");
			urgentSpreadInd= (String) request.getParameter("instantRelease");
			releaseTime = (String) request.getParameter("releaseTime");
			specifiedTime=request.getParameter("specifiedTime");
			inclTargetPayReleased=(String) request.getParameter("includeInTarget");
			inclInAvailableLiqCalc=(String) request.getParameter("includeInLiquidity");
			useLiqCheck = (String) request.getParameter("useLiquidity");
			active = (String) request.getParameter("isActive");
			offsetDays =request.getParameter("offsetDays");

			if(offsetDays != null && !SwtUtil.isEmptyOrNull(offsetDays)) {
				offsetDaysAsNumber=Integer.parseInt(offsetDays) ;
				offsetDaysAsNumber=Integer.valueOf(offsetDays);
				category.setReleaseValueDateOffset(offsetDaysAsNumber);
			}

			category.getId().setCategoryId(categoryId);
			category.setCategoryName(categoryName);
			category.setAssignmentMethod(assignMethod);
			category.setInclTargetPayReleased(inclTargetPayReleased);
			category.setUrgentSpreadInd(urgentSpreadInd);
			category.setInclInAvailableLiqCalc(inclInAvailableLiqCalc);
			category.setUseLiqCheck(useLiqCheck);
			category.setIsActive(active);
			category.setSetReleaseTime(releaseTime);
			category.setSpecifiedReleaseTime(specifiedTime);

			// Save the added values in DB through category manager
			//categoryMaintenanceManager.updateCategory(category);
			crudResult = doCrudOperation(currentUserId, xmlData, null);

			//ReOrder ordinal of Category
//			String reOrder =(String) request.getParameter("reOrder");
//			String value_from =(String) request.getParameter("order");
//			if(reOrder.equals("true")) {
//				categoryMaintenanceManager.reOrder(Integer.valueOf(value_from), ordinal);
//			}

			ListChangesCategory changes = getListChangesCategory(crudResult, categoryId);
			if(changes != null) {
				listCategoryRuleAdd=changes.getListCategoryRuleAdd();
				listCategoryRuleUpdate=changes.getListCategoryRuleUpdate();
				listCategoryRuleDelete=changes.getListCategoryRuleDelete();
			}

			categoryMaintenanceManager.crudCategoryRule(category, listCategoryRuleAdd, listCategoryRuleUpdate, listCategoryRuleDelete,"change");

			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			display();


		} catch (SwtException swtExp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"RECORD_EXIST");
				//}else if(swtExp.getErrorCode().equals("errors.DataIntegrityExceptioninName")) {
				//	request.setAttribute("reply_message",	"ERROR_NAME");
			}else if(swtExp.getErrorCode().equals("errors.DataIntegrityExceptioninRuleName")) {
				request.setAttribute("reply_message",
						"ERROR_NAME_RULE");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}

		}catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message","ERROR_SAVE");
		}finally {
			xmlData= null;
			crudResult =null;
			listCategoryRuleAdd =null;
			listCategoryRuleDelete = null;
			listCategoryRuleUpdate = null;
			categoryId = null;
			categoryName = null;
			urgentSpreadInd = null;
			assignMethod = null;
			useLiqCheck = null;
			inclTargetPayReleased = null;
			inclInAvailableLiqCalc = null;
			specifiedTime = null;
			releaseTime = null;
			active = null;
			ordinal= null;

			log.debug(this.getClass().getName()
					+ "- [update] - Exit");
		}
		return getView("statechange");
	}


	/**
	 * remove()
	 *
	 * Method to remove a category
	 *
	 * @return ActionForward
	 */
	public String delete() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String categoryId = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [delete] - " + "Entry");

			categoryId=request.getParameter("categoryId");
			if(categoryId != null) {

				categoryMaintenanceManager.deleteCategory(categoryId);
			}

			return display();
		} catch (SwtException exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [delete] - Exception -"
					+ exp.getMessage());
			// Log error message in DB
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete",
					CategoryMaintenanceAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			if (exp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninDelete")) {
				request.setAttribute("reply_message","Record cannot be deleted as other transactions depend on it");

			}else if(exp.getErrorCode().equals("errors.SwtRecordNotExist")) {
				request.setAttribute("reply_message","Record does not exist");

			}else {
				request.setAttribute("reply_message", "- [delete] - generic Exception - "+ exp.getErrorDesc());
			}
			//return getExceptionXmlData(exp.getMessage(), request, mapping);

		}finally {
			categoryId = null;
			log.debug(this.getClass().getName() + " - [delete] - Exit");
		}
		return getView("statechange");
	}



	/**
	 * This method forms the xml for displaying the category list.
	 *
	 * @param categoryDetails
	 * @param languageId
	 *            - passing languageId
	 * @param systemFormats
	 *            - passing system formats date
	 * @return
	 */
	public String sendDisplayResponse(List<Category> categoryDetails, String languageId,
									  SystemFormats systemFormats) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String type = null;
		Integer maxOrder;
		String listOrdre;
		String listRuleAssignPriority= null;
		Integer ordre;
		Integer priority;
		ArrayList<Integer> listOrdinalCategory= new ArrayList<Integer>() ;
		ArrayList<Integer> listPriorityCategory= new ArrayList<Integer>() ;
		int menuAccessId = 2;
		CommonDataManager cdm = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			maxOrder=categoryMaintenanceManager.getMaxOrder();
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "categoryMaintenance";

			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_CATEGORY_MAINTENANCE+"", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request
					.getSession()));
			xmlWriter.startElement(PCMConstant.CATEGORY);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart();
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getCategoryMaintenanceGridColumns(width, columnOrder, hiddenColumns));


			responseConstructor.formRowsStart(categoryDetails.size());
			// Iterating category  details
			for (Iterator<Category> it = categoryDetails.iterator(); it.hasNext();) {
				// Obtain category tag from iterator
				Category category = (Category) it.next();

				ordre = (Integer)( category.getOrdinal() == null ? 0 : category.getOrdinal());
				if(!listOrdinalCategory.contains(ordre)  &&  ordre != 0) {
					listOrdinalCategory.add(ordre);
				}

				priority= (Integer)( category.getRuleAssignPriority() == null ? 0 : category.getRuleAssignPriority());
				if(priority != 0) {
					listPriorityCategory.add(priority);
				}


				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.CATEGORY_ID, category.getId().getCategoryId()+"");
				responseConstructor.createRowElement(PCMConstant.CATEGORY_NAME, category.getCategoryName());
				responseConstructor.createRowElement(PCMConstant.ORDINAL,  category.getOrdinal()== null ? "" :category.getOrdinal()+"");

				if(category.getAssignmentMethod()!= null) {
					if(category.getAssignmentMethod().equals("M")) {
						type="Manual";
					}else if(category.getAssignmentMethod().equals("S")) {
						type="System";
					}else {
						type="Both";
					}
				}
				responseConstructor.createRowElement(PCMConstant.ASSIGN_METHOD, type);
				if(category.getUrgentSpreadInd()== null) {
					category.setUrgentSpreadInd("U");
				}
				responseConstructor.createRowElement(PCMConstant.URGENT_SPREAD_IND,category.getUrgentSpreadInd().equals("U") ?"Urgent":"Spreadable");
				responseConstructor.createRowElement(PCMConstant.USE_LIQ_CHECK, category.getUseLiqCheck()== null? "N":category.getUseLiqCheck());
				responseConstructor.createRowElement(PCMConstant.INCL_TARGET_PAY_RELEASED, category.getInclTargetPayReleased()== null? "N":category.getInclTargetPayReleased());
				responseConstructor.createRowElement(PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC, category.getInclInAvailableLiqCalc()== null? "N": category.getInclInAvailableLiqCalc());
				responseConstructor.createRowElement(PCMConstant.IS_ACTIVE,category.getIsActive());
				responseConstructor.createRowElement(PCMConstant.RULE_ASSIGN_PRIORITY, category.getRuleAssignPriority()== null ? "" :category.getRuleAssignPriority()+"");

				responseConstructor.formRowEnd();
			}
			listOrdre = listOrdinalCategory.toString();
			listRuleAssignPriority = listPriorityCategory.toString();


			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			CacheManager cacheManagerInst = CacheManager.getInstance();

			//get the getMiscParamsLVL  and set it to the request
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"PCM_INPUT", entityId);
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			LabelValueBean priorityWarning = (LabelValueBean) targetSignList.get(1);
			if(priorityWarning!=null)
				responseConstructor.createElement(PCMConstant.PCM_INPUT, priorityWarning.getLabel());

			responseConstructor.createElement(PCMConstant.ORDINAL_LIST, listOrdre);
			responseConstructor.createElement(PCMConstant.PRIORITY_LIST, listRuleAssignPriority);
			responseConstructor.createElement(PCMConstant.MAX_ORDINAL,maxOrder== null? "" : maxOrder.toString());
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.CATEGORY);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName()
					+ " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			componentId = null;
			maxOrder = null;
			listOrdre = null;
			ordre = null;
			listOrdinalCategory = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ sendDisplayResponse ] - Exit");
		}
		return null;

	}



	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getCategoryMaintenanceGridColumns(String width, String columnOrder, String hiddenColumns) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  PCMConstant.CATEGORY_ID + "=180,"
						+ PCMConstant.CATEGORY_NAME + "=300,"
						+ PCMConstant.ORDINAL + "=80,"
						+ PCMConstant.URGENT_SPREAD_IND + "=85,"
						+ PCMConstant.ASSIGN_METHOD + "=119,"
						+ PCMConstant.IS_ACTIVE + "=90,"
						+ PCMConstant.USE_LIQ_CHECK + "=118,"
						+ PCMConstant.INCL_TARGET_PAY_RELEASED + "=135,"
						+ PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC + "=175,"
						+ PCMConstant.RULE_ASSIGN_PRIORITY + "=220,";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.CATEGORY_ID + ","
						+ PCMConstant.CATEGORY_NAME + ","
						+ PCMConstant.ORDINAL + ","
						+ PCMConstant.URGENT_SPREAD_IND + ","
						+ PCMConstant.ASSIGN_METHOD + ","
						+ PCMConstant.IS_ACTIVE + ","
						+ PCMConstant.USE_LIQ_CHECK + ","
						+ PCMConstant.INCL_TARGET_PAY_RELEASED + ","
						+ PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC+ ","
						+ PCMConstant.RULE_ASSIGN_PRIORITY;

			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Category ID type column
				if (order.equals(PCMConstant.CATEGORY_ID))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.CATEGORY_ID_HEADER,
									PCMConstant.CATEGORY_ID,
									PCMConstant.COLUMN_TYPE_STRING,
									0,
									Integer.parseInt(widths.get(PCMConstant.CATEGORY_ID)),
									false,
									true, hiddenColumnsMap.get(PCMConstant.CATEGORY_ID)));

				// CATEGORY_NAME
				if (order.equals(PCMConstant.CATEGORY_NAME))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.NAME_COLUMN_HEADER,
									PCMConstant.CATEGORY_NAME,
									PCMConstant.COLUMN_TYPE_STRING,
									1,
									Integer.parseInt(widths.get(PCMConstant.CATEGORY_NAME)),
									true,
									true, hiddenColumnsMap.get(PCMConstant.CATEGORY_NAME)));
				// RULE
				if (order.equals(PCMConstant.ORDINAL))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.ORDER_COLUMN_HEADER,
									PCMConstant.ORDINAL,
									PCMConstant.COLUMN_TYPE_NUMBER,
									2,
									Integer.parseInt(widths.get(PCMConstant.ORDINAL)),
									true,
									true, hiddenColumnsMap.get(PCMConstant.ORDINAL)));
				// Instant Release column
				if (order.equals(PCMConstant.URGENT_SPREAD_IND))
					lstColumns.add(new ColumnInfo(
							PCMConstant.TYPE_COLUMN_HEADER,
							PCMConstant.URGENT_SPREAD_IND,
							PCMConstant.COLUMN_TYPE_STRING,
							3,
							Integer.parseInt(widths.get(PCMConstant.URGENT_SPREAD_IND)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.URGENT_SPREAD_IND)));

				// ASSIGN_MANUALLY column
				if (order.equals(PCMConstant.ASSIGN_METHOD))
					lstColumns.add(new ColumnInfo(
							"Assignment",
							PCMConstant.ASSIGN_METHOD,
							PCMConstant.COLUMN_TYPE_STRING,
							5,
							Integer.parseInt(widths.get(PCMConstant.ASSIGN_METHOD)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.ASSIGN_METHOD)));

				// Include in Use Liq column
				if (order.equals(PCMConstant.USE_LIQ_CHECK))
					lstColumns.add(new ColumnInfo(
							"Liq. Check",
							PCMConstant.USE_LIQ_CHECK,
							PCMConstant.COLUMN_TYPE_CHECK,
							4,
							Integer.parseInt(widths.get(PCMConstant.USE_LIQ_CHECK)),
							false,
							true, hiddenColumnsMap.get(PCMConstant.USE_LIQ_CHECK)));

				// Include in Target column
				if (order.equals(PCMConstant.INCL_TARGET_PAY_RELEASED))
					lstColumns.add(new ColumnInfo(
							PCMConstant.INCL_TARGET_COLUMN_HEADER,
							PCMConstant.INCL_TARGET_PAY_RELEASED,
							PCMConstant.COLUMN_TYPE_CHECK,
							4,
							Integer.parseInt(widths.get(PCMConstant.INCL_TARGET_PAY_RELEASED)),
							false,
							true, hiddenColumnsMap.get(PCMConstant.INCL_TARGET_PAY_RELEASED)));



				// Include in Available Liquidity column
				if (order.equals(PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC))
					lstColumns.add(new ColumnInfo(
							PCMConstant.INCL_AVAILABLE_LIQ_COLUMN_HEADER,
							PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC,
							PCMConstant.COLUMN_TYPE_CHECK,
							6,
							Integer.parseInt(widths.get(PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC)),
							false,
							true, hiddenColumnsMap.get(PCMConstant.INCL_IN_AVAILABLE_LIQ_CALC)));

				// Active column
				if (order.equals(PCMConstant.IS_ACTIVE))
					lstColumns.add(new ColumnInfo(
							"Active",
							PCMConstant.IS_ACTIVE,
							PCMConstant.COLUMN_TYPE_CHECK,
							6,
							Integer.parseInt(widths.get(PCMConstant.IS_ACTIVE)),
							false,
							true, hiddenColumnsMap.get(PCMConstant.IS_ACTIVE)));


				// Rule Assignment Priority column
				if (order.equals(PCMConstant.RULE_ASSIGN_PRIORITY))
					lstColumns.add(new ColumnInfo(
							PCMConstant.RULE_ASSIGN_PRIORITY_HEADER,
							PCMConstant.RULE_ASSIGN_PRIORITY,
							PCMConstant.COLUMN_TYPE_NUMBER,
							7,
							Integer.parseInt(widths.get(PCMConstant.RULE_ASSIGN_PRIORITY)),
							false,
							true, hiddenColumnsMap.get(PCMConstant.RULE_ASSIGN_PRIORITY)));



			}

		} catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getCategoryMaintenanceGridColumns] method : - "
							+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryMaintenanceGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getSpreadGridColumns(String width, String columnOrder, String hiddenColumns) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  PCMConstant.SPREAD_ID + "=200,"
						+ PCMConstant.SPREAD_NAME + "=220,"
						+ PCMConstant.CURRENCY + "=220,";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.SPREAD_ID + ","
						+ PCMConstant.SPREAD_NAME + ","
						+ PCMConstant.CURRENCY;

			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// SPREAD ID type column
				if (order.equals(PCMConstant.SPREAD_ID))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.SP_ID_COLUMN_HEADER,
									PCMConstant.SPREAD_ID,
									PCMConstant.COLUMN_TYPE_LINK,
									0,
									Integer.parseInt(widths.get(PCMConstant.SPREAD_ID)),
									false,
									true, hiddenColumnsMap.get(PCMConstant.SPREAD_ID)));

				// SPREAD_NAME
				if (order.equals(PCMConstant.SPREAD_NAME))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.SP_NAME_COLUMN_HEADER,
									PCMConstant.SPREAD_NAME,
									PCMConstant.COLUMN_TYPE_STRING,
									1,
									Integer.parseInt(widths.get(PCMConstant.SPREAD_NAME)),
									false,
									true, hiddenColumnsMap.get(PCMConstant.SPREAD_NAME)));


				// CURRENCY
				if (order.equals(PCMConstant.CURRENCY))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.CURRENCY_COLUMN_HEADER,
									PCMConstant.CURRENCY,
									PCMConstant.COLUMN_TYPE_STRING,
									1,
									Integer.parseInt(widths.get(PCMConstant.CURRENCY)),
									false,
									true, hiddenColumnsMap.get(PCMConstant.CURRENCY)));
			}

		} catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getSpreadGridColumns] method : - "
							+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSpreadGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSpreadGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getCategoryRuleGridColumns(String width, String columnOrder, String hiddenColumns) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryRuleGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.RULE_ID + "=0,"
						+ PCMConstant.CATEGORY_RULE_ID + "=0,"
						+ PCMConstant.RULETEXT+ "=0,"
						+ PCMConstant.RULEQUERY + "=0,"
						+ PCMConstant.RULE_CONDITIONS + "=0,"
						+ PCMConstant.ORDINAL + "=60,"
						+ PCMConstant.CATEGORY_RULE_NAME + "=220,"
						+ PCMConstant.APPLY_ONLY_TO_SOURCE + "=150,"
						+ PCMConstant.APPLY_ONLY_TO_CCY + "=130,"
						+ PCMConstant.APPLY_ONLY_TO_ENTITY + "=130,";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.RULE_ID  + ","
						+ PCMConstant.CATEGORY_RULE_ID  + ","
						+ PCMConstant.RULETEXT + ","
						+ PCMConstant.RULEQUERY + ","
						+ PCMConstant.RULE_CONDITIONS + ","
						+ PCMConstant.ORDINAL + ","
						+ PCMConstant.CATEGORY_RULE_NAME + ","
						+ PCMConstant.APPLY_ONLY_TO_SOURCE+ ","
						+ PCMConstant.APPLY_ONLY_TO_CCY + ","
						+ PCMConstant.APPLY_ONLY_TO_ENTITY ;

			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();


				// RULE_ID
				if (order.equals(PCMConstant.RULE_ID))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.RULE,
									PCMConstant.RULE_ID,
									PCMConstant.COLUMN_TYPE_NUMBER,
									9,
									Integer.parseInt(widths.get(PCMConstant.RULE_ID)),
									false,
									false, false));
				// CATEGORY_RULE_ID
				if (order.equals(PCMConstant.CATEGORY_RULE_ID))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.ID_COLUMN_HEADER ,
									PCMConstant.CATEGORY_RULE_ID,
									PCMConstant.COLUMN_TYPE_NUMBER,
									8,
									Integer.parseInt(widths.get(PCMConstant.CATEGORY_RULE_ID)),
									false,
									false, false));
				// RULETEXT
				if (order.equals(PCMConstant.RULETEXT))
					lstColumns
							.add(new ColumnInfo(
									"RULETEXT",
									PCMConstant.RULETEXT,
									PCMConstant.COLUMN_TYPE_STRING,
									7,
									Integer.parseInt(widths.get(PCMConstant.RULETEXT)),
									false,
									false, false));

				// RULE_QUERY
				if (order.equals(PCMConstant.RULEQUERY))
					lstColumns
							.add(new ColumnInfo(
									"RULEQUERY",
									PCMConstant.RULEQUERY,
									PCMConstant.COLUMN_TYPE_STRING,
									6,
									Integer.parseInt(widths.get(PCMConstant.RULEQUERY)),
									false,
									false, false));



				// RULE_CONDITION
				if (order.equals(PCMConstant.RULE_CONDITIONS))
					lstColumns
							.add(new ColumnInfo(
									"ruleConditions",
									PCMConstant.RULE_CONDITIONS,
									PCMConstant.COLUMN_TYPE_STRING,
									10,
									Integer.parseInt(widths.get(PCMConstant.RULE_CONDITIONS)),
									false,
									false, false));

				// ordinal
				if (order.equals(PCMConstant.ORDINAL))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.ORDER_COLUMN_HEADER,
									PCMConstant.ORDINAL,
									PCMConstant.COLUMN_TYPE_NUMBER,
									1,
									Integer.parseInt(widths.get(PCMConstant.ORDINAL)),
									true,
									true, hiddenColumnsMap.get(PCMConstant.ORDINAL)));
				// CATEGORY_RULE_NAME
				if (order.equals(PCMConstant.CATEGORY_RULE_NAME))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.NAME_COLUMN_HEADER,
									PCMConstant.CATEGORY_RULE_NAME,
									PCMConstant.COLUMN_TYPE_STRING,
									2,
									Integer.parseInt(widths.get(PCMConstant.CATEGORY_RULE_NAME)),
									true,
									true, hiddenColumnsMap.get(PCMConstant.CATEGORY_RULE_NAME)));

				// APPLY_ONLY_TO_SOURCE column
				if (order.equals(PCMConstant.APPLY_ONLY_TO_SOURCE))
					lstColumns.add(new ColumnInfo(
							PCMConstant.SOURCE_COLUMN_HEADER,
							PCMConstant.APPLY_ONLY_TO_SOURCE,
							PCMConstant.COLUMN_TYPE_STRING,
							3,
							Integer.parseInt(widths.get(PCMConstant.APPLY_ONLY_TO_SOURCE)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.APPLY_ONLY_TO_SOURCE)));

				// APPLY_ONLY_TO_ENTITY
				if (order.equals(PCMConstant.APPLY_ONLY_TO_ENTITY))
					lstColumns.add(new ColumnInfo(
							PCMConstant.ENTITY_COLUMN_HEADER,
							PCMConstant.APPLY_ONLY_TO_ENTITY,
							PCMConstant.COLUMN_TYPE_STRING,
							4,
							Integer.parseInt(widths.get(PCMConstant.APPLY_ONLY_TO_ENTITY)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.APPLY_ONLY_TO_ENTITY)));

				// APPLY_ONLY_TO_CCY column
				if (order.equals(PCMConstant.APPLY_ONLY_TO_CCY))
					lstColumns.add(new ColumnInfo(
							PCMConstant.CURRENCY_COLUMN_HEADER,
							PCMConstant.APPLY_ONLY_TO_CCY,
							PCMConstant.COLUMN_TYPE_STRING,
							5,
							Integer.parseInt(widths.get(PCMConstant.APPLY_ONLY_TO_CCY)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.APPLY_ONLY_TO_CCY)));
			}
		} catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getCategoryRuleGridColumns] method : - "
							+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryRuleGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryRuleGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/**
	 * Method to generate Exception XML data
	 *
	 * @param expMesage
	 * @return ActionForward
	 */
	public String getExceptionXmlData(String expMesage) throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		// debug message
		log.debug(this.getClass().getName()
				+ " - [ getExceptionXmlData ] - Entry");


		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "categoryMaintenance";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
		xmlWriter.startElement(PCMConstant.EXCEPTION_TAG);
		xmlWriter.clearAttribute();
		if (expMesage != null)
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_FALSE), expMesage);
		else
			responseConstructor.formRequestReply(false);
		xmlWriter.endElement(PCMConstant.EXCEPTION_TAG);

		request.setAttribute("data", xmlWriter.getData());

		return getView("fail");
	}


	/**
	 * Method to generate Save XML data
	 *
	 * @return ActionForward
	 */

	public String sendSaveResponse() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		// debug message
		log.debug(this.getClass().getName()
				+ " - [ sendSaveResponse ] - Entry");


		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "categoryMaintenance";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
		xmlWriter.startElement(PCMConstant.CATEGORY);
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement(PCMConstant.CATEGORY);

		request.setAttribute("data", xmlWriter.getData());
		return getView("add");
	}


}