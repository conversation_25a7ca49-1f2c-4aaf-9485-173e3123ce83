/*
 * @(#)CurrencyMaintenanceAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;



import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.config.springMVC.BaseController;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;






/**
 * <AUTHOR>
 *
 */









import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/currencyPCM", "/currencyPCM.do"})
public class CurrencyMaintenanceAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/maintenance/currencymaintenance");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();





	@Autowired
	private CurrencyMaintenanceManager currencyMaintenanceManager = null;



	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(CurrencyMaintenanceAction.class);


	public void setCurrencyMaintenanceManager(CurrencyMaintenanceManager currencyMaintenanceManager) {
		this.currencyMaintenanceManager = currencyMaintenanceManager;
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {


		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "view":
				return view();
			case "add":
				return add();
			case "update":
				return update();
			case "save":
				return save();
			case "remove":
				return remove();
		}
		return unspecified();
	}
	protected String unspecified() throws Exception {
		// log debug message
		log.debug(this.getClass().getName()
				+ " - [unspecified] - Enter/Exit - Call to "
				+ this.getClass().getName()
				+ " - [unspecified]");

		return getView("success");
	}


	/**
	 * This method gets currency maintenance screen
	 * display it.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To build error message and log the same
		String errorMessage = null;
		PCMCurrencyDetailsVO currencyDetailsVO;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<PCMCurrency> currencyListDetails = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			currencyListDetails = new ArrayList<PCMCurrency>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
			currencyListDetails= (List<PCMCurrency>) currencyDetailsVO.getCurrencyListDetails();

			// build XML response
			return sendDisplayResponse(currencyListDetails, languageId, systemFormats);

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display] method : - "
					+ swtexp.getMessage());
		} catch (Exception ex) {
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "."
					+ ex.getStackTrace()[0].getMethodName() + ":"
					+ ex.getStackTrace()[0].getLineNumber() + " "
					+ ex.getMessage();
			// log error message
			log.error(this.getClass().getName()
					+ " - [display] - SwtException -"
					+ errorMessage);
		}finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}

	/**
	 * Method to display the view currency screen with its loaded values
	 * collected from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		Collection currencyList;
		Collection multiplierList;
		PCMCurrency pcmCurrency = null;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;
		String currencyCode = null;
		String currencyName = null;
		SystemFormats systemFormats = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ view ] - " + "Entry");
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "currencyMaintenance";
			currencyCode=(String) request.getParameter("ccy");
			currencyName=(String) request.getParameter("ccyName");

			if (!SwtUtil.isEmptyOrNull(currencyCode)) {
				pcmCurrency= currencyMaintenanceManager.getCurrencyDetailById(currencyCode);
			}

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.CURRENCY);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean
							.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.CURRENCYNAME,	currencyName);
			responseConstructor.createElement(PCMConstant.ORDINAL,	(pcmCurrency.getOrdinal() == null ? "" :pcmCurrency.getOrdinal()+""));
			responseConstructor.createElement(PCMConstant.MULTIPLIER,		pcmCurrency.getMultiplierDesc());
			responseConstructor.createElement(PCMConstant.CURRENCYCODE,		pcmCurrency.getId().getCurrencyCode());
			String strValue = pcmCurrency.getLargeAmountThreshold()== null ? "": SwtUtil.formatCurrency(pcmCurrency.getLargeAmountThreshold().doubleValue(),systemFormats.getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.LARGE_AMOUNT_THRESHOLD, strValue);
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());

			xmlWriter.endElement(SwtConstants.SINGLETONS);


			/*
			 * the currency Dropdown
			 */
			lstSelect = new ArrayList<SelectInfo>();
			lstOptions = new ArrayList<OptionInfo>();
			currencyList = getCurrencyList();
			Iterator j = currencyList.iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row =(LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));


			/*
			 * the multiplier Dropdown
			 */

			lstOptions = new ArrayList<OptionInfo>();
			multiplierList=getMultiplier();
			j = multiplierList.iterator();
			row = null;
			while (j.hasNext()) {
				row =(LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.MULTIPLIERLIST, lstOptions));


			// Add the selects node
			responseConstructor.formSelect(lstSelect);

			xmlWriter.endElement(PCMConstant.CURRENCY);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [view] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method : - "
					+ swtexp.getMessage());

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method : - "
					+ exp.getMessage());
			return getView("fail");
		}
	}

	/**
	 * Method to display the add currency screen with its loaded values
	 * collected from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		Collection currencyList;
		Collection multiplierList;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;
		try {

			log.debug(this.getClass().getName() + " - [add] - " + "Entry");


			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "currencyMaintenanceAdd";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.CURRENCY);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean
							.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.HELPURL,	"swf/system/help/Help.swf?message=");
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			currencyList = getCurrencyList();
			Iterator j = currencyList.iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row =(LabelValueBean) j.next();
				if(row.getValue().equals("") && row.getLabel().equals("")) {
					row =(LabelValueBean) j.next();
				}
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*
			 * the multiplier Dropdown
			 */

			lstOptions = new ArrayList<OptionInfo>();
			multiplierList=getMultiplier();
			j = multiplierList.iterator();
			row = null;
			while (j.hasNext()) {
				row =(LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.MULTIPLIERLIST, lstOptions));

			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.CURRENCY);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());

			return getView("fail");
			//return getExceptionXmlData(swtexp.getMessage(), request, mapping);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			return getView("fail");
		}
	}

	/**
	 * Method to display the change currency screen with its loaded values
	 * collected from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		PCMCurrency pcmCurrency;
		Integer ordinal= null;
		String multiplier = null;
		String currencyCode = null;
		SystemFormats systemFormats = null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [update] - Entry");
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Instantiate PCMCurrency
			pcmCurrency = new PCMCurrency();
			multiplier= (String) request.getParameter("multiplier");
			String order=request.getParameter("ordinal") == null ? null : (String) request.getParameter("ordinal") ;
			if(order != null && ! order.equals("")) {
				ordinal=Integer.parseInt(order) ;
				pcmCurrency.setOrdinal(ordinal);
			}

			currencyCode=(String) request.getParameter("ccy");
			String largeAmount= request.getParameter("largeAmountThr") == null ? null : (String) request.getParameter("largeAmountThr");
			if(largeAmount != null && ! largeAmount.equals("")) {
				pcmCurrency.setLargeAmountThreshold(SwtUtil.parseCurrency(largeAmount, systemFormats.getCurrencyFormat()).longValue());
			}

			pcmCurrency.setDisplayMultiplier(multiplier);

			pcmCurrency.getId().setCurrencyCode(currencyCode);

			//ReOrder ordinal of Currency
			String value_from =(String) request.getParameter("order");
			String reOrder =(String) request.getParameter("reOrder");
			if(reOrder.equals("true")) {
				currencyMaintenanceManager.reOrder(Integer.valueOf(value_from), ordinal);
			}



			// Save the added values in DB through currency manager
			currencyMaintenanceManager.updateCurrency(pcmCurrency);

			return sendSaveResponse();
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [update] - Exception -"
					+ ex.getMessage());

			return getExceptionXmlData(ex.getMessage());
		} finally {
			log.debug(this.getClass().getName()
					+ "- [update] - Exit");
		}
	}




	/**
	 * This method saves the currency details
	 * into the DB.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		PCMCurrency pcmCurrency = null;
		String currencyCode = null;
		Integer ordinal = null;
		String multiplier = null;
		Long largeAmountThreshold = null;
		SystemFormats systemFormats = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [save] - Entry");
			// Instantiate PCMCurrency
			pcmCurrency = new PCMCurrency();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			multiplier= (String) request.getParameter("multiplier");
			String orden=request.getParameter("ordinal")== null ? null : (String) request.getParameter("ordinal") ;
			if(orden != null && ! orden.equals("")) {
				ordinal=Integer.parseInt(orden) ;
				pcmCurrency.setOrdinal(ordinal);
			}
			currencyCode=(String) request.getParameter("ccy");
			String largeAmount=  request.getParameter("largeAmountThr") == null ? null: (String) request.getParameter("largeAmountThr");

			if(largeAmount != null && ! largeAmount.equals("")) {
				pcmCurrency.setLargeAmountThreshold(SwtUtil.parseCurrency(largeAmount, systemFormats.getCurrencyFormat()).longValue());
			}
			pcmCurrency.setDisplayMultiplier(multiplier);
			pcmCurrency.setOrdinal(ordinal);
			pcmCurrency.getId().setCurrencyCode(currencyCode);


			//ReOrder ordinal of Currency
			String value_from =(String) request.getParameter("order");
			String reOrder =(String) request.getParameter("reOrder");
			if(reOrder.equals("true")) {
				currencyMaintenanceManager.reOrder(Integer.valueOf(value_from), ordinal);
			}

			// Save the added values in DB through currency manager
			currencyMaintenanceManager.saveCurrency(pcmCurrency);

			return sendSaveResponse();

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [save] - Exception -"
					+ ex.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ ex.getMessage());
			request.setAttribute("reply_status_ok", "false");
			if (ex.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"RECORD_EXIST");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}

			//return getExceptionXmlData(ex.getMessage(), request, mapping);
		} finally {
			log.debug(this.getClass().getName()
					+ "- [save] - Exit");
			// nullify objects
			ordinal = null;
			multiplier = null;
			systemFormats = null;
			largeAmountThreshold = null;
			systemFormats = null;
			currencyCode = null;
			pcmCurrency = null;
		}
		return getView("statechange");

	}

	/**
	 * remove()
	 *
	 * Method to remove a group of rules
	 *
	 * @return ActionForward
	 */
	public String remove() throws Exception {
		String currencyCode= null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [remove] - " + "Entry");

			currencyCode=(String) request.getParameter("ccy");
			if(currencyCode!= null && !currencyCode.isEmpty()) {
				// remove the selected values from DB through manager
				currencyMaintenanceManager.deleteCurrency(currencyCode);
			}

			return display();
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [remove] - Exception -"
					+ ex.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					ex, "delete",
					CategoryMaintenanceAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			if (ex.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninDelete")) {
				request.setAttribute("reply_message","Record cannot be deleted as other transactions depend on it");
			}else {
				request.setAttribute("reply_message",
						"ERROR_DELETE");
			}

		}finally {
			log.debug(this.getClass().getName() + " - [remove] - Exit");
		}
		return getView("statechange");
	}



	/**
	 * This method forms the xml for displaying the currency list.
	 * @param languageId
	 *            - passing languageId
	 * @param systemFormats
	 *            - passing system formats date
	 * @return
	 */
	public String sendDisplayResponse(List<PCMCurrency> CurrencyDetails, String languageId,
									  SystemFormats systemFormats) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		Integer maxOrder;
		String listOrdre;
		Integer ordre;
		ArrayList<Integer> listOrdinalCurrency= new ArrayList<Integer>() ;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "currencyMaintenance";

			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PCM_CURRENCY_MAINTENANCE+"", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			maxOrder=currencyMaintenanceManager.getMaxOrder();

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request
					.getSession()));


			xmlWriter.startElement(PCMConstant.CURRENCY);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);


			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getCurrencyMaintenanceGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(CurrencyDetails.size());

			// Iterating currency  details
			for (Iterator<PCMCurrency> it = CurrencyDetails.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				PCMCurrency currency = (PCMCurrency) it.next();
				ordre = (Integer)( currency.getOrdinal() == null ? 0 : currency.getOrdinal());
				if(!listOrdinalCurrency.contains(ordre)  &&  ordre != 0) {
					listOrdinalCurrency.add(ordre);
				}

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.CURRENCYCODE, currency.getId().getCurrencyCode());
				responseConstructor.createRowElement(PCMConstant.CURRENCYNAME, currency.getCurrencyMaster().getCurrencyName());
				responseConstructor.createRowElement(PCMConstant.ORDINAL,(currency.getOrdinal() == null ? "" :currency.getOrdinal()+""));
				responseConstructor.createRowElement(PCMConstant.MULTIPLIER, currency.getMultiplierDesc());
				String strValue = currency.getLargeAmountThreshold()== null ? "": SwtUtil.formatCurrency(currency.getId().getCurrencyCode(), currency.getLargeAmountThreshold().doubleValue());
				responseConstructor.createRowElement(PCMConstant.LARGE_AMOUNT_THRESHOLD, strValue);
				responseConstructor.formRowEnd();
			}
			listOrdre = listOrdinalCurrency.toString();

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			responseConstructor.createElement(PCMConstant.ORDINAL_LIST, listOrdre);
			responseConstructor.createElement("maxOrdinal",maxOrder.toString());
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.CURRENCY);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log
					.error(this.getClass().getName()
							+ " - SwtException Catched in [sendDisplayResponse] method : - "
							+ exp.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName()
					+ " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			maxOrder = null;
			listOrdre = null;
			ordre = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ sendDisplayResponse ] - Exit");
		}
	}


	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getCurrencyMaintenanceGridColumns(String width, String columnOrder, String hiddenColumns) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =   PCMConstant.CURRENCYCODE + "=100,"
						+ PCMConstant.CURRENCYNAME + "=200,"
						+ PCMConstant.ORDINAL + "=100,"
						+ PCMConstant.MULTIPLIER + "=110,"
						+ PCMConstant.LARGE_AMOUNT_THRESHOLD + "=200,";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder =
						PCMConstant.CURRENCYCODE + ","
								+ PCMConstant.CURRENCYNAME + ","
								+ PCMConstant.ORDINAL + ","
								+ PCMConstant.MULTIPLIER + ","
								+ PCMConstant.LARGE_AMOUNT_THRESHOLD;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// CURRENCY CODE type column
				if (order.equals(PCMConstant.CURRENCYCODE))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.CURRENCY_COLUMN_HEADER,
									PCMConstant.CURRENCYCODE,
									PCMConstant.COLUMN_TYPE_STRING,
									0,
									Integer.parseInt(widths.get(PCMConstant.CURRENCYCODE)),
									false,
									true, hiddenColumnsMap.get(PCMConstant.CURRENCYCODE)));

				// CURRENCY NAME type column
				if (order.equals(PCMConstant.CURRENCYNAME))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.CURRENCY_NAME_COLUMN_HEADER,
									PCMConstant.CURRENCYNAME,
									PCMConstant.COLUMN_TYPE_STRING,
									1,
									Integer.parseInt(widths.get(PCMConstant.CURRENCYNAME)),
									true,
									true, hiddenColumnsMap.get(PCMConstant.CURRENCYNAME)));

				// Ordinal column
				if (order.equals(PCMConstant.ORDINAL))
					lstColumns.add(new ColumnInfo(
							PCMConstant.ORDINAL_COLUMN_HEADER,
							PCMConstant.ORDINAL,
							PCMConstant.COLUMN_TYPE_NUMBER,
							2,
							Integer.parseInt(widths.get(PCMConstant.ORDINAL)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.ORDINAL)));

				// MULTIPLIER column
				if (order.equals(PCMConstant.MULTIPLIER))
					lstColumns.add(new ColumnInfo(
							PCMConstant.MULTIPLIER_COLUMN_HEADER,
							PCMConstant.MULTIPLIER,
							PCMConstant.COLUMN_TYPE_STRING,
							3,
							Integer.parseInt(widths.get(PCMConstant.MULTIPLIER)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.MULTIPLIER)));

				// LARGE_AMOUNT_THRESHOLD column
				if (order.equals(PCMConstant.LARGE_AMOUNT_THRESHOLD))
					lstColumns.add(new ColumnInfo(
							PCMConstant.LARGE_AMOUNT_COLUMN_HEADER,
							PCMConstant.LARGE_AMOUNT_THRESHOLD,
							PCMConstant.COLUMN_TYPE_NUMBER,
							4,
							Integer.parseInt(widths.get(PCMConstant.LARGE_AMOUNT_THRESHOLD)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.LARGE_AMOUNT_THRESHOLD)));
			}

		} catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyMaintenanceGridColumns] method : - "
							+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyMaintenanceGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/**
	 * Get the multiplier from label value bean and put in the request attribute
	 * @return
	 * @throws SwtException
	 */
	private Collection getMultiplier() throws SwtException {

		log.debug(this.getClass().getName() + " - [putMultiplierInReq] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection multiplierColl;
		Iterator multiplierItr;
		LabelValueBean L1;
		LabelValueBean L2;
		LabelValueBean L3;
		LabelValueBean L4;
		MiscParams miscParams;
		/* Collect the multiplier list from the Cache manager */
		multiplierColl = (Collection) CacheManager.getInstance().getMiscParams("CURRENCYMULTIPLIER", null);

		/* Iterate the multiplierlist */
		multiplierItr = multiplierColl.iterator();
		multiplierColl = new ArrayList<LabelValueBean>();
		L1 = new LabelValueBean("", "");
		L2 = new LabelValueBean("", "");
		L3 = new LabelValueBean("", "");
		L4 = new LabelValueBean("", "");
		/* Loop to set the label vale bean of multiplier values */
		while (multiplierItr.hasNext()) {
			miscParams = (MiscParams) (multiplierItr.next());
			if (index == 0) { // Billion
				L4 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());
			} else if (index == 1) { // Million
				L3 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());
			} else if (index == 2) { // None
				L1 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());
			} else if (index == 3) { // Thousand
				L2 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());

			}
			index++;
		}

		multiplierColl.add(L1); // None
		multiplierColl.add(L2); // Thousand
		multiplierColl.add(L3); // Million
		multiplierColl.add(L4); // Billion

		log.debug(this.getClass().getName() + " - [getMultiplier] - "
				+ "Exit");
		return multiplierColl;

	}


	/**
	 * Method to collect the currency list from cache manager and set the
	 * currency list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCurrencyList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - "
				+ "Entry");
		/* Method's local variable declaration */
		Collection  coll;
		/* Collect the currencies from cache manager */
		coll = currencyMaintenanceManager.getCurrencyMaintenanceCombo();
		//coll =  CacheManager.getInstance().getCurrencies();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - "
				+ "Exit");
		return coll;
	}




	/**
	 * Method to generate Exception XML data
	 *
	 * @param expMesage
	 * @return ActionForward
	 */
	public String getExceptionXmlData(String expMesage) throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		// debug message
		log.debug(this.getClass().getName()
				+ " - [ getExceptionXmlData ] - Entry");


		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "currencyMaintenance";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
		xmlWriter.startElement(PCMConstant.EXCEPTION_TAG);
		xmlWriter.clearAttribute();
		if (expMesage != null)
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_FALSE), expMesage);
		else
			responseConstructor.formRequestReply(false);
		xmlWriter.endElement(PCMConstant.EXCEPTION_TAG);

		request.setAttribute("data", xmlWriter.getData());

		return getView("fail");
	}


	/**
	 * Method to generate Save XML data
	 *s
	 * @return ActionForward
	 */

	public String sendSaveResponse() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		// debug message
		log.debug(this.getClass().getName()
				+ " - [ sendSaveResponse ] - Entry");


		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "currencyMaintenance";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
		xmlWriter.startElement(PCMConstant.CURRENCY);
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement(PCMConstant.CURRENCY);

		request.setAttribute("data", xmlWriter.getData());
		return getView("data");
	}



}