/*
 * @(#)CommonAction.java 1.0 22/04/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.model.BaseObject;
import org.swallow.pcm.maintenance.model.core.kv.KVType;
import org.swallow.pcm.maintenance.model.core.kv.OperationsList;
import org.swallow.pcm.maintenance.model.core.kv.TabKVType;
import org.swallow.pcm.maintenance.service.CommonManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR>
 * 
 */
public class CommonAction extends CustomActionSupport {

	public CommonManager commonManager = null;
	
	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(CommonAction.class);

	public void setCommonManager(
			CommonManager commonManager) {
		this.commonManager = commonManager;
	}
	

	protected String unspecified() throws Exception {
		
		
		return "success";
	}
	/**
	 * Performs a crud operation
	 * @param userId
	 * @param operationListAsXML
	 * @param columsToReturnList
	 * @return
	 * @throws SwtException
	 */
	public static class ObjectFromKVType extends BaseObject {
		
		public String columnValue;
		public String columnName;
		public String operationType;
		public String table;
		
		public ObjectFromKVType( String columnValue, String columnName, String operationType,  String table ) {
			this.setColumnName(columnName);
			this.setColumnValue(columnValue);
			this.setTable(table); 
			this.setOperationType(operationType);
		}

		public ObjectFromKVType() {
		}

		public String getColumnValue() {
			return columnValue;
		}

		public void setColumnValue(String columnValue) {
			this.columnValue = columnValue;
		}

		public String getColumnName() {
			return columnName;
		}

		public void setColumnName(String columnName) {
			this.columnName = columnName;
		}

		public String getOperationType() {
			return operationType;
		}

		public void setOperationType(String operationType) {
			this.operationType = operationType;
		}

		public String getTable() {
			return table;
		}

		public void setTable(String table) {
			this.table = table;
		}
	}
	public ArrayList<TabKVType> doCrudOperation(String userId, String operationListAsXML,String activityId) throws SwtException { 
	    ArrayList<TabKVType> crudOperationsList =null;
	    TabKVType crudOperation =null;
	    String tableName= null;
	    String operationType = null;
	    String tableLevel = null;
	    String primaryKeyValuesFromDB = null;
	    ArrayList<HashMap<String, String>> rtnColValuesMapList = null; 
	    HashMap<String, String> rtnColValuesMap  = null;
	    HashMap<String, String> masterPrimaryKeyValuesMap = null;
	    OperationsList operationList = null;
	    try {
	    	
	    	// List of column values map that will be returned
	    	rtnColValuesMapList = new ArrayList<HashMap<String,String>>();
	    	
	    	// Primary keys map
			masterPrimaryKeyValuesMap = new HashMap<String, String>();
			
			//Unmarshal the coming XML to OperationsList object
			operationList = new OperationsList(operationListAsXML);
			
			//Get the list of operation
			crudOperationsList = operationList.getElementList();
			
			
			// Added by Saber Chebka on 17-03-2014: Provide a dummy index for all KVtype entries on doCrudOperation method
			int kvIdx = 1;
			for(TabKVType crudOper:crudOperationsList){
				kvIdx = 1;
				for(KVType columnValue:crudOper.getElementList()){
					columnValue.setIndex(kvIdx);
					kvIdx++;
				}
			}

			// Loop into operations list, level master is by default placed the first
			for (int i = 0; i < crudOperationsList.size(); i++) {
				// The CRUD operation object
				crudOperation = crudOperationsList.get(i);
				//get the table name
				tableName = crudOperation.getTableName();
				//get operation
				operationType = crudOperation.getOperation();
				// get table level if is primary key then "M", "S" in the other case
				tableLevel = crudOperation.getTableLevel();
				// Columns and their values
				ArrayList<KVType> columnValuesList = crudOperation.getElementList();
				
//				if(columsToReturnList.length>0){
//					for(int k =0; k<columnValuesList.size();k++){
//						KVType colmunValue = columnValuesList.get(k);
//						// If the column/value are to be returned
//						if(arrayContains(tableName+"."+colmunValue.getKey(), columsToReturnList)){
//							rtnColValuesMap.put(tableName+"|"+colmunValue.getKey(), colmunValue.getValue());
//					
//						}
//						
//						
//					}
//				}
				
				
			}
		}catch (Exception e) {
			//throw new Exception("Error occurred on CRUD operation: UserId="+userId+", CRUD Data:"+operationListAsXML, e);
		}
	    return crudOperationsList;		
	}
	/**
	 * Parses string with multiple separators
	 * Example: "USER_ID:Admin@S|COMPONENT_ID:G_REC_TYPE@S|PROGRAM_ID:1002@S", will be parsed into [USER_ID,Admin,S],[COMPONENT_ID,G_REC_TYPE,S],[PROGRAM_ID,1002,S] 
	 * @param input
	 * @param separators
	 * @return
	 * @throws SwtException
	 */
	public static ArrayList<String[]> parseMultiseparatedString (String input, String...separators) throws SwtException{
		ArrayList<String[]> result = new ArrayList<String[]>();
		ArrayList<String> tmpRow = new ArrayList<String>();
		if(separators.length>3)
			throw new SwtException("Programming error: Incorrectly calling parseMultiseparatedString() method with more than 3 separators maximum");
		if(separators.length==0){
			result.add(new String[]{input});
			return result;
		}else if(separators.length>=1){
			for(String part:input.split(SwtUtil.fixRegex(separators[0]))){
				tmpRow = new ArrayList<String>();
				if(separators.length>=2){
					String part1 = null;
					String part2 = null;
					String part3 = null;
					String[] split2 = part.split(separators[1]);
					part1 = split2[0];
					if(separators.length==3){
						String[] split3 = split2[1].split(separators[2]);
						part2 = split3[0];
						part3 = split3[1];
						result.add(new String[]{part1, part2, part3});
					}else{
						// NULL data as empty string
						if(split2.length>1){
							part2 = split2[1];
						}
						result.add(new String[]{part1,part2});
					}
				}
				else
					result.add(new String[]{part});				
			}
		}
		return result;
	}
	 //Find value in String array 
	 public static boolean arrayContains(String inputStr, String[] items) {
		
		 for(int i =0; i < items.length; i++)
		    {
		        if(inputStr.contains(items[i]))
		        {
		            return true;
		        }
		    }
		    return false;
		}

}
