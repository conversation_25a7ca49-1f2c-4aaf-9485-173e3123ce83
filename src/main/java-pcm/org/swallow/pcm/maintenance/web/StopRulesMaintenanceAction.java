/*
 * @(#)StopRulesMaintenanceAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.codehaus.jackson.JsonGenerationException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.map.ObjectMapper;
import org.swallow.config.springMVC.BaseController;
import org.swallow.control.model.FacilityAccess;
import org.swallow.control.service.RoleManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.authorization.MaintenanceAuthUtils;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.StopRule;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.pcm.maintenance.service.StopRulesMaintenanceManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import com.google.gson.Gson;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;






/**
 * <AUTHOR>
 *
 */

















import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/stopRulesPCM", "/stopRulesPCM.do"})
public class StopRulesMaintenanceAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/pc/maintenance/stoprulemaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/maintenance/stoprulemaintenance");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("expressionBuilder", "jsp/pc/maintenance/expressionbuilder");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");























	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "stopRuleAdd":
				return stopRuleAdd();
			case "stopRuleView":
				return stopRuleView();
			case "display":
				return display();
			case "add":
				return add();
			case "delete":
				return delete();
			case "activateDesactive":
				return activateDesactive();
			case "viewOrChange":
				return viewOrChange();
			case "checkIfRecordExist":
				return checkIfRecordExist();
			case "save":
				return save();
			case "update":
				return update();
		}


		return unspecified();
	}




	@Autowired
	private StopRulesMaintenanceManager stopRulesMaintenanceManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(StopRulesMaintenanceAction.class);

	public void setStopRulesMaintenanceManager(StopRulesMaintenanceManager stopRulesMaintenanceManager) {
		this.stopRulesMaintenanceManager = stopRulesMaintenanceManager;
	}

	protected String unspecified() throws Exception {

//		stopRulesMaintenanceManager.saveNewDetailInRemoteDB();

		return getView("success");
	}



	public String stopRuleAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "stopRuleAdd");
		return getView("add");
	}


	public String stopRuleView() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "stopRuleView");
		return getView("add");
	}

	/**
	 * This method gets Category maintenance screen display it.
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To build error message and log the same
		String errorMessage = null;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<StopRule> categoryListDetails = null;
		String selectedStatus = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			categoryListDetails = new ArrayList<StopRule>();
			selectedStatus = (String) request.getParameter("selectedStatus");
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			if(SwtUtil.isEmptyOrNull(selectedStatus)) {
				selectedStatus = "A";
			}

			categoryListDetails = (ArrayList<StopRule>) stopRulesMaintenanceManager.getStopRulesDetailList(selectedStatus);

			// build XML response
			return sendDisplayResponse(categoryListDetails, languageId, selectedStatus);

		} catch (SwtException swtexp) {
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
					+ ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
		}

		return null;
	}

	/**
	 * This method forms the xml for displaying the category list.
	 *
	 * @return
	 */
	public String sendDisplayResponse(List<StopRule> stopRules, String languageId, String selectedStatus) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		String ruleType = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String actionOnDeactivation = "";
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "categoryMaintenance";

			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_STOPRULE_MAINTENANCE+"", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);




			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsStopRuleWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID);

			xmlWriter.startElement(PCMConstant.CATEGORY);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.clearAttribute();
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			if(SwtUtil.isEmptyOrNull(selectedStatus)) {
				selectedStatus = "A";
			}

			responseConstructor.createElement(PCMConstant.STATUS, selectedStatus);


			xmlWriter.endElement(SwtConstants.SINGLETONS);



			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getCategoryMaintenanceGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(stopRules.size());
			// Iterating category details
			for (Iterator<StopRule> it = stopRules.iterator(); it.hasNext();) {
				// Obtain category tag from iterator
				StopRule stopRule = (StopRule) it.next();
				if(!SwtUtil.isEmptyOrNull(stopRule.getRuleType())) {
					if("A".equals(stopRule.getRuleType())) {
						ruleType = PCMConstant.STOPRULE_COMPLEX_LABEL;
					}else {
						ruleType = PCMConstant.STOPRULE_SIMPLE_LABEL;
					}

				}

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME,
						stopRule.getId().getStopRuleId() + "");

				if(!SwtUtil.isEmptyOrNull(stopRule.getActionOnDeactivation())) {
					if("W".equals(stopRule.getActionOnDeactivation())) {
						actionOnDeactivation = PCMConstant.STOPRULE_ACTION_WAITING_LABEL;
					}else {
						actionOnDeactivation = PCMConstant.STOPRULE_ACTION_LEAVE_LABEL;
					}
				}
				responseConstructor.createRowElement(PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME,actionOnDeactivation);

				responseConstructor.createRowElement(PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME,
						stopRule.getStopReasonText());
				responseConstructor.createRowElement(PCMConstant.STOPRULE_START_DATE_TAGNAME,
						"" + SwtUtil.formatDate(stopRule.getActivateOnDate(), SwtUtil.getCurrentDateFormat(request.getSession())));
				responseConstructor.createRowElement(PCMConstant.STOPRULE_END_DATE_TAGNAME,
						"" + SwtUtil.formatDate(stopRule.getDeactivateOnDate(), SwtUtil.getCurrentDateFormat(request.getSession())));
				responseConstructor.createRowElement(PCMConstant.STOPRULE_STATUS_TAGNAME,
						"" + (stopRule.getIsActive() != null && "Y".equals(stopRule.getIsActive())?PCMConstant.STOPRULE_ACTIVE_LABEL:PCMConstant.STOPRULE_INACTIVE_LABEL));

				responseConstructor.createRowElement(PCMConstant.STOPRULE_ACTIVE_STATUS_TAGNAME,
						"" + (stopRule.getIsActive() != null && "Y".equals(stopRule.getIsActive())));

				responseConstructor.createRowElement(PCMConstant.STOPRULE_ACTIVATED_ON_DATE_TAGNAME,stopRule.getActivatedOnDate()!=null ? SwtUtil.formatDate(stopRule.getActivatedOnDate(), SwtUtil.getCurrentDateFormat(request.getSession())): "");
				responseConstructor.createRowElement(PCMConstant.STOPRULE_NUMBER_OF_LINKED_PR_TAGNAME,stopRule.getNumberOfLinkedPR());
				responseConstructor.createRowElement(PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_STATUS_TAGNAME,stopRule.getActionOnDeactivation());


				if(stopRule.getDeactivateOnDate() != null) {
					if(PCMUtil.getIsDeactivatedRule(stopRule.getId().getStopRuleId())) {
						responseConstructor.createRowElement(PCMConstant.STOPRULE_IS_CHANGABLE_TAGNAME,SwtConstants.STR_FALSE);
					}else {
						responseConstructor.createRowElement(PCMConstant.STOPRULE_IS_CHANGABLE_TAGNAME,SwtConstants.STR_TRUE);
					}

				}else {
					responseConstructor.createRowElement(PCMConstant.STOPRULE_IS_CHANGABLE_TAGNAME,SwtConstants.STR_TRUE);
				}

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();


			xmlWriter.clearAttribute();
			// form drop down details
			xmlWriter.endElement(PCMConstant.CATEGORY);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			ex.printStackTrace();
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			ex.printStackTrace();
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
		return null;

	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getCategoryMaintenanceGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME + "=170," + PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME+ "=170"+","+
						PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME + "=230,"+ PCMConstant.STOPRULE_START_DATE_TAGNAME + "=110," +
						PCMConstant.STOPRULE_END_DATE_TAGNAME + "=110,"+ PCMConstant.STOPRULE_STATUS_TAGNAME+ "=100";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME + ","
						+ PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME + "," + PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME
						+ "," + PCMConstant.STOPRULE_START_DATE_TAGNAME + "," + PCMConstant.STOPRULE_END_DATE_TAGNAME + "," +
						PCMConstant.STOPRULE_STATUS_TAGNAME;

			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Category ID type column
				if (order.equals(PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.STOPRULE_STOP_RULE_ID_HEADING,
							PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.STOPRULE_STOP_RULE_ID_TAGNAME)));

				// RULE
				if (order.equals(PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.STOPRULE_STOP_RULE_NAME_HEADING,
							PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.STOPRULE_STOP_RULE_NAME_TAGNAME)));

				// RULE
				if (order.equals(PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_HEADING,
							PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME)));

				// Instant Release column
				if (order.equals(PCMConstant.STOPRULE_START_DATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.STOPRULE_START_DATE_HEADING,
							PCMConstant.STOPRULE_START_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 3,
							Integer.parseInt(widths.get(PCMConstant.STOPRULE_START_DATE_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.STOPRULE_START_DATE_TAGNAME)));

				// Include in Target column
				if (order.equals(PCMConstant.STOPRULE_END_DATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.STOPRULE_END_DATE_HEADING,
							PCMConstant.STOPRULE_END_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 4,
							Integer.parseInt(widths.get(PCMConstant.STOPRULE_END_DATE_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.STOPRULE_END_DATE_TAGNAME)));
				// CATEGORY_NAME
				if (order.equals(PCMConstant.STOPRULE_STATUS_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.STOPRULE_STATUS_HEADING,
							PCMConstant.STOPRULE_STATUS_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(PCMConstant.STOPRULE_STATUS_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.STOPRULE_STATUS_TAGNAME)));

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCategoryMaintenanceGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCategoryMaintenanceGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String add() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String languageId = null;
		PCMCurrencyDetailsVO currencyList;
		Collection countryList;
		Collection sourcesList;
		Collection messageTypesList;
		Collection accountGroupList;
		Collection operatorList;
		SystemFormats systemFormats = null;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;
		String roleId = null;
		ArrayList<FacilityAccess>  accessList = null;
		try {

			log.debug(this.getClass().getName() + " - [add] - " + "Entry");

			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "stopRuleAdd";


			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);

			boolean containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID);
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsStopRuleWithReqAuthY);

			xmlWriter.startElement(PCMConstant.STOPRULEADD_ROOT_TAG);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);


			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.HELPURL, "swf/system/help/Help.swf?message=");
			responseConstructor.createElement(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));


			Calendar calMinDate = Calendar.getInstance();
			calMinDate.setTime(SwtUtil.getSystemDateFromDB());
			calMinDate.add(Calendar.HOUR, -25);

			responseConstructor.createElement("minDate", SwtUtil.formatDate(calMinDate.getTime() , SwtUtil.getCurrentDateFormat(request.getSession())));


			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));

			CurrencyMaintenanceManager	currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*
			 * the country Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));

			countryList = getCountryList();
			j = countryList.iterator();
			j.next();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.COUNTRYLIST, lstOptions));
			/*
			 * the sources Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));

			sourcesList = getSourceList();
			j = sourcesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));
			/*
			 * the message types Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			messageTypesList = getMessageTypesList();
			j = messageTypesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.MESSAGE_TYPESLIST, lstOptions));
			/*
			 * the accountGroups types Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			accountGroupList = getAccountGroupsList();
			j = accountGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));


			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			operatorList = getOperatorList();
			j = operatorList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_OPERATORLIST, lstOptions));

			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.STOPRULEADD_ROOT_TAG);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "add", StopRulesMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String delete() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String stopRuleId = null;
		StopRule stopRule = null;
		Boolean requireAuthorisation = false;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		try {

			log.debug(this.getClass().getName() + " - [add] - " + "Entry");

			// Get component ID

			stopRuleId = (String) request.getParameter("stopRuleId");

			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			if(containsStopRuleWithReqAuthY)
				requireAuthorisation = true;


			if(!SwtUtil.isEmptyOrNull(stopRuleId)) {
				if(stopRulesMaintenanceManager.checkProcessStopRule(stopRuleId)) {
					request.setAttribute("reply_status_ok", "false");
					request.setAttribute("reply_message",
							"PROCESS_RUNNING");
					return getView("statechange");

				}else {

					Long idGenerated = null;
					if(requireAuthorisation) {
						idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("STOP_RULE", stopRuleId, SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, null, null, "D", "P");;
						stopRule = stopRulesMaintenanceManager.getStopRuleDetails(stopRuleId);
						stopRule.setMainEventId(""+idGenerated);
					}
					stopRulesMaintenanceManager.deleteStopRule(stopRuleId, idGenerated);
					if(requireAuthorisation) {
						ObjectMapper objectMapper = new ObjectMapper();
						String json = null;
						try {
							json = objectMapper.writeValueAsString(stopRule);
						} catch (JsonGenerationException e) {
							e.printStackTrace();
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (IOException e) {
							e.printStackTrace();
						}
						MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "STOP_RULE", stopRuleId, "D", null, json);
					}


				}
			}

			return display();

		} catch (SwtException swtexp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			request.setAttribute("reply_status_ok", "false");
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninDelete")) {
				request.setAttribute("reply_message",
						"OTHER_RECORD_DEPEND");
			}else {
				request.setAttribute("reply_message",
						"ERROR_DELETE");
			}


			return getView("statechange");
		} catch (Exception exp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "add", StopRulesMaintenanceAction.class), request,
					"");

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",
					"ERROR_SAVE");


			return getView("statechange");
		}
	}
	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String activateDesactive() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String stopRuleId = null;
		StopRule stopRule = null;
		String active = null;
		boolean requireAuthorisation = false;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		try {

			log.debug(this.getClass().getName() + " - [add] - " + "Entry");
			// Get component ID

			stopRuleId = (String) request.getParameter("stopRuleId");
			active = (String) request.getParameter("active");


			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			if(containsStopRuleWithReqAuthY)
				requireAuthorisation = true;


			if(!SwtUtil.isEmptyOrNull(stopRuleId) && !SwtUtil.isEmptyOrNull(active)) {

				if(stopRulesMaintenanceManager.checkProcessStopRule(stopRuleId)) {
					request.setAttribute("reply_status_ok", "false");
					request.setAttribute("reply_message",
							"PROCESS_RUNNING");
					return getView("statechange");

				}






				stopRule = stopRulesMaintenanceManager.getStopRuleDetails(stopRuleId);
				stopRule.setIsActive("true".equals(active)?"Y":"N");
				if("true".equals(active)) {
					stopRule.setActivatedOnDate(SwtUtil.getSystemDateFromDB());
					stopRule.setActivatedBy(SwtUtil.getCurrentUserId(request.getSession()));
				}else {
					stopRule.setDeactivatedOnDate(SwtUtil.getSystemDateFromDB());
					stopRule.setDeactivatedBy(SwtUtil.getCurrentUserId(request.getSession()));
				}
				Long idGenerated = null;
				if(requireAuthorisation) {
					idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("STOP_RULE", stopRuleId, SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, null, null, "U", "P");;
					stopRule.setMainEventId(""+idGenerated);
				}
				stopRulesMaintenanceManager.saveStopRule(stopRule, false);
				if(requireAuthorisation) {
					ObjectMapper objectMapper = new ObjectMapper();
					String json = null;
					try {
						json = objectMapper.writeValueAsString(stopRule);
					} catch (JsonGenerationException e) {
						e.printStackTrace();
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (IOException e) {
						e.printStackTrace();
					}
					MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "STOP_RULE", stopRuleId, "U", null, json);
				}

			}

			return display();

		} catch (SwtException swtexp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "add", StopRulesMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}
	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String viewOrChange() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String languageId = null;
		String screenName = null;
		PCMCurrencyDetailsVO currencyList;
		Collection countryList;
		Collection sourcesList;
		Collection messageTypesList;
		Collection accountGroupList;
		Collection operatorList;
		SystemFormats systemFormats = null;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;
		String stopRuleId = null;
		StopRule stopRule = null;
		String selectedCcy = "";
		String selectedCountry = "";
		String selectedCounterParty = "";
		String selectedMessageType = "";
		String selectedSource = "";
		String selectedAcctGrp = "";
		String selectedAmount = "";
		String selectedOperator = "";
		String queryText = "";
		int menuAccessId = 2;
		CommonDataManager cdm;
		boolean isFromMaintenanceEvent = false;
		String maintEventId = null;
		StopRule stopRuleOldObject = null;
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;

		try {
			log.debug(this.getClass().getName() + " - [add] - " + "Entry");

			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "stopRuleAdd";

			stopRuleId = (String) request.getParameter("stopRuleId");
			screenName = (String) request.getParameter("screenName");
			isFromMaintenanceEvent = Boolean.parseBoolean( request.getParameter("isFromMaintenanceEvent"));
			if(isFromMaintenanceEvent) {
				maintEventId =  (String) request.getParameter("maintEventId");
				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "STOP_RULE");

				String json = details.getNewState();

				ObjectMapper objectMapper = new ObjectMapper();
				stopRule = objectMapper.readValue(json, StopRule.class);
				stopRuleOldObject = stopRulesMaintenanceManager.getStopRuleDetails(stopRuleId);
			}else {

				stopRule = stopRulesMaintenanceManager.getStopRuleDetails(stopRuleId);
			}

			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));




			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsStopRuleWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID);
			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_STOPRULE_MAINTENANCE+"", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);


			xmlWriter.startElement(PCMConstant.STOPRULEADD_ROOT_TAG);

			xmlWriter.clearAttribute();
			// xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			responseConstructor.createElement(SwtConstants.HELPURL, "swf/system/help/Help.swf?message=");
			if (stopRule != null) {
				responseConstructor.createElement("predifinedRules", "" + stopRule.getRuleType());
				responseConstructor.createElement("stopAll", stopRule.getRule() != null && SwtUtil.isEmptyOrNull(stopRule.getRule().getRuleText())?"Y":"N");
				responseConstructor.createElement("actionOnDeactivation", "" + stopRule.getActionOnDeactivation());
				responseConstructor.createElement("selectedStopRuleName", stopRule.getStopReasonText());
				responseConstructor.createElement("selectedStopRuleId", stopRule.getId().getStopRuleId());
				responseConstructor.createElement("isActive", "" + stopRule.getIsActive());
				responseConstructor.createElement("activatedOn",
						stopRule.getActivatedOnDate() == null ? ""
								: SwtUtil.formatDate(stopRule.getActivatedOnDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) +" HH:mm:ss"));
				responseConstructor.createElement("activatedBy", stopRule.getActivatedBy());
				responseConstructor.createElement("deactivatedOn",
						stopRule.getDeactivatedOnDate() == null ? ""
								: SwtUtil.formatDate(stopRule.getDeactivatedOnDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) +" HH:mm:ss"));
				responseConstructor.createElement("deactivatedBy", stopRule.getDeactivatedBy());
				responseConstructor.createElement("selectedStartDate",
						stopRule.getActivateOnDate() == null ? ""
								: SwtUtil.formatDate(stopRule.getActivateOnDate(),
								SwtUtil.getCurrentDateFormat(request.getSession())));
				responseConstructor.createElement("selectedEndDate",
						stopRule.getDeactivateOnDate() == null ? ""
								: SwtUtil.formatDate(stopRule.getDeactivateOnDate(),
								SwtUtil.getCurrentDateFormat(request.getSession())));



				// if it's a maintenance Event and and it's not insert stop rule so change only
				if(isFromMaintenanceEvent && stopRuleOldObject != null) {
					responseConstructor.createElement("actionOnDeactivation"+SwtConstants.MAINT_EVENT_OLD_VALUE, "" + stopRuleOldObject.getActionOnDeactivation());
					responseConstructor.createElement("selectedStopRuleName"+SwtConstants.MAINT_EVENT_OLD_VALUE, stopRuleOldObject.getStopReasonText());
					responseConstructor.createElement("isActive"+SwtConstants.MAINT_EVENT_OLD_VALUE, "" + stopRuleOldObject.getIsActive());
					responseConstructor.createElement("activatedOn"+SwtConstants.MAINT_EVENT_OLD_VALUE,
							stopRuleOldObject.getActivatedOnDate() == null ? ""
									: SwtUtil.formatDate(stopRuleOldObject.getActivatedOnDate(),
									SwtUtil.getCurrentDateFormat(request.getSession()) +" HH:mm:ss"));
					responseConstructor.createElement("activatedBy"+SwtConstants.MAINT_EVENT_OLD_VALUE, stopRuleOldObject.getActivatedBy());
					responseConstructor.createElement("deactivatedOn"+SwtConstants.MAINT_EVENT_OLD_VALUE,
							stopRuleOldObject.getDeactivatedOnDate() == null ? ""
									: SwtUtil.formatDate(stopRuleOldObject.getDeactivatedOnDate(),
									SwtUtil.getCurrentDateFormat(request.getSession()) +" HH:mm:ss"));
					responseConstructor.createElement("deactivatedBy"+SwtConstants.MAINT_EVENT_OLD_VALUE, stopRuleOldObject.getDeactivatedBy());
					responseConstructor.createElement("selectedStartDate"+SwtConstants.MAINT_EVENT_OLD_VALUE,
							stopRuleOldObject.getActivateOnDate() == null ? ""
									: SwtUtil.formatDate(stopRuleOldObject.getActivateOnDate(),
									SwtUtil.getCurrentDateFormat(request.getSession())));
					responseConstructor.createElement("selectedEndDate"+SwtConstants.MAINT_EVENT_OLD_VALUE,
							stopRuleOldObject.getDeactivateOnDate() == null ? ""
									: SwtUtil.formatDate(stopRuleOldObject.getDeactivateOnDate(),
									SwtUtil.getCurrentDateFormat(request.getSession())));
				}

				if (stopRule.getRule() != null && "Q".equals(stopRule.getRuleType())) {
					HashMap<String, String> mappedQuery = null;
					mappedQuery = splitQueryIntoHashMap(stopRule.getRule().getRuleQuery());

					selectedCcy = mappedQuery.get("selectedCcy");
					selectedCountry = mappedQuery.get("selectedCountry");
					selectedCounterParty = mappedQuery.get("selectedCounterParty");
					selectedMessageType = mappedQuery.get("selectedMessageType");
					selectedSource = mappedQuery.get("selectedSource");
					selectedAcctGrp = mappedQuery.get("selectedAcctGrp");
					selectedAmount = mappedQuery.get("selectedAmount");
					selectedOperator = mappedQuery.get("selectedOperator");

					responseConstructor.createElement("selectedCcy", selectedCcy);
					responseConstructor.createElement("selectedCountry", selectedCountry);
					responseConstructor.createElement("selectedCounterParty", selectedCounterParty);
					responseConstructor.createElement("selectedMessageType", selectedMessageType);
					responseConstructor.createElement("selectedSource", selectedSource);
					responseConstructor.createElement("selectedAcctGrp", selectedAcctGrp);
					responseConstructor.createElement("selectedOperator", selectedOperator);
					responseConstructor.createElement("selectedAmount", !SwtUtil.isEmptyOrNull(selectedAmount)?SwtUtil.formatCurrency(selectedCcy, new BigDecimal(selectedAmount)):"");
				}else {
					responseConstructor.createElement("selectedCcy", "");
					responseConstructor.createElement("selectedCountry", "");
					responseConstructor.createElement("selectedCounterParty", "");
					responseConstructor.createElement("selectedMessageType", "");
					responseConstructor.createElement("selectedSource", "");
					responseConstructor.createElement("selectedAcctGrp", "");
					responseConstructor.createElement("selectedOperator", "");
					responseConstructor.createElement("selectedAmount", "");

				}

				queryText = stopRule.getRule().getRuleText();
				responseConstructor.createElement("queryText", queryText);
				responseConstructor.createElement("numberOfLinkedPR", stopRule.getNumberOfLinkedPR());

						/*Calendar cal = Calendar.getInstance();
						// remove next line if you're always using the current time.
						cal.setTime(SwtUtil.getSysParamDate());
						cal.add(Calendar.HOUR, -12);
						responseConstructor.createElement("minDate", SwtUtil.formatDate(cal.getTime() , SwtUtil.getCurrentDateFormat(request.getSession())));
						*/

				Calendar calMinDate = Calendar.getInstance();
				calMinDate.setTime(SwtUtil.getSystemDateFromDB());
				calMinDate.add(Calendar.HOUR, -25);

				responseConstructor.createElement("minDate", SwtUtil.formatDate(calMinDate.getTime() , SwtUtil.getCurrentDateFormat(request.getSession())));
				if(stopRule.getDeactivateOnDate() != null) {
					if(PCMUtil.getIsDeactivatedRule(stopRule.getId().getStopRuleId())) {
						responseConstructor.createElement("isChangable", "false");
					}else {
						responseConstructor.createElement("isChangable", "true");
					}

				}else {
					responseConstructor.createElement("isChangable", "true");
				}

			}
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));

			CurrencyMaintenanceManager	currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*
			 * the country Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));

			countryList = getCountryList();
			j = countryList.iterator();
			j.next();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.COUNTRYLIST, lstOptions));
			/*
			 * the sources Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			sourcesList = getSourceList();
			j = sourcesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));
			/*
			 * the message types Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			messageTypesList = getMessageTypesList();
			j = messageTypesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.MESSAGE_TYPESLIST, lstOptions));
			/*
			 * the accountGroups types Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			accountGroupList = getAccountGroupsList();
			j = accountGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			operatorList = getOperatorList();
			j = operatorList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_OPERATORLIST, lstOptions));


			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.STOPRULEADD_ROOT_TAG);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "add", StopRulesMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}





	/**
	 * This method used to check if record already exist in database before saving
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkIfRecordExist() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		StopRule stopRule;

		String stopRuleId = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [checkIfRecordExist] - Entry");


			stopRuleId = (String) request.getParameter("stopRuleId");
			stopRule = stopRulesMaintenanceManager.getStopRuleDetails(stopRuleId);
			if(stopRule != null) {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message","RECORD_EXIST");
			}
			else {
				request.setAttribute("reply_status_ok", "true");
				request.setAttribute("reply_message", "SUCCESS");
			}

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [checkIfRecordExist] - Exception -" + ex.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(ex, "saveCurrency", StopRulesMaintenanceAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",
					"ERROR_SAVE");


		} finally {
			log.debug(this.getClass().getName() + "- [checkIfRecordExist] - Exit");
		}
		return getView("statechange");
	}
	/**
	 * This method saves the currency details into the DB.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		StopRule stopRule;

		String stopRuleId = null;
		String stopRuleName = null;
		String isActive = null;
		String activeFrom = null;
		String activeTo = null;
		String stopRuleType = null;
		String actionOnDeactivation = null;
		String stopAll = null;
		String stopCcy = null;
		String amount = null;
		String stopCountries = null;
		String stopCounterParties = null;
		String stopSources = null;
		String stopMessageTypes = null;
		String stopAccountGroups = null;
		String queryText = null;
		String querySQL = null;
		String subQuery = null;
		RuleConditions[] ruleConditions = null;
		ArrayList<RuleConditions> ruleConditionsList = null;
		String tableToJoinQuery = "";
		String jsonResult = null;
		String maintEventId = null;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		boolean requireAuthorisation = false;
		boolean containsStopRuleWithReqAuthY = false;
		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [save] - Entry");
			// Instantiate PCMCurrency
			stopRule = new StopRule();
			stopRuleId = (String) request.getParameter("stopRuleId");
			stopRuleName = (String) request.getParameter("stopRuleName");
			isActive = (String) request.getParameter("isActive");
			activeFrom = (String) request.getParameter("activeFrom");
			activeTo = (String) request.getParameter("activeTo");
			stopRuleType = (String) request.getParameter("stopRuleType");
			actionOnDeactivation = (String) request.getParameter("actionOnDeactivation");
			stopAll = (String) request.getParameter("stopAll");

			stopCcy = (String) request.getParameter("stopCcy");
			stopCountries = (String) request.getParameter("stopCountries");
			stopCounterParties = (String) request.getParameter("stopCounterParties");
			stopSources = (String) request.getParameter("stopSources");
			amount = (String) request.getParameter("amount");
			stopMessageTypes = (String) request.getParameter("stopMessageTypes");
			stopAccountGroups = (String) request.getParameter("stopAccountGroups");

			Long idGenerated = null;

			maintEventId =  (String) request.getParameter("maintEventId");
			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			if(containsStopRuleWithReqAuthY || !SwtUtil.isEmptyOrNull(maintEventId))
				requireAuthorisation = true;

			jsonResult = (String) request.getParameter("jsonResult");
			Gson gson = new Gson();
			ruleConditions = gson.fromJson(jsonResult, RuleConditions[].class);
			if(ruleConditions.length>0) {
				ruleConditionsList = new ArrayList<RuleConditions>(Arrays.asList(ruleConditions));
				for (int i = 0; i < ruleConditionsList.size(); i++) {
					if(ruleConditionsList.get(i).getTableName().equals("PC_PAYMENT_REQUEST_INFO")) {
						tableToJoinQuery+="select count(*) from PC_PAYMENT_REQUEST P INNER JOIN pc_payment_request_info f ON p.payreq_id = f.payreq_id ";
						break;
					}
				}
			}


			queryText = (String) request.getParameter("queryText");
			querySQL = (String) request.getParameter("querySQL");




			if(requireAuthorisation) {
				idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("STOP_RULE", stopRuleId, SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, SwtUtil.isEmptyOrNull(maintEventId)?null:Long.parseLong(maintEventId), null, "I", "P");;
				stopRule.setMainEventId(""+idGenerated);
			}

			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceAuthUtils.updateMaintenanceEventStatus(Long.parseLong(maintEventId), "R", SwtUtil.getCurrentUserId(request.getSession()), null, idGenerated);
			}




			stopRule.getId().setStopRuleId(stopRuleId);
			stopRule.setStopReasonText(stopRuleName);
			stopRule.setActionOnDeactivation(actionOnDeactivation);
			stopRule.setIsActive(isActive != null && isActive.equals("true")?"Y":"N");
			if (!SwtUtil.isEmptyOrNull(activeFrom)) {
				stopRule.setActivateOnDate(
						SwtUtil.parseDate(activeFrom, SwtUtil.getCurrentDateFormat(request.getSession())));
			}else {
				stopRule.setActivateOnDate(SwtUtil.getSystemDatewithoutTime());
			}
			if (!SwtUtil.isEmptyOrNull(activeTo)) {
				stopRule.setDeactivateOnDate(
						SwtUtil.parseDate(activeTo, SwtUtil.getCurrentDateFormat(request.getSession())));
			}
			if(isActive.equals("true")) {
				stopRule.setActivatedOnDate(SwtUtil.getSystemDateFromDB());
				stopRule.setActivatedBy(SwtUtil.getCurrentUserId(request.getSession()));
			}

			RulesDefinition rule = new RulesDefinition();

			if (!SwtUtil.isEmptyOrNull(stopRuleType)) {
				if ("ALL".equals(stopRuleType)) {
					stopRule.setRuleType("Q");
					rule.setRuleQuery("select count(*) from PC_PAYMENT_REQUEST P where (1=1)");
					rule.setRuleText("");
					rule.setRuleType("S");
					stopRule.setRule(rule);
				} else {
					stopRule.setRuleType(stopRuleType);

					if (!stopRuleType.equals("A")) {
						querySQL = createSubQueryFromFields(stopCcy, amount, stopCountries, stopCounterParties, stopSources,
								stopMessageTypes, stopAccountGroups);
						if (querySQL.length() > 0) {
							querySQL = "select count(*) from PC_PAYMENT_REQUEST P where (" + querySQL + ")";
						}
					}else {
						if(ruleConditionsList.size()>0) {
							rule.setRuleConditions(ruleConditionsList);
						}
					}
					if(tableToJoinQuery.length()>0) {
						int indexOfWhere = querySQL.indexOf("where");
						querySQL = tableToJoinQuery+querySQL.substring(indexOfWhere,querySQL.length());
					}
					rule.setRuleQuery(querySQL);
					rule.setRuleText(queryText);
					rule.setRuleType("S");
					stopRule.setRule(rule);

				}
			}


			// Save the added values in DB through currency manager
			stopRulesMaintenanceManager.saveStopRule(stopRule, true);
			if(requireAuthorisation) {
				ObjectMapper objectMapper = new ObjectMapper();
				String json = null;
				try {
					json = objectMapper.writeValueAsString(stopRule);
				} catch (JsonGenerationException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "STOP_RULE", stopRuleId, "I", null, json);

				if(!containsStopRuleWithReqAuthY) {
					MaintenanceAuthUtils.updateMaintenanceEventStatus(idGenerated, "A", SwtUtil.getCurrentUserId(request.getSession()),null,null );
					MaintenanceAuthUtils.acceptMaintenanceEvent(""+idGenerated);
				}
			}


			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "SUCCESS");

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [save] - Exception -" + ex.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(ex, "saveCurrency", StopRulesMaintenanceAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			if (ex.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"RECORD_EXIST");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}


		} finally {
			log.debug(this.getClass().getName() + "- [save] - Exit");
		}
		return getView("statechange");
	}


	/**
	 * This method update the currency details into the DB.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		StopRule stopRule;

		String stopRuleId = null;
		String stopRuleName = null;
		String activeFrom = null;
		String activeTo = null;
		String actionOnDeactivation = null;
		String isActive = null;
		boolean requireAuthorisation = false;
		String maintEventId = null;
		MaintenanceEvent  event = null;
		boolean isInsertEvent = false;
		ArrayList<FacilityAccess> accessList = null;
		boolean containsStopRuleWithReqAuthY = false;
		String roleId = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [save] - Entry");
			// Instantiate PCMCurrency
			stopRule = new StopRule();
			stopRuleId = (String) request.getParameter("stopRuleId");
			stopRuleName = (String) request.getParameter("stopRuleName");
			activeFrom = (String) request.getParameter("activeFrom");
			activeTo = (String) request.getParameter("activeTo");
			isActive = (String) request.getParameter("isActive");
			actionOnDeactivation = (String) request.getParameter("actionOnDeactivation");

			maintEventId =  (String) request.getParameter("maintEventId");

			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			containsStopRuleWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			if(containsStopRuleWithReqAuthY || !SwtUtil.isEmptyOrNull(maintEventId))
				requireAuthorisation = true;

			if(stopRulesMaintenanceManager.checkProcessStopRule(stopRuleId)) {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message",
						"PROCESS_RUNNING");
				return getView("statechange");
			}

			stopRule = new StopRule();
			if(!SwtUtil.isEmptyOrNull(maintEventId)) {

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				event = maintenanceEventMaintenanceManager.getMaintenanceEvent(""+maintEventId);

				if(event != null) {
					if("I".equalsIgnoreCase(event.getAction())){
						isInsertEvent = true;
					}
				}
				MaintenanceEventDetails details = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "STOP_RULE");

				String json = details.getNewState();

				ObjectMapper objectMapper = new ObjectMapper();
				try {
					stopRule = objectMapper.readValue(json, StopRule.class);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}

			}else {

				stopRule = stopRulesMaintenanceManager.getStopRuleDetails(stopRuleId);
			}

			stopRule.getId().setStopRuleId(stopRuleId);
			stopRule.setStopReasonText(stopRuleName);
			if (!SwtUtil.isEmptyOrNull(activeFrom)) {
				stopRule.setActivateOnDate(
						SwtUtil.parseDate(activeFrom, SwtUtil.getCurrentDateFormat(request.getSession())));
			}else {
				stopRule.setActivateOnDate(SwtUtil.getSystemDatewithoutTime());
			}
			if (!SwtUtil.isEmptyOrNull(activeTo)) {
				stopRule.setDeactivateOnDate(
						SwtUtil.parseDate(activeTo, SwtUtil.getCurrentDateFormat(request.getSession())));
			}else {
				stopRule.setDeactivateOnDate(null);
			}

			stopRule.setActionOnDeactivation(actionOnDeactivation);
			if(!SwtUtil.isEmptyOrNull(stopRule.getIsActive()) && !stopRule.getIsActive().equals((isActive.equals("true")?"Y":"N")) ) {
				if("true".equals(isActive)) {
					stopRule.setActivatedOnDate(SwtUtil.getSystemDateFromDB());
					stopRule.setActivatedBy(SwtUtil.getCurrentUserId(request.getSession()));
				}else {
					stopRule.setDeactivatedOnDate(SwtUtil.getSystemDateFromDB());
					stopRule.setDeactivatedBy(SwtUtil.getCurrentUserId(request.getSession()));
				}
			}
			stopRule.setIsActive(isActive != null && isActive.equals("true")?"Y":"N");


			Long idGenerated = null;
			if(requireAuthorisation) {
				idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("STOP_RULE", stopRuleId, SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, SwtUtil.isEmptyOrNull(maintEventId)?null:Long.parseLong(maintEventId), null, isInsertEvent?"I":"U", "P");;
				stopRule.setMainEventId(""+idGenerated);
			}

			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceAuthUtils.updateMaintenanceEventStatus(Long.parseLong(maintEventId), "R", SwtUtil.getCurrentUserId(request.getSession()), null , idGenerated);
			}

			// Save the added values in DB through currency manager
			stopRulesMaintenanceManager.saveStopRule(stopRule, isInsertEvent);
			if(requireAuthorisation) {
				ObjectMapper objectMapper = new ObjectMapper();
				String json = null;
				try {
					json = objectMapper.writeValueAsString(stopRule);
				} catch (JsonGenerationException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "STOP_RULE", stopRuleId, isInsertEvent?"I":"U", null, json);

				if(!containsStopRuleWithReqAuthY) {
					MaintenanceAuthUtils.updateMaintenanceEventStatus(idGenerated, "A", SwtUtil.getCurrentUserId(request.getSession()),null,null );
					MaintenanceAuthUtils.acceptMaintenanceEvent(""+idGenerated);
				}
			}

			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "SUCCESS");
			return getView("statechange");

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [save] - Exception -" + ex.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(ex, "saveCurrency", StopRulesMaintenanceAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			if (ex.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"RECORD_EXIST");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}

			return getView("statechange");

		} finally {
			log.debug(this.getClass().getName() + "- [save] - Exit");
		}
	}


	private String createSubQueryFromFields(String stopCcy, String amount, String stopCountries, String stopCounterParties,
											String stopSources, String stopMessageTypes, String stopAccountGroups) throws SwtException {
		String subQuery = "";
		if (!SwtUtil.isEmptyOrNull(stopCcy)) {
			subQuery += "P.CURRENCY_CODE IN(" + convertListToInParameterForQuery(stopCcy) + ")";
		}

		if (!SwtUtil.isEmptyOrNull(amount)) {
			subQuery += (subQuery.length() > 0 ? " AND " : "") + "P.AMOUNT " + amount + "";
		}

		if (!SwtUtil.isEmptyOrNull(stopCountries)) {
			subQuery += (subQuery.length() > 0 ? " AND " : "") + PCMUtil.getCounterPartySubQuery(convertListToInParameterForQuery(stopCountries),"COUNTRY");
		}

		if (!SwtUtil.isEmptyOrNull(stopCounterParties)) {
			subQuery += (subQuery.length() > 0 ? " AND " : "") +PCMUtil.getCounterPartySubQuery(stopCounterParties,"BIC");
		}

		if (!SwtUtil.isEmptyOrNull(stopSources)) {
			subQuery += (subQuery.length() > 0 ? " AND " : "") + "P.SOURCE_ID IN("
					+ convertListToInParameterForQuery(stopSources) + ")";
		}

		if (!SwtUtil.isEmptyOrNull(stopMessageTypes)) {
			subQuery += (subQuery.length() > 0 ? " AND " : "") + "P.MESSAGE_TYPE IN("
					+ convertListToInParameterForQuery(stopMessageTypes) + ")";
		}

		if (!SwtUtil.isEmptyOrNull(stopAccountGroups)) {
			subQuery += (subQuery.length() > 0 ? " AND " : "")
					+ "P.ACCOUNT_ID IN (SELECT ACCOUNT_ID FROM PC_ACCOUNT_IN_GROUP WHERE ACC_GRP_ID IN("
					+ convertListToInParameterForQuery(stopAccountGroups) + "))";
		}

		return subQuery;
	}

	private HashMap<String, String> splitQueryIntoHashMap(String query) {
		HashMap<String, String> result = new HashMap<String, String>();
		String[] subQueries = null;
		String selectedItems = null;


		if (query.indexOf("where (") > -1) {
			query = query.substring(query.indexOf("where (") + 7, query.length() - 1);
			if (!SwtUtil.isEmptyOrNull(query)) {
				subQueries = query.split("AND(\\s)*");
				for (int i = 0; i < subQueries.length; i++) {
					if (subQueries[i].length() > 0 && subQueries[i].indexOf("IN(") > -1) {

						selectedItems = findSelectedItemsFromSubQuery(subQueries[i]);
						if (subQueries[i].indexOf("P.CURRENCY_CODE") > -1) {
							result.put("selectedCcy", selectedItems);
						}
//						if (subQueries[i].indexOf("SUBSTR(P.RECEIVER_BIC, 5, 2)") > -1) {
//							result.put("selectedCountry", selectedItems);
//						}
//						if (subQueries[i].indexOf("SUBSTR(P.RECEIVER_BIC, 1, 4)") > -1
//								|| subQueries[i].indexOf("SUBSTR(P.RECEIVER_BIC, 1, 6)") > -1
//								|| subQueries[i].indexOf("SUBSTR(P.RECEIVER_BIC, 1, 8)") > -1
//								|| subQueries[i].indexOf("SUBSTR(P.RECEIVER_BIC, 1, 11)") > -1) {
//							result.put("selectedCounterParty", selectedItems);
//						}
						if (subQueries[i].indexOf("P.MESSAGE_TYPE") > -1) {
							result.put("selectedMessageType", selectedItems);
						}
						if (subQueries[i].indexOf("P.SOURCE_ID") > -1) {
							result.put("selectedSource", selectedItems);
						}
						if (subQueries[i].indexOf("P.ACCOUNT_ID") > -1) {
							result.put("selectedAcctGrp", selectedItems);
						}

					}else if (subQueries[i].length() > 0 && subQueries[i].indexOf("LIKE '") > -1){

						Pattern p = Pattern.compile("'([^' ]*)'");
						Matcher m = p.matcher(subQueries[i]);
						String text = null;
						if (m.find()) {
							text = m.group(1);
							if(!SwtUtil.isEmptyOrNull(text) && text.indexOf("%")>-1) {
								text = text.substring(0, text.length()-1);
							}
						}
						result.put("selectedCounterParty", text);

					}else if (subQueries[i].length() > 0 && subQueries[i].indexOf("RECEIVER_BIC") > -1) {
						selectedItems = findSelectedItemsFromSubQuery(subQueries[i]);
						result.put("selectedCountry", selectedItems);

					}else if(subQueries[i].length() > 0 && subQueries[i].indexOf("P.AMOUNT") > -1) {
//						selectedItems = subQueries[i].substring(subQueries[i].indexOf(" "), subQueries[i].length());

						String[] amountSplited = subQueries[i].split(" ");
						result.put("selectedOperator", amountSplited[1]);
						result.put("selectedAmount", amountSplited[2]);
					}
				}

			}

		}

		return result;
	}

	private String findSelectedItemsFromSubQuery(String subQuery) {
		String result = "";
		Matcher m = Pattern.compile("IN\\s?\\(([^)(]+)\\)").matcher(subQuery);
		while (m.find()) {
			result = m.group(1);
		}

		result = result.replaceAll("'", "");

		return result;
	}

	private static String convertListToInParameterForQuery(String listAsString) {
		String result = "";
		if (!SwtUtil.isEmptyOrNull(listAsString)) {
			if (listAsString.substring(listAsString.length() - 1).equals(",")) {
				listAsString = listAsString.substring(0, (listAsString.length() - 1));
			}
			String[] listArray = listAsString.split(",(\\s)?");
			result = StringUtils.join(listArray, "','");
			result = "'" + result + "'";

		}

		return result;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCurrencyList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = CacheManager.getInstance().getCurrencies();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCountryList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = CacheManager.getInstance().getCountries();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCounterPartyList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = new ArrayList<>();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getSourceList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = PCMUtil.getSourceList();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getMessageTypesList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = PCMUtil.getMessageTypeList();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getAccountGroupsList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		List<AccountGroup> accountGroupsDetails = new ArrayList<AccountGroup>();
		ArrayList<LabelValueBean> listAccountsGroup = null;
		try {
			log.debug(this.getClass().getName() + " - [displayAccountGroupsGrid] - " + "Entry");
			LabelValueBean bean = null;
			listAccountsGroup = new ArrayList<LabelValueBean>();
			// Retrieves current system formats
			AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
					.getBean("accountGroupsMaintenanceManager");
			accountGroupsDetails = accountGroupsMaintenanceManager.getAccountGroupDetailList(null);
			for (int i = 0; i < accountGroupsDetails.size(); i++) {
				bean = new LabelValueBean(accountGroupsDetails.get(i).getDescription() != null?accountGroupsDetails.get(i).getDescription():"",
						""+accountGroupsDetails.get(i).getId().getAccGrpId());
				listAccountsGroup.add(bean);
			}

		} catch (SwtException e) {

		}

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return listAccountsGroup;
	}
	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getOperatorList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getOperatorList] - " + "Entry");
		ArrayList<LabelValueBean> listOperators = null;
		log.debug(this.getClass().getName() + " - [getOperatorList] - " + "Entry");
		LabelValueBean bean = null;
		listOperators = new ArrayList<LabelValueBean>();
		// Retrieves current system formats
		listOperators.add(new LabelValueBean(">=", ">="));
		listOperators.add(new LabelValueBean(">", ">"));
		listOperators.add(new LabelValueBean("=", "="));
		listOperators.add(new LabelValueBean("<", "<"));
		listOperators.add(new LabelValueBean("<=", "<="));

		log.debug(this.getClass().getName() + " - [getOperatorList] - " + "Exit");
		return listOperators;
	}

}