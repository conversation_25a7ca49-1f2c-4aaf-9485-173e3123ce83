/**
 * @(#)ResponseHandler.java / 1.0 / 15 Aug 2009 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;

/**
 * ResponseHandler.java
 * 
 * This is a response dispatcher, used to send response to the client
 * 
 * <AUTHOR> R
 * @version New SMART-1.0
 * @date 15 Aug 2009
 */
public class ResponseHandler {

	/* Instance of of Logger Object */
	private final Log log = LogFactory.getLog(this.getClass());

	/**
	 * This method sends response to client in xml format
	 * 
	 * @param response
	 * @param outputStream
	 * @throws FPError
	 * @throws FPException
	 */
	public void sendResponse(HttpServletResponse response,
			ByteArrayOutputStream outputStream) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "[sendResponse] - Sending response to client");
			if (outputStream != null) {
				// set header information
				response.setContentType("text/xml");
				 //START:code commented for HTTPS support by Thirumurugan on 01-02-09
				  /*response.setHeader("CACHE-CONTROL", "NO-CACHE");
				  response.setHeader("PRAGMA", "NO-CACHE");*/
				 //END:code commented for HTTPS support by Thirumurugan on 01-02-09
				 
				// send response to client
				response.getOutputStream().write(outputStream.toByteArray());
			}
			// log debug message
			log.debug(this.getClass().getName()
					+ "[sendResponse] - Response has been sent");
		} catch (IOException ex) {
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ "[sendResponse] - Error while sent response to client. ",
					ex);
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ "[sendResponse] - Error while sent response to client. ",
					ex);
			throw new SwtException(ex.getMessage());
		} finally {
			// close output stream
			try {
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (Exception ex) {
				// log error message
				log.error(this.getClass().getName()
						+ "[sendResponse] - Error while close stream.", ex);
			}
			// nullify object(s)
			outputStream = null;
		}
	}

	/**
	 * This method sends response to client in xml format
	 * 
	 * @param response
	 * @param message
	 * @throws FPError
	 * @throws FPException
	 */
	public void sendMessage(HttpServletResponse response, String message)
			throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "[sendMessage] - Sending response to client");
			if (message != null) {
				// send response to client
				response.getWriter().print(message);
			}
			// log debug message
			log.debug(this.getClass().getName()
					+ "[sendMessage] - Response has been sent");
		} catch (IOException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ "[sendMessage] - Error while sent response to client. ",
					ex);
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ "[sendMessage] - Error while sent response to client. ",
					ex);
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify object(s)
			message = null;
		}
	}
}