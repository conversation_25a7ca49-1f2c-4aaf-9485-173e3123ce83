/*
 * @(#)ExpressionBuilderAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential IndateFormat
 * formation and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.TypeValues;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.ExpressionBuilderManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 *
 */



import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/expressionBuilderPCM", "/expressionBuilderPCM.do"})
public class ExpressionBuilderAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/maintenance/expressionbuilder");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "expressionBuilder":
				return expressionBuilder();
			case "getSearchListCriteria":
				return getSearchListCriteria();
			case "validateQuery":
				return validateQuery();
			case "display":
				return display();
			case "add":
				return add();
			case "save":
				return save();
			case "view":
				return view();



		}


		return null;
	}




	@Autowired
	private ExpressionBuilderManager expressionBuilderManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(ExpressionBuilderAction.class);

	public void setExpressionBuilderManager(
			ExpressionBuilderManager expressionBuilderManager) {
		this.expressionBuilderManager = expressionBuilderManager;
	}


	public String expressionBuilder() throws SwtException {
		return getView("success");
	}



	/**
	 * This method is used to return the list of search criteria to be added to
	 * the search screen
	 * @return
	 *
	 * @throws SwtException
	 */
	public String getSearchListCriteria() throws SwtException {
		// identify the type id
		int typeId = 0;
		//typeId = 1500;
		// user id get the type id from user request
		String curruserId = null;
		// User language Id
		String userLanguageId = null;
		// to get list criteria
		List<TypeValues> listCriteria = null;
		// to get typeValues of list criteria
		List<TypeValues> listTypeValues = null;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option , drop down list
		List<OptionInfo> lstOption = null;
//		// to get list criteria  to add
//		List<FieldsProfile> listCriteriaToAdd = null;
		// Date variable for modifyDate
		Date mDate = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// List for sub option , drop down list
		List<OptionInfo> subLstOption = null;
		// variable to know if the selected column is indexed
		boolean isIndexFunction = false;
		// to get typeValues of type list
		List<TypeValues> typeList = null;
		String currentLangId = null;
		// to get list criteria  to add
		List<RuleConditions> listCriteriaToBeChanged = null;
		String fatcaCurrency = null;
		String externalFields = null;
		String screenName = null;
		String ruleType = null;
		String moduleId = null;
		String ruleId = null;

		SwtXMLWriter xmlWriter = null;
		SwtResponseConstructor responseConstructor = null;
		ResponseHandler responseHandler= null;

		try {
			// log entry
			log.debug(this.getClass().getName() + " - [ getSearchListCriteria ] - Entry");
			if(SwtUtil.isEmptyOrNull(request.getParameter("typeId"))) {
				typeId = 0;
			}else {
				typeId = Integer.parseInt(request.getParameter("typeId"));
				typeId = 10;
			}
			// Instantiate SwtResponseConstructor
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter =  responseConstructor.getXMLWriter();
			externalFields = request.getParameter("externalFields");
			screenName = request.getParameter("screenName");
			ruleType = request.getParameter("ruleType");
			moduleId = request.getParameter("moduleId");
			ruleId = request.getParameter("ruleId");
			// user id get the report id from user request
			curruserId = SwtUtil.getCurrentUserId(request.getSession());
			// Get user Language id
			userLanguageId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			listCriteria = expressionBuilderManager.getSearchListCriteria(typeId, userLanguageId, true);
			screenName = "add";

//			listCriteriaToAdd = new ArrayList<FieldsProfile>();
//			if (!SwtUtil.isEmptyOrNull(externalFields) && externalFields.equals("Y")){
//				listCriteriaToAdd = expressionBuilderManager.getListCriteriaToAdd(userLanguageId, ruleType, moduleId);
//			}

			if (screenName.equals("change"))
				listCriteriaToBeChanged = expressionBuilderManager.getCriteriaToBeChanged(typeId, ruleId, userLanguageId, true, moduleId);



			// Adding date format as attributes


			// Append search tag
			xmlWriter.startElement(PCMConstant.SEARCH);
			xmlWriter.clearAttribute();
			// forms request node
			responseConstructor.formRequestReply(true, SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			// add an element advanced user to singletons
			responseConstructor.createElement(SwtConstants.HELPURL, SwtConstants.HELPURL);


			// format test date to system format
			mDate = SwtUtil.getSysParamDate();
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement("testdate", testDate);

//			if (moduleId.equals(SwtConstants.FATCAMODULE))
//			{
//				fatcaCurrency = FatcaUtil.getParamValue("FATCA_CURRENCY");
//				responseConstructor.createElement(SwtConstants.CURRENCY, fatcaCurrency);
//			}

			responseConstructor.createElement("dateFormat", SwtUtil.getCurrentDateFormat(request
					.getSession()).toLowerCase());
			responseConstructor.createElement("currencyPattern", SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form grid starts
			responseConstructor.formGridStart();

			// form rows (records)
			responseConstructor.formRowsStart(listCriteria.size() );//+ listCriteriaToAdd.size());
			// Iterating words details and forming row data
			for (Iterator<TypeValues> it = listCriteria.iterator(); it.hasNext();) {
				TypeValues typeValues = (TypeValues) it.next();

				isIndexFunction = false;

				xmlWriter.addAttribute(PCMConstant.SEARCH_TYPEID, typeValues.getId().getTypeId());
				xmlWriter.addAttribute(PCMConstant.SEARCH_VALUEID, typeValues.getId().getValueId());
				xmlWriter.addAttribute(PCMConstant.SEARCH_TYPECODE, typeValues.getTypeCode());
				xmlWriter.addAttribute(PCMConstant.SEARCH_TYPEENUM, typeValues.getTypeEnum());
				xmlWriter.addAttribute(PCMConstant.SEARCH_CODE, typeValues.getCode());
				xmlWriter.addAttribute(PCMConstant.SEARCH_PROFILEFIELD, "N");
				xmlWriter.addAttribute(PCMConstant.SEARCH_TABLENAME, typeValues.getTableName());
				xmlWriter.addAttribute(PCMConstant.SEARCH_COLUMN_VALUE_UPPER, typeValues.getColumnValueUpper());
				xmlWriter.addAttribute(PCMConstant.SEARCH_TYPELIST, typeValues.getTypeList());

				if (!SwtUtil.isEmptyOrNull(typeValues.getMacroColumn()))
					xmlWriter.addAttribute(PCMConstant.SEARCH_MACRO_COLUMN, typeValues.getMacroColumn());


				if (!SwtUtil.isEmptyOrNull(typeValues.getHiddenOperator()))
					xmlWriter.addAttribute(PCMConstant.SEARCH_HIDDEN_OPERATOR, typeValues.getHiddenOperator());
				if (typeValues.getTypeCode().equalsIgnoreCase("DATE") && !SwtUtil.isEmptyOrNull(typeValues.getTableName()))
				{
					String code = typeValues.getCode();
					if (code.indexOf(".") >= 0)
						code = code.substring(2);
					isIndexFunction = PCMUtil.isIndexFunctionBased(typeValues.getTableName(), "%TRUNC%" + code + "%");
					xmlWriter.addAttribute(PCMConstant.IS_INDEX_FUNC, isIndexFunction);
				}
				String fieldLabel = "";
				if (!SwtUtil.isEmptyOrNull(typeValues.getLabelPlus())) {
					xmlWriter.addAttribute(PCMConstant.SEARCH_LABEL, typeValues.getLabelFromDict() + typeValues.getLabelPlus());
					fieldLabel = typeValues.getLabelFromDict() + typeValues.getLabelPlus();
				} else {
					xmlWriter.addAttribute(PCMConstant.SEARCH_LABEL, typeValues.getLabelFromDict());
					fieldLabel = typeValues.getLabelFromDict();
				}


//				if (typeValues.getTypeCode().toString().equals("DECI"))
//				{
//					if (moduleId.equals(SwtConstants.FATCAMODULE))
//					{
//						if (!SwtUtil.isEmptyOrNull(fatcaCurrency))
//							fieldLabel = fieldLabel + " ( " +fatcaCurrency + " ) ";
//					}
//					else if (!SwtUtil.isEmptyOrNull(SwtUtil.getReferenceCurrency()))
//						fieldLabel = fieldLabel + " ( " +SwtUtil.getReferenceCurrency() + " ) ";
//				}


				if (screenName.equals("change")) {

					boolean find = false;
					for (Iterator<RuleConditions> iter = listCriteriaToBeChanged.iterator(); iter.hasNext();) {
						RuleConditions ruleConditions = (RuleConditions) iter.next();
						if (ruleConditions.getFieldCode().equals(typeValues.getCode())) {
							xmlWriter.addAttribute(PCMConstant.SEARCH_FIELD_VALUE, ruleConditions.getFieldValue());
							xmlWriter.addAttribute("operatorId", ruleConditions.getOperatorId());
							xmlWriter.addAttribute("conditionId", ruleConditions.getId().getConditionId());
							xmlWriter.addAttribute("condFieldName", ruleConditions.getFieldName());
							xmlWriter.addAttribute("profileFieldValue", ruleConditions.getProfileFieldValue());
							if (!SwtUtil.isEmptyOrNull( ruleConditions.getNextCondition()))
								xmlWriter.addAttribute("nextCondition", ruleConditions.getNextCondition());
							xmlWriter.addAttribute("toChange", "Y");
							// Create new row tag
							xmlWriter.startElement(String.valueOf(typeValues.getCode()));
							// row value in cdata section
							xmlWriter.createCDATA(fieldLabel);
							// end row tag
							xmlWriter.endElement(String.valueOf(typeValues.getCode()));
							find = true;
							xmlWriter.removeAttribute(PCMConstant.SEARCH_FIELD_VALUE);
							xmlWriter.removeAttribute("operatorId");
							xmlWriter.removeAttribute("conditionId");
							xmlWriter.removeAttribute("condFieldName");
							xmlWriter.removeAttribute("profileFieldValue");
							if (!SwtUtil.isEmptyOrNull( ruleConditions.getNextCondition()))
								xmlWriter.removeAttribute("nextCondition");
							xmlWriter.removeAttribute("toChange");


						}
					}
					if (!find)
					{
						xmlWriter.addAttribute("toChange", "N");
						responseConstructor.createRowElement(String.valueOf(typeValues.getCode()), fieldLabel);
					}
					else
						xmlWriter.clearAttribute();

				}

				if (screenName.equals("add")) {
					responseConstructor.createRowElement(String.valueOf(typeValues.getCode()), fieldLabel);
				}



				xmlWriter.clearAttribute();
			}
			/*if (!SwtUtil.isEmptyOrNull(externalFields) && externalFields.equals("Y")){
				// Hash map to hold column profiles list
				LinkedHashMap<String, String> hashRules = null;

				hashRules = expressionBuilderManager.getProfileFromDictionary(null, userLanguageId, ruleType, moduleId);

				for (Iterator<FieldsProfile> it = listCriteriaToAdd.iterator(); it.hasNext();) {
					FieldsProfile ruleProfiles = (FieldsProfile) it.next();

					xmlWriter.addAttribute(SwtConstants.SEARCH_TYPEID, ruleProfiles.getProfileId());

					if (SwtUtil.isEmptyOrNull(ruleProfiles.getNameTextId()))
						xmlWriter.addAttribute(SwtConstants.SEARCH_LABEL, ruleProfiles.getProfileName());
					else
						xmlWriter.addAttribute(SwtConstants.SEARCH_LABEL, hashRules.get(String.valueOf(ruleProfiles.getNameTextId())));

					xmlWriter.addAttribute(SwtConstants.SEARCH_TYPECODE, ruleProfiles.getResultType());
					xmlWriter.addAttribute(SwtConstants.SEARCH_CODE, ruleProfiles.getProfileCode());
					xmlWriter.addAttribute(SwtConstants.SEARCH_PROFILEFIELD, "Y");
					xmlWriter.addAttribute(SwtConstants.SEARCH_TABLENAME, ruleProfiles.getTableName());
					xmlWriter.addAttribute(SwtConstants.SEARCH_EXPRESSION, ruleProfiles.getAllQuery());
					xmlWriter.addAttribute(SwtConstants.SEARCH_FIELDNAME, ruleProfiles.getFieldName());
					xmlWriter.addAttribute(SwtConstants.SEARCH_ALIAS_TABLE, ruleProfiles.getProfileOn());
					xmlWriter.addAttribute(PCMConstant.FP_DATA_TYPE, ruleProfiles.getDataType());
					String fieldLabel ="";
					if (SwtUtil.isEmptyOrNull(ruleProfiles.getNameTextId()))
						fieldLabel = ruleProfiles.getProfileName();
					else
						fieldLabel = hashRules.get(String.valueOf(ruleProfiles.getNameTextId()));


					if (ruleProfiles.getDataType().equals("A"))
					{
						if (moduleId.equals(SwtConstants.FATCAMODULE))
						{
							if (!SwtUtil.isEmptyOrNull(fatcaCurrency))
								fieldLabel = fieldLabel + " ( " +fatcaCurrency + " ) ";
						}
						else if (!SwtUtil.isEmptyOrNull(SwtUtil.getReferenceCurrency()))
							fieldLabel = fieldLabel + " ( " +SwtUtil.getReferenceCurrency() + " ) ";
					}

					if (screenName.equals("change")) {
						boolean find = false;
						for (Iterator<RuleConditions> iter = listCriteriaToBeChanged.iterator(); iter.hasNext();) {
							RuleConditions ruleConditions = (RuleConditions) iter.next();
							if (ruleConditions.getFieldCode().equals(ruleProfiles.getProfileCode())) {
								xmlWriter.addAttribute(SwtConstants.SEARCH_FIELD_VALUE, ruleConditions.getFieldValue());
								xmlWriter.addAttribute("operatorId", ruleConditions.getOperatorId());
								xmlWriter.addAttribute("conditionId", ruleConditions.getId().getConditionId());
								xmlWriter.addAttribute("condFieldName", ruleConditions.getFieldName());
								xmlWriter.addAttribute("profileFieldValue", ruleConditions.getProfileFieldValue());
								if (!SwtUtil.isEmptyOrNull( ruleConditions.getNextCondition()))
									xmlWriter.addAttribute("nextCondition", ruleConditions.getNextCondition());
								xmlWriter.addAttribute("toChange", "Y");
								// Create new row tag
								xmlWriter.startElement(String.valueOf(ruleProfiles.getProfileCode()));
								// row value in cdata section
								xmlWriter.createCDATA(fieldLabel);
								// end row tag
								xmlWriter.endElement(String.valueOf(ruleProfiles.getProfileCode()));
								find = true;
								xmlWriter.removeAttribute(SwtConstants.SEARCH_FIELD_VALUE);
								xmlWriter.removeAttribute("operatorId");
								xmlWriter.removeAttribute("conditionId");
								xmlWriter.removeAttribute("condFieldName");
								xmlWriter.removeAttribute("profileFieldValue");
								if (!SwtUtil.isEmptyOrNull( ruleConditions.getNextCondition()))
									xmlWriter.removeAttribute("nextCondition");
								xmlWriter.removeAttribute("toChange");
							}
						}

						if (!find)
						{
							xmlWriter.addAttribute("toChange", "N");
							responseConstructor.createRowElement(String.valueOf(ruleProfiles.getProfileCode()), fieldLabel);
						}
						else
							xmlWriter.clearAttribute();
					}

					if (screenName.equals("add")) {
						responseConstructor.createRowElement(String.valueOf(ruleProfiles.getProfileCode()), fieldLabel);
					}

					xmlWriter.clearAttribute();
				}
			}*/

			// Rows node end
			responseConstructor.formRowsEnd();
			// grid node ends
			responseConstructor.formGridEnd();

			// List for selects info
			lstSelect = new ArrayList<SelectInfo>();

			currentLangId = (SwtUtil.getCurrentUser(request.getSession())).getLanguage();
			for (Iterator<TypeValues> it = listCriteria.iterator(); it.hasNext();) {
				TypeValues typeValues = (TypeValues) it.next();
				// if the typeEnum is a number ==> refer to another type id to
				// get list of data
				if (!SwtUtil.isEmptyOrNull(typeValues.getTypeEnum()) && Integer.parseInt(typeValues.getTypeEnum()) != 0) {

					listTypeValues = new ArrayList<TypeValues>();
					listTypeValues = PCMUtil.getListTypeValuesAllFlds(Integer.parseInt(typeValues.getTypeEnum()), userLanguageId, true, "alphabetic");
					// List for option , drop down list
					lstOption = new ArrayList<OptionInfo>();

					for (int i = 0; i < listTypeValues.size(); i++) {

						String typeCode=listTypeValues.get(i).getTypeCode();

						if (i == 0)
							lstOption.add(new OptionInfo(typeCode,
									String.valueOf(listTypeValues.get(i).getLabelFromDict()), true, String
									.valueOf(listTypeValues.get(i).getCode()), ""));
						else
							lstOption.add(new OptionInfo(typeCode,
									String.valueOf(listTypeValues.get(i).getLabelFromDict()), false, String
									.valueOf(listTypeValues.get(i).getCode()), ""));

					}
					lstSelect.add(new SelectInfo(String.valueOf(typeValues.getCode()), lstOption));
				}
				// if the typeEnum is empty and sQuery is not null ==> execute
				// query to get list of data
				else if ((SwtUtil.isEmptyOrNull(typeValues.getTypeEnum()) || Integer.parseInt(typeValues.getTypeEnum()) == 0)
						 && !SwtUtil.isEmptyOrNull(typeValues.getSquery())) {
					String queryToGetList = typeValues.getSquery();

					if ((queryToGetList.indexOf("languageId") > -1 || (queryToGetList.indexOf("language_id") > -1))
						&& (queryToGetList.indexOf("?") > -1)) {
						queryToGetList = queryToGetList.replaceAll("\\?", "'" + currentLangId + "'");
					}
					queryToGetList = queryToGetList.replaceAll(":lang_id", "'" + currentLangId + "'");
					queryToGetList = queryToGetList.replaceAll(":sysdateformat", "'" + systemFormats.getDateFormatValue() + "'");
					List<Object[]> query = new ArrayList<Object[]>(3);

					query = expressionBuilderManager.executeSelectQuery(queryToGetList);
					// List for option , drop down list
					lstOption = new ArrayList<OptionInfo>();

					int i=0;

					for (Object[] qu : query) {
						if (qu.length >2 && !SwtUtil.isEmptyOrNull(String.valueOf(qu[2])))
							if (i==0)
							{
								lstOption.add(new OptionInfo(typeValues.getTypeCode(), String
										.valueOf(qu[1]), true, String.valueOf(qu[0]), String.valueOf(qu[2])));
								i++;
							}
							else
								lstOption.add(new OptionInfo(typeValues.getTypeCode(), String
										.valueOf(qu[1]), false, String.valueOf(qu[0]), String.valueOf(qu[2])));
						else
						if (i==0)
						{
							lstOption.add(new OptionInfo(typeValues.getTypeCode(), String
									.valueOf(qu[1]), true, String.valueOf(qu[0]), ""));

							i++;
						}
						else
							lstOption.add(new OptionInfo(typeValues.getTypeCode(), String
									.valueOf(qu[1]), false, String.valueOf(qu[0]), ""));
					}

					lstSelect.add(new SelectInfo(String.valueOf(typeValues.getCode()), lstOption));
				}
				// get type list of criteria (in the case when a criteria contains a "LIST" type code )
				if (!SwtUtil.isEmptyOrNull(typeValues.getTypeList()) && Integer.parseInt(typeValues.getTypeList()) != 0)
				{
					List<Object[]> typeIdList = new ArrayList<Object[]>(3);
					typeIdList = expressionBuilderManager.executeSelectQuery("select t.typeId, t.name  from ListTypes t where t.moduleId = 'PREDICT' and t.typeId < 0 and t.editable = 'Y' order by name");
					// List for option , drop down list
					subLstOption = new ArrayList<OptionInfo>();

					int k = 0;
					for (Object[] qu : typeIdList) {
						if (k == 0)
						{
							subLstOption.add(new OptionInfo("LIST", String.valueOf(qu[1]), true, "select code from s_cfg_type_values t where t.type_id = "+String.valueOf(qu[0]), ""));
							k++;
						}
						else
							subLstOption.add(new OptionInfo("LIST", String.valueOf(qu[1]), false, "select code from s_cfg_type_values t where t.type_id = "+String.valueOf(qu[0]), ""));
					}

					//System.err.println("---------------");
					if (!SwtUtil.isEmptyOrNull(typeValues.getTypeList()) && Integer.parseInt(typeValues.getTypeList()) > 0)
					{
						typeList = new ArrayList<TypeValues>();
						typeList = PCMUtil.getListTypeValuesAllFlds(Integer.parseInt(typeValues.getTypeList()), userLanguageId, true, "alphabetic");

						for (int i = 0; i < typeList.size(); i++) {
							List<Object[]> typeListQuery = new ArrayList<Object[]>(3);
							String selectQuery = typeList.get(i).getSquery().substring(0, typeList.get(i).getSquery().indexOf("@||@"));
							selectQuery = selectQuery.replaceAll(":lang_id", "'" + currentLangId + "'");
							selectQuery = selectQuery.replaceAll(":sysdateformat", "'" + systemFormats.getDateFormatValue() + "'");
							typeListQuery = expressionBuilderManager.executeSelectQuery(selectQuery);
							//System.err.println("selectQuery ============"+selectQuery);

							int j = 0;
							for (Object[] qu : typeListQuery) {
								if (j == 0)
								{
									subLstOption.add(new OptionInfo(typeList.get(i).getTypeCode(), String.valueOf(qu[1]), true, typeList.get(i).getSquery().substring(typeList.get(i).getSquery().indexOf("@||@") + 4), String.valueOf(qu[0])));
									j++;
								}
								else
									subLstOption.add(new OptionInfo(typeList.get(i).getTypeCode(), String.valueOf(qu[1]), false, typeList.get(i).getSquery().substring(typeList.get(i).getSquery().indexOf("@||@") + 4), String.valueOf(qu[0])));
							}
						}
					}

					lstSelect.add(new SelectInfo(String.valueOf(typeValues.getCode()) + "_LIST", subLstOption));
				}
			}

			// create select node to add the list of Amount Threshold
//			if (moduleId.equals(SwtConstants.FATCAMODULE))
//				listTypeValues = ruleDefMgr.getSearchListCriteria(1251, userLanguageId, true);
//			else


			listTypeValues = expressionBuilderManager.getSearchListCriteria(940, userLanguageId, true);
			//System.err.println("*************************");

			// List for option , drop down list
			lstOption = new ArrayList<OptionInfo>();

			for (int i = 0; i < listTypeValues.size(); i++) {
				if (!SwtUtil.isEmptyOrNull(listTypeValues.get(i).getLabelFromDict())) {
					if (listTypeValues.get(i).getCode().equals("TYPETEXT"))
						lstOption.add(new OptionInfo(listTypeValues.get(i).getTypeCode(),
								String.valueOf(listTypeValues.get(i).getLabelFromDict()), false, String
								.valueOf(listTypeValues.get(i).getCode()), "TT")); // B: to indicate that is a variable for binding
					else
						lstOption.add(new OptionInfo(listTypeValues.get(i).getTypeCode(),
								String.valueOf(listTypeValues.get(i).getLabelFromDict()), false, String
								.valueOf(listTypeValues.get(i).getCode()), "B")); // B: to indicate that is a variable for binding
				} else {
					if (listTypeValues.get(i).getCode().equals("TYPETEXT"))
						lstOption.add(new OptionInfo(listTypeValues.get(i).getTypeCode(),
								String.valueOf(listTypeValues.get(i).getCode()), false, String
								.valueOf(listTypeValues.get(i).getCode()), "TT"));
					else
						lstOption.add(new OptionInfo(listTypeValues.get(i).getTypeCode(),
								String.valueOf(listTypeValues.get(i).getCode()), false, String
								.valueOf(listTypeValues.get(i).getCode()), "B"));
				}
			}
			lstSelect.add(new SelectInfo("THRESHOLD_AMOUNT", lstOption));

//			if (!moduleId.equals(SwtConstants.FATCAMODULE))
//			{
//				// create select node to add the list of Count Threshold
//				listTypeValues = ruleDefMgr.getSearchListCriteria(950, userLanguageId, true);
//				// List for option , drop down list
//				lstOption = new ArrayList<OptionInfo>();
//
//				for (int j = 0; j < listTypeValues.size(); j++) {
//					if (!SwtUtil.isEmptyOrNull(listTypeValues.get(j).getLabelFromDict())) {
//						if (listTypeValues.get(j).getCode().equals("TYPETEXT"))
//							lstOption.add(new OptionInfo(listTypeValues.get(j).getTypeCode(),
//									String.valueOf(listTypeValues.get(j).getLabelFromDict()), false, String
//											.valueOf(listTypeValues.get(j).getCode()), "TT"));
//						else
//							lstOption.add(new OptionInfo(listTypeValues.get(j).getTypeCode(),
//									String.valueOf(listTypeValues.get(j).getLabelFromDict()), false, String
//											.valueOf(listTypeValues.get(j).getCode()), "B")); // B: to indicate that is a variable for binding
//					} else {
//						if (listTypeValues.get(j).getCode().equals("TYPETEXT"))
//							lstOption.add(new OptionInfo(listTypeValues.get(j).getTypeCode(),
//									String.valueOf(listTypeValues.get(j).getCode()), false, String
//											.valueOf(listTypeValues.get(j).getCode()), "TT"));
//						else
//							lstOption.add(new OptionInfo(listTypeValues.get(j).getTypeCode(),
//									String.valueOf(listTypeValues.get(j).getCode()), false, String
//											.valueOf(listTypeValues.get(j).getCode()), "B")); // B: to indicate that is a variable for binding
//					}
//				}
//				lstSelect.add(new SelectInfo("THRESHOLD_COUNT", lstOption));
//			}

			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.SEARCH);

			// send response to the client
			//responseHandler.sendResponse(response, xmlWriter.getData());

			request.setAttribute("data", xmlWriter.getData());

			//System.err.println(xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [displayAccountAttributes] - Exit");
			return getView("data");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getSearchListCriteria] method : - "
					  + e.getMessage());
			e.printStackTrace();
			// log error message
//			SwtUtil.logErrorInDatabase((SwtErrorHandler.getInstance().handleException(e,
//					"getSearchListCriteria", this.getClass()), "SYSTEM", curruserId, request
//					.getRemoteAddr(), "Y");
			// Exception arises, reset buffer to write error message
			xmlWriter.resetBuffer();
			// request reply message tag
			responseConstructor.formRequestReply(false, e.getMessage());
			// send response to the client
			//responseHandler.sendResponse(response, xmlWriter.getData());
			return getView("fail");

		} finally {
			// Nullify objects
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			curruserId = null;
			userLanguageId = null;
			listCriteria = null;
			listTypeValues = null;
			lstSelect = null;
			lstOption = null;
			moduleId = null;
			ruleType = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSearchListCriteria ]  - Exit");
		}
	}


	/**
	 * This method is used to return if the search query is valid or is not
	 * valid to the search screen
	 * @return
	 *
	 * @throws SwtException
	 */
	public String validateQuery() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		// To send response to client
		ResponseHandler responseHandler = null;
		// To build logout response
		SwtResponseConstructor responseConstructor = null;
		// To build logout response
		SwtXMLWriter xmlWriter = null;
		// user id get the type id from user request
		String curruserId = null;
		boolean validQuery = false;
		String sQuery = null;
		try {
			// log entry
			log.debug(this.getClass().getName() + " - [ validateQuery ] - Entry");

			sQuery = request.getParameter("sQuery");
			if (!(sQuery.contains(" "))) {
				sQuery = SwtUtil.decode64(sQuery);
			}
			// user id get the report id from user request
			curruserId = SwtUtil.getCurrentUserId(request.getSession());

			// Instantiate ResponseHandler
			responseHandler = new ResponseHandler();
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();

			// Append search tag
			xmlWriter.startElement("search");

			//System.err.println(sQuery);
			validQuery = expressionBuilderManager.validateQueryFromDataBase(sQuery);

			if (validQuery)
				// forms request node
				responseConstructor.formRequestReply(true, SwtConstants.DATA_FETCH_OK);
			else
				// forms request node
				responseConstructor.formRequestReply(false, SwtConstants.DATA_FETCH_OK);

			xmlWriter.endElement("search");

			// send response to the client
			//	responseHandler.sendResponse(response, xmlWriter.getData());

			request.setAttribute("data", xmlWriter.getData());

			//System.err.println(xmlWriter.getData());
			//response.getWriter().print("atefffffffff");
			log.debug(this.getClass().getName() + " - [validateQuery] - Exit");
			return getView("data");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [validateQuery] method : - " + e.getMessage() + " Query : "+sQuery);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			return getView("statechange");

		} finally {
			// Nullify objects
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			curruserId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ validateQuery ]  - Exit");
		}
	}

	/**
	 * This method is used to display the list of rules on grid and is called
	 * when clicked menu item RulesDefinition
	 *
	 * @throws SwtException
	 */
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// String variable to hold userId
		String userId = null;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;

		// variable to hold list of module license details
		List<RulesDefinition> rulesDefDetails = null;

		// Hash map to hold column rules list
		LinkedHashMap<String, String> hashRules = null;

		String sQuery= null;
		String moduleId= null;
		String riskFactor = null;
		String selectedFilter = null;
		String selectedSort = null;
		boolean searchFlag = false;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			rulesDefDetails = new ArrayList<RulesDefinition>();

			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			// Fetches rules details by calling manager
			if(searchFlag)
				rulesDefDetails = expressionBuilderManager.getSearchData(sQuery, languageId, moduleId , riskFactor);
			else
				rulesDefDetails = expressionBuilderManager.getListRules(languageId, moduleId, riskFactor , selectedFilter, selectedSort);

			hashRules = new LinkedHashMap<String, String>();
			hashRules = expressionBuilderManager.getRulesFromDictionary(null , languageId, moduleId);

			for (int i = 0 ; i < rulesDefDetails.size() ; i++)
			{
				if (rulesDefDetails.get(i).getNameTextId() != null)
					rulesDefDetails.get(i).setRuleName(hashRules.get(String.valueOf(rulesDefDetails.get(i).getNameTextId())));
				if (rulesDefDetails.get(i).getDescTextId() != null)
					rulesDefDetails.get(i).setDescription(hashRules.get(String.valueOf(rulesDefDetails.get(i).getDescTextId())));
				if(String.valueOf(rulesDefDetails.get(i).getLevelCode()).indexOf(".0" ) != -1 ) {
					rulesDefDetails.get(i).setLevelCodeToDisplay(String.valueOf((int)rulesDefDetails.get(i).getLevelCode()));
				}else {
					rulesDefDetails.get(i).setLevelCodeToDisplay(String.valueOf(rulesDefDetails.get(i).getLevelCode()));
				}
			}

			// build XML response
			return sendDisplayResponse(rulesDefDetails, languageId, systemFormats);

		} catch (SwtException ex) {
			//getExceptionXmlData(ex.getMessage());
			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "display", this.getClass()), moduleId,
//					userId, request.getRemoteAddr(), "Y");
//			log.error(this.getClass().getName()
//					+ " - SwtException Catched in [display] method : - "
//					+ ex.getMessage());
		} catch (Exception ex) {
			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "display", this.getClass()), moduleId,
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - Exception Catched in [display] method : - "
					  + ex.getMessage());
			ex.printStackTrace();
		} finally {
			// nullify objects
			userId = null;
			languageId = null;
			systemFormats = null;
			rulesDefDetails = null;
			searchFlag = false;
			sQuery = "";
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - Exit");
		}
		return null;

	}


	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getRulesDefGridColumns(String width, String columnOrder, String hiddenColumns) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getRulesDefGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =   PCMConstant.RULEID + "=70,"
						  + PCMConstant.RULENAME + "=300,"
						  + PCMConstant.RULESTATUS + "=150,"
						  + PCMConstant.RULETYPE + "=200,"
						  + PCMConstant.LEVELCODE + "=150,"
						  + PCMConstant.LEVEL_TYPE + "=150";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.RULEID + ","
							  + PCMConstant.RULENAME + ","
							  + PCMConstant.RULESTATUS + ","
							  + PCMConstant.RULETYPE + ","
							  + PCMConstant.LEVELCODE+ ","
							  + PCMConstant.LEVEL_TYPE;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// rule ID type column
				if (order.equals(PCMConstant.RULEID))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.RULEID_COLUMN_HEADER,
									PCMConstant.RULEID,
									SwtConstants.COLUMN_TYPE_NUMBER,
									0,
									Integer.parseInt(widths.get(PCMConstant.RULEID)),
									false,
									true, hiddenColumnsMap.get(PCMConstant.RULEID)));

				// rule name type column
				if (order.equals(PCMConstant.RULENAME))
					lstColumns
							.add(new ColumnInfo(
									PCMConstant.RULENAME_COLUMN_HEADER,
									PCMConstant.RULENAME,
									PCMConstant.COLUMN_TYPE_STRING,
									1,
									Integer.parseInt(widths.get(PCMConstant.RULENAME)),
									false,
									false, hiddenColumnsMap.get(PCMConstant.RULENAME)));

				// ruleStatus column
				if (order.equals(PCMConstant.RULESTATUS))
					lstColumns.add(new ColumnInfo(
							PCMConstant.RULESTATUS_COLUMN_HEADER,
							PCMConstant.RULESTATUS,
							PCMConstant.COLUMN_TYPE_STRING,
							2,
							Integer.parseInt(widths.get(PCMConstant.RULESTATUS)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.RULESTATUS)));

				// ruleType column
				if (order.equals(PCMConstant.RULETYPE))
					lstColumns.add(new ColumnInfo(
							PCMConstant.RULETYPE_COLUMN_HEADER,
							PCMConstant.RULETYPE,
							PCMConstant.COLUMN_TYPE_STRING,
							3,
							Integer.parseInt(widths.get(PCMConstant.RULETYPE)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.RULETYPE)));

				// levelCode column
				if (order.equals(PCMConstant.LEVELCODE))
					lstColumns.add(new ColumnInfo(
							PCMConstant.LEVELCODE_COLUMN_HEADER,
							PCMConstant.LEVELCODE,
							PCMConstant.COLUMN_TYPE_STRING,
							4,
							Integer.parseInt(widths.get(PCMConstant.LEVELCODE)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.LEVELCODE)));
				// level type column
				if (order.equals(PCMConstant.LEVEL_TYPE))
					lstColumns.add(new ColumnInfo(
							PCMConstant.LEVEL_TYPE_COLUMN_HEADER,
							PCMConstant.LEVEL_TYPE,
							PCMConstant.COLUMN_TYPE_STRING,
							5,
							Integer.parseInt(widths.get(PCMConstant.LEVEL_TYPE)),
							true,
							true, true));
			}

		} catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
						   + " - Exception Catched in [getRulesDefGridColumns] method : - "
						   + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getRulesDefGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getRulesDefGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}



	/**
	 * This method forms the xml for displaying the rules list.
	 *
	 * @param width
	 *            - passing default widths
	 * @param columnOrder
	 *            - passing default order
	 * @param ruleDetails
	 *            - passing rule details
	 * @param languageId
	 *            - passing languageId
	 * @param systemFormats
	 *            - passing system formats date
	 * @return
	 */
	public String sendDisplayResponse(List<RulesDefinition> rulesDefDetails, String languageId,
									  SystemFormats systemFormats) throws SwtException {

		// To send response to client
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// LinkedHashMap variable to hold ruleStatusList
		LinkedHashMap<String, String> ruleStatusList;
		// LinkedHashMap variable to hold levelCodeList
		LinkedHashMap<String, String> levelCodeList;
		// LinkedHashMap variable to hold ruleTypeList
		LinkedHashMap<String, String> ruleTypeList;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		// HashMap to hold list rule level type
		HashMap<String, String> lstLevelType = null;
		String riskFactor = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// Instantiate ResponseHandler
			responseHandler = new ResponseHandler();
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
//			if (!SwtUtil.isEmptyOrNull(riskFactor) && "Y".equals(riskFactor))
//				componentId = PCMConstant.RISK_FACTOR_COMPONENT_ID;
//			else
			componentId = "ruleDefinition";

			/* Get column width from SwtUtil */
//			width = SwtUtil.getPropertyValue(request, getProgramId(),
//					componentId, SwtConstants.SCREEN_PROPERTIES_COLUMN_WIDTH);
//			/* Get column order from swtUtil */
//			columnOrder = SwtUtil.getPropertyValue(request, getProgramId(),
//					componentId, SwtConstants.SCREEN_PROPERTIES_COLUMN_ORDER);
//			/* Get hidden columns from swtUtil */
//			hiddenColumns = SwtUtil.getPropertyValue(request, getProgramId(),
//					componentId, SwtConstants.SCREEN_PROPERTIES_COLUMN_HIDDEN);


			ruleStatusList = new LinkedHashMap<String, String>();
			levelCodeList = new LinkedHashMap<String, String>();
			ruleTypeList = new LinkedHashMap<String, String>();

			ruleStatusList = expressionBuilderManager.getTypeValuesList(30,languageId, true);
			levelCodeList = expressionBuilderManager.getTypeValuesList(520,languageId, true);
			ruleTypeList = expressionBuilderManager.getTypeValuesList(510,languageId, true);
			// Get list from S_CFG_TYPE_VALUES and S_DICTIONARY
//			lstLevelType = (HashMap<String, String>) PCMUtil.getListTypeValuesAllFlds(
//					512, languageId, true);
//

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request
					.getSession()));
			xmlWriter.startElement(PCMConstant.RULES);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			//responseConstructor.createElement(SwtConstants.HELPURL, SwtConstants.HELP_URL);
			responseConstructor.createElement(PCMConstant.RULEDETAILURL, PCMConstant.RULE_DETAIL_URL);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getRulesDefGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(rulesDefDetails.size());
			// Iterating rules definition details
			for (Iterator<RulesDefinition> it = rulesDefDetails.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				RulesDefinition ruleDef = (RulesDefinition) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.RULEID, ruleDef.getRuleId());
				responseConstructor.createRowElement(PCMConstant.RULENAME, ruleDef.getRuleName());
				responseConstructor.createRowElement(PCMConstant.RULESTATUS, ruleStatusList.get(ruleDef.getRuleStatus()), ruleDef.getRuleStatus());
				responseConstructor.createRowElement(PCMConstant.RULETYPE, ruleTypeList.get(ruleDef.getRuleType()), ruleDef.getRuleType());
				responseConstructor.createRowElement(PCMConstant.LEVELCODE, levelCodeList.get(ruleDef.getLevelCodeToDisplay()), ruleDef.getLevelCodeToDisplay());



				responseConstructor.createRowElement(PCMConstant.LEVEL_TYPE, "1");
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.RULES);

			// send response to the client
			//responseHandler.sendResponse(response, xmlWriter.getData());
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();
			//getExceptionXmlData(ex.getMessage());
			// log exception in database
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");
			log
					.error(this.getClass().getName()
						   + " - SwtException Catched in [sendDisplayResponse] method : - "
						   + ex.getMessage());
		} catch (Exception ex) {
			xmlWriter.clearData();
			// logs exception in data base
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");

			// log error message

			log
					.error(this.getClass().getName()
						   + " - Exception Catched in [sendDisplayResponse] method : - "
						   + ex.getMessage());
			ex.printStackTrace();
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ sendDisplayResponse ] - Exit");
		}
		return null;

	}


	/**
	 * This method is used to display the details of Rule on view screen
	 *
	 * @throws SwtException
	 */
	public String view() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Object instance Rule
		RulesDefinition ruleDetails = null;
		// To send response to client
		ResponseHandler responseHandler = null;
		// To build logout response
		SwtResponseConstructor responseConstructor = null;
		// To build logout response
		SwtXMLWriter xmlWriter = null;
		// Instance of simple date format
		SimpleDateFormat sdf = null;
		// String variable for uDate
		String modifyDate = "";
		// Date variable for modifyDate
		Date mDate = null;
		// Variable String to hold UserId
		String userId = null;
		// Variable String to hold languageId
		String languageId = null;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// String variable to hold componentId
		String componentId = null;

		LinkedHashMap<String, String> ruleStatusList;
		LinkedHashMap<String, String> periodTypeList;
		LinkedHashMap<String, String> levelCodeList;
		LinkedHashMap<String, String> ruleTypeList;
		LinkedHashMap<String, String> hashRules = null;
		String expDate = null;
		// HashMap to load sign of transactions
		HashMap<String, String> ruleSignList = null;
		// variable to hold list of secondary rules
		List<RulesDefinition> lstSecondaryRules = null;
		// HashMap to hold list Sub Type factors label
		HashMap<String, String> lstSubTypeFactor = null;
		String ruleId= null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ view ] - " + "Entry");

			// Retrieves current user
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// check screen type
//			if (!SwtUtil.isEmptyOrNull(riskFactor) && "Y".equals(riskFactor))
//				componentId = PCMConstant.RISK_FACTOR_COMPONENT_ID;
//			else

			ruleId = request.getParameter("ruleId");

			componentId = PCMConstant.RULE_COMPONENT_ID;
			// Retrieves current language
			languageId = (SwtUtil.getCurrentUser(request.getSession()))
					.getLanguage();

			ruleStatusList = new LinkedHashMap<String, String>();
			periodTypeList = new LinkedHashMap<String, String>();
			levelCodeList = new LinkedHashMap<String, String>();
			ruleTypeList = new LinkedHashMap<String, String>();

			ruleStatusList = expressionBuilderManager.getTypeValuesList(30,languageId, true);
			periodTypeList = expressionBuilderManager.getTypeValuesList(850,languageId, true);
			levelCodeList = expressionBuilderManager.getTypeValuesList(520, languageId, true);
			ruleTypeList = expressionBuilderManager.getTypeValuesList(510,languageId, true);
			// Get list from S_CFG_TYPE_VALUES and S_DICTIONARY
//			lstSubTypeFactor = (HashMap<String, String>) SwtUtil.getListTypeValues(
//					513, languageId, true);

			// Instantiate profileSignList HashMap
			ruleSignList = new HashMap<String, String>();
			// HashMap to load profileSignList
			//ruleSignList = SwtUtil.getListTypeValues(60, languageId, true);

			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			if (!SwtUtil.isEmptyOrNull(ruleId)) {
				// Get Rule details
				ruleDetails = (RulesDefinition) expressionBuilderManager.getRuleById(Integer.parseInt(ruleId), languageId);

				hashRules = new LinkedHashMap<String, String>();
				hashRules = expressionBuilderManager.getRulesFromDictionary(ruleId , languageId, "");

				if (ruleDetails.getNameTextId() != null)
					ruleDetails.setRuleName(hashRules.get(String.valueOf(ruleDetails.getNameTextId())));
				if (ruleDetails.getDescTextId() != null)
					ruleDetails.setDescription(hashRules.get(String.valueOf(ruleDetails.getDescTextId())));
			}
			if (ruleDetails != null) {
				// Instantiate ResponseHandler
				responseHandler = new ResponseHandler();
				// Instantiate SwtResponseConstructor
				responseConstructor = new SwtResponseConstructor();
				// Get instance of SwtXMLWriter
				xmlWriter = responseConstructor.getXMLWriter();
				// Adding screen id and current user id as attributes
				xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
				xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
				// Adding screen id and current user id as attributes
				xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
				xmlWriter.startElement(PCMConstant.RULES);
				xmlWriter.clearAttribute();
				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
				// forms singleton node
				xmlWriter.startElement(SwtConstants.SINGLETONS);
				//responseConstructor.createElement(SwtConstants.HELPURL,	PCMConstant.HELP_URL);
				responseConstructor.createElement(PCMConstant.ADDRULEURL, PCMConstant.ADD_RULE_URL);
				xmlWriter.endElement(SwtConstants.SINGLETONS);

				responseConstructor.formGridStart();
				// form paging details
				responseConstructor.formPaging(null);
				// form rows (records)
				responseConstructor.formRowsStart(1);
				// format modify date to system format
				mDate = ruleDetails.getUpdateDate();
				if (mDate != null) {
					// formats the date
					modifyDate = SwtUtil.formatDate(mDate, systemFormats
							.getDateFormatValue());
					sdf = new SimpleDateFormat("HH:mm:ss");
					modifyDate = modifyDate + " " + sdf.format(mDate);
				}

				// format modify date to system format
				mDate = ruleDetails.getExpiryDate();
				if (mDate != null) {
					// formats the date
					expDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
				}

				// Get list secondary rules
				lstSecondaryRules = expressionBuilderManager.getListSecondaryRules(ruleDetails.getModuleId());

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.RULEID, ruleDetails.getRuleId());
				responseConstructor.createRowElement(PCMConstant.RULENAME, ruleDetails.getRuleName());
				responseConstructor.createRowElement(PCMConstant.RULESTATUS, ruleDetails.getRuleStatus());
				responseConstructor.createRowElement(PCMConstant.RULESTATUSLABEL, ruleStatusList.get(ruleDetails.getRuleStatus()));
				responseConstructor.createRowElement(PCMConstant.RULETYPE,	ruleDetails.getRuleType());
				responseConstructor.createRowElement(PCMConstant.RULETYPELABEL, ruleTypeList.get(ruleDetails.getRuleType()));
				responseConstructor.createRowElement(PCMConstant.LEVELCODE, String.valueOf(ruleDetails.getLevelCode()));
				responseConstructor.createRowElement(PCMConstant.LEVELLABEL, levelCodeList.get(String.valueOf((int)ruleDetails.getLevelCode())));
				responseConstructor.createRowElement(PCMConstant.RULE_DESCRIPTION, ruleDetails.getDescription());
				responseConstructor.createRowElement(PCMConstant.PERIODTYPELABEL,periodTypeList.get(String.valueOf(ruleDetails.getPeriodValue())));
				responseConstructor.createRowElement(PCMConstant.PERIODVALUE, String.valueOf(ruleDetails.getPeriodValue()));
				responseConstructor.createRowElement(PCMConstant.EDITABLE, ruleDetails.getEditable());
				responseConstructor.createRowElement(PCMConstant.RULEQUERY, ruleDetails.getRuleQuery());
				responseConstructor.createRowElement(PCMConstant.RULETEXT, ruleDetails.getRuleText());
				responseConstructor.createRowElement(PCMConstant.BLOCKED, ruleDetails.getBlocked());
				responseConstructor.createRowElement(PCMConstant.UPDATE_ALERT, ruleDetails.getUpdateAlert());
//				responseConstructor.createRowElement(SwtConstants.UPDATE_USER, ruleDetails.getUpdateUser());
//				responseConstructor.createRowElement(SwtConstants.UPDATE_DATE, String.valueOf(modifyDate));
				if (!SwtUtil.isEmptyOrNull(expDate))
					responseConstructor.createRowElement(PCMConstant.EXPIRY_DATE, String.valueOf(expDate));
				if (ruleDetails.getNameTextId() != null)
					responseConstructor.createRowElement(PCMConstant.RULE_TEXT_ID, String.valueOf(ruleDetails.getNameTextId()));
				if (ruleDetails.getDescTextId() != null)
					responseConstructor.createRowElement(PCMConstant.RULE_DESC_ID, String.valueOf(ruleDetails.getDescTextId()));
				if (!SwtUtil.isEmptyOrNull(ruleDetails.getSign()))
				{
					responseConstructor.createRowElement(PCMConstant.RULE_SIGN, ruleDetails.getSign());
					responseConstructor.createRowElement(PCMConstant.RULE_SIGN_LABEL, ruleSignList.get(ruleDetails.getSign()));
				}
				responseConstructor.createRowElement(PCMConstant.ALL_CLT_ACCOUNT, ruleDetails.getAllCltAccount());
				responseConstructor.createRowElement(PCMConstant.HANDLE_PROCESSED_ALERTS, ruleDetails.gethandleProcessedAlerts());

				if (SwtUtil.isEmptyOrNull(ruleDetails.getMissingInfo()))
					responseConstructor.createRowElement(PCMConstant.MISSING_INFO, "N");
				else
					responseConstructor.createRowElement(PCMConstant.MISSING_INFO, ruleDetails.getMissingInfo());

				responseConstructor.createRowElement(PCMConstant.LEVEL_TYPE, ruleDetails.getLevelType());
				responseConstructor.createRowElement(PCMConstant.SECONDARY_RULE,(ruleDetails.getAcceptRuleId() != null)?"Y":"N");

				responseConstructor.formRowEnd();
				responseConstructor.formRowsEnd();
				responseConstructor.formGridEnd();


				// form drop down details
				lstSelect = new ArrayList<SelectInfo>();

				// status drop down list
				lstOptions = new ArrayList<OptionInfo>();

				Set<Map.Entry<String, String>> status = ruleStatusList.entrySet();

				Iterator<Map.Entry<String, String>> j = status.iterator();

				while (j.hasNext()) {
					Map.Entry<String, String> actionT = (Map.Entry<String, String>) j.next();
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
				}
				lstSelect.add(new SelectInfo(PCMConstant.RULESTATUSLIST, lstOptions));


				// period type drop down list
				lstOptions = new ArrayList<OptionInfo>();

				Set<Map.Entry<String, String>> periodeType = periodTypeList.entrySet();

				Iterator<Map.Entry<String, String>> k = periodeType.iterator();

				while (k.hasNext()) {
					Map.Entry<String, String> actionT = (Map.Entry<String, String>) k.next();
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
				}
				lstSelect.add(new SelectInfo(PCMConstant.PERIODETYPELIST, lstOptions));


				// risk level drop down list
				lstOptions = new ArrayList<OptionInfo>();

				Set<Map.Entry<String, String>> levelCode = levelCodeList.entrySet();

				Iterator<Map.Entry<String, String>> l = levelCode.iterator();

				while (l.hasNext()) {
					Map.Entry<String, String> actionT = (Map.Entry<String, String>) l.next();
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
				}
				lstSelect.add(new SelectInfo(PCMConstant.LEVELCODELIST, lstOptions));


				// rule type drop down list
				lstOptions = new ArrayList<OptionInfo>();

				Set<Map.Entry<String, String>> ruleType = ruleTypeList.entrySet();

				Iterator<Map.Entry<String, String>> m = ruleType.iterator();

				while (m.hasNext()) {
					Map.Entry<String, String> actionT = (Map.Entry<String, String>) m.next();
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
				}
				lstSelect.add(new SelectInfo(PCMConstant.RULETYPELIST, lstOptions));


				// rule type drop down list
				lstOptions = new ArrayList<OptionInfo>();

				lstOptions.add(new OptionInfo(ruleDetails.getRuleType(), String.valueOf(ruleTypeList.get(ruleDetails.getRuleType())), false));
				lstSelect.add(new SelectInfo(PCMConstant.VIEWRULETYPELIST, lstOptions));


				// sign drop down list
				lstOptions = new ArrayList<OptionInfo>();

				Set<Map.Entry<String, String>> sign = ruleSignList.entrySet();

				Iterator<Map.Entry<String, String>> n = sign.iterator();

				while (n.hasNext()) {
					Map.Entry<String, String> actionT = (Map.Entry<String, String>) n.next();
					if (actionT.getKey().equals("C"))
					{
						lstOptions.add(new OptionInfo(String.valueOf(actionT
								.getKey()), String.valueOf(actionT.getValue()), true));
					}
					else
					{
						lstOptions.add(new OptionInfo(String.valueOf(actionT
								.getKey()), String.valueOf(actionT.getValue()), false));
					}
				}
				lstSelect.add(new SelectInfo(PCMConstant.RULE_SIGN_LIST, lstOptions));


				/************************************************************************************/
				// Secondary rules drop down list
				lstOptions = new ArrayList<OptionInfo>();
				if (lstSecondaryRules.size() > 0) {
					boolean found = false;
					for (int i = 0; i < lstSecondaryRules.size(); i++) {
						if (ruleDetails.getAcceptRuleId()!=null
							&& lstSecondaryRules.get(i).getRuleId().equals(ruleDetails.getAcceptRuleId())) {
							lstOptions.add(new OptionInfo("" + lstSecondaryRules.get(i).getRuleId(),
									lstSecondaryRules.get(i).getRuleName(), true));
							found = true;
						} else {
							lstOptions.add(new OptionInfo("" + lstSecondaryRules.get(i).getRuleId(),
									lstSecondaryRules.get(i).getRuleName(), false));
						}
					}
					if (!found)
						lstOptions.add(0, new OptionInfo("", "", true));
					else
						lstOptions.add(0, new OptionInfo("", "", false));

					lstSelect.add(new SelectInfo(PCMConstant.RULE_LIST_SECONDARY_RULES, lstOptions));
				}
				/************************************************************************************/
				//
				lstOptions = new ArrayList<OptionInfo>();
				if (lstSubTypeFactor != null) {
					boolean found = false;
					Iterator<String> listKeys = lstSubTypeFactor.keySet().iterator();
					for (int i = 0; i < lstSubTypeFactor.size(); i++) {
						String tempKey = listKeys.next();

						if (!SwtUtil.isEmptyOrNull(ruleDetails.getSubType())
							&& tempKey.equals(ruleDetails.getSubType())) {
							lstOptions.add(new OptionInfo(tempKey, lstSubTypeFactor.get(tempKey), true));
							found = true;
						}
						else {
							lstOptions.add(new OptionInfo(tempKey, lstSubTypeFactor.get(tempKey), false));
						}
					}
					if (!found)
						lstOptions.add(0, new OptionInfo("", "", true));
					else
						lstOptions.add(0, new OptionInfo("", "", false));
					// add to selects tag
					lstSelect.add(new SelectInfo(PCMConstant.RULE_LIST_SUB_TYPE_FACTOR, lstOptions));
				}

				// Add the selects node
				responseConstructor.formSelect(lstSelect);

				xmlWriter.endElement(PCMConstant.RULES);

				// Send response to client as XML
				responseHandler.sendResponse(response, xmlWriter.getData());

			} else {
				// Instantiating SwtResponseConstructor
				responseConstructor = new SwtResponseConstructor();
				// Instantiating ResponseHandler
				responseHandler = new ResponseHandler();
				// Get xmlWriter from responseConstructor
				xmlWriter = responseConstructor.getXMLWriter();

				xmlWriter.startElement(PCMConstant.EXCEPTION_TAG);
				responseConstructor.formRequestReply(false,	"errors.details.not.found");
				xmlWriter.endElement(PCMConstant.EXCEPTION_TAG);
				// send response to client as xml
				responseHandler.sendResponse(response, xmlWriter.getData());
				// log error message
//				SwtUtil
//						.logErrorInDatabase(
//								SwtErrorHandler
//										.getInstance()
//										.handleException(
//												new Exception(
//														"Error occurred while getting details Rule. Rule deleted or there are null parameters"),
//												"view", this.getClass()),
//								"", userId, request.getRemoteAddr(), "Y");

			}

		} catch (SwtException ex) {
			// xmlWriter.resetBuffer();
			//	getExceptionXmlData(ex.getMessage());
			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "view", this.getClass()), "",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - SwtException Caught in [view] method : - "
					  + ex.getMessage());
		} catch (Exception ex) {
			// xmlWriter.resetBuffer();
			//getExceptionXmlData(ex.getMessage());
			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "view", this.getClass()), "",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - Exception Caught in [view] method : - "
					  + ex.getMessage());
			ex.printStackTrace();
		} finally {
			// Nullify Objects
			ruleId = null;
			ruleDetails = null;
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			sdf = null;
			modifyDate = null;
			mDate = null;
			systemFormats = null;
			lstOptions = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ view ] - Exit");
		}
	return null;
	}

	/**
	 * save()
	 *
	 * Method to add a rule
	 *
	 * @return
	 */
	@SuppressWarnings("static-access")
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// String variable to hold userId
		String userId = null;
		// get date format
		SimpleDateFormat formatter = null;
		// list of required document types
		// map to hold values of document types
		HashMap<String, String> mapTypes = new HashMap<String, String>();
		// String variable for current language
		String languageId = null;
		String ruleId = null;
		String ruleName = null;
		// variable to hold rule details
		RulesDefinition ruleDef = null;
		String actionSave = null;
		// variable to hold ruleConditionsAction
		String ruleConditionsAction = null;
		// variable to hold ruleConditions
		ArrayList<RuleConditions> ruleConditions = new ArrayList<RuleConditions>();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [save] - " + "Entry");
			ruleId = request.getParameter("ruleId");
			actionSave = request.getParameter("actionSave");
			ruleConditionsAction = request.getParameter("ruleConditionsAction");
			ruleName = request.getParameter("ruleName").trim();
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId =  SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// built rule object
			ruleDef = new RulesDefinition();
			ruleDef.setRuleId(Integer.parseInt(ruleId));
			ruleDef.setRuleName(ruleName);
			ruleDef.setRuleStatus("");
			ruleDef.setRuleType("");
			ruleDef.setLevelCode(15);
			ruleDef.setDescription("");
			ruleDef.setPeriodValue(15);
			ruleDef.setSign("");
			ruleDef.setEditable("Y");
			//ruleDef.setBlocked(blocked);
//			ruleDef.setUpdateAlert(updateAlert);
//			ruleDef.setMissingInfo(missingInfo);
//			ruleDef.setLevelType(levelType);
//			if(!SwtUtil.isEmptyOrNull(acceptRuleId))
//				ruleDef.setAcceptRuleId(Integer.parseInt(acceptRuleId));
//			ruleDef.sethandleProcessedAlerts(handleProcessedAlerts);
//
//
//			ruleDef.setAllCltAccount(allCltAccount);
//			ruleDef.setRuleQuery(ruleQuery);
//			ruleDef.setRuleText(ruleText);
//            ruleDef.setModuleId(moduleId);
			// check if risk factor screen type
//			if (!SwtUtil.isEmptyOrNull(riskFactor)
//					&& "Y".equals(riskFactor)) {
//				ruleDef.setIsRiskFactor("Y");
//				ruleDef.setSubType(getSubType());
//			}
//			else
//				ruleDef.setIsRiskFactor("N");
//
//			if (!SwtUtil.isEmptyOrNull(ruleTextId))
//				ruleDef.setNameTextId(Integer.parseInt(ruleTextId));
//			if (!SwtUtil.isEmptyOrNull(ruleDescId))
//				ruleDef.setDescTextId(Integer.parseInt(ruleDescId));
			ruleDef.setUpdateUser(userId);
			ruleDef.setUpdateDate(SwtUtil.getSystemDatewithTime());
//
//			if (!SwtUtil.isEmptyOrNull(expiryDate))
//			{
//				formatter = new SimpleDateFormat(SwtUtil.getCurrentDateFormat(request.getSession()),Locale.ENGLISH);
//				Date expDate = (Date)formatter.parse(expiryDate);
//				ruleDef.setExpiryDate(expDate);
//			}
//
			// Save the added values in DB through rule manager
			expressionBuilderManager.saveRule(ruleDef, actionSave, "PREDICT");

//			if(selectedDocTypes!=null){
//				if(!SwtUtil.isEmptyOrNull(selectedDocTypes)){
//					String [] docTypeList;
//					List<TypeValues> list = SwtUtil.getListTypeValuesAllFlds(1245, languageId,true);
//					for (TypeValues value : list) {
//						mapTypes.put(value.getLabelFromDict(), value.getCode());
//					}
//					docTypeList=selectedDocTypes.split(",");
//					for (String docName : docTypeList) {
//						FatcaRequiredDocument doc = new FatcaRequiredDocument();
//						FatcaRequiredDocument.Id id = new FatcaRequiredDocument.Id();
//						id.setDocumentTypeId(mapTypes.get(docName));
//						if(!SwtUtil.isEmptyOrNull(ruleId)){
//							id.setRuleId(Integer.parseInt(ruleId));
//						}
//						doc.setId(id);
//						requiredDocList.add(doc);
//					}
//				}
//				ruleDef.setRequiredDocList(requiredDocList);
//				ruleDefMgr.saveRuleRequiredDocs(requiredDocList, ruleName, ruleId, actionSave);
//			}

			if(!SwtUtil.isEmptyOrNull(ruleConditionsAction) && ruleConditionsAction.equals("toSave")){
				expressionBuilderManager.saveRulesConditions(ruleConditions, ruleDef.getRuleId(), actionSave, false);
			}
			else if(!SwtUtil.isEmptyOrNull(ruleConditionsAction) && ruleConditionsAction.equals("toUpdate")){
				expressionBuilderManager.saveRulesConditions(ruleConditions, ruleDef.getRuleId(), actionSave, true); //true: to indicate that the condition will be just updated not deleted and reinserted
			}

			// display new data
			display();

		} catch (SwtException ex) {
			// xmlWriter.resetBuffer();
			//getExceptionXmlData(ex.getMessage());
			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "save", this.getClass()), "",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - SwtException Catched in [save] method : - "
					  + ex.getMessage());
		} catch (Exception ex) {
			// xmlWriter.resetBuffer();
			//	getExceptionXmlData(ex.getMessage());
			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "save", this.getClass()), "",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - Exception Catched in [save] method : - "
					  + ex.getMessage());
			ex.printStackTrace();
		} finally {
			// Nullify objects
			userId = null;
			ruleName = null;
			actionSave = null;
			// Debug message
			log.debug(this.getClass().getName() + " - [save] - Exit");
		}
		return  null;
	}


	/**
	 * This method is used to display the details of Rule on view screen
	 *
	 * @throws SwtException
	 */
	public String add() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// Methods local variable declaration
		// To send response to client
		ResponseHandler responseHandler = null;
		// To build logout response
		SwtResponseConstructor responseConstructor = null;
		// To build logout response
		SwtXMLWriter xmlWriter = null;
		// Variable String to hold UserId
		String userId = null;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		// String variable to hold componentId
		String componentId = null;
		LinkedHashMap<String, String> ruleStatusList;
		LinkedHashMap<String, String> periodTypeList;
		LinkedHashMap<String, String> levelCodeList;
		LinkedHashMap<String, String> ruleTypeList;
		//get languageId
		String languageId = null;
		// HashMap to load sign of transactions
		HashMap<String, String> ruleSignList = null;
		// variable to hold list of secondary rules
		List<RulesDefinition> lstSecondaryRules = null;
		// HashMap to hold list Sub Type factors label
		HashMap<String, String> lstSubTypeFactor = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - " + "Entry");

			// Retrieves current user
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// check screen type 
//			if (!SwtUtil.isEmptyOrNull(riskFactor) && "Y".equals(riskFactor))
//				componentId = PCMConstant.RISK_FACTOR_COMPONENT_ID;	
//			else
			componentId = PCMConstant.RULE_COMPONENT_ID;

			ruleStatusList = new LinkedHashMap<String, String>();
			periodTypeList = new LinkedHashMap<String, String>();
			levelCodeList = new LinkedHashMap<String, String>();
			ruleTypeList = new LinkedHashMap<String, String>();

			ruleStatusList = expressionBuilderManager.getTypeValuesList(30,languageId, true);
			periodTypeList = expressionBuilderManager.getTypeValuesList(850,languageId, true);
			levelCodeList = expressionBuilderManager.getTypeValuesList(520, languageId, true);

			ruleTypeList = expressionBuilderManager.getTypeValuesList(510,languageId, true);
			// Get list from S_CFG_TYPE_VALUES and S_DICTIONARY
//			lstSubTypeFactor = (HashMap<String, String>) SwtUtil.getListTypeValues(
//					513, languageId, true);

			// Instantiate profileSignList HashMap 
			ruleSignList = new HashMap<String, String>();
			// HashMap to load profileSignList
//			ruleSignList = SwtUtil.getListTypeValues(60, languageId, true);
//			// Get list secondary rules
//			lstSecondaryRules = ruleDefMgr.getListSecondaryRules(getModuleId());
			// Instantiate ResponseHandler
			responseHandler = new ResponseHandler();
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.RULES);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
//			responseConstructor.createElement(SwtConstants.HELPURL,	SwtConstants.HELP_URL);
			responseConstructor.createElement(PCMConstant.ADDRULEURL, PCMConstant.ADD_RULE_URL);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();

			// status drop down list
			lstOptions = new ArrayList<OptionInfo>();

			Set<Map.Entry<String, String>> status = ruleStatusList.entrySet();

			Iterator<Map.Entry<String, String>> j = status.iterator();

			while (j.hasNext()) {
				Map.Entry<String, String> actionT = (Map.Entry<String, String>) j.next();
				lstOptions.add(new OptionInfo(String.valueOf(actionT
						.getKey()), String.valueOf(actionT.getValue()), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.RULESTATUSLIST, lstOptions));


			// period type drop down list
			lstOptions = new ArrayList<OptionInfo>();

			Set<Map.Entry<String, String>> periodeType = periodTypeList.entrySet();

			Iterator<Map.Entry<String, String>> k = periodeType.iterator();

			while (k.hasNext()) {
				Map.Entry<String, String> actionT = (Map.Entry<String, String>) k.next();
				if (!actionT.getKey().equalsIgnoreCase("YEAR"))
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
				else
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), true));
			}
			lstSelect.add(new SelectInfo(PCMConstant.PERIODETYPELIST, lstOptions));

			// level code drop down list
			lstOptions = new ArrayList<OptionInfo>();

			Set<Map.Entry<String, String>> levelCode = levelCodeList.entrySet();

			Iterator<Map.Entry<String, String>> l = levelCode.iterator();

			while (l.hasNext()) {
				Map.Entry<String, String> actionT = (Map.Entry<String, String>) l.next();
				if (String.valueOf(actionT.getKey()).equals("1"))
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), true));
				else
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.LEVELCODELIST, lstOptions));

			// rule type drop down list
			lstOptions = new ArrayList<OptionInfo>();

			Set<Map.Entry<String, String>> ruleType = ruleTypeList.entrySet();

			Iterator<Map.Entry<String, String>> m = ruleType.iterator();

			while (m.hasNext()) {
				Map.Entry<String, String> actionT = (Map.Entry<String, String>) m.next();
				lstOptions.add(new OptionInfo(String.valueOf(actionT
						.getKey()), String.valueOf(actionT.getValue()), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.RULETYPELIST, lstOptions));

			// sign drop down list
			lstOptions = new ArrayList<OptionInfo>();

			Set<Map.Entry<String, String>> sign = ruleSignList.entrySet();

			Iterator<Map.Entry<String, String>> o = sign.iterator();

			while (o.hasNext()) {
				Map.Entry<String, String> actionT = (Map.Entry<String, String>) o.next();
				if (actionT.getKey().equals("C"))
				{
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), true));
				}
				else
				{
					lstOptions.add(new OptionInfo(String.valueOf(actionT
							.getKey()), String.valueOf(actionT.getValue()), false));
				}
			}
			lstSelect.add(new SelectInfo(PCMConstant.RULE_SIGN_LIST, lstOptions));

			/************************************************************************************/
			// Secondary rules drop down list
			lstOptions = new ArrayList<OptionInfo>();
//			if(lstSecondaryRules.size()>0) {
//				lstOptions.add(new OptionInfo("", "", true));
//				for(int i=0; i<lstSecondaryRules.size();i++) {
//					lstOptions.add(new OptionInfo("" + lstSecondaryRules.get(i).getRuleId(),
//							lstSecondaryRules.get(i).getRuleName(), false));
//				}
//				lstSelect.add(new SelectInfo(PCMConstant.RULE_LIST_SECONDARY_RULES, lstOptions));
//			}
			/************************************************************************************/
			//
			lstOptions = new ArrayList<OptionInfo>();
			// build select tag for Occupation-Profession
			lstOptions.add(new OptionInfo("", "", true));
//			if (lstSubTypeFactor != null) {
//				Iterator<String> listKeys = lstSubTypeFactor.keySet().iterator();
//				for (int i = 0; i < lstSubTypeFactor.size(); i++) {
//					String tempKey = listKeys.next();
//
//					lstOptions.add(new OptionInfo(tempKey, lstSubTypeFactor.get(tempKey), false));
//				}
//				// add to selects tag
//				lstSelect.add(new SelectInfo(PCMConstant.RULE_LIST_SUB_TYPE_FACTOR, lstOptions));
//			}

			// Add the selects node
			responseConstructor.formSelect(lstSelect);

			xmlWriter.endElement(PCMConstant.RULES);

			// Send response to client as XML
			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException ex) {
			// xmlWriter.resetBuffer();
//			getExceptionXmlData(ex.getMessage());
//			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "add", this.getClass()), moduleId,
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - SwtException Caught in [add] method : - "
					  + ex.getMessage());
		} catch (Exception ex) {
			// xmlWriter.resetBuffer();
//			getExceptionXmlData(ex.getMessage());
//			// log error message
//			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
//					.handleException(ex, "add", this.getClass()), moduleId,
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName()
					  + " - Exception Caught in [add] method : - "
					  + ex.getMessage());
			ex.printStackTrace();
		} finally {
			// Nullify Objects
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			languageId = null;
			userId = null;
			lstOptions = null;
			lstSelect = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - Exit");
		}

		return null;

	}



}