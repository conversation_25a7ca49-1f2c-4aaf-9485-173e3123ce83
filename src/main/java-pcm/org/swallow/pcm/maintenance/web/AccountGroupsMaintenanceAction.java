/*
 * @(#)AccountGroupsMaintenanceAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.type.CollectionType;
import org.codehaus.jackson.map.type.TypeFactory;
import org.codehaus.jackson.type.JavaType;
import org.swallow.config.springMVC.BaseController;
import org.swallow.control.model.FacilityAccess;
import org.swallow.control.service.RoleManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.authorization.MaintenanceAuthUtils;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.AccountGroupCutoff;
import org.swallow.pcm.maintenance.model.AccountInGroup;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.Reserve;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.model.core.kv.KVType;
import org.swallow.pcm.maintenance.model.core.kv.TabKVType;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.pcm.maintenance.service.SpreadProfilesMaintenanceManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

import com.google.gson.Gson;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
/**
 * <AUTHOR>
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/accountGroupsPCM", "/accountGroupsPCM.do"})
public class AccountGroupsMaintenanceAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("addScreen", "jsp/pc/maintenance/accountgroupsadd");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/maintenance/accountgroups");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("expressionBuilder", "jsp/pc/maintenance/expressionbuilder");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();



	@Autowired
	private AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = null;


	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(AccountGroupsMaintenanceAction.class);

	public void setAccountGroupsMaintenanceManager(
			AccountGroupsMaintenanceManager accountGroupsMaintenanceManager) {
		this.accountGroupsMaintenanceManager = accountGroupsMaintenanceManager;
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "addScreen":
				return addScreen();
			case "viewScreen":
				return viewScreen();
			case "add":
				return add();
			case "save":
				return save();
			case "delete":
				return delete();
			case "checkAccountInPayment":
				return checkAccountInPayment();
			case "getAvailableAccounts":
				return getAvailableAccounts();

		}


		return unspecified();
	}


	protected String unspecified() throws Exception {

		log.debug(this.getClass().getName() + " - [Unspecified] - "
				+ "returns to displayListByEntity");
		//accountGroupsMaintenanceManager.saveNewDetailInRemoteDB();

		//return display();
		return getView("success");
	}


	/**
	 * Action method to display account groups
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		List<AccountGroup> accountGroupsDetails = new ArrayList<AccountGroup>();
		String errorMessage = null;
		String languageId = null;
		SystemFormats systemFormats = null;
		try {

			log.debug(this.getClass().getName() + " - [display] - "
					+ "Entry");
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			accountGroupsDetails = accountGroupsMaintenanceManager.getAccountGroupDetailList(null);
			request.setAttribute("accountGroupsDetailsList", accountGroupsDetails);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// build XML response
			return sendDisplayResponse( accountGroupsDetails, languageId, systemFormats);

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":"
					+ swtexp.getStackTrace()[0].getLineNumber() + " "
					+ swtexp.getMessage();
			log.error(this.getClass().getName()
					+ " - [displayAccountGroupsGrid] - SwtException -"
					+ errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());


		}  finally {
			languageId = null;
			systemFormats = null;
			accountGroupsDetails = null;
			// log debug message
			log.debug(this.getClass().getName()+ " - [display] - Exit");
		} return null;
	}
	/**
	 * This method forms the xml for displaying the rules list.
	 * @param languageId
	 *            - passing languageId
	 * @param systemFormats
	 *            - passing system formats date
	 * @return
	 */
	public String sendDisplayResponse(List<AccountGroup> accountGroupsDetails, String languageId,
									  SystemFormats systemFormats) throws SwtException {

		// To send response to client
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;

		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "PCAccountMaintenance";
			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			// The menu item to the relevant match
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PCM_ACCOUNT_GROUPS_MAINTENANCE+"", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsAccountGroupWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));


			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsAccountGroupWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID);

			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request
					.getSession()));
			xmlWriter.startElement(PCMConstant.ACCOUNT_MAINTENANCE);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns, ""));

			// form rows (records)
			responseConstructor.formRowsStart(accountGroupsDetails.size());

			for (Iterator<AccountGroup> it = accountGroupsDetails.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				AccountGroup acctGrp = (AccountGroup) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.CURRENCY, acctGrp.getCurrencyCode());
				responseConstructor.createRowElement(PCMConstant.ORDINAL, (acctGrp.getOrdinal() != null) ? acctGrp.getOrdinal()+ "" : "");
				responseConstructor.createRowElement(PCMConstant.ACC_GP_ID, acctGrp.getId().getAccGrpId());
				responseConstructor.createRowElement(PCMConstant.ACC_GP_NAME, acctGrp.getDescription());
				responseConstructor.createRowElement(PCMConstant.NO_ACCOUNTS, accountGroupsMaintenanceManager.accountListCount(acctGrp.getId().getAccGrpId()));
				responseConstructor.createRowElement(PCMConstant.SPREAD_ID, acctGrp.getSpreadProfileId());
				responseConstructor.createRowElement(PCMConstant.KICK_OFF_TIME, acctGrp.getKickoff());
				responseConstructor.createRowElement(PCMConstant.EOD_TIME, acctGrp.getEodPhaseBegins());
				responseConstructor.createRowElement(PCMConstant.COB_TIME, acctGrp.getCobCutoff());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.ACCOUNT_MAINTENANCE);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName()
					+ " - SwtException Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName()
					+ " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			ex.printStackTrace();
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ sendDisplayResponse ] - Exit");
		}
		return null;

	}



	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getAccountGroupsGridColumns(String width, String columnOrder, String hiddenColumns, String fromSource) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getRulesDefGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				if (fromSource == "gridCutOff") {
					width =   PCMConstant.TESTORDER + "=120,"
							+PCMConstant.CUTTOFFTIME + "=150,"
							+ PCMConstant.RULETEXT + "=520,"
							+ PCMConstant.RULEQUERY + "=0,"
							+ PCMConstant.RULE_CONDITION + "=0,"
							+ PCMConstant.LOGTEXT + "=250";
				}
				else if (fromSource == "gridReserve") {
					width =    PCMConstant.ID_RESERVE + "=0,"
							+ PCMConstant.TIME + "=150,"
							+PCMConstant.RESERVE_BALANCE + "=230,"
							+ PCMConstant.USECL + "=120";
				}
				else if (fromSource== "gridSpread") {
					width =  PCMConstant.PROCESS_NAME + "=180," + PCMConstant.TIME + "=90,"
							+ PCMConstant.TARGET + "=110,"
							+ PCMConstant.PROCESS + "=100,"
							+ PCMConstant.CATEGORIES + "=260";
				}
				else if (fromSource == "gridAccounts") {
					width =  PCMConstant.ACCT_ID_NAME + "=300,"
							+ PCMConstant.ENTITY + "=120,"
							+ PCMConstant.TYPE + "=70,"
							+ PCMConstant.LEVEL + "=75,"
							+ PCMConstant.BIC + "=130,"
							+ PCMConstant.ILMCB + "=90";
				} else {
					// default width for columns
					/********summary grid*********/
					width =   PCMConstant.CURRENCY + "=120,"
							+ PCMConstant.ORDINAL + "=80,"
							+ PCMConstant.ACC_GP_ID + "=180,"
							+ PCMConstant.ACC_GP_NAME + "=200,"
							+ PCMConstant.NO_ACCOUNTS + "=120,"
							+ PCMConstant.SPREAD_ID + "=160,"
							+ PCMConstant.KICK_OFF_TIME + "=140,"
							+ PCMConstant.EOD_TIME + "=180,"
							+ PCMConstant.COB_TIME + "=120";
				}
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				if (fromSource == "gridCutOff") {
					columnOrder =   PCMConstant.TESTORDER + ","
							+PCMConstant.CUTTOFFTIME + ","
							+ PCMConstant.RULETEXT + ","
							+ PCMConstant.RULEQUERY + ","
							+ PCMConstant.RULE_CONDITION + ","
							+ PCMConstant.LOGTEXT ;
				}
				else if (fromSource == "gridReserve") {
					columnOrder =
							PCMConstant.ID_RESERVE + ","
									+PCMConstant.TIME + ","
									+PCMConstant.RESERVE_BALANCE + ","
									+ PCMConstant.USECL ;
				}
				else if (fromSource== "gridSpread") {
					columnOrder = PCMConstant.PROCESS_NAME + "," + PCMConstant.TIME + ","
							+ PCMConstant.TARGET + ","
							+ PCMConstant.PROCESS + ","
							+ PCMConstant.CATEGORIES;
				}
				else if (fromSource == "gridAccounts") {
					columnOrder =
							PCMConstant.ACCT_ID_NAME + ","
									+ PCMConstant.ENTITY + ","
									+ PCMConstant.TYPE + ","
									+ PCMConstant.BIC + ","
									+ PCMConstant.LEVEL + ","
									+ PCMConstant.ILMCB ;
				} else {
					// Default values for column order
					columnOrder = PCMConstant.CURRENCY + ","
							+ PCMConstant.ORDINAL + ","
							+ PCMConstant.ACC_GP_ID + ","
							+ PCMConstant.ACC_GP_NAME + ","
							+ PCMConstant.NO_ACCOUNTS+ ","
							+ PCMConstant.SPREAD_ID+ ","
							+ PCMConstant.KICK_OFF_TIME+ ","
							+ PCMConstant.EOD_TIME+ ","
							+ PCMConstant.COB_TIME;
				}
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				if (fromSource == "gridCutOff") {
					if (order.equals(PCMConstant.TESTORDER))
						lstColumns.add(new ColumnInfo(
								PCMConstant.TESTORDER_COLUMN_HEADER,
								PCMConstant.TESTORDER,
								PCMConstant.COLUMN_TYPE_NUMBER,
								0,
								Integer.parseInt(widths.get(PCMConstant.TESTORDER)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.TESTORDER)));
					if (order.equals(PCMConstant.CUTTOFFTIME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.CUTTOFFTIME_COLUMN_HEADER,
								PCMConstant.CUTTOFFTIME,
								PCMConstant.COLUMN_TYPE_STRING,
								1,
								Integer.parseInt(widths.get(PCMConstant.CUTTOFFTIME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.CUTTOFFTIME)));
					if (order.equals(PCMConstant.RULETEXT))
						lstColumns.add(new ColumnInfo(
								PCMConstant.RULETEXT_COLUMN_HEADER,
								PCMConstant.RULETEXT,
								PCMConstant.COLUMN_TYPE_STRING,
								2,
								Integer.parseInt(widths.get(PCMConstant.RULETEXT)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.RULETEXT)));
					if (order.equals(PCMConstant.RULEQUERY))
						lstColumns.add(new ColumnInfo(
								PCMConstant.RULEQUERY_COLUMN_HEADER,
								PCMConstant.RULEQUERY,
								PCMConstant.COLUMN_TYPE_STRING,
								2,
								0,
								false,
								false, false));
					if (order.equals(PCMConstant.RULE_CONDITION))
						lstColumns.add(new ColumnInfo(
								PCMConstant.RULE_CONDITION,
								PCMConstant.RULE_CONDITION,
								PCMConstant.COLUMN_TYPE_STRING,
								3,
								0,
								false,
								false, false));
					if (order.equals(PCMConstant.LOGTEXT))
						lstColumns.add(new ColumnInfo(
								PCMConstant.LOGTEXT_COLUMN_HEADER,
								PCMConstant.LOGTEXT,
								PCMConstant.COLUMN_TYPE_STRING,
								4,
								Integer.parseInt(widths.get(PCMConstant.LOGTEXT)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.LOGTEXT)));

				}
				else if (fromSource == "gridReserve") {
					if (order.equals(PCMConstant.ID_RESERVE))
						lstColumns.add(new ColumnInfo(
								PCMConstant.ID_RESERVE_COLUMN_HEADER,
								PCMConstant.ID_RESERVE,
								PCMConstant.COLUMN_TYPE_STRING,
								0,
								0,
								false,
								false, false));
					if (order.equals(PCMConstant.TIME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.TIME_COLUMN_HEADER,
								PCMConstant.TIME,
								PCMConstant.COLUMN_TYPE_STRING,
								0,
								Integer.parseInt(widths.get(PCMConstant.TIME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.TIME)));
					if (order.equals(PCMConstant.RESERVE_BALANCE))
						lstColumns.add(new ColumnInfo(
								PCMConstant.RESERVE_BALANCE_COLUMN_HEADER,
								PCMConstant.RESERVE_BALANCE,
								PCMConstant.COLUMN_TYPE_NUMBER,
								0,
								Integer.parseInt(widths.get(PCMConstant.RESERVE_BALANCE)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.RESERVE_BALANCE)));
					if (order.equals(PCMConstant.USECL))
						lstColumns.add(new ColumnInfo(
								PCMConstant.USECL_COLUMN_HEADER,
								PCMConstant.USECL,
								PCMConstant.COLUMN_TYPE_CHECK,
								0,
								Integer.parseInt(widths.get(PCMConstant.USECL)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.USECL)));
				}
				else if (fromSource== "gridSpread") {
					if (order.equals(PCMConstant.PROCESS_NAME))
						lstColumns.add(new ColumnInfo(PCMConstant.PROCESS_NAME_HEADER,
								PCMConstant.PROCESS_NAME,
								PCMConstant.COLUMN_TYPE_STRING,
								1,
								Integer.parseInt(widths.get(PCMConstant.PROCESS_NAME)),
								false, true, hiddenColumnsMap.get(PCMConstant.PROCESS_NAME)));
					if (order.equals(PCMConstant.TIME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.TIME_COLUMN_HEADER,
								PCMConstant.TIME,
								PCMConstant.COLUMN_TYPE_STRING,
								2,
								Integer.parseInt(widths.get(PCMConstant.TIME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.TIME)));
					if (order.equals(PCMConstant.TARGET))
						lstColumns.add(new ColumnInfo(
								PCMConstant.TARGET_COLUMN_HEADER,
								PCMConstant.TARGET,
								PCMConstant.COLUMN_TYPE_NUMBER,
								3,
								Integer.parseInt(widths.get(PCMConstant.TARGET)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.TARGET)));
					if (order.equals(PCMConstant.PROCESS))
						lstColumns.add(new ColumnInfo(
								PCMConstant.PROCESS_COLUMN_HEADER,
								PCMConstant.PROCESS,
								PCMConstant.COLUMN_TYPE_STRING,
								4,
								Integer.parseInt(widths.get(PCMConstant.PROCESS)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.PROCESS)));
					if (order.equals(PCMConstant.CATEGORIES))
						lstColumns.add(new ColumnInfo(
								PCMConstant.CATEGORIES_COLUMN_HEADER,
								PCMConstant.CATEGORIES,
								PCMConstant.COLUMN_TYPE_STRING,
								5,
								Integer.parseInt(widths.get(PCMConstant.CATEGORIES)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.CATEGORIES)));
				}
				else if (fromSource == "gridAccounts") {

					if (order.equals(PCMConstant.ACCT_ID_NAME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.ACCT_ID_NAME_COLUMN_HEADER,
								PCMConstant.ACCT_ID_NAME,
								PCMConstant.COLUMN_TYPE_STRING,
								1,
								Integer.parseInt(widths.get(PCMConstant.ACCT_ID_NAME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.ACCT_ID_NAME)));
					if (order.equals(PCMConstant.ENTITY))
						lstColumns.add(new ColumnInfo(
								PCMConstant.ENTITY_COLUMN_HEADER,
								PCMConstant.ENTITY,
								PCMConstant.COLUMN_TYPE_STRING,
								2,
								Integer.parseInt(widths.get(PCMConstant.ENTITY)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.ENTITY)));
					if (order.equals(PCMConstant.TYPE))
						lstColumns.add(new ColumnInfo(
								PCMConstant.TYPE_COLUMN_HEADER,
								PCMConstant.TYPE,
								PCMConstant.COLUMN_TYPE_STRING,
								3,
								Integer.parseInt(widths.get(PCMConstant.TYPE)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.TYPE)));

					if (order.equals(PCMConstant.LEVEL))
						lstColumns.add(new ColumnInfo(
								PCMConstant.LEVEL_COLUMN_HEADER,
								PCMConstant.LEVEL,
								PCMConstant.COLUMN_TYPE_STRING,
								4,
								Integer.parseInt(widths.get(PCMConstant.LEVEL)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.LEVEL)));
					if (order.equals(PCMConstant.BIC))
						lstColumns.add(new ColumnInfo(
								PCMConstant.BIC_COLUMN_HEADER,
								PCMConstant.BIC,
								PCMConstant.COLUMN_TYPE_STRING,
								5,
								Integer.parseInt(widths.get(PCMConstant.BIC)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.BIC)));
					if (order.equals(PCMConstant.ILMCB))
						lstColumns.add(new ColumnInfo(
								PCMConstant.ILMCB_COLUMN_HEADER,
								PCMConstant.ILMCB,
								PCMConstant.COLUMN_TYPE_CHECK,
								6,
								Integer.parseInt(widths.get(PCMConstant.ILMCB)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.ILMCB)));

				}
				else {
					// rule ID type column
					if (order.equals(PCMConstant.CURRENCY))
						lstColumns
								.add(new ColumnInfo(
										PCMConstant.CURRENCY_COLUMN_HEADER,
										PCMConstant.CURRENCY,
										PCMConstant.COLUMN_TYPE_STRING,
										0,
										Integer.parseInt(widths.get(PCMConstant.CURRENCY)),
										false,
										true, hiddenColumnsMap.get(PCMConstant.CURRENCY)));

					// rule name type column
					if (order.equals(PCMConstant.ORDINAL))
						lstColumns
								.add(new ColumnInfo(
										PCMConstant.ORDER_COLUMN_HEADER,
										PCMConstant.ORDINAL,
										SwtConstants.COLUMN_TYPE_NUMBER,
										1,
										Integer.parseInt(widths.get(PCMConstant.ORDINAL)),
										true,
										true, hiddenColumnsMap.get(PCMConstant.ORDINAL)));

					// ruleStatus column
					if (order.equals(PCMConstant.ACC_GP_ID))
						lstColumns.add(new ColumnInfo(
								PCMConstant.ACC_GP_ID_COLUMN_HEADER,
								PCMConstant.ACC_GP_ID,
								PCMConstant.COLUMN_TYPE_STRING,
								2,
								Integer.parseInt(widths.get(PCMConstant.ACC_GP_ID)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.ACC_GP_ID)));

					// ruleType column
					if (order.equals(PCMConstant.ACC_GP_NAME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.ACC_GP_NAME_COLUMN_HEADER,
								PCMConstant.ACC_GP_NAME,
								PCMConstant.COLUMN_TYPE_STRING,
								3,
								Integer.parseInt(widths.get(PCMConstant.ACC_GP_NAME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.ACC_GP_NAME)));

					// levelCode column
					if (order.equals(PCMConstant.NO_ACCOUNTS))
						lstColumns.add(new ColumnInfo(
								PCMConstant.NO_ACCOUNTS_COLUMN_HEADER,
								PCMConstant.NO_ACCOUNTS,
								PCMConstant.COLUMN_TYPE_NUMBER,
								4,
								Integer.parseInt(widths.get(PCMConstant.NO_ACCOUNTS)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.NO_ACCOUNTS)));
					// level type column
					if (order.equals(PCMConstant.SPREAD_ID))
						lstColumns.add(new ColumnInfo(
								PCMConstant.SPREAD_ID_COLUMN_HEADER,
								PCMConstant.SPREAD_ID,
								PCMConstant.COLUMN_TYPE_STRING,
								5,
								Integer.parseInt(widths.get(PCMConstant.SPREAD_ID)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.SPREAD_ID)));

					if (order.equals(PCMConstant.KICK_OFF_TIME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.KICK_OFF_TIME_COLUMN_HEADER,
								PCMConstant.KICK_OFF_TIME,
								PCMConstant.COLUMN_TYPE_STRING,
								5,
								Integer.parseInt(widths.get(PCMConstant.KICK_OFF_TIME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.KICK_OFF_TIME)));
					if (order.equals(PCMConstant.EOD_TIME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.EOD_TIME_COLUMN_HEADER,
								PCMConstant.EOD_TIME,
								PCMConstant.COLUMN_TYPE_STRING,
								5,
								Integer.parseInt(widths.get(PCMConstant.EOD_TIME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.EOD_TIME)));
					if (order.equals(PCMConstant.COB_TIME))
						lstColumns.add(new ColumnInfo(
								PCMConstant.COB_TIME_COLUMN_HEADER,
								PCMConstant.COB_TIME,
								PCMConstant.COLUMN_TYPE_STRING,
								5,
								Integer.parseInt(widths.get(PCMConstant.COB_TIME)),
								true,
								true, hiddenColumnsMap.get(PCMConstant.COB_TIME)));
				}
			}

		} catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getAccountGroupsGridColumns] method : - "
							+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountGroupsGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAccountGroupsGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}
	public String getAvailableAccounts() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ArrayList<AcctMaintenance> accountList = null;
		ResponseHandler responseHandler = null;
		String screenName= null;
		String width = null;
		// String variable to column order
		String columnOrder = null;
		String hiddenColumns= null;
		SystemFormats systemFormats= null;
		String acctClass= null;
		String currencyCode= null;
		// To build logout response
		SwtResponseConstructor responseConstructor = null;
		// To build logout response
		SwtXMLWriter xmlWriter = null;
		String componentId = PCMConstant.ACCOUNT_MAINTENANCE;
		ArrayList ordinalId = new ArrayList<>();
		Iterator ordinalIdIterator = null;
		int index = 0;
		int maxOrdinal;
		String accountGroup = null;
		ArrayList<AcctMaintenance> listAccountDelete ;
		ArrayList<AcctMaintenance> listAccountDeleteToIterate = null ;
		boolean isFromMaintenanceEventAmendMode = false;
		boolean isFromMaintenanceEvent = false;
		String maintEventId = null;
		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - " + "Entry");

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			currencyCode = request.getParameter("currencyCode");

			accountGroup = request.getParameter("accountGroup");



			isFromMaintenanceEventAmendMode = Boolean.parseBoolean(request.getParameter("isFromMaintenanceEventAmendMode"));
			isFromMaintenanceEvent = Boolean.parseBoolean( request.getParameter("isFromMaintenanceEvent"));

//		isFromMaintenanceEvent =false;
			if(!isFromMaintenanceEvent)
				accountList = new ArrayList<AcctMaintenance>(accountGroupsMaintenanceManager.getAccountListDetails(currencyCode ));
			else {
				maintEventId = request.getParameter("maintEventId");
				accountList = new ArrayList<AcctMaintenance>(accountGroupsMaintenanceManager.getAccountListDetailsMaintEvent(currencyCode,accountGroup));

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");


				ArrayList<AcctMaintenance> accountsInGroupList = null;
				//Get list of cut off rule
				//Get list of account in this group from P_account
				accountsInGroupList = (ArrayList<AcctMaintenance>) accountGroupsMaintenanceManager.getAccountsInGroupList(accountGroup);



				MaintenanceEventDetails detailsAccountInGroup = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_IN_GROUP");
				if(detailsAccountInGroup != null) {
					String jsonAccountInGroup= detailsAccountInGroup.getNewState();

					//
					ObjectMapper objectMapper = new ObjectMapper();
					TypeFactory typeFactory = objectMapper.getTypeFactory();
					CollectionType collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountInGroup.class);
					JavaType mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
					HashMap<String, ArrayList<AccountInGroup>> accountMap = objectMapper.readValue(jsonAccountInGroup, mapType);


					ArrayList<AccountInGroup> listInGroupAccountAdd = accountMap.get("add");
					ArrayList<AccountInGroup> listInGroupAccountDelete =  accountMap.get("delete");

					ArrayList<AcctMaintenance> listAccountAdd = null ;



					listAccountAdd = new ArrayList<AcctMaintenance>();
					listAccountDelete = new ArrayList<AcctMaintenance>();


					AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
							.getBean("acctMaintenanceManager");
					SystemInfo systemInfo = new SystemInfo();


					for (int i = 0; i < listInGroupAccountAdd.size(); i++) {
						listAccountAdd.add(acctMaintenanceManager.getEditableDataDetailList(
								listInGroupAccountAdd.get(i).getId().getEntityId(), listInGroupAccountAdd.get(i).getId().getHostId(),listInGroupAccountAdd.get(i).getId().getAccountId(),systemInfo, SwtUtil.getCurrentSystemFormats(request.getSession()))) ;

					}

					for (int i = 0; i < listInGroupAccountDelete.size(); i++) {
						listAccountDelete.add(acctMaintenanceManager.getEditableDataDetailList(
								listInGroupAccountDelete.get(i).getId().getEntityId(), listInGroupAccountDelete.get(i).getId().getHostId(),listInGroupAccountDelete.get(i).getId().getAccountId(),systemInfo, SwtUtil.getCurrentSystemFormats(request.getSession()))) ;

					}
					listAccountDeleteToIterate = listAccountDelete;



					// Add new Reserve records
					accountsInGroupList.addAll(listAccountAdd);

					// Delete existing Reserve records
					accountsInGroupList = accountsInGroupList.stream()
							.filter(r -> !listAccountDelete.stream().map(AcctMaintenance::getId).anyMatch(id -> id.equals(r.getId())))
							.collect(Collectors.toCollection(ArrayList::new));


					ArrayList<AcctMaintenance> finalList = accountsInGroupList;
					accountList = accountList.stream()
							.filter(r -> !finalList.stream().map(AcctMaintenance::getId).anyMatch(id -> id.equals(r.getId())))
							.collect(Collectors.toCollection(ArrayList::new));
				}

			}


			ordinalId = (ArrayList) accountGroupsMaintenanceManager.getOrdinalByCurrency(currencyCode);
			ArrayList<SpreadProcessPoint> spreadProcessPointList = null;
			SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil.getBean("spreadProfilesMaintenanceManager");
			spreadProcessPointList = new ArrayList<SpreadProcessPoint>(spreadProfilesMaintenanceManager.getSpreadProcessPointList(request, currencyCode));

			// get accounts not in this group from AcctMaintenance

			ordinalIdIterator =  ordinalId.iterator();
			ArrayList ordinalList = new ArrayList();
			AccountGroup rowId = null;
			while (ordinalIdIterator.hasNext()) {
				rowId =(AccountGroup) ordinalIdIterator.next();
				if(rowId.getOrdinal() != null) {
					ordinalList.add(rowId.getOrdinal());
					index++;
				}

			}
			if (ordinalList.size() > 0) {
				maxOrdinal = (Integer) Collections.max(ordinalList) + 1;
			} else {
				maxOrdinal = 1;
			}

			xmlWriter.startElement(componentId);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.ORDINAL_LIST, ordinalList.toString());
			responseConstructor.createElement(PCMConstant.MAX_ORDINAL, maxOrdinal);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			/*****SpreadProfile Combo***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection spreadProfileList = getSpreadProfileList(currencyCode);
			Iterator j = spreadProfileList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SPREAD_LIST, lstOptions));
			/*****TargetCalculation Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			Collection targetCalculationList = getTargetCalculation();
			j = targetCalculationList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.TARGET_CALCULATION_LIST, lstOptions));
			responseConstructor.formSelect(lstSelect);
			responseConstructor.formGridStart("AccountsNotGrpGrid");


			// form paging details
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns, "gridAccounts"));

			// form column details


			// Accounts not in this group Grid
			responseConstructor.formRowsStart(accountList.size());

			if(isFromMaintenanceEvent) {
				HashMap<String, String> att = null;


				for (Iterator<AcctMaintenance> it = accountList.iterator(); it.hasNext();) {
					// Obtain rules definition tag from iterator
					AcctMaintenance acctGrp = (AcctMaintenance) it.next();
					att = new HashMap<String, String>();
					boolean accountExist = Optional.ofNullable(listAccountDeleteToIterate)
							.map(list -> list.stream()
									.anyMatch(r -> r.getId().getAccountId().equals(acctGrp.getId().getAccountId())))
							.orElse(false);
					if(accountExist) {
						att.put("isDeletedRow", "Y");
					}else {
						att.put("isDeletedRow", "N");
					}

					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.ACCT_ID_NAME, acctGrp.getId().getAccountId() + " - " + acctGrp.getAcctname(), att);
					responseConstructor.createRowElement(PCMConstant.ENTITY, acctGrp.getId().getEntityId(), att);
					responseConstructor.createRowElement(PCMConstant.TYPE, (acctGrp.getAccttype().equals("C")) ? "Cash" : "Custodian", att);
					responseConstructor.createRowElement(PCMConstant.LEVEL, (acctGrp.getAcctlevel().equals("M")) ? "Main" : "Sub", att);
					responseConstructor.createRowElement(PCMConstant.BIC, acctGrp.getAcctbiccode(), att);
					responseConstructor.createRowElement(PCMConstant.ILMCB, (SwtUtil.isEmptyOrNull(acctGrp.getIsIlmCentralBankMember())) ? "N" : acctGrp.getIsIlmCentralBankMember(), att);
					responseConstructor.formRowEnd();
				}
			}else {

				for (Iterator<AcctMaintenance> it = accountList.iterator(); it.hasNext();) {
					// Obtain rules definition tag from iterator
					AcctMaintenance acctGrp = (AcctMaintenance) it.next();
					//				acctClass = "";
					//				if (acctGrp.getAcctClass().equals("C")) {
					//					acctClass = "Current";
					//				} else if (acctGrp.getAcctClass().equals("L")) {
					//					acctClass = "Loro";
					//				} else if (acctGrp.getAcctClass().equals("E")) {
					//				   acctClass = "Netting";
					//			    } else {
					//				acctClass = "Nostro";
					//			    }
					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.ACCT_ID_NAME, acctGrp.getId().getAccountId() + " - " + acctGrp.getAcctname());
					responseConstructor.createRowElement(PCMConstant.ENTITY, acctGrp.getId().getEntityId());
					responseConstructor.createRowElement(PCMConstant.TYPE, (acctGrp.getAccttype().equals("C")) ? "Cash" : "Custodian");
					responseConstructor.createRowElement(PCMConstant.LEVEL, (acctGrp.getAcctlevel().equals("M")) ? "Main" : "Sub");
					responseConstructor.createRowElement(PCMConstant.BIC, acctGrp.getAcctbiccode());
					responseConstructor.createRowElement(PCMConstant.ILMCB, (SwtUtil.isEmptyOrNull(acctGrp.getIsIlmCentralBankMember())) ? "N" : acctGrp.getIsIlmCentralBankMember());
					responseConstructor.formRowEnd();
				}

			}




			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.ACCOUNT_MAINTENANCE);

			// Send response to client as XML
			responseHandler.sendResponse(response, xmlWriter.getData());
		} catch (SwtException ex) {

			log.error(this.getClass().getName()
					+ " - SwtException Caught in [add] method : - "
					+ ex.getMessage());
		} catch (Exception ex) {
			// xmlWriter.resetBuffer();

			log.error(this.getClass().getName()
					+ " - Exception Caught in [add] method : - "
					+ ex.getMessage());
			ex.printStackTrace();
		} finally {
			// Nullify Objects
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			accountList = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - Exit");
		}
	return  null;

	}


	public String addScreen() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "addScreen");
		request.setAttribute("screenName", request.getParameter("screenName"));
		return getView("addScreen");
	}
	public String viewScreen() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "viewScreen");
		request.setAttribute("screenName", request.getParameter("screenName"));
		return getView("addScreen");
	}

	/**
	 * This method is used to display the details of account groups on view screen
	 *
	 * @throws SwtException
	 */
	public String add() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// Methods local variable declaration
		// To send response to client
		ResponseHandler responseHandler = null;
		// To build logout response
		SwtResponseConstructor responseConstructor = null;
		// To build logout response
		SwtXMLWriter xmlWriter = null;
		// Variable String to hold UserId
		String userId = null;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		List groupIdArray = null;
		Iterator groupIdIterator = null;
		Integer maxOrdinal= null;
		int index= 0;
		// String variable to hold componentId
		String componentId = null;
		String accountGroupId= null;
		PCMCurrencyDetailsVO currencyList;
		Collection spreadProfileList;
		Collection targetCalculationList;
		Collection categoryList;
		//get languageId
		String languageId = null;
		String screenName= null;
		String width = null;
		// String variable to column order
		String columnOrder = null;
		String hiddenColumns= null;
		SystemFormats systemFormats= null;
		AccountGroup accountGroupsDetails=  new AccountGroup();
		AccountGroup accountGroupsDetailsOldRecord=  new AccountGroup();
		SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil.getBean("spreadProfilesMaintenanceManager");
		ArrayList<SpreadProcessPoint> spreadProcessPointList = null;
		ArrayList<AcctMaintenance> accountList = null;
		ArrayList<AcctMaintenance> accountsInGroupList = null;
		ArrayList<AcctMaintenance> accountsInGroupListForEyes = null;
		ArrayList<Reserve> reserveList = null;
		ArrayList<AccountGroupCutoff> cutOffList = null;
		ArrayList <RuleConditions> arrayTabCondition = new ArrayList<RuleConditions>();
		boolean isFromMaintenanceEvent = false;
		boolean isFromMaintenanceEventAmendMode = false;

		String maintEventId = null;



		ArrayList<AccountGroupCutoff> listCutoffAdd = null ;
		ArrayList<AccountGroupCutoff> listCutoffUpdate = null ;
		ArrayList<AccountGroupCutoff> listCutoffUpdateIterate  ;

		ArrayList<AccountGroupCutoff> listCutoffDelete = null ;
		ArrayList<AccountGroupCutoff> listCutoffDeleteToIterate ;
		ArrayList<AcctMaintenance> listAccountAdd = null ;
		ArrayList<AcctMaintenance> listAccountDelete ;
		ArrayList<AcctMaintenance> listAccountDeleteToIterate = null ;
		ArrayList<Reserve> listReserveAdd = null ;
		ArrayList<Reserve> listReserveUpdate = null ;
		ArrayList<Reserve> listReserveDelete = null ;

		ArrayList<Reserve> listReserveDeleteToIterate ;

		ArrayList<Reserve> oldReserveList = null;
		ArrayList<AccountGroupCutoff> oldCutOffList = null;
		boolean isNewRecrod = false;
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;
		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - " + "Entry");

			// Retrieves current user
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			componentId = PCMConstant.ACCOUNT_MAINTENANCE;
			screenName = request.getParameter("screenName");

			spreadProcessPointList = new ArrayList<SpreadProcessPoint>(spreadProfilesMaintenanceManager.getSpreadProcessPointList(request, null));


			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsAccountGroupWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));


			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsAccountGroupWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID);
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			groupIdArray = accountGroupsMaintenanceManager.getAccountGroupDetailList(null);


			// get accounts not in this group from AcctMaintenance

			groupIdIterator =  groupIdArray.iterator();
			groupIdArray = new ArrayList();
			ArrayList groupIdList = new ArrayList();

			AccountGroup rowId = null;
			while (groupIdIterator.hasNext()) {
				rowId =(AccountGroup) groupIdIterator.next();
				groupIdList.add( rowId.getId().getAccGrpId());
				index++;

			}


			xmlWriter.startElement(SwtConstants.SINGLETONS);
			if(!("add".equalsIgnoreCase(screenName))){
				accountGroupId = (String) request.getParameter("accountGroupId");

				isFromMaintenanceEventAmendMode = Boolean.parseBoolean(request.getParameter("isFromMaintenanceEventAmendMode"));
				isFromMaintenanceEvent = Boolean.parseBoolean( request.getParameter("isFromMaintenanceEvent"));

//			isFromMaintenanceEvent = false;
				//Get details of account group
				if(isFromMaintenanceEvent) {
					maintEventId =  (String) request.getParameter("maintEventId");
					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");


					MaintenanceEvent event = maintenanceEventMaintenanceManager.getMaintenanceEvent(""+maintEventId);
					if(event != null && "I".equalsIgnoreCase(event.getAction())) {
						isNewRecrod = true;
					}

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_GROUP");

					String json = details.getNewState();

					ObjectMapper objectMapper = new ObjectMapper();
					accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);

					//Get list of liquidity  from P_Reserve
					reserveList = (ArrayList<Reserve>) accountGroupsMaintenanceManager.getReserveDetails(accountGroupId);

					TypeFactory typeFactory = null;
					CollectionType collectionType = null;
					JavaType mapType = null;
					MaintenanceEventDetails detailsReserve = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_GROUP_RESERVE");
					if(detailsReserve != null) {
						String jsonReserve = detailsReserve.getNewState();
						//				HashMap<String, ArrayList<Reserve>> reserveMap  = objectMapper.readValue(jsonReserve, HashMap.class);

						//				ObjectMapper objectMapper = new ObjectMapper();
						typeFactory = objectMapper.getTypeFactory();
						collectionType = typeFactory.constructCollectionType(ArrayList.class, Reserve.class);
						mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
						HashMap<String, ArrayList<Reserve>> reserveMap = objectMapper.readValue(jsonReserve, mapType);


						listReserveAdd = reserveMap.get("add");
						listReserveUpdate = reserveMap.get("update");
						listReserveDelete =  reserveMap.get("delete");


						// Add new Reserve records
						reserveList.addAll(listReserveAdd);


						ArrayList<Reserve> listReserveUpdateToIterate = listReserveUpdate;
						reserveList = reserveList.stream()
								.filter(r -> !listReserveUpdateToIterate.stream().map(Reserve::getReserveId).anyMatch(id -> id.equals(r.getReserveId())))
								.collect(Collectors.toCollection(ArrayList::new));

						reserveList.addAll(listReserveUpdate);

						listReserveDeleteToIterate = listReserveDelete;
						// Delete existing Reserve records
						reserveList = reserveList.stream()
								.filter(r -> !listReserveDeleteToIterate.stream().map(Reserve::getReserveId).anyMatch(id -> id.equals(r.getReserveId())))
								.collect(Collectors.toCollection(ArrayList::new));

					}



					cutOffList = (ArrayList<AccountGroupCutoff>) accountGroupsMaintenanceManager.getCutOffRulesDetails(accountGroupId);

					MaintenanceEventDetails detailsCutoff = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_GROUP_CUT_OFF");
					if(detailsCutoff != null) {
						String jsonCutoff = detailsCutoff.getNewState();

						typeFactory = objectMapper.getTypeFactory();
						collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountGroupCutoff.class);
						mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
						HashMap<String, ArrayList<AccountGroupCutoff>> cutOffMap = objectMapper.readValue(jsonCutoff, mapType);

						listCutoffAdd = cutOffMap.get("add");
						listCutoffUpdate = cutOffMap.get("update");
						listCutoffDelete =  cutOffMap.get("delete");

						//Get list of cut off rule


						// Add new Reserve records
						cutOffList.addAll(listCutoffAdd);


						listCutoffUpdateIterate = listCutoffUpdate;
						cutOffList = cutOffList.stream()
								.filter(r -> !listCutoffUpdateIterate.stream().map(AccountGroupCutoff::getCutOffRuleId).anyMatch(id -> id.equals(r.getCutOffRuleId())))
								.collect(Collectors.toCollection(ArrayList::new));

						cutOffList.addAll(listCutoffUpdate);


						listCutoffDeleteToIterate = listCutoffDelete;
						// Delete existing Reserve records
						cutOffList = cutOffList.stream()
								.filter(r -> !listCutoffDeleteToIterate.stream().map(AccountGroupCutoff::getCutOffRuleId).anyMatch(id -> id.equals(r.getCutOffRuleId())))
								.collect(Collectors.toCollection(ArrayList::new));

					}


					//Get list of account in this group from P_account
					accountsInGroupList = (ArrayList<AcctMaintenance>) accountGroupsMaintenanceManager.getAccountsInGroupList(accountGroupId);


					MaintenanceEventDetails detailsAccountInGroup = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_IN_GROUP");
					if(detailsAccountInGroup != null) {
						String jsonAccountInGroup= detailsAccountInGroup.getNewState();

						typeFactory = objectMapper.getTypeFactory();
						collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountInGroup.class);
						mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
						HashMap<String, ArrayList<AccountInGroup>> accountMap = objectMapper.readValue(jsonAccountInGroup, mapType);

						ArrayList<AccountInGroup> listInGroupAccountAdd = accountMap.get("add");
						ArrayList<AccountInGroup> listInGroupAccountDelete =  accountMap.get("delete");

						listAccountAdd = new ArrayList<AcctMaintenance>();
						listAccountDelete = new ArrayList<AcctMaintenance>();

						AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
								.getBean("acctMaintenanceManager");
						SystemInfo systemInfo = new SystemInfo();


						for (int i = 0; i < listInGroupAccountAdd.size(); i++) {
							listAccountAdd.add(acctMaintenanceManager.getEditableDataDetailList(
									listInGroupAccountAdd.get(i).getId().getEntityId(), listInGroupAccountAdd.get(i).getId().getHostId(),listInGroupAccountAdd.get(i).getId().getAccountId(),systemInfo, SwtUtil.getCurrentSystemFormats(request.getSession()))) ;

						}

						for (int i = 0; i < listInGroupAccountDelete.size(); i++) {
							listAccountDelete.add(acctMaintenanceManager.getEditableDataDetailList(
									listInGroupAccountDelete.get(i).getId().getEntityId(), listInGroupAccountDelete.get(i).getId().getHostId(),listInGroupAccountDelete.get(i).getId().getAccountId(),systemInfo, SwtUtil.getCurrentSystemFormats(request.getSession()))) ;

						}





						// Add new Reserve records
						accountsInGroupList.addAll(listAccountAdd);

						// Delete existing Reserve records
						accountsInGroupList = accountsInGroupList.stream()
								.filter(r -> !listAccountDelete.stream().map(AcctMaintenance::getId).anyMatch(id -> id.equals(r.getId())))
								.collect(Collectors.toCollection(ArrayList::new));


						listAccountDeleteToIterate = listAccountDelete;
					}
					accountGroupsDetailsOldRecord = accountGroupsMaintenanceManager.getAcctGroupDetail(accountGroupId);
					if(accountGroupsDetailsOldRecord == null)
						accountGroupsDetailsOldRecord = new AccountGroup();


					oldReserveList = (ArrayList<Reserve>) accountGroupsMaintenanceManager.getReserveDetails(accountGroupId);
					oldCutOffList = (ArrayList<AccountGroupCutoff>) accountGroupsMaintenanceManager.getCutOffRulesDetails(accountGroupId);
				}else {

					accountGroupsDetails = accountGroupsMaintenanceManager.getAcctGroupDetail(accountGroupId);
					//Get list of liquidity  from P_Reserve
					reserveList = (ArrayList<Reserve>) accountGroupsMaintenanceManager.getReserveDetails(accountGroupId);

					//Get list of account in this group from P_account
					accountsInGroupList = (ArrayList<AcctMaintenance>) accountGroupsMaintenanceManager.getAccountsInGroupList(accountGroupId);

					//Get list of cut off rule
					cutOffList = (ArrayList<AccountGroupCutoff>) accountGroupsMaintenanceManager.getCutOffRulesDetails(accountGroupId);
				}






				accountsInGroupListForEyes = (ArrayList<AcctMaintenance>) accountGroupsMaintenanceManager.getAccountsInGroupList(accountGroupId);


				CategoryMaintenanceManager catgMaintenanceManager = (CategoryMaintenanceManager) SwtUtil.getBean("categoryMaintenanceManager");
				Category catgRowD = new Category();
				Category catgRowQ = new Category();
				String quickCategoryId = null;
				String defautCategoryId = null;
				if(accountGroupsDetails != null) {
					quickCategoryId = accountGroupsDetails.getQuickCategoryId();
					defautCategoryId =accountGroupsDetails.getDefaultCategoryId();
				}
				if(!SwtUtil.isEmptyOrNull(quickCategoryId)) {
					catgRowQ = catgMaintenanceManager.getCategoryDetailById(quickCategoryId);
					responseConstructor.createElement(PCMConstant.QUICK_CATEGORY, catgRowQ.getId().getCategoryId());
				}
				if(!SwtUtil.isEmptyOrNull(defautCategoryId)) {
					catgRowD = catgMaintenanceManager.getCategoryDetailById(defautCategoryId);
					responseConstructor.createElement(PCMConstant.DEFAULT_CATEGORY, catgRowD.getId().getCategoryId());
				}

				responseConstructor.createElement(PCMConstant.ACC_GP_ID, accountGroupsDetails.getId().getAccGrpId());
				responseConstructor.createElement(PCMConstant.ACC_GP_NAME, accountGroupsDetails.getDescription());
				responseConstructor.createElement(PCMConstant.KICK_OFF_TIME, accountGroupsDetails.getKickoff());
				responseConstructor.createElement(PCMConstant.EOD_TIME, accountGroupsDetails.getEodPhaseBegins());
				responseConstructor.createElement(PCMConstant.COB_TIME, accountGroupsDetails.getCobCutoff());
				responseConstructor.createElement(PCMConstant.ORDINAL, (accountGroupsDetails.getOrdinal() != null) ? accountGroupsDetails.getOrdinal()+"" : "");
				responseConstructor.createElement(PCMConstant.CURRENCYCODE, accountGroupsDetails.getCurrencyCode());
				responseConstructor.createElement(PCMConstant.SPREAD_ID, accountGroupsDetails.getSpreadProfileId());
				responseConstructor.createElement(PCMConstant.TARGET_CALCULATION, accountGroupsDetails.getTargetPaymentPercentMethod());

				if(isFromMaintenanceEvent && !isNewRecrod) {
					responseConstructor.createElement(PCMConstant.ACC_GP_ID+SwtConstants.MAINT_EVENT_OLD_VALUE , accountGroupsDetailsOldRecord.getId().getAccGrpId());
					responseConstructor.createElement(PCMConstant.ACC_GP_NAME+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getDescription());
					responseConstructor.createElement(PCMConstant.KICK_OFF_TIME+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getKickoff());
					responseConstructor.createElement(PCMConstant.EOD_TIME+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getEodPhaseBegins());
					responseConstructor.createElement(PCMConstant.COB_TIME+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getCobCutoff());
					responseConstructor.createElement(PCMConstant.ORDINAL+SwtConstants.MAINT_EVENT_OLD_VALUE, (accountGroupsDetailsOldRecord.getOrdinal() != null) ? accountGroupsDetailsOldRecord.getOrdinal()+"" : "");
					responseConstructor.createElement(PCMConstant.CURRENCYCODE+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getCurrencyCode());
					responseConstructor.createElement(PCMConstant.SPREAD_ID+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getSpreadProfileId() != null?accountGroupsDetailsOldRecord.getSpreadProfileId() :"");
					responseConstructor.createElement(PCMConstant.TARGET_CALCULATION+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getTargetPaymentPercentMethod());
					responseConstructor.createElement(PCMConstant.QUICK_CATEGORY+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getQuickCategoryId());
					responseConstructor.createElement(PCMConstant.DEFAULT_CATEGORY+SwtConstants.MAINT_EVENT_OLD_VALUE, accountGroupsDetailsOldRecord.getDefaultCategoryId());
				}



			}
			if("add".equalsIgnoreCase(screenName)) {
				responseConstructor.createElement(PCMConstant.GROUP_ID_LIST, groupIdList.toString());
				isNewRecrod = true;
			}

			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/****CurrencyCombo***********/
			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;

			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*****TargetCalculation Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			targetCalculationList = getTargetCalculation();
			j = targetCalculationList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.TARGET_CALCULATION_LIST, lstOptions));
			/*****DefaultCategory Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			categoryList = getCategoryList();
			j = categoryList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CATEGORY_LIST, lstOptions));
			/*****SpreadProfile Combo***********/

//			lstOptions = new ArrayList<OptionInfo>();
//
//           spreadProfileList = getSpreadProfileList(null);
//			j = spreadProfileList.iterator();
//			row = null;
//			while (j.hasNext()) {
//				row = (LabelValueBean) j.next();
//				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
//			}
//			lstSelect.add(new SelectInfo(PCMConstant.SPREAD_LIST, lstOptions));
			responseConstructor.formSelect(lstSelect);
//			accountsInGroupListForEyes.remove(15);
//			accountsInGroupListForEyes.remove(16);
//			AcctMaintenance acct = new AcctMaintenance();
//			acct.getId().setAccountId("atef");
//			acct.getId().setEntityId("RABONL2U");
//			acct.getId().setHostId("RABO");
//			acct.setAccttype("C");
//			acct.setAcctname("ateffffffffff ss");
//			acct.setIsIlmCentralBankMember(null);
//			acct.setAcctbiccode("EAEECC");
//			acct.setAcctlevel("M");
//			accountsInGroupListForEyes.add(acct);
//			accountsInGroupListForEyes.get(17).setAcctname("new account Name");
//			accountsInGroupListForEyes.get(13).setAcctname("new Modified one !! Name");
//			accountsInGroupListForEyes.get(13).setAcctlevel("S");
//			System.err.println(SwtUtil.compareObjects(lstSelect.get(0), lstSelect.get(0)));
//
//			ArrayList<AcctMaintenance> additions = new ArrayList<>(accountsInGroupListForEyes);
//			additions.removeAll(accountsInGroupList);
//
//			ArrayList<AcctMaintenance> deletions = new ArrayList<>(accountsInGroupList);
//			deletions.removeAll(accountsInGroupListForEyes);
//
//			System.err.println("equals =="+accountsInGroupListForEyes.get(0).equals(accountsInGroupList.get(0)));

			/*******Account Not in group Grid*******/

			/*******Accounts in Group Grid*******/
			responseConstructor.formGridStart("acountsGrpGrid");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns, "gridAccounts"));
			if(!("add".equalsIgnoreCase(screenName))) {
				HashMap<String, String> att = null;
				// Accounts not in this group Grid


				responseConstructor.formRowsStart(accountsInGroupList.size());
				for (Iterator<AcctMaintenance> it = accountsInGroupList.iterator(); it.hasNext();) {


					// Obtain rules definition tag from iterator
					AcctMaintenance acctGrp = (AcctMaintenance) it.next();
					if(isFromMaintenanceEvent && !isNewRecrod) {
						att = new HashMap<String, String>();
						boolean accountExist = (listAccountAdd != null && !listAccountAdd.isEmpty())
								? listAccountAdd.stream()
								.anyMatch(r -> r.getId().getAccountId().equals(acctGrp.getId().getAccountId()))
								: false;
						if(accountExist) {
							att.put("isNewRow", "Y");
						}else {
							att.put("isNewRow", "N");
						}
//							System.err.println("acctGrp.getId().getAccountId()="+acctGrp.getId().getAccountId());
						responseConstructor.formRowStart();
						responseConstructor.createRowElement(PCMConstant.ACCT_ID_NAME, acctGrp.getId().getAccountId() + " - " + acctGrp.getAcctname(),att);
						responseConstructor.createRowElement(PCMConstant.ENTITY, acctGrp.getId().getEntityId(),att);
						responseConstructor.createRowElement(PCMConstant.TYPE, (acctGrp.getAccttype().equals("C")) ? "Cash" : "Custodian", att);
						responseConstructor.createRowElement(PCMConstant.LEVEL, (acctGrp.getAcctlevel().equals("M")) ? "Main" : "Sub", att);
						responseConstructor.createRowElement(PCMConstant.BIC, acctGrp.getAcctbiccode(), att);
						responseConstructor.createRowElement(PCMConstant.ILMCB, (SwtUtil.isEmptyOrNull(acctGrp.getIsIlmCentralBankMember())) ? "N" : acctGrp.getIsIlmCentralBankMember(), att);

					}else {
						responseConstructor.formRowStart();
						responseConstructor.createRowElement(PCMConstant.ACCT_ID_NAME, acctGrp.getId().getAccountId() + " - " + acctGrp.getAcctname());
						responseConstructor.createRowElement(PCMConstant.ENTITY, acctGrp.getId().getEntityId());
						responseConstructor.createRowElement(PCMConstant.TYPE, (acctGrp.getAccttype().equals("C")) ? "Cash" : "Custodian");
						responseConstructor.createRowElement(PCMConstant.LEVEL, (acctGrp.getAcctlevel().equals("M")) ? "Main" : "Sub");
						responseConstructor.createRowElement(PCMConstant.BIC, acctGrp.getAcctbiccode());
						responseConstructor.createRowElement(PCMConstant.ILMCB, (SwtUtil.isEmptyOrNull(acctGrp.getIsIlmCentralBankMember())) ? "N" : acctGrp.getIsIlmCentralBankMember());

					}
					responseConstructor.formRowEnd();
				}

//						if(fromMaintenanceQueue) {
//							for (Iterator<AcctMaintenance> it = listAccountDeleteToIterate.iterator(); it.hasNext();) {
//
//
//								// Obtain rules definition tag from iterator
//								AcctMaintenance acctGrp = (AcctMaintenance) it.next();
//								if(fromMaintenanceQueue) {
//										att.put("isDeletedRow", "Y");
//								}
////								System.err.println("acctGrp.getId().getAccountId()="+acctGrp.getId().getAccountId());
//								responseConstructor.formRowStart();
//								responseConstructor.createRowElement(PCMConstant.ACCT_ID_NAME, acctGrp.getId().getAccountId() + " - " + acctGrp.getAcctname(), att);
//								responseConstructor.createRowElement(PCMConstant.ENTITY, acctGrp.getId().getEntityId(), att);
//								responseConstructor.createRowElement(PCMConstant.TYPE, (acctGrp.getAccttype().equals("C")) ? "Cash" : "Custodian", att);
//								responseConstructor.createRowElement(PCMConstant.LEVEL, (acctGrp.getAcctlevel().equals("M")) ? "Main" : "Sub", att);
//								responseConstructor.createRowElement(PCMConstant.BIC, acctGrp.getAcctbiccode(), att);
//								responseConstructor.createRowElement(PCMConstant.ILMCB, (SwtUtil.isEmptyOrNull(acctGrp.getIsIlmCentralBankMember())) ? "N" : acctGrp.getIsIlmCentralBankMember(), att);
//
//								responseConstructor.formRowEnd();
//							}
//
//
//						}

				responseConstructor.formRowsEnd();
			}
			responseConstructor.formGridEnd();

			/*****Spread Grid********/
			responseConstructor.formGridStart("spreadGrid");
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns, "gridSpread"));
			responseConstructor.formRowsStart(spreadProcessPointList.size());
			for (Iterator<SpreadProcessPoint> it = spreadProcessPointList.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				SpreadProcessPoint spreadPoint = (SpreadProcessPoint) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.SPREAD_ID, spreadPoint.getSpreadProfileId());
				responseConstructor.createRowElement(PCMConstant.PROCESS_NAME, spreadPoint.getProcessName());
				responseConstructor.createRowElement(PCMConstant.TIME, spreadPoint.getProcessPointTime());
				responseConstructor.createRowElement(PCMConstant.TARGET, spreadPoint.getTargetPaymentPercent()+"");
				responseConstructor.createRowElement(PCMConstant.PROCESS, spreadPoint.getProcessCategoriesText());
				responseConstructor.createRowElement(PCMConstant.CATEGORIES, spreadPoint.getCategories());
				responseConstructor.formRowEnd();
			}

			// Iterating rules definition details


			responseConstructor.formRowsEnd();

			responseConstructor.formGridEnd();

			responseConstructor.formGridStart("reserveGrid");
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns, "gridReserve"));
			if(!("add".equalsIgnoreCase(screenName))) {


				responseConstructor.formRowsStart(reserveList.size());

				if(isFromMaintenanceEvent && !isNewRecrod) {


//						ArrayList<Reserve> listReserveAdd ;
//						ArrayList<Reserve> listReserveUpdate ;
//						ArrayList<Reserve> listReserveDelete ;



					HashMap<String, String> att = null;




					for (Iterator<Reserve> it = reserveList.iterator(); it.hasNext();) {
						// Obtain rules definition tag from iterator
						Reserve reserve = (Reserve) it.next();
						Reserve oldReserve = null;
						att = new HashMap<String, String>();
						boolean reserveNew = Optional.ofNullable(listReserveAdd)
								.map(list -> list.stream().anyMatch(r -> r.getReserveId().equals(reserve.getReserveId())))
								.orElse(false);
						if(reserveNew) {
							att.put("isNewRow", "Y");
							oldReserve = new Reserve();
						}else {
							boolean reserveChanged = Optional.ofNullable(listReserveUpdate)
									.map(list -> list.stream().anyMatch(r -> r.getReserveId().equals(reserve.getReserveId())))
									.orElse(false);

							oldReserve = oldReserveList.stream()
									.filter(r -> r.getReserveId().equals(reserve.getReserveId())).findAny().orElse(new Reserve());

							if(reserveChanged)
								att.put("isUpdatedRow", "Y");
							else
								att.put("isUpdatedRow", "N");

						}

						responseConstructor.formRowStart();
						responseConstructor.createRowElement(PCMConstant.ID_RESERVE, reserve.getReserveId().toString(), att);
						responseConstructor.createRowElement(PCMConstant.TIME, reserve.getAccGrpTime(), MaintenanceAuthUtils.addPreviousValueBasedOnField(reserve, oldReserve, "accGrpTime", att));
						String balanceFormatted = reserve.getReserveBalance()== null ? "": SwtUtil.formatCurrency(accountGroupsDetails.getCurrencyCode(), reserve.getReserveBalance().doubleValue());
						responseConstructor.createRowElement(PCMConstant.RESERVE_BALANCE, balanceFormatted, MaintenanceAuthUtils.addPreviousValueBasedOnField(reserve, oldReserve, "reserveBalance", att));
						responseConstructor.createRowElement(PCMConstant.USECL, reserve.getUseCreditLine(), MaintenanceAuthUtils.addPreviousValueBasedOnField(reserve, oldReserve, "useCreditLine", att));
						responseConstructor.formRowEnd();
					}

					if(listReserveDelete != null) {
						for (Iterator<Reserve> it = listReserveDelete.iterator(); it.hasNext();) {
							// Obtain rules definition tag from iterator
							Reserve reserve = (Reserve) it.next();
							att = new HashMap<String, String>();
							att.put("isDeletedRow", "Y");


							responseConstructor.formRowStart();
							responseConstructor.createRowElement(PCMConstant.ID_RESERVE, reserve.getReserveId().toString(), att);
							responseConstructor.createRowElement(PCMConstant.TIME, reserve.getAccGrpTime(), att);
							String balanceFormatted = reserve.getReserveBalance()== null ? "": SwtUtil.formatCurrency(accountGroupsDetails.getCurrencyCode(), reserve.getReserveBalance().doubleValue());
							responseConstructor.createRowElement(PCMConstant.RESERVE_BALANCE, balanceFormatted, att);
							responseConstructor.createRowElement(PCMConstant.USECL, reserve.getUseCreditLine(), att);
							responseConstructor.formRowEnd();
						}
					}



				}else {
					for (Iterator<Reserve> it = reserveList.iterator(); it.hasNext();) {
						// Obtain rules definition tag from iterator
						Reserve reserve = (Reserve) it.next();

						responseConstructor.formRowStart();
						responseConstructor.createRowElement(PCMConstant.ID_RESERVE, reserve.getReserveId().toString());
						responseConstructor.createRowElement(PCMConstant.TIME, reserve.getAccGrpTime());
						String balanceFormatted = reserve.getReserveBalance()== null ? "": SwtUtil.formatCurrency(accountGroupsDetails.getCurrencyCode(), reserve.getReserveBalance().doubleValue());
						responseConstructor.createRowElement(PCMConstant.RESERVE_BALANCE, balanceFormatted);
						responseConstructor.createRowElement(PCMConstant.USECL, reserve.getUseCreditLine());
						responseConstructor.formRowEnd();
					}


				}
				responseConstructor.formRowsEnd();
			}

			responseConstructor.formGridEnd();
			/***CutOff Rules*/
			responseConstructor.formGridStart("cutOffGrid");
			responseConstructor.formPaging(null);
			Gson gson = new Gson();
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns, "gridCutOff"));
			if(!("add".equalsIgnoreCase(screenName))) {
				responseConstructor.formRowsStart(cutOffList.size());


				if(isFromMaintenanceEvent && !isNewRecrod) {


//							ArrayList<Reserve> listReserveAdd ;
//							ArrayList<Reserve> listReserveUpdate ;
//							ArrayList<Reserve> listReserveDelete ;



					HashMap<String, String> att = null;


					for (Iterator<AccountGroupCutoff> it = cutOffList.iterator(); it.hasNext();) {
						// Obtain rules definition tag from iterator
						AccountGroupCutoff cutOff = (AccountGroupCutoff) it.next();
						AccountGroupCutoff oldCutOff = null;
						att = new HashMap<String, String>();
						boolean reserveNew = listCutoffAdd.stream()
								.anyMatch(r -> r.getCutOffRuleId().equals(cutOff.getCutOffRuleId()));
						if(reserveNew) {
							att.put("isNewRow", "Y");
							oldCutOff = new AccountGroupCutoff();
						}else {
							boolean reserveChanged = listCutoffUpdate.stream()
									.anyMatch(r -> r.getCutOffRuleId().equals(cutOff.getCutOffRuleId()));

							oldCutOff  = oldCutOffList.stream()
									.filter(r -> r.getCutOffRuleId().equals(cutOff.getCutOffRuleId())).findAny().orElse(new AccountGroupCutoff());

							if(reserveChanged)
								att.put("isUpdatedRow", "Y");
							else
								att.put("isUpdatedRow", "N");

						}


						Integer ruleId = cutOff.getRulesDefinition().getRuleId();
						arrayTabCondition = new ArrayList();
						if(cutOff.getRulesDefinition().getRuleConditions().size() > 0) {
							for (int i = 0; i < cutOff.getRulesDefinition().getRuleConditions().size(); i++) {
								arrayTabCondition.add(cutOff.getRulesDefinition().getRuleConditions().get(i));
							}
						}
						String nullContent = "";

						responseConstructor.formRowStart();
						responseConstructor.createRowElement(PCMConstant.CUT_OFF_RULE_ID, cutOff.getCutOffRuleId().toString(), att);
						responseConstructor.createRowElement(PCMConstant.TESTORDER, cutOff.getOrdinal().toString(),MaintenanceAuthUtils.addPreviousValueBasedOnField(cutOff, oldCutOff, "ordinal", att));
						responseConstructor.createRowElement(PCMConstant.CUTTOFFTIME, cutOff.getCutoffTime(),MaintenanceAuthUtils.addPreviousValueBasedOnField(cutOff, oldCutOff, "cutoffTime", att));
						responseConstructor.createRowElement(PCMConstant.RULETEXT, cutOff.getRulesDefinition().getRuleText(),MaintenanceAuthUtils.addPreviousValueBasedOnField(cutOff.getRulesDefinition(), oldCutOff.getRulesDefinition(), "ruleText", att));
						responseConstructor.createRowElement(PCMConstant.RULEQUERY, cutOff.getRulesDefinition().getRuleQuery(),MaintenanceAuthUtils.addPreviousValueBasedOnField(cutOff.getRulesDefinition(), oldCutOff.getRulesDefinition(), "ruleQuery", att));
						responseConstructor.createRowElement(PCMConstant.RULE_CONDITION, gson.toJson(arrayTabCondition),MaintenanceAuthUtils.addPreviousValueBasedOnField(cutOff, oldCutOff, "useCreditLine", att));
						responseConstructor.createRowElement(PCMConstant.LOGTEXT,  cutOff.getLogText(),MaintenanceAuthUtils.addPreviousValueBasedOnField(cutOff, oldCutOff, "logText", att));
						responseConstructor.formRowEnd();
					}



					if( listCutoffDelete != null) {
						for (Iterator<AccountGroupCutoff> it = listCutoffDelete.iterator(); it.hasNext();) {
							// Obtain rules definition tag from iterator
							AccountGroupCutoff cutOff = (AccountGroupCutoff) it.next();
							Integer ruleId = cutOff.getRulesDefinition().getRuleId();

							att = new HashMap<String, String>();
							att.put("isDeletedRow", "Y");

							arrayTabCondition = new ArrayList();
							if(cutOff.getRulesDefinition().getRuleConditions().size() > 0) {
								for (int i = 0; i < cutOff.getRulesDefinition().getRuleConditions().size(); i++) {
									arrayTabCondition.add(cutOff.getRulesDefinition().getRuleConditions().get(i));
								}
							}
							String nullContent = "";

							responseConstructor.formRowStart();
							responseConstructor.createRowElement(PCMConstant.CUT_OFF_RULE_ID, cutOff.getCutOffRuleId().toString(),att);
							responseConstructor.createRowElement(PCMConstant.TESTORDER, cutOff.getOrdinal().toString(),att);
							responseConstructor.createRowElement(PCMConstant.CUTTOFFTIME, cutOff.getCutoffTime(),att);
							responseConstructor.createRowElement(PCMConstant.RULETEXT, cutOff.getRulesDefinition().getRuleText(),att);
							responseConstructor.createRowElement(PCMConstant.RULEQUERY, cutOff.getRulesDefinition().getRuleQuery(),att);
							responseConstructor.createRowElement(PCMConstant.RULE_CONDITION, gson.toJson(arrayTabCondition),att);
							responseConstructor.createRowElement(PCMConstant.LOGTEXT,  cutOff.getLogText(),att);
							responseConstructor.formRowEnd();
						}
					}



				}else {



					for (Iterator<AccountGroupCutoff> it = cutOffList.iterator(); it.hasNext();) {
						// Obtain rules definition tag from iterator
						AccountGroupCutoff cutOff = (AccountGroupCutoff) it.next();
						Integer ruleId = cutOff.getRulesDefinition().getRuleId();
						arrayTabCondition = new ArrayList();
						if(cutOff.getRulesDefinition().getRuleConditions().size() > 0) {
							for (int i = 0; i < cutOff.getRulesDefinition().getRuleConditions().size(); i++) {
								arrayTabCondition.add(cutOff.getRulesDefinition().getRuleConditions().get(i));
							}
						}
						String nullContent = "";

						responseConstructor.formRowStart();
						responseConstructor.createRowElement(PCMConstant.CUT_OFF_RULE_ID, cutOff.getCutOffRuleId().toString());
						responseConstructor.createRowElement(PCMConstant.TESTORDER, cutOff.getOrdinal().toString());
						responseConstructor.createRowElement(PCMConstant.CUTTOFFTIME, cutOff.getCutoffTime());
						responseConstructor.createRowElement(PCMConstant.RULETEXT, cutOff.getRulesDefinition().getRuleText());
						responseConstructor.createRowElement(PCMConstant.RULEQUERY, cutOff.getRulesDefinition().getRuleQuery());
						responseConstructor.createRowElement(PCMConstant.RULE_CONDITION, gson.toJson(arrayTabCondition));
						responseConstructor.createRowElement(PCMConstant.LOGTEXT,  cutOff.getLogText());
						responseConstructor.formRowEnd();
					}

				}



				responseConstructor.formRowsEnd();
			}

			responseConstructor.formGridEnd();



			xmlWriter.endElement(PCMConstant.ACCOUNT_MAINTENANCE);

			// Send response to client as XML
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
			//responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException ex) {

			log.error(this.getClass().getName()
					+ " - SwtException Caught in [add] method : - "
					+ ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			// xmlWriter.resetBuffer();

			log.error(this.getClass().getName()
					+ " - Exception Caught in [add] method : - "
					+ ex.getMessage());
			ex.printStackTrace();
			return getView("fail");
		} finally {
			// Nullify Objects
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			languageId = null;
			userId = null;
			lstOptions = null;
			lstSelect = null;
			accountList = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ add ] - Exit");
		}

	}





	/* Method to collect the category list
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getCategoryList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getDefaultCategoryList] - "
				+ "Entry");
		CategoryMaintenanceManager categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil.getBean("categoryMaintenanceManager");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection categoryColl;
		Iterator categoryItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		/* Collect the multiplier list from the Cache manager */
		categoryColl = (Collection) categoryMaintenanceManager.getCategoryCombo();

		/* Iterate the multiplierlist */
		categoryItr =  categoryColl.iterator();
		categoryColl = new ArrayList();
		L1 = new LabelValueBean("", "");
		L2 = new LabelValueBean("", "");
		Category row = null;
		categoryColl.add(L1);
		while (categoryItr.hasNext()) {
			row =(Category) categoryItr.next();
			L2 = new LabelValueBean(row.getId().getCategoryId(), row.getCategoryName());
			categoryColl.add(L2); // None
			index++;

		}

		log.debug(this.getClass().getName() + " - [getCategoryList] - "
				+ "Exit");
		return categoryColl;

	}
	/**
	 * Method to collect the spread list
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getSpreadProfileList(String currency) throws SwtException {

		log.debug(this.getClass().getName() + " - [getSpreadProfileList] - "
				+ "Entry");
		SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil.getBean("spreadProfilesMaintenanceManager");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection spreadColl;
		Iterator spreadItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		/* Collect the multiplier list from the Cache manager */
		spreadColl = (Collection) spreadProfilesMaintenanceManager.getSpreadProfilesList(currency);

		/* Iterate the multiplierlist */
		spreadItr =  spreadColl.iterator();
		spreadColl = new ArrayList();
		L1= new LabelValueBean("", "");
		L2= new LabelValueBean("", "");


		SpreadProfile row = null;
		spreadColl.add(L2);
		while (spreadItr.hasNext()) {
			row =(SpreadProfile) spreadItr.next();
			//Stack st = new Stack();
			L1 = new LabelValueBean(row.getId().getSpreadProfileId(), row.getSpreadProfileName());
			spreadColl.add(L1);
			index++;

		}

		log.debug(this.getClass().getName() + " - [getSpreadProfileList] - "
				+ "Exit");
		return spreadColl;

	}
	/**
	 * Get the targetCalculation list
	 *
	 * @param
	 * @return
	 * @throws SwtException
	 */
	private Collection getTargetCalculation() throws SwtException {

		log.debug(this.getClass().getName() + " - [getTargetCalculation] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		Collection targetColl;
		LabelValueBean L1;
		LabelValueBean L2;

		targetColl = new ArrayList();
		L1 = new LabelValueBean("All", "A");
		L2 = new LabelValueBean("Outstanding", "O");

		targetColl.add(L1);
		targetColl.add(L2);


		log.debug(this.getClass().getName() + " - [getTargetCalculation] - "
				+ "Exit");
		return targetColl;

	}

	/**
	 * Action method to save account groups details
	 *
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();



		String errorMessage = null;
		String accountGroupId= null;
		String description= null;
		Integer ordinal= null;
		String ccyCode= null;
		String kickOffTime= null;
		String eodPhaseBeginsTime= null;
		String cobTime= null;
		String targetPayementMethod= null;
		String spreadProfileId= null;
		String quickCategoryId= null;
		String defaultCategoryId= null;
		String isOnlyCentralBank= null;
		String screen= null;
		ActionErrors errors = null;
		AccountGroup acctGroup;
		String currentUserId = null;
		String xmlData= null;
		String accountIdAfterSave = null;
		ArrayList<TabKVType> crudResult =null;
		RuleConditions[] ruleConditions = null;
		ArrayList<RuleConditions> ruleConditionsList = null;
		SystemFormats systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
		boolean requireAuthorisation = false;
		String maintEventId = null;
		boolean isNewRecord = false;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		try {
			currentUserId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			acctGroup = new AccountGroup();
			Reserve reserveAdd = new Reserve();
			Reserve reserveUpdate = new Reserve();
			Reserve reserveDelete = new Reserve();
			ArrayList<Reserve> listReserveAdd = new ArrayList<Reserve>();
			ArrayList<Reserve> listReserveUpdate = new ArrayList<Reserve>();
			ArrayList<Reserve> listReserveDelete = new ArrayList<Reserve>();

			ArrayList<Reserve> listReserveAddFromEvent = new ArrayList<Reserve>();
			ArrayList<Reserve> listReserveUpdateFromEvent = new ArrayList<Reserve>();
			ArrayList<Reserve> listReserveDeleteFromEvent = new ArrayList<Reserve>();

			AccountGroupCutoff cutOffAdd = new AccountGroupCutoff();
			AccountGroupCutoff cutOffUpdate = new AccountGroupCutoff();
			AccountGroupCutoff cutOffDelete = new AccountGroupCutoff();
			ArrayList<AccountGroupCutoff> listCutOffAdd = new ArrayList<AccountGroupCutoff>();
			ArrayList<AccountGroupCutoff> listCutOffUpdate = new ArrayList<AccountGroupCutoff>();
			ArrayList<AccountGroupCutoff> listCutOffDelete = new ArrayList<AccountGroupCutoff>();

			ArrayList<AccountGroupCutoff> listCutOffAddFromEvent = new ArrayList<AccountGroupCutoff>();
			ArrayList<AccountGroupCutoff> listCutOffUpdateFromEvent = new ArrayList<AccountGroupCutoff>();
			ArrayList<AccountGroupCutoff> listCutOffDeleteFromEvent = new ArrayList<AccountGroupCutoff>();

			RulesDefinition rulesAdd = new RulesDefinition();
			RuleConditions ruleConditionAdd = new RuleConditions();
			RulesDefinition rulesUpdate = new RulesDefinition();
			RulesDefinition rulesDelete= new RulesDefinition();
			AccountInGroup accountsInGroupAdd = new AccountInGroup();
			ArrayList<AccountInGroup> listAccountsInGroupAdd = new ArrayList<AccountInGroup>();
			AccountInGroup accountsInGroupDelete = new AccountInGroup();
			ArrayList<AccountInGroup> listAccountsInGroupDelete = new ArrayList<AccountInGroup>();
			boolean containsAccountGroupWithReqAuthY = false;

			log.debug(this.getClass().getName() + " - [displayAccountGroupsGrid] - "
					+ "Entry");
			accountGroupId = (String)request.getParameter("accountGroupId");
			description = request.getParameter("description");
			if(request.getParameter("ordinal") != null && !(SwtUtil.isEmptyOrNull(request.getParameter("ordinal")))) {
				ordinal =Integer.parseInt(request.getParameter("ordinal"));
			}

			ccyCode = request.getParameter("ccyCode");
			kickOffTime = request.getParameter("kickOffTime");
			eodPhaseBeginsTime = request.getParameter("eodTime");
			cobTime = request.getParameter("cobTime");
			targetPayementMethod = request.getParameter("targetPayementMethod");
			spreadProfileId = request.getParameter("spreadProfileId");
			defaultCategoryId = request.getParameter("defaultCategoryId") ;
			quickCategoryId = request.getParameter("quickCategoryId") ;
			screen = request.getParameter("screenName");
			xmlData = request.getParameter("xmlData");
			maintEventId = request.getParameter("maintEventId");


			acctGroup.getId().setAccGrpId(accountGroupId);
			acctGroup.setDescription(description);
			acctGroup.setOrdinal(ordinal);
			acctGroup.setCurrencyCode(ccyCode);
			acctGroup.setKickoff(kickOffTime);
			acctGroup.setEodPhaseBegins(eodPhaseBeginsTime);
			acctGroup.setCobCutoff(cobTime);
			acctGroup.setTargetPaymentPercentMethod(targetPayementMethod);
			acctGroup.setSpreadProfileId(spreadProfileId);
			acctGroup.setDefaultCategoryId(defaultCategoryId);
			acctGroup.setQuickCategoryId(quickCategoryId);
			//String[] myStringArray = new String[]{"PC_RESERVE.TIME", "PC_RESERVE.RESERVE", "PC_RESERVE.CREDIT_LIN"};
			crudResult =doCrudOperation(currentUserId, xmlData, null);
			String tableToJoinQuery = "";




			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			containsAccountGroupWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			if(containsAccountGroupWithReqAuthY || !SwtUtil.isEmptyOrNull(maintEventId))
				requireAuthorisation = true;

			//If it's from add screen so it's new record
			if("add".equalsIgnoreCase(screen)){
				isNewRecord = true;
			}

			Long idGenerated = null;


			if(requireAuthorisation) {
				idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("ACCT_GRP", acctGroup.getId().getAccGrpId(), SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, SwtUtil.isEmptyOrNull(maintEventId)?null:Long.parseLong(maintEventId), null, isNewRecord?"I":"U", "P");;
				acctGroup.setMainEventId(""+idGenerated);
			}

			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceAuthUtils.updateMaintenanceEventStatus(Long.parseLong(maintEventId), "R", SwtUtil.getCurrentUserId(request.getSession()),null,idGenerated );

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEvent event = maintenanceEventMaintenanceManager.getMaintenanceEvent(""+maintEventId);
				if("I".equalsIgnoreCase(event.getAction())) {
					isNewRecord = true;
				}
				Thread.sleep(1000);
			}




			MaintenanceEventDetails maintenanceEventDetails = new MaintenanceEventDetails();







			if("add".equalsIgnoreCase(screen) || isNewRecord){
				accountGroupsMaintenanceManager.saveAccountGroup(acctGroup);
				accountIdAfterSave = acctGroup.getId().getAccGrpId();

			}else{
				accountGroupsMaintenanceManager.updateAcctGroup(acctGroup);
			}

			ObjectMapper objectMapper = new ObjectMapper();
			String json = objectMapper.writeValueAsString(acctGroup);


//			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil.getBean("maintenanceEventMaintenanceManager");
//			MaintenanceEvent maintenanceEvent = new MaintenanceEvent();
//			maintenanceEvent.setAction("U");
//			maintenanceEvent.setAuthDate(new  Date());
//			maintenanceEvent.setAuthUser("Atef");
////			maintenanceEvent.setMaintEventId(5);
//			maintenanceEvent.setMaintFacilityId("FACILTY_1");
//			maintenanceEvent.setNextId(new Double(1));
//			maintenanceEvent.setPrevId(new Double(2));
//			maintenanceEvent.setRecordId(acctGroup.getId().getAccGrpId());
//			maintenanceEvent.setRequestDate(new Date());
//			maintenanceEvent.setRequestUser("BlythD");
//			maintenanceEvent.setStatus("P");
//
//			long a = maintenanceEventMaintenanceManager.saveMaintenanceEvent(maintenanceEvent);
//			Long fileId = SequenceFactory.getSequenceFromDbAsLong("SEQ_S_MAINT_DETAILS_EVENT");



//
//			maintenanceEventDetails.setAction("U");
//			maintenanceEventDetails.getId().setMaintEventId(a);
//			maintenanceEventDetails.getId().setMaintSeq(fileId);
//			maintenanceEventDetails.setNewState(json);
//			maintenanceEventDetails.setOldState("");
//			maintenanceEventDetails.setRecordId(acctGroup.getId().getAccGrpId());
//			maintenanceEventDetails.setTableName("ACCOUNT_GROUP");

//			maintenanceEventMaintenanceManager.saveMaintenanceEventDetails(maintenanceEventDetails);
			if(requireAuthorisation)
				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "ACCOUNT_GROUP", acctGroup.getId().getAccGrpId(), isNewRecord?"I":"U", null, json);

			for (Iterator iterator = crudResult.iterator(); iterator.hasNext();) {
				TabKVType object = (TabKVType) iterator.next();
				if (object.getTableName().equals("PC_ACCOUNTS_IN_GROUP")) {
					if (object.getOperation().equals("I")) {
						for(int k =0; k<object.getElementList().size();k++){

							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("ACC_GROUP_ID")) {
								accountsInGroupAdd.setAccGrpId(colmunValue.getValue());
							}
							if (colmunValue.getKey().equals("ACCOUNT_ID")) {
								accountsInGroupAdd.getId().setAccountId(colmunValue.getValue());
							}
							if (colmunValue.getKey().equals("ENTITY")) {
								accountsInGroupAdd.getId().setEntityId(colmunValue.getValue());
							}

							accountsInGroupAdd.getId().setHostId(SwtUtil.getCurrentHostId());



						}
						if(requireAuthorisation)
							accountsInGroupAdd.setMainEventId(""+idGenerated);
						listAccountsInGroupAdd.add(accountsInGroupAdd);
						accountsInGroupAdd = new AccountInGroup();


					} else if (object.getOperation().equals("D")) {
						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("ACC_GROUP_ID")) {
								accountsInGroupDelete.setAccGrpId(colmunValue.getValue());
							} else if (colmunValue.getKey().equals("ACCOUNT_ID")) {
								accountsInGroupDelete.getId().setAccountId(colmunValue.getValue());
							} else if (colmunValue.getKey().equals("ENTITY")) {
								accountsInGroupDelete.getId().setEntityId(colmunValue.getValue());
							}

							accountsInGroupDelete.getId().setHostId(SwtUtil.getCurrentHostId());

						}
						if(requireAuthorisation)
							accountsInGroupDelete.setMainEventId(""+idGenerated);
						listAccountsInGroupDelete.add(accountsInGroupDelete);
						accountsInGroupDelete = new AccountInGroup();

					}
				}
				if (object.getTableName().equals("PC_RESERVE")) {
					if (object.getOperation().equals("I")) {

						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("TIME")) {
								reserveAdd.setAccGrpTime(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("CREDIT_LINE")) {
								reserveAdd.setUseCreditLine(colmunValue.getValue());

							}else if (colmunValue.getKey().equals("RESERVE")) {
								String balance = colmunValue.getValue();
								reserveAdd.setReserveBalance(SwtUtil.parseCurrency(balance, systemFormats.getCurrencyFormat()).longValue());

							}



						}

						reserveAdd.setAccGrpId(accountGroupId);
						if(requireAuthorisation)
							reserveAdd.setMainEventId(""+idGenerated);
						listReserveAdd.add(reserveAdd);
						reserveAdd = new Reserve();
					} else if (object.getOperation().equals("U")) {
						reserveUpdate.setAccGrpId(accountGroupId);
						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("ID_RESERVE")) {
								reserveUpdate.setReserveId(Long.parseLong(colmunValue.getValue()));

							}else if (colmunValue.getKey().equals("TIME")) {
								reserveUpdate.setAccGrpTime(colmunValue.getValue());

							}else if (colmunValue.getKey().equals("CREDIT_LINE")) {
								reserveUpdate.setUseCreditLine(colmunValue.getValue());

							}else if (colmunValue.getKey()
									.equals("RESERVE")) {
								String balance = colmunValue.getValue();
								reserveUpdate.setReserveBalance(SwtUtil.parseCurrency(balance, systemFormats.getCurrencyFormat()).longValue());

							}


						}
						if(requireAuthorisation)
							reserveUpdate.setMainEventId(""+idGenerated);
						listReserveUpdate.add(reserveUpdate);
						reserveUpdate = new Reserve();
					} else {
						//delete reserve
						reserveDelete.setAccGrpId(accountGroupId);
						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("ID_RESERVE")) {
								reserveDelete.setReserveId(Long.parseLong(colmunValue.getValue()));

							}else if (colmunValue.getKey().equals("TIME")) {
								reserveDelete.setAccGrpTime(colmunValue.getValue());

							}else if (colmunValue.getKey().equals("CREDIT_LINE")) {
								reserveDelete.setUseCreditLine(colmunValue.getValue());

							}else if (colmunValue.getKey().equals("RESERVE")) {
								String balance = colmunValue.getValue();
								reserveDelete.setReserveBalance(SwtUtil.parseCurrency(balance, systemFormats.getCurrencyFormat()).longValue());

							}


						}
						if(requireAuthorisation)
							reserveDelete.setMainEventId(""+idGenerated);
						listReserveDelete.add(reserveDelete);
						reserveDelete = new Reserve();
					}

				} else if(object.getTableName().equals("PC_CUTOFF")) {
					if (object.getOperation().equals("I")) {
						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("CUTOFF_TIME")) {
								cutOffAdd.setCutoffTime(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("ORDINAL")) {
								if(colmunValue.getValue() != null && !(SwtUtil.isEmptyOrNull(colmunValue.getValue()))) {
									cutOffAdd.setOrdinal(Long.parseLong(colmunValue.getValue()));
								}

							} else if (colmunValue.getKey().equals("LOG_TEXT")) {
								cutOffAdd.setLogText(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("RULE_TYPE")) {
								rulesAdd.setRuleType(colmunValue.getValue());

							}  else if (colmunValue.getKey().equals("RULE_TEXT")) {
								rulesAdd.setRuleText(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("TAB_JOIN")) {
								ruleConditionAdd.setTableName(colmunValue.getValue());
							} else if (colmunValue.getKey().equals("TAB_CONDITION")) {
								Gson gson = new Gson();
								ruleConditions = gson.fromJson(colmunValue.getValue(), RuleConditions[].class);
								if(ruleConditions.length>0) {
									ruleConditionsList = new ArrayList<RuleConditions>(Arrays.asList(ruleConditions));
									for (int i = 0; i < ruleConditionsList.size(); i++) {
										if(ruleConditionsList.get(i).getTableName().equals("PC_PAYMENT_REQUEST_INFO")) {
											tableToJoinQuery+="select count(*) from PC_PAYMENT_REQUEST P INNER JOIN pc_payment_request_info f ON p.payreq_id = f.payreq_id ";

										}
									}
								}
							}else if (colmunValue.getKey().equals("RULE_QUERY")) {
								String querySQL = colmunValue.getValue();
								if(tableToJoinQuery.length()>0) {
									int indexOfWhere = querySQL.indexOf("where");
									querySQL = tableToJoinQuery+querySQL.substring(indexOfWhere,querySQL.length());
								}
								rulesAdd.setRuleQuery(querySQL);

							}
						}
						cutOffAdd.setAccGrpId(accountGroupId);
						if(requireAuthorisation)
							cutOffAdd.setMainEventId(""+idGenerated);
						cutOffAdd.setRulesDefinition(rulesAdd);
						rulesAdd.setRuleConditions(ruleConditionsList);
						//cutOffAdd.get.setRuleConditions(ruleConditionAdd);
						listCutOffAdd.add(cutOffAdd);
						cutOffAdd = new AccountGroupCutoff();
						rulesAdd = new RulesDefinition();
						ruleConditions = null;
						tableToJoinQuery= "";

					} else if (object.getOperation().equals("U")) {
						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("ID_CUTOFF")) {
								cutOffUpdate.setCutOffRuleId(Long.parseLong(colmunValue.getValue()));

							}
							else if (colmunValue.getKey().equals("CUTOFF_TIME")) {
								cutOffUpdate.setCutoffTime(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("ORDINAL")) {
								cutOffUpdate.setOrdinal(Long.parseLong(colmunValue.getValue()));

							} else if (colmunValue.getKey().equals("LOG_TEXT")) {
								cutOffUpdate.setLogText(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("RULE_TYPE")) {
								rulesUpdate.setRuleType(colmunValue.getValue());

							}  else if (colmunValue.getKey().equals("RULE_TEXT")) {
								rulesUpdate.setRuleText(colmunValue.getValue());

							}else if (colmunValue.getKey().equals("TAB_CONDITION")) {
								Gson gson = new Gson();
								ruleConditions = gson.fromJson(colmunValue.getValue(), RuleConditions[].class);
								if(ruleConditions.length>0) {
									ruleConditionsList = new ArrayList<RuleConditions>(Arrays.asList(ruleConditions));
									for (int i = 0; i < ruleConditionsList.size(); i++) {
										if(ruleConditionsList.get(i).getTableName().equals("PC_PAYMENT_REQUEST_INFO")) {
											tableToJoinQuery+="select count(*) from PC_PAYMENT_REQUEST P INNER JOIN pc_payment_request_info f ON p.payreq_id = f.payreq_id ";

										}
									}
								}
							}
							else if (colmunValue.getKey().equals("RULE_QUERY")) {
								String querySQL = colmunValue.getValue();
								if(tableToJoinQuery.length()>0) {
									int indexOfWhere = querySQL.indexOf("where");
									querySQL = tableToJoinQuery+querySQL.substring(indexOfWhere,querySQL.length());
								}
								rulesUpdate.setRuleQuery(querySQL);

							}
						}

						cutOffUpdate.setAccGrpId(accountGroupId);
						if(requireAuthorisation)
							cutOffUpdate.setMainEventId(""+idGenerated);
						cutOffUpdate.setRulesDefinition(rulesUpdate);
						rulesUpdate.setRuleConditions(ruleConditionsList);
						listCutOffUpdate.add(cutOffUpdate);
						cutOffUpdate = new AccountGroupCutoff();
						rulesUpdate = new RulesDefinition();
						ruleConditions = null;
						tableToJoinQuery= "";
					} else if(object.getOperation().equals("D")) {
						for(int k =0; k<object.getElementList().size();k++){
							KVType colmunValue = object.getElementList().get(k);
							if (colmunValue.getKey().equals("ID_CUTOFF")) {
								cutOffDelete.setCutOffRuleId(Long.parseLong(colmunValue.getValue()));

							}
							else if (colmunValue.getKey().equals("CUTOFF_TIME")) {
								cutOffDelete.setCutoffTime(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("ORDINAL")) {
								cutOffDelete.setOrdinal(Long.parseLong(colmunValue.getValue()));

							} else if (colmunValue.getKey().equals("LOG_TEXT")) {
								cutOffDelete.setLogText(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("RULE_TYPE")) {
								rulesDelete.setRuleType(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("RULE_QUERY")) {
								rulesDelete.setRuleQuery(colmunValue.getValue());

							} else if (colmunValue.getKey().equals("RULE_TEXT")) {
								rulesDelete.setRuleText(colmunValue.getValue());

							}
						}

						cutOffDelete.setAccGrpId(accountGroupId);
						cutOffDelete.setRulesDefinition(rulesDelete);
						if(requireAuthorisation)
							cutOffDelete.setMainEventId(""+idGenerated);
						listCutOffDelete.add(cutOffDelete);
						cutOffDelete = new AccountGroupCutoff();
						rulesDelete = new RulesDefinition();

					}
				}
			}


			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_GROUP");

				json = details.getNewState();

				objectMapper = new ObjectMapper();
//				accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);
//
//				//Get list of liquidity  from P_Reserve
//				 reserveList = (ArrayList<Reserve>) accountGroupsMaintenanceManager.getReserveDetails(accountGroupId);




//
//				//Merge lists with the previous event to create a real event with old and new modification
//				//For reserve List
//
				MaintenanceEventDetails detailsReserve = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_GROUP_RESERVE");
				String jsonReserve = detailsReserve.getNewState();
//				HashMap<String, ArrayList<Reserve>> reserveMap  = objectMapper.readValue(jsonReserve, HashMap.class);

//				ObjectMapper objectMapper = new ObjectMapper();
				TypeFactory typeFactory = objectMapper.getTypeFactory();
				CollectionType collectionType = typeFactory.constructCollectionType(ArrayList.class, Reserve.class);
				JavaType mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
				HashMap<String, ArrayList<Reserve>> reserveMap = objectMapper.readValue(jsonReserve, mapType);

				listReserveAddFromEvent = reserveMap.get("add");
				listReserveUpdateFromEvent = reserveMap.get("update");
				listReserveDeleteFromEvent =  reserveMap.get("delete");

				ArrayList<Reserve> listReserveAddDump = new ArrayList<>(listReserveAdd);
				ArrayList<Reserve> listReserveDeleteDump = new ArrayList<>(listReserveDelete);
				ArrayList<Reserve> listReserveUpdateDump = new ArrayList<>(listReserveUpdate);



				// create a map from processPointId to SpreadProcessPoint for the elements in listSpreadProcessPointUpdate
				Map<Long, Reserve> updateMap = listReserveUpdate.stream()
						.collect(Collectors.toMap(Reserve::getReserveId, Function.identity()));


				// replace elements in listSpreadProcessPointAddFromEvent with matching processPointId or processName from listSpreadProcessPointUpdate
				listReserveAddFromEvent.replaceAll(spp -> {
					Reserve updatedSpp = updateMap.get(spp.getReserveId());
					if (updatedSpp != null) {
						listReserveUpdateDump.remove(updatedSpp);
						return updatedSpp;
					} else {
						return spp;
					}
				});

				listReserveUpdate = listReserveUpdateDump;



				listReserveAddFromEvent.stream()
						.filter(item -> listReserveAddDump.stream()
								.noneMatch(existingItem -> existingItem.getReserveId() != null && existingItem.getReserveId().equals(item.getReserveId())))
						.filter(item -> listReserveDeleteDump.stream()
								.noneMatch(existingItem -> existingItem.getReserveId() != null &&  existingItem.getReserveId().equals(item.getReserveId())))
						.forEach(listReserveAddDump::add);

				listReserveAdd = listReserveAddDump;

				ArrayList<Reserve> listSpreadProcessPointAddEventDump = new ArrayList<>(listReserveAddFromEvent);


				ArrayList<Reserve> filteredList = listReserveDelete.stream()
						.filter(spp -> listSpreadProcessPointAddEventDump.stream()
								.noneMatch(sppAdd -> sppAdd.getReserveId() != null && sppAdd.getReserveId().equals(spp.getReserveId())))
						.collect(Collectors.toCollection(ArrayList::new));


				listReserveDeleteFromEvent.stream()
						.filter(spp -> filteredList.stream()
								.noneMatch(sppDelete -> sppDelete.getReserveId().equals(spp.getReserveId())))
						.forEach(filteredList::add);

				listReserveDelete = filteredList;

				ArrayList<Reserve> listSpreadProcessPointAddDump2 = new ArrayList<>(listReserveAdd);

				ArrayList<Reserve> updatedListSpreadProcessPointUpdate = new ArrayList<>(listReserveUpdate);
				updatedListSpreadProcessPointUpdate.removeIf(item -> listSpreadProcessPointAddDump2.stream()
						.anyMatch(existingItem -> existingItem.getReserveId() != null && existingItem.getReserveId().equals(item.getReserveId())));

				listReserveUpdateFromEvent.stream()
						.filter(spp -> updatedListSpreadProcessPointUpdate.stream()
								.noneMatch(sppUpdate -> sppUpdate.getReserveId().equals(spp.getReserveId()))
								&& filteredList.stream()
								.noneMatch(sppDelete -> sppDelete.getReserveId().equals(spp.getReserveId())))
						.forEach(updatedListSpreadProcessPointUpdate::add);

				listReserveUpdate = updatedListSpreadProcessPointUpdate;






				//For reserve List

				MaintenanceEventDetails detailsCutoff = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_GROUP_CUT_OFF");
				String jsonCutoff = detailsCutoff.getNewState();

				typeFactory = objectMapper.getTypeFactory();
				collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountGroupCutoff.class);
				mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
				HashMap<String, ArrayList<AccountGroupCutoff>> cutOffMap = objectMapper.readValue(jsonCutoff, mapType);

				listCutOffAddFromEvent = cutOffMap.get("add");
				listCutOffUpdateFromEvent = cutOffMap.get("update");
				listCutOffDeleteFromEvent =  cutOffMap.get("delete");

				ArrayList<AccountGroupCutoff> listCutOffAddDump = new ArrayList<>(listCutOffAdd);
				ArrayList<AccountGroupCutoff> listCutOffDeleteDump = new ArrayList<>(listCutOffDelete);
				ArrayList<AccountGroupCutoff> listCutOffUpdateDump = new ArrayList<>(listCutOffUpdate);





				// create a map from cutOffRuleId to AccountGroupCutoff for the elements in listCutOffUpdate
				Map<Long, AccountGroupCutoff> updateMap2 = listCutOffUpdate.stream()
						.collect(Collectors.toMap(AccountGroupCutoff::getCutOffRuleId, Function.identity()));

				// replace elements in listCutOffAddFromEvent with matching cutOffRuleId from listCutOffUpdate
				listCutOffAddFromEvent.replaceAll(cutOff -> {
					AccountGroupCutoff updatedCutOff = updateMap2.get(cutOff.getCutOffRuleId());
					if (updatedCutOff != null) {
						listCutOffUpdateDump.remove(updatedCutOff);
						return updatedCutOff;
					} else {
						return cutOff;
					}
				});

				listCutOffUpdate = listCutOffUpdateDump;

				listCutOffAddFromEvent.stream()
						.filter(item -> listCutOffAddDump.stream()
								.noneMatch(existingItem -> existingItem.getCutOffRuleId() != null && existingItem.getCutOffRuleId().equals(item.getCutOffRuleId())))
						.filter(item -> listCutOffDeleteDump.stream()
								.noneMatch(existingItem -> existingItem.getCutOffRuleId() != null && existingItem.getCutOffRuleId().equals(item.getCutOffRuleId())))
						.forEach(listCutOffAddDump::add);

				listCutOffAdd = listCutOffAddDump;




				//
				//
//					listReserveDeleteFromEvent.stream()
//						    .filter(spp -> filteredList.stream()
//						        .noneMatch(sppDelete -> sppDelete.getReserveId().equals(spp.getReserveId())))
//						    .forEach(filteredList::add);
				//
//						listReserveDelete = filteredList;
//
//						ArrayList<Reserve> listSpreadProcessPointAddDump2 = new ArrayList<>(listReserveAdd);
//
//						ArrayList<Reserve> updatedListSpreadProcessPointUpdate = new ArrayList<>(listReserveUpdate);
//						updatedListSpreadProcessPointUpdate.removeIf(item -> listSpreadProcessPointAddDump2.stream()
//						        .anyMatch(existingItem -> existingItem.getReserveId() != null && existingItem.getReserveId().equals(item.getReserveId())));
				//

				final ArrayList<AccountGroupCutoff> listCutOffAddEventDump = new ArrayList<>(listCutOffAddFromEvent);

				ArrayList<AccountGroupCutoff> filteredList2 = listCutOffDelete.stream()
						.filter(cutOff -> listCutOffAddEventDump.stream()
								.noneMatch(sppAdd -> sppAdd.getCutOffRuleId() != null && sppAdd.getCutOffRuleId().equals(cutOff.getCutOffRuleId())))
						.collect(Collectors.toCollection(ArrayList::new));

				listCutOffDeleteFromEvent.stream()
						.filter(cutOff -> filteredList2.stream()
								.noneMatch(cutOffDelete2 -> cutOffDelete2.getCutOffRuleId().equals(cutOff.getCutOffRuleId())))
						.forEach(filteredList2::add);

				listCutOffDelete = filteredList2;

				ArrayList<AccountGroupCutoff> listCutOffAddDump2 = new ArrayList<>(listCutOffAdd);

				ArrayList<AccountGroupCutoff> updatedListCutOffUpdate = new ArrayList<>(listCutOffUpdate);
				updatedListCutOffUpdate.removeIf(item -> listCutOffAddDump2.stream()
						.anyMatch(existingItem -> existingItem.getCutOffRuleId() != null && existingItem.getCutOffRuleId().equals(item.getCutOffRuleId())));

				listCutOffUpdateFromEvent.stream()
						.filter(cutOff -> updatedListCutOffUpdate.stream()
								.noneMatch(cutOffUpdate2 -> cutOffUpdate2.getCutOffRuleId().equals(cutOff.getCutOffRuleId()))
								&& filteredList2.stream()
								.noneMatch(cutOffDelete2 -> cutOffDelete2.getCutOffRuleId().equals(cutOff.getCutOffRuleId())))
						.forEach(updatedListCutOffUpdate::add);


				listCutOffUpdate = updatedListCutOffUpdate;









				MaintenanceEventDetails detailsAccountInGroup = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "ACCOUNT_IN_GROUP");
				String jsonAccountInGroup= detailsAccountInGroup.getNewState();

				typeFactory = objectMapper.getTypeFactory();
				collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountInGroup.class);
				mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
				HashMap<String, ArrayList<AccountInGroup>> accountMap = objectMapper.readValue(jsonAccountInGroup, mapType);

				ArrayList<AccountInGroup> listInGroupAccountAddFromEvent = accountMap.get("add");
				ArrayList<AccountInGroup> listInGroupAccountDeleteFromEvent =  accountMap.get("delete");


				ArrayList<AccountInGroup> listAccountsInGroupAddDump = new ArrayList<>(listAccountsInGroupAdd);
				ArrayList<AccountInGroup> listAccountsInGroupDeleteDump = new ArrayList<>(listAccountsInGroupDelete);

//					System.err.println(listAccountsInGroupDeleteDump.get(0).equals(listInGroupAccountAddFromEvent.get(0)));

				listInGroupAccountAddFromEvent.removeIf(account -> {
					boolean existsInDeleteList = listAccountsInGroupDeleteDump.contains(account);
					if (existsInDeleteList) {
						listAccountsInGroupDeleteDump.remove(account);
					}
					return existsInDeleteList;
				});

				listInGroupAccountDeleteFromEvent.removeIf(account -> {
					boolean existsInDeleteList = listAccountsInGroupAddDump.contains(account);
					if (existsInDeleteList) {
						listAccountsInGroupAddDump.remove(account);
					}
					return existsInDeleteList;
				});


				listInGroupAccountAddFromEvent.stream()
						.filter(account -> !listAccountsInGroupDeleteDump.contains(account))
						.forEach(listAccountsInGroupAddDump::add);


				listInGroupAccountDeleteFromEvent.stream()
						.filter(account -> !listAccountsInGroupAddDump.contains(account))
						.forEach(listAccountsInGroupDeleteDump::add);


				listAccountsInGroupAdd = listAccountsInGroupAddDump;
				listAccountsInGroupDelete = listAccountsInGroupDeleteDump;

			}

			accountGroupsMaintenanceManager.crudReserve(listReserveAdd, listReserveUpdate, listReserveDelete, idGenerated);
			accountGroupsMaintenanceManager.crudCutOff(listCutOffAdd, listCutOffUpdate, listCutOffDelete, idGenerated);
			accountGroupsMaintenanceManager.crudAccountsInGroup(listAccountsInGroupAdd, listAccountsInGroupDelete, idGenerated);


			if(requireAuthorisation) {
				HashMap<String, ArrayList> changesMap = new HashMap<String, ArrayList>();
				String outputJsonString = new String();

				changesMap.put("add",listReserveAdd);
				changesMap.put("update",listReserveUpdate);
				changesMap.put("delete",listReserveDelete);

				outputJsonString = objectMapper.writeValueAsString(changesMap);
				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "ACCOUNT_GROUP_RESERVE", acctGroup.getId().getAccGrpId(), isNewRecord?"I":"U", null, outputJsonString);


				changesMap = new HashMap<String, ArrayList>();
				outputJsonString = new String();


				changesMap.put("add",listCutOffAdd);
				changesMap.put("update",listCutOffUpdate);
				changesMap.put("delete",listCutOffDelete);

				outputJsonString = objectMapper.writeValueAsString(changesMap);

				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "ACCOUNT_GROUP_CUT_OFF", acctGroup.getId().getAccGrpId(), isNewRecord?"I":"U", null, outputJsonString);

				changesMap = new HashMap<String, ArrayList>();
				outputJsonString = new String();


				changesMap.put("add",listAccountsInGroupAdd);
				changesMap.put("delete",listAccountsInGroupDelete);

				outputJsonString = objectMapper.writeValueAsString(changesMap);


				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "ACCOUNT_IN_GROUP", acctGroup.getId().getAccGrpId(), isNewRecord?"I":"U", null, outputJsonString);

				if(!containsAccountGroupWithReqAuthY) {
					MaintenanceAuthUtils.updateMaintenanceEventStatus(Long.parseLong(""+idGenerated), "A", SwtUtil.getCurrentUserId(request.getSession()),null,null );
					MaintenanceAuthUtils.acceptMaintenanceEvent(""+idGenerated);
				}
			}

			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			display();


		} catch (SwtException swtExp) {
			swtExp.printStackTrace();
			// log error message
			log.error(this.getClass().getName() + " - [save] - Exception -" + swtExp.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtExp, "saveAccountGroup", AccountGroupsMaintenanceAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}

		}catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message","ERROR_SAVE");

		}finally {
			xmlData= null;
		}
		return getView("statechange");
	}


	/**
	 *  Used To delete an account attribute HDR
	 * @return
	 * @throws SwtException
	 */
	public String delete() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String groupId = null;
		boolean isCascade = true;
		AccountGroup accountGrp= null;
		Boolean requireAuthorisation = false;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		try {
			log.debug(this.getClass().getName() + " method [delete] - Enter ");

			groupId = request.getParameter("groupId");
			//Fetch the required account attribute HDR
			accountGrp = accountGroupsMaintenanceManager.getAcctGroupDetail(groupId);


			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsAccountGroupWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));

			if(containsAccountGroupWithReqAuthY)
				requireAuthorisation = true;


			if(requireAuthorisation) {
				Long idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("ACCT_GRP", accountGrp.getId().getAccGrpId(), SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, null, null, "D", "P");
				if(requireAuthorisation)
					accountGrp.setMainEventId(""+idGenerated);
				ObjectMapper objectMapper = new ObjectMapper();
				String json = objectMapper.writeValueAsString(accountGrp);
				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "ACCOUNT_GROUP", accountGrp.getId().getAccGrpId(), "D", null, json);

			}

			accountGroupsMaintenanceManager.deleteAcctGroup(accountGrp);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [delete] - Exit ");
			return display();
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [deleteAccountGroup] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());

			return getView("fail");
		}


	}
	/**
	 * This method is to check if account group  is related to  payment or not.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkAccountInPayment() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		HashMap<String, String> result = new LinkedHashMap<String, String>();
		String accountsID = null;
		ArrayList accountID = new ArrayList<>();
		ArrayList entityOfAccount = new ArrayList<>();

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [checkAccountInPayment] - Entry");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "accountCheck";
			accountsID = request.getParameter("accountsId");
			ArrayList accountsIDList= new ArrayList(Arrays.asList(accountsID.split(",")));
			for (int i = 0; i < accountsIDList.size(); i++) {
				accountID.add(((String) accountsIDList.get(i)).split(";")[0]);
				entityOfAccount.add(((String) accountsIDList.get(i)).split(";")[1]);

			}
			xmlWriter.startElement(componentId);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			result = accountGroupsMaintenanceManager.checkAccountInPayment(accountID, entityOfAccount);

			String resultMap ="";
			for (Map.Entry<String, String> entry : result.entrySet()) {
				String key = entry.getKey().split(";")[0];
				String value = entry.getValue();
				resultMap+=key +"="+value+";";
				// ...
			}
			if(resultMap.length()>0) {
				resultMap = resultMap.substring(0, resultMap.length()-1);
			}
			responseConstructor.createElement(PCMConstant.IS_ACCOUNT_IN_PAY, resultMap);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(componentId);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException swtExp) {
			request.setAttribute("reply_message",
					"GENERIC_ERROR");

		} finally {
			log.debug(this.getClass().getName() + "- [checkAccountInPayment] - Exit");
		}
		return getView("statechange");
	}

}