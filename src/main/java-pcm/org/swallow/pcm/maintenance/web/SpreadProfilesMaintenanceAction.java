/*
 * @(#)SpreadProfilesMaintenanceAction.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.type.CollectionType;
import org.codehaus.jackson.map.type.TypeFactory;
import org.codehaus.jackson.type.JavaType;
import org.swallow.config.springMVC.BaseController;
import org.swallow.control.model.FacilityAccess;
import org.swallow.control.service.RoleManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.authorization.MaintenanceAuthUtils;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import org.swallow.maintenance.web.CurrencyAction;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.ProcessPointCategory;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.model.core.kv.KVType;
import org.swallow.pcm.maintenance.model.core.kv.TabKVType;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.pcm.maintenance.service.SpreadProfilesMaintenanceManager;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;





/**
 * <AUTHOR>
 *
 */



















import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/spreadProfilesPCM", "/spreadProfilesPCM.do"})
public class SpreadProfilesMaintenanceAction extends CommonAction {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/pc/maintenance/spreadprofilesadd");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/maintenance/spreadprofiles");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "spreadProfilesAdd":
				return spreadProfilesAdd();
			case "spreadProfilesView":
				return spreadProfilesView();
			case "display":
				return display();
			case "displayAdd":
				return displayAdd();
			case "displayChangeOrView":
				return displayChangeOrView();
			case "displayAddProcess":
				return displayAddProcess();
			case "displayChangeOrViewProcess":
				return displayChangeOrViewProcess();
			case "deleteSpreadProfile":
				return deleteSpreadProfile();
			case "save":
				return save();
		}
		return unspecified();
	}




	@Autowired
	private SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(SpreadProfilesMaintenanceAction.class);

	public void setSpreadProfilesMaintenanceManager(SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager) {
		this.spreadProfilesMaintenanceManager = spreadProfilesMaintenanceManager;
	}

	protected String unspecified() throws Exception {

		// return display();
		return getView("success");
	}

	public String spreadProfilesAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("method", "spreadProfilesAdd");
		return getView("add");
	}

	public String spreadProfilesView() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("method", "spreadProfilesView");
		return getView("add");
	}

	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		List<SpreadProfile> spreadProfilesDetails = new ArrayList<SpreadProfile>();
		String errorMessage = null;
		SystemFormats systemFormats = null;
		String currencyCode = null;

		try {

			log.debug(this.getClass().getName() + " - [display] - " + "Entry");
			currencyCode = request.getParameter("currencyCode");
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				currencyCode = SwtConstants.ALL_LABEL;
			}
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			spreadProfilesDetails = spreadProfilesMaintenanceManager.getSpreadProfilesList(currencyCode);
			request.setAttribute("spreadProfilesDetailsList", spreadProfilesDetails);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// build XML response
			return sendDisplayResponse(spreadProfilesDetails, systemFormats);

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
					+ ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			spreadProfilesDetails = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
		}
		return null;
	}

	public String displayAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String errorMessage = null;
		SystemFormats systemFormats = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		PCMCurrencyDetailsVO currencyList;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;
		try {

			log.debug(this.getClass().getName() + " - [displayAdd] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "SpreadPrfoilesMaintenanceAdd";



			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsSpreadProfileWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));




			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsSpreadProfileWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID);
			xmlWriter.startElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE_ADD);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** CurrencyCombo ***********/
			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil
					.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));
			// TODO
			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart("gridSpreadProcessPoints");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getSpreadProcessPointsGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(0);
			responseConstructor.formRowEnd();

			responseConstructor.formGridEnd("gridSpreadProcessPoints");

			xmlWriter.endElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE_ADD);
			request.setAttribute("data", xmlWriter.getData());

			// build XML response
			return getView("data");

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
					+ ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [displayCurrencyDetails] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [displayAdd] - Exit");
		}
		return null;
	}

	public String displayChangeOrView() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		List<SpreadProcessPoint> spreadProcessPoints = new ArrayList<SpreadProcessPoint>();
		List<SpreadProcessPoint> spreadProcessPointsOld = new ArrayList<SpreadProcessPoint>();

		List<AccountGroup> accountGroupsDetails = new ArrayList<AccountGroup>();
		String errorMessage = null;
		SystemFormats systemFormats = null;
		String selectedSpreadProfileId = null;
		AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = null;
		SpreadProfile spreadProfile = null;
		SpreadProfile spreadProfileOld = null;
		String maintEventId = null;

		ArrayList<SpreadProcessPoint> listSpreadProcessPointAdd = null ;
		ArrayList<SpreadProcessPoint> listSpreadProcessPointUpdate = null ;
		ArrayList<SpreadProcessPoint> listSpreadProcessPointDelete = null ;

		ArrayList<SpreadProcessPoint> listSpreadPointDeleteToIterate ;

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		PCMCurrencyDetailsVO currencyList;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		String selectedCurrencyCode = null;
		CurrencyMaintenanceManager currencyMaintenanceManager = null;
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;
		try {

			log.debug(this.getClass().getName() + " - [displayChangeOrView] - " + "Entry");
			selectedSpreadProfileId = request.getParameter("spreadId");
			maintEventId =  (String) request.getParameter("maintEventId");
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "SPREAD_PROFILE");

				String json = details.getNewState();

				ObjectMapper objectMapper = new ObjectMapper();
				try {
					spreadProfile = objectMapper.readValue(json, SpreadProfile.class);
				} catch (Exception e) {
				}


				//Get list of liquidity  from P_Reserve
				spreadProcessPoints = spreadProfilesMaintenanceManager.getSpreadProcessPointList(request,
						selectedSpreadProfileId);



				MaintenanceEventDetails detailsSpreadProcessPoints = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "SPREAD_PROCESS_POINT");
				String jsonReserve = detailsSpreadProcessPoints.getNewState();
//				HashMap<String, ArrayList<Reserve>> reserveMap  = objectMapper.readValue(jsonReserve, HashMap.class);

//				ObjectMapper objectMapper = new ObjectMapper();
				TypeFactory typeFactory = objectMapper.getTypeFactory();
				CollectionType collectionType = typeFactory.constructCollectionType(ArrayList.class, SpreadProcessPoint.class);
				JavaType mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
				HashMap<String, ArrayList<SpreadProcessPoint>> processPointMap = null;
				try {
					processPointMap = objectMapper.readValue(jsonReserve, mapType);
				} catch (Exception e) {
				}


				listSpreadProcessPointAdd = processPointMap.get("add");
				listSpreadProcessPointUpdate = processPointMap.get("update");
				listSpreadProcessPointDelete =  processPointMap.get("delete");


				// Add new Reserve records
				spreadProcessPoints.addAll(listSpreadProcessPointAdd);


				ArrayList<SpreadProcessPoint> listReserveUpdateToIterate = listSpreadProcessPointUpdate;
				spreadProcessPoints = spreadProcessPoints.stream()
						.filter(r -> !listReserveUpdateToIterate.stream().map(SpreadProcessPoint::getProcessPointId).anyMatch(id -> id.equals(r.getProcessPointId())))
						.collect(Collectors.toCollection(ArrayList::new));

				spreadProcessPoints.addAll(listSpreadProcessPointUpdate);

				listSpreadPointDeleteToIterate = listSpreadProcessPointDelete;
				// Delete existing Reserve records
				spreadProcessPoints = spreadProcessPoints.stream()
						.filter(r -> !listSpreadPointDeleteToIterate.stream().map(SpreadProcessPoint::getProcessPointId).anyMatch(id -> id.equals(r.getProcessPointId())))
						.collect(Collectors.toCollection(ArrayList::new));

				String categories = null;
				for (Iterator iterator = spreadProcessPoints.iterator(); iterator.hasNext();) {
					SpreadProcessPoint spreadProcessPoint = (SpreadProcessPoint) iterator.next();
					// Set the category process for each spread process point
					if ("A".equals(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.All", request));
					} else if ("O".equals(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.Only", request));
					} else if ("E".equals(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.ExceptAll", request));
					}


					Set<ProcessPointCategory> processPointCategories = spreadProcessPoint.getProcessPointCategory();
					if ("A".equalsIgnoreCase(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setCategories("All");
					}
				}


				for (Iterator iterator = listSpreadProcessPointDelete.iterator(); iterator.hasNext();) {
					SpreadProcessPoint spreadProcessPoint = (SpreadProcessPoint) iterator.next();
					// Set the category process for each spread process point
					if ("A".equals(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.All", request));
					} else if ("O".equals(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.Only", request));
					} else if ("E".equals(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.ExceptAll", request));
					}


					Set<ProcessPointCategory> processPointCategories = spreadProcessPoint.getProcessPointCategory();
					if (processPointCategories.isEmpty() && "A".equalsIgnoreCase(spreadProcessPoint.getProcessCategories())) {
						spreadProcessPoint.setCategories("All");
					}
				}



				spreadProfileOld = spreadProfilesMaintenanceManager.getSpreadProfileDetails(selectedSpreadProfileId);
				if(spreadProfileOld != null)
					spreadProcessPointsOld = spreadProfilesMaintenanceManager.getSpreadProcessPointList(request,
							selectedSpreadProfileId);
			}else {

				spreadProfile = spreadProfilesMaintenanceManager.getSpreadProfileDetails(selectedSpreadProfileId);
				spreadProcessPoints = spreadProfilesMaintenanceManager.getSpreadProcessPointList(request,
						selectedSpreadProfileId);
			}
			accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
					.getBean("accountGroupsMaintenanceManager");
			accountGroupsDetails = accountGroupsMaintenanceManager.getAccountGroupDetailList(selectedSpreadProfileId);

			request.setAttribute("spreadProfilesDetailsList", spreadProcessPoints);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// build XML response
//			return sendDisplayResponseViewChange(request, mapping, spreadProfile, spreadProcessPoints,
//					accountGroupsDetails, systemFormats);

			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsSpreadProfileWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			selectedCurrencyCode = request.getParameter("currencyCode");
			if (SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				selectedCurrencyCode = SwtConstants.ALL_LABEL;
			}
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "SpreadPrfoilesMaintenance";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsSpreadProfileWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID);
			xmlWriter.startElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE_ADD);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			responseConstructor.createElement(PCMConstant.SPREAD_NAME, spreadProfile.getSpreadProfileName());
			if(!SwtUtil.isEmptyOrNull(maintEventId) && spreadProfileOld != null) {
				responseConstructor.createElement(PCMConstant.SPREAD_NAME+SwtConstants.MAINT_EVENT_OLD_VALUE, spreadProfileOld.getSpreadProfileName());
			}
			responseConstructor.createElement(PCMConstant.CURRENCY_CODE, spreadProfile.getCurrencyCode());

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** CurrencyCombo ***********/
			currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (selectedCurrencyCode.equals(row.getValue())) {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				} else {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));

				}

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart("gridSpreadProcessPoints");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getSpreadProcessPointsGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(spreadProcessPoints.size());
			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				HashMap<String, String> att = null;
				for (Iterator<SpreadProcessPoint> it = spreadProcessPoints.iterator(); it.hasNext();) {
					SpreadProcessPoint spreadProcessPoint = it.next();
					SpreadProcessPoint  oldSpreadProcessPoint = null;
					att = new HashMap<String, String>();
					boolean reserveNew = listSpreadProcessPointAdd.stream()
							.anyMatch(r -> r.getProcessPointId().equals(spreadProcessPoint.getProcessPointId()));
					if(reserveNew) {
						att.put("isNewRow", "Y");
						oldSpreadProcessPoint = new SpreadProcessPoint();
					}else {
						boolean reserveChanged = listSpreadProcessPointUpdate.stream()
								.anyMatch(r -> r.getProcessPointId().equals(spreadProcessPoint.getProcessPointId()));

						oldSpreadProcessPoint  = spreadProcessPointsOld.stream()
								.filter(r -> r.getProcessPointId().equals(spreadProcessPoint.getProcessPointId())).findAny().orElse(new SpreadProcessPoint());
						if(reserveChanged)
							att.put("isUpdatedRow", "Y");
						else
							att.put("isUpdatedRow", "N");

					}


					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.PROCESS_ID,
							spreadProcessPoint.getProcessPointId() + "", att);
					responseConstructor.createRowElement(PCMConstant.PROCESS_NAME, spreadProcessPoint.getProcessName(), MaintenanceAuthUtils.addPreviousValueBasedOnField(spreadProcessPoint, oldSpreadProcessPoint, "processName", att));
					responseConstructor.createRowElement(PCMConstant.TIME, spreadProcessPoint.getProcessPointTime(), MaintenanceAuthUtils.addPreviousValueBasedOnField(spreadProcessPoint, oldSpreadProcessPoint, "processPointTime", att));
					responseConstructor.createRowElement(PCMConstant.TARGET,
							spreadProcessPoint.getTargetPaymentPercent() + "", MaintenanceAuthUtils.addPreviousValueBasedOnField(spreadProcessPoint, oldSpreadProcessPoint, "targetPaymentPercent", att));
					responseConstructor.createRowElement(PCMConstant.PROCESS,
							spreadProcessPoint.getProcessCategoriesText(), MaintenanceAuthUtils.addPreviousValueBasedOnField(spreadProcessPoint, oldSpreadProcessPoint, "processCategoriesText", att));
					responseConstructor.createRowElement(PCMConstant.CATEGORIES, spreadProcessPoint.getCategories(), MaintenanceAuthUtils.addPreviousValueBasedOnField(spreadProcessPoint, oldSpreadProcessPoint, "categories", att));
					responseConstructor.formRowEnd();

				}

				for (Iterator<SpreadProcessPoint> it = listSpreadProcessPointDelete.iterator(); it.hasNext();) {
					SpreadProcessPoint spreadProcessPoint = it.next();
					att = new HashMap<String, String>();
					att.put("isDeletedRow", "Y");

					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.PROCESS_ID,
							spreadProcessPoint.getProcessPointId() + "", att);
					responseConstructor.createRowElement(PCMConstant.PROCESS_NAME, spreadProcessPoint.getProcessName(), att);
					responseConstructor.createRowElement(PCMConstant.TIME, spreadProcessPoint.getProcessPointTime(), att);
					responseConstructor.createRowElement(PCMConstant.TARGET,
							spreadProcessPoint.getTargetPaymentPercent() + "", att);
					responseConstructor.createRowElement(PCMConstant.PROCESS,
							spreadProcessPoint.getProcessCategoriesText(), att);
					responseConstructor.createRowElement(PCMConstant.CATEGORIES, spreadProcessPoint.getCategories(), att);
					responseConstructor.formRowEnd();
				}

			}else {
				// Iterating rules definition details
				for (Iterator<SpreadProcessPoint> it = spreadProcessPoints.iterator(); it.hasNext();) {
					// Obtain spread Profile tag from iterator
					SpreadProcessPoint spreadProcessPoint = it.next();
					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.PROCESS_ID,
							spreadProcessPoint.getProcessPointId() + "");
					responseConstructor.createRowElement(PCMConstant.PROCESS_NAME, spreadProcessPoint.getProcessName());
					responseConstructor.createRowElement(PCMConstant.TIME, spreadProcessPoint.getProcessPointTime());
					responseConstructor.createRowElement(PCMConstant.TARGET,
							spreadProcessPoint.getTargetPaymentPercent() + "");
					responseConstructor.createRowElement(PCMConstant.PROCESS,
							spreadProcessPoint.getProcessCategoriesText());
					responseConstructor.createRowElement(PCMConstant.CATEGORIES, spreadProcessPoint.getCategories());
					responseConstructor.formRowEnd();
				}
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridSpreadProcessPoints");

			responseConstructor.formGridStart("gridAccountGroups");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(accountGroupsDetails.size());
			// Iterating rules definition details
			for (Iterator<AccountGroup> it = accountGroupsDetails.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				AccountGroup accountGroup = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.ACC_GP_ID, accountGroup.getId().getAccGrpId());
				responseConstructor.createRowElement(PCMConstant.ACC_GP_NAME, accountGroup.getDescription());
				responseConstructor.createRowElement(PCMConstant.CURRENCY_CODE, accountGroup.getCurrencyCode());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridAccountGroups");

			xmlWriter.endElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE_ADD);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			ex.printStackTrace();
			xmlWriter.clearData();
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [sendDisplayResponseViewChange] method : - " + ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			xmlWriter.clearData();

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponseViewChange] method : - "
					+ ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponseViewChange ] - Exit");
		}

	}

	public String displayAddProcess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String errorMessage = null;
		SystemFormats systemFormats = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		PCMCurrencyDetailsVO processList;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		List<Category> categoryDetails = new ArrayList<Category>();

		try {

			log.debug(this.getClass().getName() + " - [displayAddProcess] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "spreadProcessPoints";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.SPREAD_PROCESS_POINT);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** process Category Combo ***********/
			lstOptions.add(new OptionInfo("All", "ALL", false));
			lstOptions.add(new OptionInfo("All Except", "ALLEXCEPT", false));
			lstOptions.add(new OptionInfo("Only", "ONLY", false));
			lstSelect.add(new SelectInfo(PCMConstant.PROCESS_CATEGORY_LIST, lstOptions));
			// TODO
			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getCategoryGridColumns(width, columnOrder, hiddenColumns));
			CategoryMaintenanceManager categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil
					.getBean("categoryMaintenanceManager");
			categoryDetails = (List<Category>) categoryMaintenanceManager.getCategoryDetailList();
			// form rows (records)
			responseConstructor.formRowsStart(categoryDetails.size());
			// Iterating rules definition details
			for (Iterator<Category> it = categoryDetails.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				Category category = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.CATEGORY_ID, category.getId().getCategoryId());
				responseConstructor.createRowElement(PCMConstant.CATEGORY_NAME, category.getCategoryName());
				responseConstructor.createRowElement(PCMConstant.CHECKED, "false");
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowEnd();

			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.SPREAD_PROCESS_POINT);
			request.setAttribute("data", xmlWriter.getData());

			// build XML response
			return getView("data");

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
					+ ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [displayAddProcess] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [displayAddProcess] - Exit");
		}
		return null;
	}

	public String displayChangeOrViewProcess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		List<Category> categoriesList = new ArrayList<Category>();
		List<AccountGroup> accountGroupsDetails = new ArrayList<AccountGroup>();
		String errorMessage = null;
		SystemFormats systemFormats = null;
		String selectedSpreadProfileId = null;
		CategoryMaintenanceManager categoryMaintenanceManager = null;

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		String selectedProcessName = null;
		String selectedProcessTime = null;
		String selectedTarget = null;
		String selectedProcessCategory = null;
		String selectedCategories = null;
		String maintEventId = null;
		Long processId = null;
		String spreadId = null;
		SpreadProcessPoint selectedSpreadProcessPoint = null;
		SpreadProcessPoint oldSpreadProcessPoint = null;
		List<String> addedCategories = null;
		List<String> removedCategories = null;
		try {

			log.debug(this.getClass().getName() + " - [displayChangeOrViewProcess] - " + "Entry");
			selectedSpreadProfileId = request.getParameter("spreadId");
			maintEventId = request.getParameter("maintEventId");
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			processId = !SwtUtil.isEmptyOrNull(request.getParameter("processId"))?Long.parseLong(request.getParameter("processId")):null;
			if(processId != null) {
				spreadId = request.getParameter("spreadId");
				if(!SwtUtil.isEmptyOrNull(maintEventId)) {
					ArrayList<SpreadProcessPoint> listSpreadProcessPointAdd = null;
					ArrayList<SpreadProcessPoint> listSpreadProcessPointUpdate = null;
					ArrayList<SpreadProcessPoint> listSpreadProcessPointDelete = null;

					ObjectMapper objectMapper = new ObjectMapper();
					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails detailsSpreadProcessPoints = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "SPREAD_PROCESS_POINT");
					String jsonReserve = detailsSpreadProcessPoints.getNewState();
					//				HashMap<String, ArrayList<Reserve>> reserveMap  = objectMapper.readValue(jsonReserve, HashMap.class);

					//				ObjectMapper objectMapper = new ObjectMapper();
					TypeFactory typeFactory = objectMapper.getTypeFactory();
					CollectionType collectionType = typeFactory.constructCollectionType(ArrayList.class, SpreadProcessPoint.class);
					JavaType mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
					HashMap<String, ArrayList<SpreadProcessPoint>> processPointMap = null;
					try {
						processPointMap = objectMapper.readValue(jsonReserve, mapType);
					} catch (Exception e) {
					}


					listSpreadProcessPointAdd = processPointMap.get("add");
					for (int i = 0; i < listSpreadProcessPointAdd.size(); i++) {
						if(listSpreadProcessPointAdd.get(i).getProcessPointId().equals(processId)) {
							selectedSpreadProcessPoint = listSpreadProcessPointAdd.get(i);
							break;
						}

					}
					if(selectedSpreadProcessPoint == null) {
						listSpreadProcessPointUpdate = processPointMap.get("update");
						for (int i = 0; i < listSpreadProcessPointUpdate.size(); i++) {
							if(listSpreadProcessPointUpdate.get(i).getProcessPointId().equals(processId)) {
								selectedSpreadProcessPoint = listSpreadProcessPointUpdate.get(i);
								break;
							}

						}
					}
//				 listSpreadProcessPointDelete =  processPointMap.get("delete");


					List<SpreadProcessPoint>  spreadProcessPoints = spreadProfilesMaintenanceManager.getSpreadProcessPointList(request,
							selectedSpreadProfileId);
					for (int i = 0; i < spreadProcessPoints.size(); i++) {
						if(spreadProcessPoints.get(i).getProcessPointId().equals(processId)) {
							oldSpreadProcessPoint = spreadProcessPoints.get(i);
							break;
						}
					}
					if(selectedSpreadProcessPoint == null && oldSpreadProcessPoint != null)
						selectedSpreadProcessPoint = oldSpreadProcessPoint;
					List<String> oldCategories = null;
					List<String> newCategories = null;
					if(selectedSpreadProcessPoint!=null && selectedSpreadProcessPoint.getCategories() != null)
						newCategories = Arrays.asList(selectedSpreadProcessPoint.getCategories().split(","));
					else
						newCategories = new ArrayList<String>();

					if(oldSpreadProcessPoint != null && oldSpreadProcessPoint.getCategories() != null)
						oldCategories = Arrays.asList(oldSpreadProcessPoint.getCategories().split(","));
					else
						oldCategories = new ArrayList<String>();

					addedCategories = new ArrayList<>(newCategories);
					addedCategories.removeAll(oldCategories);

					removedCategories = new ArrayList<>(oldCategories);
					removedCategories.removeAll(newCategories);


				}


			}
			categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil.getBean("categoryMaintenanceManager");
			categoriesList = (List<Category>) categoryMaintenanceManager.getCategoryDetailList();

			request.setAttribute("categoriesList", categoriesList);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// build XML response
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			selectedProcessName = request.getParameter("selectedProcessName");
			selectedProcessTime = request.getParameter("selectedProcessTime");
			selectedProcessCategory = request.getParameter("selectedTarget");
			selectedProcessCategory = request.getParameter("selectedProcessCategory");
			selectedCategories = request.getParameter("selectedCategories");

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "spreadProcessPoints";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.SPREAD_PROCESS_POINT);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** process Category Combo ***********/
			lstOptions.add(new OptionInfo("All", "ALL", false));
			lstOptions.add(new OptionInfo("All Except", "ALLEXCEPT", false));
			lstOptions.add(new OptionInfo("Only", "ONLY", false));
			lstSelect.add(new SelectInfo(PCMConstant.PROCESS_CATEGORY_LIST, lstOptions));
			// TODO
			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getCategoryGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(categoriesList.size());
			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				for (Iterator<Category> it = categoriesList.iterator(); it.hasNext();) {
					HashMap<String, String> att = new HashMap<String, String>();

					// Obtain spread Profile tag from iterator
					Category category = it.next();
					if(addedCategories.contains(category.getId().getCategoryId())) {
						att.put("isNewRow", "Y");
					}else if(removedCategories.contains(category.getId().getCategoryId())) {
						att.put("isDeletedRow", "Y");
					}else {
						att.put("isDeletedRow", "N");
					}
					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.CATEGORY_ID, category.getId().getCategoryId(), att);
					responseConstructor.createRowElement(PCMConstant.CATEGORY_NAME, category.getCategoryName(), att);
					responseConstructor.createRowElement(PCMConstant.CHECKED, "false", att);
					responseConstructor.formRowEnd();
				}
			}else {
				// Iterating rules definition details
				for (Iterator<Category> it = categoriesList.iterator(); it.hasNext();) {
					// Obtain spread Profile tag from iterator
					Category category = it.next();
					responseConstructor.formRowStart();
					responseConstructor.createRowElement(PCMConstant.CATEGORY_ID, category.getId().getCategoryId());
					responseConstructor.createRowElement(PCMConstant.CATEGORY_NAME, category.getCategoryName());
					responseConstructor.createRowElement(PCMConstant.CHECKED, "false");
					responseConstructor.formRowEnd();
				}
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.SPREAD_PROCESS_POINT);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			ex.printStackTrace();
			xmlWriter.clearData();
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [sendDisplayResponseViewChange] method : - " + ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			xmlWriter.clearData();

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponseViewChange] method : - "
					+ ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponseViewChange ] - Exit");
		}
	}

	public String sendDisplayResponseViewChange(SpreadProfile spreadProfile, List<SpreadProcessPoint> spreadProcessPoints,
												List<AccountGroup> accountGroupsDetails, SystemFormats systemFormats) {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		PCMCurrencyDetailsVO currencyList;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		String selectedCurrencyCode = null;
		CurrencyMaintenanceManager currencyMaintenanceManager = null;

		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponseViewChange ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			selectedCurrencyCode = request.getParameter("currencyCode");
			if (SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				selectedCurrencyCode = SwtConstants.ALL_LABEL;
			}
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "SpreadPrfoilesMaintenance";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.startElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE_ADD);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			responseConstructor.createElement(PCMConstant.SPREAD_NAME, spreadProfile.getSpreadProfileName());
			responseConstructor.createElement(PCMConstant.CURRENCY_CODE, spreadProfile.getCurrencyCode());

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** CurrencyCombo ***********/
			currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (selectedCurrencyCode.equals(row.getValue())) {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				} else {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));

				}

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart("gridSpreadProcessPoints");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getSpreadProcessPointsGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(spreadProcessPoints.size());
			// Iterating rules definition details
			for (Iterator<SpreadProcessPoint> it = spreadProcessPoints.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				SpreadProcessPoint spreadProcessPoint = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PROCESS_ID,
						spreadProcessPoint.getProcessPointId() + "");
				responseConstructor.createRowElement(PCMConstant.PROCESS_NAME, spreadProcessPoint.getProcessName());
				responseConstructor.createRowElement(PCMConstant.TIME, spreadProcessPoint.getProcessPointTime());
				responseConstructor.createRowElement(PCMConstant.TARGET,
						spreadProcessPoint.getTargetPaymentPercent() + "");
				responseConstructor.createRowElement(PCMConstant.PROCESS,
						spreadProcessPoint.getProcessCategoriesText());
				responseConstructor.createRowElement(PCMConstant.CATEGORIES, spreadProcessPoint.getCategories());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridSpreadProcessPoints");

			responseConstructor.formGridStart("gridAccountGroups");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getAccountGroupsGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(accountGroupsDetails.size());
			// Iterating rules definition details
			for (Iterator<AccountGroup> it = accountGroupsDetails.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				AccountGroup accountGroup = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.ACC_GP_ID, accountGroup.getId().getAccGrpId());
				responseConstructor.createRowElement(PCMConstant.ACC_GP_NAME, accountGroup.getDescription());
				responseConstructor.createRowElement(PCMConstant.CURRENCY_CODE, accountGroup.getCurrencyCode());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd("gridAccountGroups");

			xmlWriter.endElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE_ADD);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			ex.printStackTrace();
			xmlWriter.clearData();
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [sendDisplayResponseViewChange] method : - " + ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			xmlWriter.clearData();

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponseViewChange] method : - "
					+ ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponseViewChange ] - Exit");
		}
	}

	public String sendDisplayResponseViewChangeProcess(
			List<Category> categoriesList, SystemFormats systemFormats) {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		String selectedProcessName = null;
		String selectedProcessTime = null;
		String selectedTarget = null;
		String selectedProcessCategory = null;
		String selectedCategories = null;

		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponseViewChange ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			selectedProcessName = request.getParameter("selectedProcessName");
			selectedProcessTime = request.getParameter("selectedProcessTime");
			selectedProcessCategory = request.getParameter("selectedTarget");
			selectedProcessCategory = request.getParameter("selectedProcessCategory");
			selectedCategories = request.getParameter("selectedCategories");

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "spreadProcessPoints";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.SPREAD_PROCESS_POINT);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** process Category Combo ***********/
			lstOptions.add(new OptionInfo("All", "ALL", false));
			lstOptions.add(new OptionInfo("All Except", "ALLEXCEPT", false));
			lstOptions.add(new OptionInfo("Only", "ONLY", false));
			lstSelect.add(new SelectInfo(PCMConstant.PROCESS_CATEGORY_LIST, lstOptions));
			// TODO
			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getCategoryGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(categoriesList.size());
			// Iterating rules definition details
			for (Iterator<Category> it = categoriesList.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				Category category = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.CATEGORY_ID, category.getId().getCategoryId());
				responseConstructor.createRowElement(PCMConstant.CATEGORY_NAME, category.getCategoryName());
				responseConstructor.createRowElement(PCMConstant.CHECKED, "false");
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.SPREAD_PROCESS_POINT);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [sendDisplayResponseViewChange] method : - " + ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponseViewChange] method : - "
					+ ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponseViewChange ] - Exit");
		}
	}

	private List<ColumnInfo> getSpreadProcessPointsGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {

		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		// Hash map to hold column hidden_Columns
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		// Array list to hold hidden Column array
		ArrayList<String> lstHiddenColunms = null;
		// String array variable to hold hidden columns property
		String[] hiddenColumnsProp = null;

		try {
			log.debug(this.getClass().getName() + " - [ getSpreadProcessPointsGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PROCESS_NAME + "=180," + PCMConstant.TIME + "=90," + PCMConstant.TARGET + "=110,"
						+ PCMConstant.PROCESS + "=110," + PCMConstant.CATEGORIES + "=220";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PROCESS_NAME + "," + PCMConstant.TIME + "," + PCMConstant.TARGET + ","
						+ PCMConstant.PROCESS + "," + PCMConstant.CATEGORIES;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Spread Process point name
				if (order.equals(PCMConstant.PROCESS_NAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PROCESS_NAME_HEADER, PCMConstant.PROCESS_NAME,
							PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get(PCMConstant.PROCESS_NAME)),
							false, true, hiddenColumnsMap.get(PCMConstant.PROCESS_NAME)));

				// Spread Process point time
				if (order.equals(PCMConstant.TIME))
					lstColumns.add(new ColumnInfo(PCMConstant.TIME_COLUMN_HEADER, PCMConstant.TIME,
							PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get(PCMConstant.TIME)), false,
							true, hiddenColumnsMap.get(PCMConstant.TIME)));
				// Target percentage
				if (order.equals(PCMConstant.TARGET))
					lstColumns.add(new ColumnInfo(PCMConstant.TARGET_COLUMN_HEADER, PCMConstant.TARGET,
							PCMConstant.COLUMN_TYPE_NUMBER, 2, Integer.parseInt(widths.get(PCMConstant.TARGET)), false,
							true, hiddenColumnsMap.get(PCMConstant.TARGET)));
				// Process categories
				if (order.equals(PCMConstant.PROCESS))
					lstColumns.add(new ColumnInfo(PCMConstant.PROCESS_COLUMN_HEADER, PCMConstant.PROCESS,
							PCMConstant.COLUMN_TYPE_STRING, 3, Integer.parseInt(widths.get(PCMConstant.PROCESS)), false,
							true, hiddenColumnsMap.get(PCMConstant.PROCESS)));
				// Categories
				if (order.equals(PCMConstant.CATEGORIES))
					lstColumns.add(new ColumnInfo(PCMConstant.CATEGORIES_COLUMN_HEADER, PCMConstant.CATEGORIES,
							PCMConstant.COLUMN_TYPE_STRING, 4, Integer.parseInt(widths.get(PCMConstant.CATEGORIES)),
							false, true, hiddenColumnsMap.get(PCMConstant.CATEGORIES)));
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSpreadProcessPointsGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getSpreadProcessPointsGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getSpreadProcessPointsGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	private List<ColumnInfo> getAccountGroupsGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {

		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		// Hash map to hold column hidden_Columns
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		// Array list to hold hidden Column array
		ArrayList<String> lstHiddenColunms = null;
		// String array variable to hold hidden columns property
		String[] hiddenColumnsProp = null;

		try {
			log.debug(this.getClass().getName() + " - [ getAccountGroupsGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.ACC_GP_ID + "=300," + PCMConstant.ACC_GP_NAME + "=340," + PCMConstant.CURRENCY_CODE
						+ "=160";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.ACC_GP_ID + "," + PCMConstant.ACC_GP_NAME + "," + PCMConstant.CURRENCY_CODE;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Spread Process point name
				if (order.equals(PCMConstant.ACC_GP_ID))
					lstColumns.add(new ColumnInfo(PCMConstant.ACC_GP_ID_COLUMN_HEADER, PCMConstant.ACC_GP_ID,
							PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get(PCMConstant.ACC_GP_ID)),
							false, true, hiddenColumnsMap.get(PCMConstant.ACC_GP_ID)));

				// Spread Process point time
				if (order.equals(PCMConstant.ACC_GP_NAME))
					lstColumns.add(new ColumnInfo(PCMConstant.ACC_GP_NAME_COLUMN_HEADER, PCMConstant.ACC_GP_NAME,
							PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get(PCMConstant.ACC_GP_NAME)),
							false, true, hiddenColumnsMap.get(PCMConstant.ACC_GP_NAME)));
				// Target percentage
				if (order.equals(PCMConstant.CURRENCY_CODE))
					lstColumns.add(new ColumnInfo(PCMConstant.CURRENCY_CODE_COLUMN_HEADER, PCMConstant.CURRENCY_CODE,
							PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get(PCMConstant.CURRENCY_CODE)),
							false, true, hiddenColumnsMap.get(PCMConstant.CURRENCY_CODE)));
			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Catched in [getAccountGroupsGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getAccountGroupsGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getAccountGroupsGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	private List<ColumnInfo> getCategoryGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {

		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		// Hash map to hold column hidden_Columns
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		// Array list to hold hidden Column array
		ArrayList<String> lstHiddenColunms = null;
		// String array variable to hold hidden columns property
		String[] hiddenColumnsProp = null;

		try {
			log.debug(this.getClass().getName() + " - [ getCategoryGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.CHECKED + "=40," + PCMConstant.CATEGORY_ID + "=200," + PCMConstant.CATEGORY_NAME + "=285";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.CHECKED + "," + PCMConstant.CATEGORY_ID + "," + PCMConstant.CATEGORY_NAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Spread Process point name
				if (order.equals(PCMConstant.CATEGORY_ID))
					lstColumns.add(new ColumnInfo(PCMConstant.CATEGORY_ID_HEADER, PCMConstant.CATEGORY_ID,
							PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get(PCMConstant.CATEGORY_ID)),
							false, true, hiddenColumnsMap.get(PCMConstant.CATEGORY_ID)));

				// Spread Process point time
				if (order.equals(PCMConstant.CATEGORY_NAME))
					lstColumns.add(new ColumnInfo(PCMConstant.CATEGORY_NAME_HEADER, PCMConstant.CATEGORY_NAME,
							PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get(PCMConstant.CATEGORY_NAME)),
							false, true, hiddenColumnsMap.get(PCMConstant.CATEGORY_NAME)));
				// Target percentage
				if (order.equals(PCMConstant.CHECKED))
					lstColumns.add(new ColumnInfo("", PCMConstant.CHECKED, PCMConstant.COLUMN_TYPE_CHECK, 2,
							Integer.parseInt(widths.get(PCMConstant.CHECKED)), false, false,
							hiddenColumnsMap.get(PCMConstant.CHECKED)));
			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Catched in [getCategoryGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCategoryGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * This method forms the xml for displaying the rules list.
	 *
	 * @param systemFormats - passing system formats date
	 * @return
	 */
	public String sendDisplayResponse(List<SpreadProfile> spreadProfilesDetails, SystemFormats systemFormats) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		PCMCurrencyDetailsVO currencyList;
		// List for selects info
		List<SelectInfo> lstSelect = null;
		// List for option of status
		List<OptionInfo> lstOptions = null;
		String selectedCurrencyCode = null;
		CurrencyMaintenanceManager currencyMaintenanceManager = null;
		CommonDataManager cdm = null;
		int menuAccessId = 2;
		String roleId;
		ArrayList<FacilityAccess>  accessList = null;
		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			selectedCurrencyCode = request.getParameter("currencyCode");
			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);
			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_SPREAD_PROFILES_MAINTENANCE + "",
					cdm.getUser());
			if (menuItem != null) {
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
			}
			if (SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				selectedCurrencyCode = SwtConstants.ALL_LABEL;
			}
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "SpreadPrfoilesMaintenance";

			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			boolean containsSpreadProfileWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));


			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(PCMConstant.REQUIRE_AUTHORISATION, containsSpreadProfileWithReqAuthY);
			xmlWriter.addAttribute(PCMConstant.FACILITY_ID, PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			/**** CurrencyCombo ***********/
			currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (selectedCurrencyCode.equals(row.getValue())) {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				} else {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));

				}

			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			lstSelect.add(new SelectInfo(PCMConstant.TARGET, lstOptions));

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getSpreadProfilesGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(spreadProfilesDetails.size());
			// Iterating rules definition details
			for (Iterator<SpreadProfile> it = spreadProfilesDetails.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				SpreadProfile spreadProfile = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.SPREAD_ID, spreadProfile.getId().getSpreadProfileId());
				responseConstructor.createRowElement(PCMConstant.SPREAD_NAME, spreadProfile.getSpreadProfileName());
				responseConstructor.createRowElement(PCMConstant.CURRENCY_CODE, spreadProfile.getCurrencyCode());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.SPREAD_PROFILES_MAINTENANCE);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();
			// getExceptionXmlData(ex.getMessage());
			// log exception in database
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();
			// logs exception in data base
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getSpreadProfilesGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {

		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		// Hash map to hold column hidden_Columns
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		// Array list to hold hidden Column array
		ArrayList<String> lstHiddenColunms = null;
		// String array variable to hold hidden columns property
		String[] hiddenColumnsProp = null;

		try {
			log.debug(this.getClass().getName() + " - [ getSpreadProfilesGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.CURRENCY_CODE + "=150," + PCMConstant.SPREAD_ID + "=220," + PCMConstant.SPREAD_NAME
						+ "=300";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.CURRENCY_CODE + "," + PCMConstant.SPREAD_ID + "," + PCMConstant.SPREAD_NAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				// Currency Code column
				if (order.equals(PCMConstant.CURRENCY_CODE))
					lstColumns.add(new ColumnInfo(PCMConstant.CURRENCY_CODE_COLUMN_HEADER, PCMConstant.CURRENCY_CODE,
							PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get(PCMConstant.CURRENCY_CODE)),
							false, true, hiddenColumnsMap.get(PCMConstant.CURRENCY_CODE)));

				// Spread ID column
				if (order.equals(PCMConstant.SPREAD_ID))
					lstColumns.add(new ColumnInfo(PCMConstant.SPREAD_ID_COLUMN_HEADER, PCMConstant.SPREAD_ID,
							PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get(PCMConstant.SPREAD_ID)),
							false, true, hiddenColumnsMap.get(PCMConstant.SPREAD_ID)));

				// Spread name column
				if (order.equals(PCMConstant.SPREAD_NAME))
					lstColumns.add(new ColumnInfo(PCMConstant.SPREAD_NAME_COLUMN_HEADER, PCMConstant.SPREAD_NAME,
							PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get(PCMConstant.SPREAD_NAME)),
							false, true, hiddenColumnsMap.get(PCMConstant.SPREAD_NAME)));
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Catched in [getSpreadProfilesGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getSpreadProfilesGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSpreadProfilesGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	public String deleteSpreadProfile() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String selectedSpreadProfileId = null;
		ActionErrors errors = null;
		Boolean requireAuthorisation = false;
		Long idGenerated = null;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [deleteSpreadProfile] - " + "Entry");

			selectedSpreadProfileId = request.getParameter("selectedSpreadId");
			if (selectedSpreadProfileId != null) {


				RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
				roleId = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


				boolean containsSpreadWithReqAuthY = accessList.stream()
						.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID))
						.anyMatch(fa -> fa.getReqAuth().equals("Y"));

				if(containsSpreadWithReqAuthY)
					requireAuthorisation = true;



				if(requireAuthorisation) {
					try {
						List records = PCMUtil.executeNamedSelectQuery("spread_integrity_query", selectedSpreadProfileId);
						if (!records.isEmpty()) {
							HashMap<String,String>  exist = (HashMap<String, String>) records.get(0);
							if (!exist.get("exist").equals("Y")) {
								idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("SPREAD_PROFILES", selectedSpreadProfileId, SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, null, null, "D", "P");
								ObjectMapper objectMapper = new ObjectMapper();
								SpreadProfile spreadProfile = spreadProfilesMaintenanceManager.getSpreadProfileDetails(selectedSpreadProfileId);

								String json;
								json = objectMapper.writeValueAsString(spreadProfile);
								MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "SPREAD_PROFILE", selectedSpreadProfileId, "D", null, json);
							}
						}
					} catch (Exception e1) {
						// TODO A uto-generated catch block
						e1.printStackTrace();
					}
					spreadProfilesMaintenanceManager.deleteSpreadProfile(selectedSpreadProfileId, idGenerated);

				}else {
					spreadProfilesMaintenanceManager.deleteSpreadProfile(selectedSpreadProfileId, null);
				}



			}
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName() + " - Exception Catched in [deleteSpreadProfile] method : - " + swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			if (swtExp.getErrorCode().equals("errors.DataIntegrityViolationExceptioninDelete")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtExp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName() + " - Exception Catched in [deleteSpreadProfile] method swt : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			}

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute("reply_location", swtExp.getStackTrace()[0].getClassName() + "."
					+ swtExp.getStackTrace()[0].getMethodName() + ":" + swtExp.getStackTrace()[0].getLineNumber());
			return getView("statechange");

		} finally {
			log.debug(this.getClass().getName() + " - [deleteSpreadProfile] - Exit");
		}
		return display();
	}

	/**
	 * Action method to save account groups details
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String screenName = null;
		String spreadProfileId = null;
		String spreadProfileName = null;
		String currencyCode = null;
		ActionErrors errors = null;
		SpreadProfile spreadProfile;
		String currentUserId = null;
		String xmlData = null;
		ArrayList<TabKVType> crudResult = null;
		SpreadProcessPoint spreadProcessPoint = null;
		ArrayList<SpreadProcessPoint> listSpreadProcessPointAdd = null;
		ArrayList<SpreadProcessPoint> listSpreadProcessPointUpdate = null;
		ArrayList<SpreadProcessPoint> listSpreadProcessPointDelete = null;
		List<String> times = null;
		List<AccountGroup> accountGroupsDetails = new ArrayList<AccountGroup>();
		AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = null;
		boolean requireAuthorisation = false;
		String maintEventId = null;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		boolean containsSpreadWithReqAuthY;
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entry");
			times = new ArrayList();
			currentUserId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			spreadProfile = new SpreadProfile();
			listSpreadProcessPointAdd = new ArrayList<SpreadProcessPoint>();
			listSpreadProcessPointUpdate = new ArrayList<SpreadProcessPoint>();
			listSpreadProcessPointDelete = new ArrayList<SpreadProcessPoint>();

			spreadProfileId = request.getParameter("spreadProfileId");
			screenName = request.getParameter("screenName");
			spreadProfileName = request.getParameter("spreadProfileName");
			currencyCode = request.getParameter("currencyCode");
			xmlData = request.getParameter("xmlData");
			spreadProfile.getId().setSpreadProfileId(spreadProfileId);
			spreadProfile.setSpreadProfileName(spreadProfileName);
			spreadProfile.setCurrencyCode(currencyCode);

			crudResult = doCrudOperation(currentUserId, xmlData, null);

			Long idGenerated = null;


			RoleManager	roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);


			containsSpreadWithReqAuthY = accessList.stream()
					.filter(fa -> fa.getId().getFacilityId().equals(PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID))
					.anyMatch(fa -> fa.getReqAuth().equals("Y"));


			maintEventId =  (String) request.getParameter("maintEventId");

			if(containsSpreadWithReqAuthY || !SwtUtil.isEmptyOrNull(maintEventId))
				requireAuthorisation = true;


			if(requireAuthorisation) {
				idGenerated = MaintenanceAuthUtils.createMaintenanceEventDetailsRecord("SPREAD_PROFILES", spreadProfile.getId().getSpreadProfileId(), SwtUtil.getCurrentUserId(request.getSession()), SwtUtil.getSystemDatewithTime(), null, null, SwtUtil.isEmptyOrNull(maintEventId)?null:Long.parseLong(maintEventId), null, "add".equalsIgnoreCase(screenName)?"I":"U", "P");;
				spreadProfile.setMainEventId(""+idGenerated);
			}

			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceAuthUtils.updateMaintenanceEventStatus(Long.parseLong(maintEventId), "R", SwtUtil.getCurrentUserId(request.getSession()), null, idGenerated);
			}


			if ("add".equalsIgnoreCase(screenName)) {
				spreadProfilesMaintenanceManager.saveSpreadProfile(spreadProfile);

			} else {
				spreadProfilesMaintenanceManager.updateSpreadProfile(spreadProfile);
			}
			if(requireAuthorisation) {
				ObjectMapper objectMapper = new ObjectMapper();
				String json = objectMapper.writeValueAsString(spreadProfile);
				if(requireAuthorisation)
					MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "SPREAD_PROFILE", spreadProfile.getId().getSpreadProfileId(), "add".equalsIgnoreCase(screenName)?"I":"U", null, json);

			}

			for (Iterator iterator = crudResult.iterator(); iterator.hasNext();) {
				TabKVType object = (TabKVType) iterator.next();
				if (object.getTableName().equals("PC_SPREAD_PROCESS_POINT")) {
					spreadProcessPoint = new SpreadProcessPoint();
					spreadProcessPoint.setSpreadProfileId(spreadProfileId);
					for (int i = 0; i < object.getElementList().size(); i++) {
						KVType colmunValue = object.getElementList().get(i);
						if (colmunValue.getKey().equals("PROCESS_ID") && !object.getOperation().equals("I")) {
							spreadProcessPoint.setProcessPointId(Long.parseLong(colmunValue.getValue()));
						} else if (colmunValue.getKey().equals("PROCESS_NAME")) {
							spreadProcessPoint.setProcessName(colmunValue.getValue());
						} else if (colmunValue.getKey().equals("TIME")) {
							spreadProcessPoint.setProcessPointTime(colmunValue.getValue());
						} else if (colmunValue.getKey().equals("TARGET")) {
							spreadProcessPoint.setTargetPaymentPercent(Long.parseLong(colmunValue.getValue()));
						} else if (colmunValue.getKey().equals("PROCESS")) {
							String processCategorieValue = null;
							if (SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.All", request)
									.equals(colmunValue.getValue())) {
								processCategorieValue = "A";
							} else if (SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.Only", request)
									.equals(colmunValue.getValue())) {
								processCategorieValue = "O";
							} else if (SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.ExceptAll", request)
									.equals(colmunValue.getValue())) {
								processCategorieValue = "E";
							}
							spreadProcessPoint.setProcessCategories(processCategorieValue);
						} else if (colmunValue.getKey().equals("CATEGORIES")) {
							if (!"All".equals(colmunValue.getValue())) {
								spreadProcessPoint.setCategories(colmunValue.getValue());
								if (!object.getOperation().equals("I")) {
									Set<ProcessPointCategory> processPointCategory = new HashSet<ProcessPointCategory>(0);
									String[] categoriesList = colmunValue.getValue().split(",");
									for (int j = 0; j < categoriesList.length; j++) {
										ProcessPointCategory categ= 	new ProcessPointCategory(
												spreadProcessPoint.getProcessPointId(), categoriesList[j]);
										if(requireAuthorisation) {
											spreadProcessPoint.setMainEventId(""+idGenerated);
											categ.setMainEventId(""+idGenerated);
										}
										processPointCategory.add(categ);
									}
									spreadProcessPoint.setProcessPointCategory(processPointCategory);
								}
							}
						}
					}
					times.add(spreadProcessPoint.getProcessPointTime());
					if(requireAuthorisation)
						spreadProcessPoint.setMainEventId(""+idGenerated);

					if (object.getOperation().equals("I")) {
						spreadProcessPoint.setSpreadProfileId(spreadProfileId);
						listSpreadProcessPointAdd.add(spreadProcessPoint);
					} else if (object.getOperation().equals("U")) {
						spreadProcessPoint.setSpreadProfileId(spreadProfileId);
						listSpreadProcessPointUpdate.add(spreadProcessPoint);
					} else {
						spreadProcessPoint.setSpreadProfileId(spreadProfileId);
						listSpreadProcessPointDelete.add(spreadProcessPoint);
					}
				}
			}

			/*Check if the changed/added spread process points have a value time between kickOff and COB of the linked PC account group.
			  It will be checked Only in change mode*/
			if ("change".equalsIgnoreCase(screenName)) {
				ArrayList listOfAccountGroups = new ArrayList<>();
				accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
						.getBean("accountGroupsMaintenanceManager");
				accountGroupsDetails = accountGroupsMaintenanceManager.getAccountGroupDetailList(spreadProfileId);
				if (!accountGroupsDetails.isEmpty()) {
					SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
					for (int i = 0; i < times.size(); i++) {
						String time = times.get(i);
						Date processDate = sdf.parse(time);
						for (Iterator<AccountGroup> it = accountGroupsDetails.iterator(); it.hasNext();) {
							AccountGroup accountGroup = it.next();
							Date kickOffDate = sdf.parse(SwtUtil.isEmptyOrNull(accountGroup.getKickoff()) ? "00:00" : accountGroup.getKickoff());
							Date cobDate = sdf.parse(accountGroup.getEodPhaseBegins());

							if (processDate.before(kickOffDate) || (processDate.after(cobDate))) {
								if (listOfAccountGroups.indexOf(accountGroup.getId().getAccGrpId()) == -1) {
									listOfAccountGroups.add(accountGroup.getId().getAccGrpId());
								}
							}
						}
					}
				}
				if (!listOfAccountGroups.isEmpty()) {
					String accoutGrpsAsString = "";
					for (int i = 0; i < listOfAccountGroups.size(); i++) {
						accoutGrpsAsString += listOfAccountGroups.get(i) + ", ";
					}
					accoutGrpsAsString = accoutGrpsAsString.substring(0, accoutGrpsAsString.length() - 2);
					request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "errors.processPointTimeNotInRange");
					request.setAttribute("reply_location", accoutGrpsAsString);
					return getView("statechange");
				}
			}

			if(!SwtUtil.isEmptyOrNull(maintEventId)) {
				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "SPREAD_PROFILE");

				String json = details.getNewState();

				ObjectMapper objectMapper = new ObjectMapper();
				try {
					spreadProfile = objectMapper.readValue(json, SpreadProfile.class);
				} catch (Exception e) {
				}


				MaintenanceEventDetails detailsSpreadProcessPoints = maintenanceEventMaintenanceManager.getMaintenanceEventDetails(maintEventId, "SPREAD_PROCESS_POINT");
				String jsonReserve = detailsSpreadProcessPoints.getNewState();
				TypeFactory typeFactory = objectMapper.getTypeFactory();
				CollectionType collectionType = typeFactory.constructCollectionType(ArrayList.class, SpreadProcessPoint.class);
				JavaType mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class), collectionType);
				HashMap<String, ArrayList<SpreadProcessPoint>> processPointMap = null;
				try {
					processPointMap = objectMapper.readValue(jsonReserve, mapType);
				} catch (Exception e) {
				}


				ArrayList<SpreadProcessPoint> listSpreadProcessPointAddFromEvent = processPointMap.get("add");
				ArrayList<SpreadProcessPoint> listSpreadProcessPointUpdateFromEvent = processPointMap.get("update");
				ArrayList<SpreadProcessPoint> listSpreadProcessPointDeleteFromEvent =  processPointMap.get("delete");
				ArrayList<SpreadProcessPoint> listSpreadProcessPointAddDump = new ArrayList<>(listSpreadProcessPointAdd);
				ArrayList<SpreadProcessPoint> listSpreadProcessPointDeleteDump = new ArrayList<>(listSpreadProcessPointDelete);
				ArrayList<SpreadProcessPoint> listSpreadProcessPointUpdateDump = new ArrayList<>(listSpreadProcessPointUpdate);


				// create a map from processPointId to SpreadProcessPoint for the elements in listSpreadProcessPointUpdate
				Map<Long, SpreadProcessPoint> updateMap = listSpreadProcessPointUpdate.stream()
						.collect(Collectors.toMap(SpreadProcessPoint::getProcessPointId, Function.identity()));

				// create a map from processName to SpreadProcessPoint for the elements in listSpreadProcessPointUpdate
				Map<String, SpreadProcessPoint> updateMapByName = listSpreadProcessPointUpdate.stream()
						.collect(Collectors.toMap(SpreadProcessPoint::getProcessName, Function.identity()));

				// replace elements in listSpreadProcessPointAddFromEvent with matching processPointId or processName from listSpreadProcessPointUpdate
				listSpreadProcessPointAddFromEvent.replaceAll(spp -> {
					SpreadProcessPoint updatedSpp = updateMap.get(spp.getProcessPointId());
					if (updatedSpp == null) {
						updatedSpp = updateMapByName.get(spp.getProcessName());
					}
					if (updatedSpp != null) {
						listSpreadProcessPointUpdateDump.remove(updatedSpp);
						return updatedSpp;
					} else {
						return spp;
					}
				});

				listSpreadProcessPointUpdate = listSpreadProcessPointUpdateDump;



				listSpreadProcessPointAddFromEvent.stream()
						.filter(item -> listSpreadProcessPointAddDump.stream()
								.noneMatch(existingItem -> existingItem.getProcessName().equals(item.getProcessName())))
						.filter(item -> listSpreadProcessPointDeleteDump.stream()
								.noneMatch(existingItem -> existingItem.getProcessName().equals(item.getProcessName())))
						.forEach(listSpreadProcessPointAddDump::add);

				listSpreadProcessPointAdd = listSpreadProcessPointAddDump;


				ArrayList<SpreadProcessPoint> filteredList = listSpreadProcessPointDelete.stream()
						.filter(spp -> listSpreadProcessPointAddFromEvent.stream()
								.noneMatch(sppAdd -> sppAdd.getProcessPointId().equals(spp.getProcessPointId())))
						.collect(Collectors.toCollection(ArrayList::new));


				listSpreadProcessPointDeleteFromEvent.stream()
						.filter(spp -> filteredList.stream()
								.noneMatch(sppDelete -> sppDelete.getProcessPointId().equals(spp.getProcessPointId())))
						.forEach(filteredList::add);

				listSpreadProcessPointDelete = filteredList;

				ArrayList<SpreadProcessPoint> listSpreadProcessPointAddDump2 = new ArrayList<>(listSpreadProcessPointAdd);

				ArrayList<SpreadProcessPoint> updatedListSpreadProcessPointUpdate = new ArrayList<>(listSpreadProcessPointUpdate);
				updatedListSpreadProcessPointUpdate.removeIf(item -> listSpreadProcessPointAddDump2.stream()
						.anyMatch(existingItem -> existingItem.getProcessName().equals(item.getProcessName())));

				listSpreadProcessPointUpdateFromEvent.stream()
						.filter(spp -> updatedListSpreadProcessPointUpdate.stream()
								.noneMatch(sppUpdate -> sppUpdate.getProcessPointId().equals(spp.getProcessPointId()))
								&& filteredList.stream()
								.noneMatch(sppDelete -> sppDelete.getProcessPointId().equals(spp.getProcessPointId())))
						.forEach(updatedListSpreadProcessPointUpdate::add);

				listSpreadProcessPointUpdate = updatedListSpreadProcessPointUpdate;


				// Nullify the process category for all addition to not throws an exception
				listSpreadProcessPointAddFromEvent.forEach(spp -> {
					spp.setProcessPointCategory(null);
				});



			}



			spreadProfilesMaintenanceManager.crudSpreadProcessPoints(listSpreadProcessPointAdd,
					listSpreadProcessPointUpdate, listSpreadProcessPointDelete, idGenerated);

			if(requireAuthorisation) {
				HashMap<String, ArrayList> changesMap = new HashMap<String, ArrayList>();
				String outputJsonString = new String();
				ObjectMapper objectMapper = new ObjectMapper();
				changesMap.put("add",listSpreadProcessPointAdd);
				changesMap.put("update",listSpreadProcessPointUpdate);
				changesMap.put("delete",listSpreadProcessPointDelete);

				outputJsonString = objectMapper.writeValueAsString(changesMap);
				MaintenanceAuthUtils.createMaintenanceEventDetailsRecordDetails(idGenerated, "SPREAD_PROCESS_POINT", spreadProfile.getId().getSpreadProfileId(), "add".equalsIgnoreCase(screenName)?"I":"U", null, outputJsonString);

				if(!containsSpreadWithReqAuthY) {
					MaintenanceAuthUtils.updateMaintenanceEventStatus(idGenerated, "A", SwtUtil.getCurrentUserId(request.getSession()),null,null );
					MaintenanceAuthUtils.acceptMaintenanceEvent(""+idGenerated);
				}

			}
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
		} catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [save] method : - " + swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			if (swtExp.getErrorCode().equals("errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtExp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName() + " - Exception Catched in [save] method swt : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			}

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute("reply_location", swtExp.getStackTrace()[0].getClassName() + "."
					+ swtExp.getStackTrace()[0].getMethodName() + ":" + swtExp.getStackTrace()[0].getLineNumber());
			return getView("statechange");

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "update", CurrencyAction.class), request, "");
			return getView("fail");
		}
		return display();
	}
}