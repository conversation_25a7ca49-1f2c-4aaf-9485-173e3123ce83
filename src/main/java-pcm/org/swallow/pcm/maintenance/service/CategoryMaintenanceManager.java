/*
 * @(#)CategoryMaintenanceManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CategoryMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;

public interface CategoryMaintenanceManager {

	public void setCategoryMaintenanceDAO(CategoryMaintenanceDAO categoryMaintenanceDAO);

	public Collection getCategoryDetailList() throws SwtException;
	
	public Collection getCategoryCombo() throws SwtException;
	
	public Category getCategoryDetailById(String categoryId) throws SwtException;

	public void deleteCategory(String categoryId) throws SwtException;
	
	public void crudCategoryRule(Category category, ArrayList<CategoryRule> categoryRuleInsert, ArrayList<CategoryRule> categoryRuleUpdate, ArrayList<CategoryRule> categoryRuleDelete, String screenName) throws SwtException;

	public Integer getMaxOrder() throws SwtException;
	
	public Boolean existPayRequest(String categoryId)throws SwtException;
	
	/**
	 * Reorder ordinal of Category from DB
	 * @param value_from
	 * @param orderTo
	 * @throws SwtException
	 */
	public void reOrder(Integer value_from, Integer orderTo) throws SwtException;
}
