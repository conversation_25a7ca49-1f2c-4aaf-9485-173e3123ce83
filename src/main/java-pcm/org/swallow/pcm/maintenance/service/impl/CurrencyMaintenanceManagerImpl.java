/*
 * @(#)CurrencyMaintenanceManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;


import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.util.LabelValueBean;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.pcm.maintenance.dao.CurrencyMaintenanceDAO;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;

@Component("currencyMaintenanceManager")
public class CurrencyMaintenanceManagerImpl implements CurrencyMaintenanceManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(CurrencyMaintenanceManagerImpl.class);
	@Autowired
	private CurrencyMaintenanceDAO currencyMaintenanceDAO = null;

	public void setCurrencyMaintenanceDAO(CurrencyMaintenanceDAO currencyMaintenanceDAO) {
		this.currencyMaintenanceDAO = currencyMaintenanceDAO;
	}
	

	public PCMCurrencyDetailsVO getCurrencyDetailList(boolean includeAll) throws SwtException {
		/* Method's local variable and class instance declaration */
		ArrayList<LabelValueBean> currencyList = null;
		ArrayList<PCMCurrency> currencyDetailsList = null;
		Collection collCurre;
		PCMCurrency currency;
		PCMCurrencyDetailsVO currencyDetailsVO = new PCMCurrencyDetailsVO();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyDetailList ] - " + "Entry");
			collCurre = currencyMaintenanceDAO.getCurrencyDetailList();
			/* Iterate the collection of currency */
			Iterator itr = collCurre.iterator();
			currencyList = new ArrayList<LabelValueBean>(); 
			currencyDetailsList = new ArrayList<PCMCurrency>();
			if (includeAll) {
				currencyList.add(new LabelValueBean(SwtConstants.ALL_VALUE, SwtConstants.ALL_LABEL));
			}
			/* Loop to set iterate the currency detail list collection */
			while (itr.hasNext()) {
				/* Initializing currency bean */
				currency = new PCMCurrency();
				currency = (PCMCurrency) (itr.next());
				
				 /* Retrieve the currency and from the currency bean and adding
				 * to the currency collection list as label value bean
				 */
				currencyList.add(new LabelValueBean(currency.getCurrencyMaster()
						.getCurrencyName(), currency.getId().getCurrencyCode()));

				/* Calls the set MultilierDesciption method */
				setMultiplierDescription(currency);
				
				/* Add the currency bean to the currencyDetailListCollection */
				currencyDetailsList.add(currency);
			}
			
			currencyDetailsVO.setCurrencyList(currencyList);
			currencyDetailsVO.setCurrencyListDetails(currencyDetailsList);	
			
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCurrencyDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCurrencyDetailList] - Exit");
		}
		// return result as list
		return  currencyDetailsVO;
		
	}
	
	public PCMCurrencyDetailsVO getCurrencyCombo() throws SwtException {
		/* Method's local variable and class instance declaration */
		ArrayList<LabelValueBean> currencyList = null;
		ArrayList<PCMCurrency> currencyDetailsList = null;
		Collection collCurre;
		PCMCurrency currency;
		PCMCurrencyDetailsVO currencyDetailsVO = new PCMCurrencyDetailsVO();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyDetailList ] - " + "Entry");
			collCurre = currencyMaintenanceDAO.getCurrencyCombo();
			/* Iterate the collection of currency */
			Iterator itr = collCurre.iterator();
			currencyList = new ArrayList<LabelValueBean>(); 
			currencyDetailsList = new ArrayList<PCMCurrency>();
			/* Loop to set iterate the currency detail list collection */
			while (itr.hasNext()) {
				/* Initializing currency bean */
				currency = new PCMCurrency();
				currency = (PCMCurrency) (itr.next());
				
				/* Retrieve the currency and from the currency bean and adding
				 * to the currency collection list as label value bean
				 */
				currencyList.add(new LabelValueBean(currency.getCurrencyMaster()
						.getCurrencyName(), currency.getId().getCurrencyCode()));
				
				/* Calls the set MultilierDesciption method */
				setMultiplierDescription(currency);
				
				/* Add the currency bean to the currencyDetailListCollection */
				currencyDetailsList.add(currency);
			}
			
			currencyDetailsVO.setCurrencyList(currencyList);
			currencyDetailsVO.setCurrencyListDetails(currencyDetailsList);	
			
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCurrencyDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCurrencyDetailList] - Exit");
		}
		// return result as list
		return  currencyDetailsVO;
		
	}
	
	public Collection getCurrencyMaintenanceCombo() throws SwtException {
		ArrayList<LabelValueBean> currencyList = null;
		Collection collCurre;
		CurrencyMaster currency;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceCombo ] - " + "Entry");
			collCurre = currencyMaintenanceDAO.getCurrencyMaintenanceCombo();
			/* Iterate the collection of currency */
			Iterator itr = collCurre.iterator();
			currencyList = new ArrayList<LabelValueBean>(); 
	
			/* Loop to set iterate the currency detail list collection */
			while (itr.hasNext()) {
				/* Initializing currency bean */
				currency = new CurrencyMaster();
				currency = (CurrencyMaster) (itr.next());
				
				/* Retrieve the currency and from the currency bean and adding
				 * to the currency collection list as label value bean
				 */
				currencyList.add(new LabelValueBean(currency.getCurrencyName(), currency.getCurrencyCode()));
				
			}
			
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCurrencyMaintenanceCombo] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCurrencyMaintenanceCombo] - Exit");
		}
		// return result as list
		return  currencyList;
		
	}

	public void saveCurrency(PCMCurrency currency) throws SwtException {
		currencyMaintenanceDAO.saveCurrency(currency);
	}

	
	public void updateCurrency(PCMCurrency currency) throws SwtException {
		currencyMaintenanceDAO.updateCurrency(currency);
	
	}


	
	public void deleteCurrency(String currencyCode)throws SwtException {
		currencyMaintenanceDAO.deleteCurrency(currencyCode);

	}


	public PCMCurrency getCurrencyDetailById(String currencyCode) throws SwtException {
		PCMCurrency currency = null;
		currency = currencyMaintenanceDAO.getCurrencyDetailById(currencyCode);
		setMultiplierDescription(currency);
		return currency;
	}


	public Integer getMaxOrder() throws SwtException {
		return currencyMaintenanceDAO.getMaxOrder();
	}




	/**
	 * set the Multiplier description collected from miscparams
	 * 
	 * @param ccy
	 * @return
	 * @throws SwtException
	 */
	private void setMultiplierDescription(PCMCurrency ccy) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [setMultiplierDescription] - " + "Entry");

			/* Method's local variable and class instance declaration */
			Collection coll;
			MiscParams mp;
			/* Collects the currency multiplier from cache manager */
			coll = (Collection) CacheManager.getInstance().getMiscParams("CURRENCYMULTIPLIER",null);
			if ((coll != null) && (coll.size() > 0)) {
			
				Iterator itr = coll.iterator();
				while (itr.hasNext()) {
					mp = (MiscParams) (itr.next());
			
					if ((ccy.getDisplayMultiplier() != null)
							&& ccy.getDisplayMultiplier().equals(mp.getId().getKey2())) {
				
						ccy.setMultiplierDesc(mp.getParValue().trim());
						
						if (ccy.getMultiplierDesc().equals(
								SwtConstants.CURR_MONITOR_DEFAULT_MULTIPLIER)) {
							ccy.setMultiplierDesc("");
						}
					}
				}
			}
			log.debug(this.getClass().getName()
					+ " - [setMultiplierDescription] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [setMultiplierDescription] method : - "
							+ exp.getMessage());
			log.error(this.getClass().getName()
							+ " - Exception Catched in [setMultiplierDescription] method : - "
							+ exp.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(exp,
					"setMultiplierDescription", CurrencyMaintenanceManagerImpl.class);
		}

	}


	@Override
	public void reOrder(Integer value_from, Integer orderIni) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [ReOrder] - " + "Entry");
			currencyMaintenanceDAO.reOrder(value_from, orderIni);
		}catch(Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"ReOrder", CurrencyMaintenanceManagerImpl.class);
		}finally {
			log.debug(this.getClass().getName()
					+ " - [ReOrder] - " + "Exit");
		}
		
	}


	

	
	
}
