/*
 * @(#)PaymentControlManager.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.PaymentControlDAO;

public interface PaymentControlManager {

	public void setPaymentControlDAO(PaymentControlDAO paymentControlDAO);

	public void saveNewDetailInRemoteDB() throws SwtException;
	
}
