/*
 * @(#)PaymentControlManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.ArrayList;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CommonDAO;
import org.swallow.pcm.maintenance.model.core.kv.KVType;
import org.swallow.pcm.maintenance.service.CommonManager;


public class CommonManagerImpl implements CommonManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(CommonManagerImpl.class);

	private CommonDAO commonDAO = null;

	public void setCommonDAO(CommonDAO commonDAO) {
		this.commonDAO = commonDAO;
	}

	public String doCrudOperation(String userId,String operation,String tableName,ArrayList<KVType> list,String tableLevel, String activityId) throws SwtException{
		return commonDAO.doCrudOperation(userId,operation,tableName,list,tableLevel, activityId);
	}
	
}
