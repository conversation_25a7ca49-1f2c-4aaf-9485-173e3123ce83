/*
 * @(#)AccountGroupsMaintenanceManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.AccountGroupsMaintenanceDAO;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.AccountGroupCutoff;
import org.swallow.pcm.maintenance.model.AccountInGroup;
import org.swallow.pcm.maintenance.model.Reserve;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;

@Component ("accountGroupsMaintenanceManager")
public class AccountGroupsMaintenanceManagerImpl implements AccountGroupsMaintenanceManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(AccountGroupsMaintenanceManagerImpl.class);

	@Autowired
	private AccountGroupsMaintenanceDAO accountGroupsMaintenanceDAO = null;

	public void setAccountGroupsMaintenanceDAO(AccountGroupsMaintenanceDAO accountGroupsMaintenanceDAO) {
		this.accountGroupsMaintenanceDAO = accountGroupsMaintenanceDAO;
	}
	
	public void saveAccountGroup(AccountGroup acctGroup)
			throws SwtException {
		try {
			accountGroupsMaintenanceDAO.saveAccountGroup(acctGroup);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveAccountGroup] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveAccountGroup",
					AccountGroupsMaintenanceManagerImpl.class);
		}

	}
	

	public List<AccountGroup> getAccountGroupDetailList(String selectedSpreadProfileId) throws SwtException {
 
		List<AccountGroup> listAccts;
		
		try {
			listAccts = (List<AccountGroup>) accountGroupsMaintenanceDAO.getAccountGroupDetailList(selectedSpreadProfileId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getAcctAttributHDRDetailList] - Exception "
					+ e.getMessage());
	throw SwtErrorHandler.getInstance().handleException(e,
			"getAcctAttributHDRDetailList", AccountGroupsMaintenanceManagerImpl.class);
		}
		return listAccts;
	}

	public AccountGroup getAcctGroupDetail(String acctGroupId) throws SwtException {
		AccountGroup acctGrp= null;
		try {
			acctGrp =	accountGroupsMaintenanceDAO.getAcctGroupDetail(acctGroupId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getAcctGroupDetail] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAcctGroupDetail",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return acctGrp;
	}
	public void updateAcctGroup(AccountGroup acctGroup) throws SwtException {
		try {
			accountGroupsMaintenanceDAO.updateAcctGroup(acctGroup);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateAcctGroup] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateAcctGroup",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		
	}

	public void deleteAcctGroup(AccountGroup acctGroup) throws SwtException {
		try {
			accountGroupsMaintenanceDAO.deleteAcctGroup(acctGroup);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteAcctGroup] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteAcctGroup",
					AccountGroupsMaintenanceManagerImpl.class);
		}	
		
	}

	public Integer accountListCount(String groupId) throws SwtException {
		Integer numberOfAccounts = null;
		try {
			numberOfAccounts = accountGroupsMaintenanceDAO.accountListCount(groupId);
		
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteAcctGroup] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteAcctGroup",
					AccountGroupsMaintenanceManagerImpl.class);
		}	
		return numberOfAccounts; 
	}
	public Collection getAccountListDetails(String currencyCode)
			throws SwtException {

		Collection accountGroupList = new ArrayList();

		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - " + "Entry");

			accountGroupList = accountGroupsMaintenanceDAO.getAccountListDetails(currencyCode);

			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - " + "Entry");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountListDetails] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountListDetails",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return accountGroupList;
	}
	

	public Collection getReserveDetails(String acctGroupId)
			throws SwtException {
		Collection records=  new ArrayList();
		try {
			records = accountGroupsMaintenanceDAO.getReserveDetails(acctGroupId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getReserveDetails] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getReserveDetails",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return records;
		
	}
	
	public Collection getCutOffRulesDetails(String acctGroupId)
			throws SwtException {
		Collection records=  new ArrayList();
		try {
			records = accountGroupsMaintenanceDAO.getCutOffRulesDetails(acctGroupId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getCutOffRulesDetails] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCutOffRulesDetails",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return records;
		
	}
	
	public Collection getAccountsInGroupList(String  accountGrpId)
			throws SwtException {

		List accountGroupList= null;

		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - " + "Entry");

			accountGroupList = (List) accountGroupsMaintenanceDAO.getAccountsInGroupList(accountGrpId);

			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - " + "Entry");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountListDetails] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountListDetails",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return accountGroupList;
	}
	
	
	public void crudAccountsInGroup(ArrayList<AccountInGroup> accountsInsert, ArrayList<AccountInGroup> accountsDelete, Long maintEventId )
			throws SwtException{
		
		try {
			if(accountsInsert.size() > 0 || accountsDelete.size() > 0) {
				accountGroupsMaintenanceDAO.crudAccountsInGroup(accountsInsert, accountsDelete, maintEventId);
			}
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [crudReserve] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"crudReserve",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		
	}
	public void crudReserve(ArrayList<Reserve> reserveInsert, ArrayList<Reserve> reserveUpdate, ArrayList<Reserve> reserveDelete, Long maintEventId )
			throws SwtException {
		
		try {
			if(reserveInsert.size() > 0 || reserveUpdate.size() > 0 || reserveDelete.size() > 0 )
			accountGroupsMaintenanceDAO.crudReserve(reserveInsert, reserveUpdate, reserveDelete, maintEventId );
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [crudReserve] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"crudReserve",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		
	}
	public void crudCutOff(ArrayList<AccountGroupCutoff> cutOffInsert, ArrayList<AccountGroupCutoff> cutOffUpdate, ArrayList<AccountGroupCutoff> cutOffDelete, Long maintEventId)
			throws SwtException {
		
		try {
			if(cutOffInsert.size() > 0 ||  cutOffUpdate.size() > 0 || cutOffDelete.size() > 0) {
				accountGroupsMaintenanceDAO.crudCutOff(cutOffInsert, cutOffUpdate, cutOffDelete, maintEventId);
			}
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [crudCutOff] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"crudCutOff",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		
	}
	public Collection getOrdinalByCurrency(String currencyCode) throws SwtException {
		List ordinalByCcy= null;
		
		try {
			ordinalByCcy = (List)accountGroupsMaintenanceDAO.getOrdinalByCurrency(currencyCode);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getOrdinalByCurrency] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getOrdinalByCurrency",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return ordinalByCcy;
		
	}
	
	 public AccountGroup getAccountGroupFromAccountId(String hostId, String entityId , String accountId) throws SwtException{
		AccountGroup group= null;
		
		try {
			group =  accountGroupsMaintenanceDAO.getAccountGroupFromAccountId(hostId, entityId , accountId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getOrdinalByCurrency] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getOrdinalByCurrency",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return group;
		
	}
	 /**
		 * Check if account id is related to pay
		 * 
		 * @param accountId
		 * @return String
		 * @throws SwtException
		 */
	 public HashMap checkAccountInPayment(ArrayList accountId, ArrayList entity) throws SwtException {
			HashMap<String, String> result = new LinkedHashMap<String, String>();

			try {
				result =  accountGroupsMaintenanceDAO.checkAccountInPayment(accountId, entity);
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ "- [checkAccountInPayment] - Exception " + e.getMessage());
				throw SwtErrorHandler.getInstance().handleException(e,
						"checkAccountInPayment",
						AccountGroupsMaintenanceManagerImpl.class);
			}
			return result;
		 
	 }

	public Collection getAccountListDetailsMaintEvent(String currencyCode, String accountGroup) throws SwtException {
		Collection accountGroupList = new ArrayList();
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - " + "Entry");

			accountGroupList = accountGroupsMaintenanceDAO.getAccountListDetailsMaintEvent(currencyCode, accountGroup);

			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - " + "Entry");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountListDetails] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountListDetails",
					AccountGroupsMaintenanceManagerImpl.class);
		}
		return accountGroupList;
	}
	
	public void deleteRulesDefinitionForMaintEvent(String accountGroup) throws SwtException {
		Collection accountGroupList = new ArrayList();
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteRulesDefinitionForMaintEvent] - " + "Entry");

			accountGroupsMaintenanceDAO.deleteRulesDefinitionForMaintEvent(accountGroup);

			log.debug(this.getClass().getName()
					+ " - [deleteRulesDefinitionForMaintEvent] - " + "Entry");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteRulesDefinitionForMaintEvent] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteRulesDefinitionForMaintEvent",
					AccountGroupsMaintenanceManagerImpl.class);
		}
	}
	
	
	

	
	
}
