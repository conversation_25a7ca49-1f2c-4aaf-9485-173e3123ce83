/*
 * @(#)PaymentControlManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.PaymentControlDAO;
import org.swallow.pcm.maintenance.service.PaymentControlManager;

public class PaymentControlManagerImpl implements PaymentControlManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(PaymentControlManagerImpl.class);

	private PaymentControlDAO paymentControlDAO = null;

	public void setPaymentControlDAO(PaymentControlDAO paymentControlDAO) {
		this.paymentControlDAO = paymentControlDAO;
	}

	@Override
	public void saveNewDetailInRemoteDB() throws SwtException {
		paymentControlDAO.saveNewDetailInRemoteDB();
		
	}

	
	
	
	
}
