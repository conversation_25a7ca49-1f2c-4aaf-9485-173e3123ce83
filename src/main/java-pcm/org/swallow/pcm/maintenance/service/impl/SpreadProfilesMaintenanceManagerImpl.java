/*
 * @(#)SpreadProfilesMaintenanceManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.SpreadProfilesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.ProcessPointCategory;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.maintenance.service.SpreadProfilesMaintenanceManager;
import org.swallow.util.SwtUtil;

@Component ("spreadProfilesMaintenanceManager")
public class SpreadProfilesMaintenanceManagerImpl implements SpreadProfilesMaintenanceManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(SpreadProfilesMaintenanceManagerImpl.class);
	@Autowired
	private SpreadProfilesMaintenanceDAO spreadProfilesMaintenanceDAO = null;

	public void setSpreadProfilesMaintenanceDAO(SpreadProfilesMaintenanceDAO spreadProfilesMaintenanceDAO) {
		this.spreadProfilesMaintenanceDAO = spreadProfilesMaintenanceDAO;
	}

	public SpreadProfile getSpreadProfileDetails(String spreadProfileId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSpreadProfileDetails ] - " + "Entry");
			return spreadProfilesMaintenanceDAO.getSpreadProfileDetails(spreadProfileId);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSpreadProfileDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSpreadProfileDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getSpreadProfileDetails] - Exit");
		}
	}
	
	public List<SpreadProfile> getSpreadProfilesList(String currencyCode) throws SwtException {
		// Variable List to hold list RulesDefinition
		List<SpreadProfile> spreadProfileList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSpreadprofileDetailList ] - " + "Entry");
			spreadProfileList = (List<SpreadProfile>) spreadProfilesMaintenanceDAO.getSpreadProfilesList(currencyCode);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSpreadprofileDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSpreadprofileDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getSpreadprofileDetailList] - Exit");
		}
		// return result as list
		return  spreadProfileList;
	}
	
	public List<SpreadProcessPoint> getSpreadProcessPointList(HttpServletRequest request, String selectedSpreadProfileId) throws SwtException {

		List<SpreadProcessPoint> processPointLists = null;
		String categories = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSpreadProcessPointList ] - " + "Entry");
			processPointLists = (List<SpreadProcessPoint>) spreadProfilesMaintenanceDAO.getSpreadProcessPointList(selectedSpreadProfileId);
			for (Iterator iterator = processPointLists.iterator(); iterator.hasNext();) {
				SpreadProcessPoint spreadProcessPoint = (SpreadProcessPoint) iterator.next();
				// Set the category process for each spread process point
				if ("A".equals(spreadProcessPoint.getProcessCategories())) {
					spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.All", request));
				} else if ("O".equals(spreadProcessPoint.getProcessCategories())) {
					spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.Only", request));
				} else if ("E".equals(spreadProcessPoint.getProcessCategories())) {
					spreadProcessPoint.setProcessCategoriesText(SwtUtil.getMessage("spreadProfilesMaintenance.processPoint.ExceptAll", request));
				}
				// Set the categories that will be shown in the grid
				Set<ProcessPointCategory> processPointCategories = spreadProcessPoint.getProcessPointCategory();
				if (!processPointCategories.isEmpty()) {
					categories = "";
					for (Iterator iterator2 = processPointCategories.iterator(); iterator2.hasNext();) {
						ProcessPointCategory processPointCategory = (ProcessPointCategory) iterator2.next();
						categories += processPointCategory.getId().getCategoryId() + ",";
					}
					spreadProcessPoint.setCategories(categories.substring(0, categories.length() - 1));
				} else {
					spreadProcessPoint.setCategories("All");
				}
			}
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSpreadProcessPointList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSpreadprofileDetagetSpreadProcessPointListilList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getSpreadProcessPointList] - Exit");
		}
		// return result as list
		return  processPointLists;
	}

	public Collection getSpreadProfilesByCategoryId(String categoryId) throws SwtException {
		List spreadList = null;
		try {
			if(categoryId!= null) {
				spreadList=(List) spreadProfilesMaintenanceDAO.getSpreadProfilesByCategoryId(categoryId);
			}
			
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSpreadProfilesByCategoryId] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSpreadProfilesByCategoryId", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getSpreadProfilesByCategoryId] - Exit");
		}
		// return result as list
		return spreadList;
	}

	public void deleteSpreadProfile(String selectedSpreadProfileId, Long maintEventId) throws SwtException {
		try {
			spreadProfilesMaintenanceDAO.deleteSpreadProfile(selectedSpreadProfileId, maintEventId);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [deleteCategory] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"deleteCategory", this.getClass());
		}
		
	}

	public void saveSpreadProfile(SpreadProfile spreadProfile) throws SwtException {
		try {
			spreadProfilesMaintenanceDAO.saveSpreadProfile(spreadProfile);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveSpreadProfile] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveSpreadProfile", this.getClass());
		}

		
	}

	public void updateSpreadProfile(SpreadProfile spreadProfile) throws SwtException {
		try {
			spreadProfilesMaintenanceDAO.updateSpreadProfile(spreadProfile);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateSpreadProfile] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateSpreadProfile", this.getClass());
		}
	}

	public void crudSpreadProcessPoints(List<SpreadProcessPoint> listSpreadProcessPointAdd,
			List<SpreadProcessPoint> listSpreadProcessPointUpdate,
			List<SpreadProcessPoint> listSpreadProcessPointdelete, Long maintEventId) throws SwtException {
		try {
			if (!listSpreadProcessPointAdd.isEmpty() || !listSpreadProcessPointUpdate.isEmpty()
					|| !listSpreadProcessPointdelete.isEmpty()) {
				spreadProfilesMaintenanceDAO.crudSpreadProcessPoints(listSpreadProcessPointAdd,
						listSpreadProcessPointUpdate, listSpreadProcessPointdelete, maintEventId);
			}
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ "- [crudSpreadProcessPoints] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"crudSpreadProcessPoints", this.getClass());
		}
	}
	
}
