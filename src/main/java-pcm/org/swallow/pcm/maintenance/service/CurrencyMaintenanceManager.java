/*
 * @(#)CurrencyMaintenanceManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CurrencyMaintenanceDAO;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;

public interface CurrencyMaintenanceManager {

	public void setCurrencyMaintenanceDAO(CurrencyMaintenanceDAO currencyMaintenanceDAO);

	
	/**
	 * 
	 * @param includeAll
	 * @return PCMCurrencyDetailsVO
	 * @throws SwtException
	 */
	public PCMCurrencyDetailsVO getCurrencyDetailList(boolean includeAll) throws SwtException;
	
	/**
	 * Returns the collection of currency combo list from currency table
	 * 
	 * @return PCMCurrencyDetailsVO
	 * @throws SwtException
	 */
	public PCMCurrencyDetailsVO getCurrencyCombo() throws SwtException;
	
	
	/**
	 * Returns the collection of currency combo list from currency table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyMaintenanceCombo() throws SwtException;
	
	/**
	 * 
	 * @return Integer
	 * @throws SwtException
	 */
	public Integer getMaxOrder() throws SwtException;
	
	/**
	 * 
	 * @param id
	 * @return PCMCurrency
	 * @throws SwtException
	 */
	public PCMCurrency getCurrencyDetailById(String id) throws SwtException;

	/**
	 * 
	 * @param currency
	 * @throws SwtException
	 */
	public void saveCurrency(PCMCurrency currency) throws SwtException;

	/**
	 * 
	 * @param currency
	 * @throws SwtException
	 */
	public void deleteCurrency(String currencyCode) throws SwtException;

	/**
	 * 
	 * @param currency
	 * @throws SwtException
	 */
	public void updateCurrency(PCMCurrency currency) throws SwtException;

	
	/**
	 * Reorder ordinal of Currency from DB
	 * @param value_from
	 * @throws SwtException
	 */
	public void reOrder(Integer value_from, Integer orderIni) throws SwtException;

	
}
