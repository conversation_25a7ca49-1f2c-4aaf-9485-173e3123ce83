/*
 * @(#)PriorityRulesMaintenanceManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CategoryRulesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;
import org.swallow.pcm.maintenance.service.CategoryRulesMaintenanceManager;

@Component("categoryRulesMaintenanceManager")
public class CategoryRulesMaintenanceManagerImpl implements CategoryRulesMaintenanceManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(CategoryRulesMaintenanceManagerImpl.class);
	@Autowired
	private CategoryRulesMaintenanceDAO categoryRulesMaintenanceDAO = null;

	public void setCategoryRulesMaintenanceDAO(CategoryRulesMaintenanceDAO categoryRulesMaintenanceDAO) {
		this.categoryRulesMaintenanceDAO = categoryRulesMaintenanceDAO;
	}


	@Override
	public Collection getCategoryRuleDetailList() throws SwtException {
		
		List categoryRuleList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryRuleDetailList ] - " + "Entry");
			categoryRuleList =  (List) categoryRulesMaintenanceDAO.getCategoryRuleDetailList();
		}catch(Exception ex){
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryRuleDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryRuleDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryRuleDetailList] - Exit");
		}
		// return result as list
		return  categoryRuleList;
	}


	@Override
	public Category getCategoryRuleByCategoryId(Category category) throws SwtException {
		List categoryRuleList = null;
		try {	
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryRuleByCategoryId ] - " + "Entry");
			if(category != null) {
				category = (Category) categoryRulesMaintenanceDAO.getCategoryRuleDetailByCategoryId(category);
			}
			
		}catch(Exception ex){
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryRuleByCategoryId] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryRuleByCategoryId", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryRuleByCategoryId] - Exit");
		}
		
		return category;
	}


	@Override
	public CategoryRule getCategoryRuleById(Long categoryRuleId) throws SwtException {
		CategoryRule categoryRule=null;
		try {	
			categoryRule= categoryRulesMaintenanceDAO.getCategoryRuleById(categoryRuleId);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryRuleById] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryRuleById", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryRuleById] - Exit");
		}
		return categoryRule;
	}


	@Override
	public Integer getMaxOrder(String categoryId) throws SwtException {
		return categoryRulesMaintenanceDAO.getMaxOrder(categoryId);
	}
}
