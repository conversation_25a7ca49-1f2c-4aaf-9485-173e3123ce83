/*
 * @(#)ExpressionBuilderManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.ExpressionBuilderDAO;
import org.swallow.pcm.maintenance.model.Dictionary;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.TypeValues;

public interface ExpressionBuilderManager {

	public void setExpressionBuilderDAO(ExpressionBuilderDAO expressionBuilderDAO);

	/**
	 * This method is used to get all the rulesDefinition objects from A_RULES table
	 * 
	 * @param langId
	 * @param selectedFilter
	 * @param selectedSort
	 * @param moduleId
	 * @param isRiskFactor
	 * @return List<RulesDefinition> - list of rules objects
	 * @throws SwtException
	 */
	public List<RulesDefinition> getListRules(String langId, String moduleId, String isRiskFactor , String selectedFilter,
			String selectedSort) throws SwtException;
	
	/**
	 * Method to save Rule details
	 * 
	 * @param rule
	 * @param actionSave
	 * @return
	 * @throws SwtException
	 */
	public void saveRule(RulesDefinition rule, String actionSave, String moduleId) throws SwtException;
	
	/**
	 * Method to remove Rule details
	 * 
	 * @param rule
	 * @throws SwtException
	 */
	public int removeRule(RulesDefinition rule) throws SwtException;
	
	
	/**
	 * getRuleListReport()
	 * 
	 * @param selectedfilter - filter parameter
	 * @param selectedsort - Sort parameter
	 * @param typeCol - columns type
	 * @param sQuery
	 * @param langId
	 * @param moduleId
	 * @return Collection -get rule list
	 * @throws SwtException
	 * 
	 * This method to get the rule list for given filter and sort parameters
	 */

	public Collection<RulesDefinition> getRuleListReport(String selectedfilter,String selectedsort, HashMap<String, String> typeCol, String sQuery, String langId, String moduleId,String isRiskFactor)
			throws SwtException;

	/**
	 * getRuleById()
	 * 
	 * @param ruleId - ruleId
	 * @param langId
	 * @return {@link RulesDefinition}
	 * @throws SwtException
	 * 
	 * This method to get the rule for given rule id
	 */
	public RulesDefinition getRuleById(Integer ruleId, String langId) throws SwtException;
	
	
	/**
	 * This method is used to search list of rules objects from A_RULE table
	 * 
	 * @param sQuery
	 * @param langId
	 * @param moduleId
	 * @param isRiskFactor
	 * @return List<RulesDefinition> - list of rule objects
	 * @throws SwtException
	 */
	List<RulesDefinition> getSearchData(String sQuery, String langId, String moduleId,String isRiskFactor) throws SwtException;
	
	/**
	 * This method is used to get the list of label and code from S_CFG_TYPE_VALUES, S_DICTIONARY
	 * 
	 * @param typeId
	 * @param languageId
	 * @param showHidden
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	public LinkedHashMap<String, String> getTypeValuesList(int typeId,String languageId, Boolean showHidden) throws SwtException;;
	
	
	/**
	 * Method to save Rule conditions details
	 * 
	 * @param ruleCondition
	 * @param actionSave
	 * @param ruleId
	 * @param updateCondition
	 * @return
	 * @throws SwtException
	 */
	public void saveRulesConditions(ArrayList<RuleConditions> ruleCondition, int ruleId, String actionSave, boolean updateCondition) throws SwtException;
	
	
	/**
	 * This method is used to get search criteria from A_CFG_VALUES
	 * @param typeId
	 * @param languageId
	 * @param showHidden
	 * @return List<TypeValues>
	 * @throws SwtException
	 */
	List<TypeValues> getSearchListCriteria(int typeId,String languageId, Boolean showHidden) throws SwtException;
	
	/**
	 * getDictionaryText() Method to get Dictionary Text
	 * 
	 * @param languageId
	 * @param textId
	 * @return Dictionary - Returns dictionary object
	 * @throws SwtException
	 */
	public Dictionary getDictionaryText(String languageId, String textId) throws SwtException;
	
	/**
	 * executeSelectQuery() Method to execute sQuery parameter as query
	 * 
	 * @param squery
	 * @return List<Object[]>
	 * @throws SwtException
	 */
	public List<Object[]> executeSelectQuery(String squery) throws SwtException;
	
	
//	/**
//	 * This method is used to get criteria from A_PROFILE
//	 * @param languageId
//	 * @param ruleType
//	 * @param moduleId
//	 * @return List<FieldsProfile>
//	 * @throws SwtException
//	 */
//	public List<FieldsProfile> getListCriteriaToAdd(String languageId, String ruleType, String moduleId) throws SwtException;
	
	/**
	 * validateQueryFromDataBase() Method to validate sQuery
	 * 
	 * @param squery
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean validateQueryFromDataBase(String squery) throws SwtException;
	
	/**
	 * This method is used to get the list of rules name and descriptions from
	 * A_RULE, S_DICTIONARY
	 * 
	 * @param ruleId
	 * @param languageId
	 * @param moduleId
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	public LinkedHashMap<String, String> getRulesFromDictionary(String ruleId, String languageId, String moduleId) throws SwtException;
	
	
	/**
	 * This method is used to get the list of profile name from A_PROFILE , S_DICTIONARY
	 * 
	 * @param profileId
	 * @param languageId
	 * @param ruleType
	 * @param moduleId
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	public LinkedHashMap<String, String> getProfileFromDictionary(String profileId, String languageId, String ruleType, String moduleId) throws SwtException;
	
	
	/**
	 * This method is used to simulate a rule
	 * 
	 * @param ruleId
	 * @return int
	 * @throws SwtException
	 */
	public int simulateRule(Integer ruleId) throws SwtException;
	
	
	/**
	 * This method is used to save simulated rule
	 * 
	 * @param ruleDef
	 * @return 
	 * @throws SwtException
	 */
	public void saveSimulatedRule(RulesDefinition ruleDef) throws SwtException;
	
	
	
	/**
	 * deleteSimilatedRule()
	 * 
	 * @param ruleId
	 * Method used to delete the simulate rule
	 * 
	 * @return
	 */
	public int deleteSimilatedRule(int ruleId) throws SwtException;
	
	
	/**
	 * Method used to copy conditions of simulated rule from the original rule if ruleConditions is empty 
	 * 
	 * @param ruleId
	 * @param simulatedRuleId
	 * @return
	 * @throws SwtException
	 */
	public void copyRulesCondFromOriginalRule(int ruleId, int simulatedRuleId) throws SwtException;
	
	
	/**
	 * This method is used to get the list of label and code from A_RULE_CONDITION, S_CFG_TYPE_VALUES, S_DICTIONARY
	 * 
	 * @param typeId
	 * @param ruleId
	 * @param languageId
	 * @param showHidden
	 * @param moduleId
	 * @return List<RuleConditions>
	 * @throws SwtException
	 */
	public List<RuleConditions> getCriteriaToBeChanged(int typeId, String ruleId, String languageId, Boolean showHidden, String moduleId) throws SwtException;

        /**
	
	/**
	 * This method is used to get all the rulesDefinition objects from A_RULES table
	 * 
	 * @param moduleId
	 * @return List<RulesDefinition> - list of rules objects
	 * @throws SwtException
	 */
	public List<RulesDefinition> getListSecondaryRules(String moduleId) throws SwtException;
	
}
