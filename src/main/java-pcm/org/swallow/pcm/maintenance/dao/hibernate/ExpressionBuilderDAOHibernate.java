/*
 * @(#)ExpressionBuilderDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.lang.reflect.Field;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import javax.sql.DataSource;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.ExpressionBuilderDAO;
import org.swallow.pcm.maintenance.model.Dictionary;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.TypeValues;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;





@Repository ("expressionBuilderDAO")
@Transactional
public class ExpressionBuilderDAOHibernate extends CustomHibernateDaoSupport implements ExpressionBuilderDAO {
	public ExpressionBuilderDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(ExpressionBuilderDAOHibernate.class);

	/**
	 * This method is used to get all the rulesDefinition objects from A_RULES table
	 * 
	 * @param langId
	 * @param selectedFilter
	 * @param selectedSort
	 * @param moduleId
	 * @param isRiskFactor
	 * @return List<RulesDefinition> - list of rules objects
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public List<RulesDefinition> getListRules(String langId, String moduleId, String isRiskFactor, String selectedFilter,
			String selectedSort) throws SwtException {

		// Variable List to hold list Rules
		ArrayList<RulesDefinition> rulesList = new ArrayList<RulesDefinition>();
		String[] filterArray = null;
		String SECQUERY = null;
		String[] colArray = { "ruleId", "ruleName", "ruleStatus", "ruleType", "levelCode", "levelType" };
		String[] sortArr = null;
		int count = 0;
		int i = 0;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getListRules] - " + "Entry");

			// get the type columns
			HashMap<String, String> typeCol = new HashMap<String, String>();
			Field[] fields = RulesDefinition.class.getDeclaredFields();
			for (Field field : fields) {
				if (field.getType().toString().contains("class"))
					typeCol.put(field.getName(), (field.getType()).toString().substring(6));
				else if (field.getType().toString().contains("interface"))
					typeCol.put(field.getName(), (field.getType()).toString().substring(10));
				else
					typeCol.put(field.getName(), (field.getType()).toString());
			}
			// Condition to check whether any filter is selected or not
			if ((!(SwtUtil.isEmptyOrNull(selectedFilter)))
					&& (!(selectedFilter.equals("null"))))
				filterArray = selectedFilter.split("\\|");

			// Condition to check whether any sorting order is selected or not
			if ((!(SwtUtil.isEmptyOrNull(selectedSort)))
					&& (!(selectedSort.equals("null"))))
				sortArr = selectedSort.split("\\|");

			// Basic selection query
			SECQUERY = "select r from RulesDefinition r";

//			// added for search query
//			if (SwtUtil.isEmptyOrNull(sQuery)) {
				// Basic selection query
		//		SECQUERY = "select r from RulesDefinition r";
//			} else {
//				SECQUERY = sQuery;
//			}

		/*	if (filterArray != null) {
				// Adds filter parameters to the query
				if (!((filterArray[0]).equals("All"))
						|| !((filterArray[1]).equals("All"))
						|| !((filterArray[2]).equals("All"))
						|| !((filterArray[3]).equals("All"))
						|| !((filterArray[4]).equals("All"))
						||((isRiskFactor.equals("N"))?!((filterArray[5]).equals("All")):!((filterArray[4]).equals("All")))) {

//					if (SwtUtil.isEmptyOrNull(sQuery))
//						SECQUERY = SECQUERY + " where ";
//					else
						SECQUERY = SECQUERY + " and ";

					i = 0;
					while (((isRiskFactor.equals("Y"))?(i < 5):(i<6))) {
						if (!((filterArray[i]).equals("All"))) {

							if (count == 1) {

								SECQUERY = SECQUERY + "AND ";
							}
							count = 1;
							if (filterArray[i].equals("IS NULL")) {
								SECQUERY = SECQUERY + "r." + colArray[i] + " "
										+ filterArray[i] + " ";
							} else {
								if (filterArray[i].equals("IS NULL")) {
									SECQUERY = SECQUERY + "r." + colArray[i] + " "
											+ filterArray[i] + " ";
								} else {
									int index = colArray[i].indexOf(".");
									String field = colArray[i]
											.substring(index + 1);
									// to get the type of the field
									if ((typeCol.get(field)
											.equals("java.lang.String"))
											|| (typeCol.get(field)
													.equals("java.util.Date")))
										SECQUERY = SECQUERY + "r." + colArray[i]
												+ "='" + filterArray[i] + "' ";
									else
										SECQUERY = SECQUERY + "r." + colArray[i] + "="
												+ filterArray[i] + " ";
								}
							}

						}
						i++;
					}

				}
			}*/
			// Adds sort parameters to the query
			/*if (sortArr != null && (!(selectedSort.equals("null")))) {
				SECQUERY = SECQUERY + "ORDER BY ";
				if ((sortArr[0]).equals("0")) {
					SECQUERY = SECQUERY + "r." + colArray[0];
				} else if ((sortArr[0]).equals("1")) {
					SECQUERY = SECQUERY + "r." + colArray[1];
				} else if ((sortArr[0]).equals("2")) {
					SECQUERY = SECQUERY + "r." + colArray[2];
				} else if ((sortArr[0]).equals("3")) {
					SECQUERY = SECQUERY + "r." + colArray[3];
				} else if ((sortArr[0]).equals("4")) {
					SECQUERY = SECQUERY + "r." + colArray[4];
				} else if ((sortArr[0]).equals("5")) {
					SECQUERY = SECQUERY + "r." + colArray[5];
				}

				if ((sortArr[1]).equals("false")) {
					SECQUERY = SECQUERY + " ASC";
				} else if ((sortArr[1]).equals("true")) {
					SECQUERY = SECQUERY + " DESC";
				}
			} else {
				SECQUERY = SECQUERY + " ORDER BY r.ruleName";
			}*/
		/**********************************************************************************************/
			
//			// Query to get the rules list
//			if (!SwtUtil.isEmptyOrNull(currentFilter))
//			{
//				rulesList = (ArrayList<RulesDefinition>) getHibernateTemplate().find(
//						"select r from RulesDefinition r where r.ruleName not like '_SIMULATED_RULE_%' and r.moduleId = ? and " + currentFilter,moduleId);
//			}			
//			else
//			{
			
			try (Session session = SwtUtil.sessionFactory.openSession()) {
				rulesList = (ArrayList<RulesDefinition>) session.createQuery(SECQUERY, RulesDefinition.class).getResultList();
			}

			// return result
			return rulesList;

			} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getListRules] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getListRules", this.getClass());
			} finally {
			// Nullify object
			rulesList = null;
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getListRules ] - Exit");
			}
		}
	
	/**
	 * removeRule
	 * 
	 * Method to delete Rule
	 * 
	 * @return int
	 * @param rule
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public int removeRule(RulesDefinition rule) throws SwtException {
		return 0;
//
//		// Variable to hold result
//		int n = 0;
//		// Variable list to hold list Alert
//		List<AmlAlertDetails> ruleExistInAlert = new ArrayList<AmlAlertDetails>();
//		
//		// Variable list to hold list Group rules
//		List<RuleGroupDetails> ruleExistInGroup = new ArrayList<RuleGroupDetails>();
//		
//		try {
//			// log debug message
//			log.debug(this.getClass().getName() + " - [removeRule] - " + "Entry");
//			// get list Rule used by alerts
//			ruleExistInAlert = (List<AmlAlertDetails>) getHibernateTemplate().find(
//					"select ad from AmlAlertDetails ad where ad.ruleId = ?",
//					rule.getRuleId());
//			
//			// get list Rule exist in group
//			ruleExistInGroup = (List<RuleGroupDetails>) getHibernateTemplate().find(
//					"select gd from RuleGroupDetails gd where gd.id.ruleId = ?",
//					rule.getRuleId());
//			
//			// Check if current rule used by alerts or groups
//			if (ruleExistInAlert.size() != 0 || ruleExistInGroup.size() != 0) {
//				// we can't delete Rule
//				n = 1;
//			} else {
//				getHibernateTemplate().deleteAll(getHibernateTemplate().find("select rc from RuleConditions rc where rc.id.ruleId = ? ", rule.getRuleId()));
//				//delete related documents 
//				getHibernateTemplate().deleteAll(getHibernateTemplate().find("select rd from FatcaRequiredDocument rd where rd.id.ruleId = ? ", rule.getRuleId()));
//				// delete Rule
//				getHibernateTemplate().delete(rule);
//				// flush current session
//				getHibernateTemplate().flush();
//			}
//			// return result as int
//			return n;
//
//		} catch (Exception exp) {
//			// log error message
//			log.error(this.getClass().getName()
//					+ " - Exception Caught in [removeRule] method : - "
//					+ exp.getMessage());
//			throw SwtErrorHandler.getInstance().handleException(exp,
//					"removeRule", this.getClass());
//		} finally {
//			// Nullify objects
//			ruleExistInAlert = null;
//			// Debug Message
//			log.debug(this.getClass().getName() + "- [ removeRule ] - Exit");
//		}

	}

	/**
	 * saveRule
	 * 
	 * Method to delete Rule
	 * 
	 * @param rule
	 * @param actionSave
	 * @return
	 * @throws SwtException
	 */
	public void saveRule(RulesDefinition rule, String actionSave, String moduleId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [saveRule] - " + "Entry");
			Transaction tx = session.beginTransaction();
			try {
				if (actionSave.equals("saveRule") || actionSave.equals("copyRule")) {
					Query<Integer> query = session.createQuery(
						"select max(r.ruleId) from RulesDefinition r where r.moduleId = :moduleId", Integer.class);
					query.setParameter("moduleId", moduleId);
					Integer idMax = query.uniqueResult();
					rule.setRuleId(idMax != null ? idMax + 1 : 1);
					session.save(rule);
				}
				if (actionSave.equals("updateRule")) {
					session.update(rule);
				}
				tx.commit();
			} catch (Exception exp) {
				if (tx != null) tx.rollback();
				log.error(this.getClass().getName()
						+ " - Exception Caught in [saveRule] method : - "
						+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"saveRule", this.getClass());
			}
		} finally {
			log.debug(this.getClass().getName() + "- [ saveRule ] - Exit");
		}
	}

	/**
	 * getRuleListReport()
	 * 
	 * @param selectedfilter
	 *            - filter parameter
	 * @param selectedsort
	 *            - Sort parameter
	 * @param typeCol
	 *            - columns type
	 * @param sQuery
	 * @param langId
	 * @return Collection -get rule list
	 * @throws SwtException
	 * 
	 *             This method to get the rule list for given filter and sort
	 *             parameters
	 */
	@SuppressWarnings("unchecked")
	// ... existing code ...

	public Collection<RulesDefinition> getRuleListReport(String selectedFilter, String selectedSort, HashMap<String, String> typeCol, String sQuery, String langId, String moduleId,String isRiskFactor) throws SwtException {

		List<RulesDefinition> listData = null;
		String[] filterArray = null;
		String query = null;
		String[] colArray = {"c.ruleId", "c.ruleName", "c.ruleStatus", "c.ruleType", "c.levelCode"};
		String[] sortArray = null;
		int i = 0;
		int count = 0;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName()	+ " - [ getRuleListReport ] - Entry");

			if ((!(SwtUtil.isEmptyOrNull(selectedFilter)))
					&& (!(selectedFilter.equals("null"))))
				filterArray = selectedFilter.split("\\|");

			if ((!(SwtUtil.isEmptyOrNull(selectedSort)))
					&& (!(selectedSort.equals("null"))))
				sortArray = selectedSort.split("\\|");

			if (SwtUtil.isEmptyOrNull(sQuery)) {
				query = "from RulesDefinition c ";
			} else {
				query = sQuery;
			}

			if (filterArray != null){
				if (!filterArray[0].equals("All") || !filterArray[1].equals("All") || !filterArray[2].equals("All") || !filterArray[3].equals("All") || !filterArray[4].equals("All")) {
					if (SwtUtil.isEmptyOrNull(sQuery))
						query = query + "where ";
					else
						query = query + "and ";

					i = 0;
					while (i < colArray.length) {
						if (!filterArray[i].equals("All")) {
							if (count == 1)
							{
								query = query + "and ";
							}
							count = 1;

							if (filterArray[i].equals("IS NULL")) {
								query = query + colArray[i] + " " + filterArray[i] + " ";
							} else {
								int index = colArray[i].indexOf(".");
								String field = colArray[i].substring(index + 1);

								if (typeCol.get(field).equals("java.lang.String") || typeCol.get(field).equals("java.util.Date"))
									query = query + colArray[i] + " = '" + filterArray[i] + "' ";
								else
									query = query + colArray[i] + " = "	+ filterArray[i] + " ";
							}
						}
						i++;							
					}

					query = query + "and c.ruleName not like '_SIMULATED_RULE_%' " + "and c.moduleId = '" + moduleId + "' and c.isRiskFactor ='" + isRiskFactor + "'";	
				}
				else
				{
					if (SwtUtil.isEmptyOrNull(sQuery))
						query = query + " where c.ruleName not like '_SIMULATED_RULE_%' " + "and c.moduleId = '" + moduleId + "' and c.isRiskFactor ='" + isRiskFactor + "'";
					else
						query = query	+ " and c.ruleName not like '_SIMULATED_RULE_%' " + "and c.moduleId = '" + moduleId + "' and c.isRiskFactor ='" + isRiskFactor + "'";
				}
			} else {
				if (SwtUtil.isEmptyOrNull(sQuery))
					query = query + " where c.ruleName not like '_SIMULATED_RULE_%' " + "and c.moduleId = '" + moduleId + "' and c.isRiskFactor ='" + isRiskFactor + "'";
				else
					query = query	+ " and c.ruleName not like '_SIMULATED_RULE_%' " + "and c.moduleId = '" + moduleId + "' and c.isRiskFactor ='" + isRiskFactor + "'";
			}

			if (sortArray != null && !selectedSort.equals("null")) {
				query = query + "order by ";
				if (sortArray[0].equals("0"))
					query = query + colArray[0];
				else if (sortArray[0].equals("1"))
					query = query + colArray[1];
				else if (sortArray[0].equals("2"))
					query = query + colArray[2];
				else if (sortArray[0].equals("3"))
					query = query + colArray[3];
				else if (sortArray[0].equals("4"))
					query = query + colArray[4];

				if (sortArray[1].equals("false"))
					query = query + " ASC";
				else if ((sortArray[1]).equals("true"))
					query = query + " DESC";					

			} else {
				query = query + "order by c.ruleName";
			}

			listData = session.createQuery(query, RulesDefinition.class).getResultList();
			return listData;

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getRuleListReport] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRuleListReport", this.getClass());
		} finally {
			listData = null;
			filterArray = null;
			query = null;
			sQuery = null;
			colArray = null;
			sortArray = null;
			i = 0;
			count = 0;
			log.debug(this.getClass().getName() + "- [getRuleListReport] - Exit ");
		}
	}

	@SuppressWarnings("unchecked")
	public RulesDefinition getRuleById(Integer ruleId, String langId) throws SwtException {
		RulesDefinition rule = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getRuleById] - Entry");
			List<RulesDefinition> rules = session.createQuery(
				"SELECT rd from RulesDefinition rd WHERE rd.ruleId = :ruleId", RulesDefinition.class)
				.setParameter("ruleId", ruleId, StandardBasicTypes.INTEGER)
				.getResultList();
			if (!rules.isEmpty()) {
				rule = rules.get(0);
			}
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getRuleById] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getRuleById", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "- [getRuleById] - Exit ");
		}
		return rule;
	}

	@SuppressWarnings("unchecked")
	public List<RulesDefinition> getSearchData(String sQuery, String langId, String moduleId,String isRiskFactor) throws SwtException {
		List<RulesDefinition> rulesList= new ArrayList<RulesDefinition>();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getSearchData] - Entry");
			String hql = sQuery + " and moduleId = :moduleId order by ruleName";
			rulesList = session.createQuery(hql, RulesDefinition.class)
				.setParameter("moduleId", moduleId)
				.getResultList();
			return rulesList;
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSearchData] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSearchData", this.getClass());
		} finally {
			rulesList=null;
			log.debug(this.getClass().getName() + " - [getSearchData] - Exit");
		}
	}

	@SuppressWarnings("unchecked")
	public LinkedHashMap<String, String> getTypeValuesList(int typeId,String languageId, Boolean showHidden) throws SwtException {
		List<TypeValues> listValues = new ArrayList<TypeValues>();
		LinkedHashMap<String, String> hashTypeValues = new LinkedHashMap<String, String>();
		List<Object[]> rawList = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + "- [getTypeValuesList] - Entry ");
			if (showHidden == false) {
				rawList = session.createQuery(
								"select TV, d.text from TypeValues TV,Dictionary d where TV.id.typeId=:typeId and  TV.labelTextId = d.id.textId"
										+ " AND d.id.languageId =:languageId AND TV.modif = 'I' order by TV.id.valueId", Object[].class)
            				        .setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
                                    .setParameter("languageId", languageId, StandardBasicTypes.STRING)
                                    .getResultList();
			} else {
				rawList = session.createQuery(
								"select TV, d.text from TypeValues TV,Dictionary d where TV.id.typeId=:typeId and  TV.labelTextId = d.id.textId"
										+ " AND d.id.languageId =:languageId order by TV.id.valueId", Object[].class)
				                    .setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
				                    .setParameter("languageId", languageId, StandardBasicTypes.STRING)
				                    .getResultList();
			}

			if (rawList.size() == 0) {
				if (showHidden == false) {
					listValues = session
							.createQuery(
									"select TV from TypeValues TV where TV.id.typeId=:typeId AND TV.modif = 'I' order by TV.id.valueId", TypeValues.class)
							.setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
							.getResultList();
				} else {
					listValues = session
							.createQuery(
									"select TV from TypeValues TV where TV.id.typeId=:typeId order by TV.id.valueId", TypeValues.class)
							 .setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
							 .getResultList();
				}
				for (int i = 0; i < listValues.size(); i++) {
					hashTypeValues.put(listValues.get(i).getCode(), listValues
							.get(i).getCode());
				}
			} else {
				for (Object[] su : rawList) {
					TypeValues tv = (TypeValues) su[0];
					tv.setLabelFromDict((String) su[1]);
					hashTypeValues.put(tv.getCode(), tv.getLabelFromDict());
				}
			}
			return hashTypeValues;

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTypeValuesList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getTypeValuesList", this.getClass());
		} finally {
			listValues = null;
			rawList = null;
			hashTypeValues = null;
			log.debug(this.getClass().getName()	+ "- [getTypeValuesList] - Exit ");
		}
	}

// ... existing code ...
	
	
	/**
	 * Method to save Rule conditions details
	 * 
	 * @param ruleCondition
	 * @param actionSave
	 * @param ruleId
	 * @param updateCondition
	 * @return
	 * @throws SwtException
	 */
	// ... existing code ...

	public void saveRulesConditions(ArrayList<RuleConditions> ruleCondition, int ruleId, String actionSave, boolean updateCondition) throws SwtException{
		int savedRuleId = 0;
		RuleConditions.Id id = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [saveRulesConditions] - " + "Entry");
			Transaction tx = session.beginTransaction();
			try {
				if (actionSave.equals("saveRule")) {
					for (int i = 0 ; i < ruleCondition.size() ; i++){
						savedRuleId = ruleId;
						id = new RuleConditions.Id();
						id.setRuleId(savedRuleId);
						id.setConditionId(ruleCondition.get(i).getId().getConditionId());
						ruleCondition.get(i).setId(id);
						session.save(ruleCondition.get(i));
					}
				}
				if (actionSave.equals("updateRule")) {
					if (!updateCondition) {
						session.createQuery("delete from RuleConditions rc where rc.id.ruleId = :ruleId")
							.setParameter("ruleId", ruleCondition.get(0).getId().getRuleId())
							.executeUpdate();
						for (int i = 0 ; i < ruleCondition.size() ; i++){
							session.save(ruleCondition.get(i));
						}
					} else {
						for (int i = 0 ; i < ruleCondition.size() ; i++){
							session.update(ruleCondition.get(i));
						}
					}
				}
				tx.commit();
			} catch (Exception exp) {
				if (tx != null) tx.rollback();
				log.error(this.getClass().getName()
						+ " - Exception Caught in [saveRulesConditions] method : - "
						+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"saveRulesConditions", this.getClass());
			}
		} finally {
			log.debug(this.getClass().getName() + "- [ saveRulesConditions ] - Exit");
		}
	}

	@SuppressWarnings("unchecked")
	public List<TypeValues> getSearchListCriteria(int typeId,String languageId, Boolean showHidden)	throws SwtException {
		List<TypeValues> listCriteria = new ArrayList<TypeValues>();
		List<Object[]> rawList = new ArrayList<Object[]>();
		try (Session session = SwtUtil.sessionFactory.openSession()) {
			log.debug(this.getClass().getName() + "- [getSearchListCriteria] - Entry ");
			Transaction tx = session.beginTransaction();
			try {
				if (showHidden)
					rawList = session
							.createQuery("select TV, d.text from TypeValues TV, Dictionary d where TV.id.typeId = :typeId and TV.labelTextId = CAST(d.id.textId AS Integer) " +
									"AND d.id.languageId = :languageId order by d.text, TV.labelPlus", Object[].class)
							.setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
							.setParameter("languageId", languageId, StandardBasicTypes.STRING)
							.getResultList();
				else {
					rawList = session
							.createQuery("select TV, d.text from TypeValues TV, Dictionary d where TV.id.typeId = :typeId and TV.labelTextId = CAST(d.id.textId AS Integer) " +
									"AND d.id.languageId = :languageId AND TV.modif = 'I' order by d.text, TV.labelPlus", Object[].class)
							.setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
							.setParameter("languageId", languageId, StandardBasicTypes.STRING)
							.getResultList();
				}

				if (rawList.size() == 0) {
					listCriteria = session
							.createQuery("select TV from TypeValues TV where TV.id.typeId = :typeId order by TV.id.valueId", TypeValues.class)
							.setParameter("typeId", typeId, StandardBasicTypes.INTEGER)
							.getResultList();
				}else{
					for (Object[] su : rawList) {
						TypeValues tv = (TypeValues) su[0];
						tv.setLabelFromDict((String) su[1]);		            
						listCriteria.add(tv);
					}
				}
				tx.commit();
				return listCriteria;
			} catch (Exception exp) {
				if (tx != null) tx.rollback();
				log.error(this.getClass().getName()
						+ " - Exception Catched in [getSearchListCriteria] method : - "
						+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"getSearchListCriteria", this.getClass());
			}
		} finally {
			listCriteria = null;
			log.debug(this.getClass().getName()	+ "- [getSearchListCriteria] - Exit ");
		}
	}

	@SuppressWarnings("rawtypes")
	public Dictionary getDictionaryText(String languageId, String textId)
			throws SwtException {
		List dictionaryList = null;
		Dictionary dict = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName()
					+ "- [getDictionaryText] - Entering ");
			String hql = "from Dictionary d where d.id.languageId = :languageId and d.id.textId IN (" + textId + ")";
			dictionaryList = session.createQuery(hql)
					.setParameter("languageId", languageId)
					.getResultList();
			if (dictionaryList.size() != 0)
				dict = (Dictionary) dictionaryList.get(0);
			return dict;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDictionaryText] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDictionaryText", this.getClass());
		} finally {
			dict = null;
			dictionaryList = null;
			log.debug(this.getClass().getName()
					+ "- [getDictionaryText] - Exiting ");
		}
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> executeSelectHibernateQuery(String squery)
			throws SwtException {
		List<Object[]> list = null;
		try (Session session = SwtUtil.pcSessionFactory.openSession()) {
			log.debug(this.getClass().getName()
					+ "- [executeSelectHibernateQuery] - Entering ");
			list = session.createQuery(squery).getResultList();
			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [executeSelectHibernateQuery] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"executeSelectHibernateQuery", this.getClass());
		} finally {
			list = null;
			log.debug(this.getClass().getName()
					+ "- [executeSelectHibernateQuery] - Exiting ");
		}
	}

	@SuppressWarnings("unchecked")
	public LinkedHashMap<String, String> getRulesFromDictionary(String ruleId, String languageId, String moduleId) throws SwtException {
		List<Dictionary> dicList = new ArrayList<Dictionary>();
		LinkedHashMap<String, String> hashRules = new LinkedHashMap<String, String>();
		if(1==1)
			return hashRules;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName()
					+ "- [getRulesFromDictionary] - Entry ");
			if (SwtUtil.isEmptyOrNull(ruleId))
			{
				dicList = session.createQuery(
						"select d from RulesDefinition r, Dictionary d where r.nameTextId = d.id.textId"
								+ " and r.moduleId = :moduleId"
								+ " and d.id.languageId = :languageId", Dictionary.class)
						.setParameter("moduleId", moduleId)
						.setParameter("languageId", languageId)
						.getResultList();
			}
			else
			{
				dicList = session.createQuery(
						"select d from RulesDefinition r, Dictionary d where r.nameTextId = d.id.textId"
								+ " and r.ruleId = :ruleId and d.id.languageId = :languageId", Dictionary.class)
						.setParameter("ruleId", ruleId)
						.setParameter("languageId", languageId)
						.getResultList();
			}

			for (int i = 0; i < dicList.size(); i++) {
				hashRules.put(dicList.get(i).getId().getTextId(), dicList
						.get(i).getText());
			}

			if (SwtUtil.isEmptyOrNull(ruleId))
			{
				dicList = session.createQuery(
						"select d from RulesDefinition r, Dictionary d where r.descTextId = d.id.textId"
								+ " and r.moduleId = :moduleId"
								+ " and d.id.languageId = :languageId", Dictionary.class)
						.setParameter("moduleId", moduleId)
						.setParameter("languageId", languageId)
						.getResultList();
			}
			else
			{
				dicList = session.createQuery(
						"select d from RulesDefinition r, Dictionary d where r.descTextId = d.id.textId"
								+ " and r.ruleId = :ruleId and d.id.languageId = :languageId", Dictionary.class)
						.setParameter("ruleId", ruleId)
						.setParameter("languageId", languageId)
						.getResultList();
			}

			for (int i = 0; i < dicList.size(); i++) {
				hashRules.put(dicList.get(i).getId().getTextId(), dicList
						.get(i).getText());
			}

			return hashRules;

		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getRulesFromDictionary] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRulesFromDictionary", this.getClass());
		} finally {
			hashRules = null;
			log.debug(this.getClass().getName()
					+ "- [getRulesFromDictionary] - Exit ");
		}
	}

	
	
	/**
	 * This method is used to get the list of profile name from A_PROFILE , S_DICTIONARY
	 * 
	 * @param profileId
	 * @param languageId
	 * @param ruleType
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	// ... existing code ...

	public LinkedHashMap<String, String> getProfileFromDictionary(String profileId, String languageId, String ruleType, String moduleId) throws SwtException {
		LinkedHashMap<String, String> hashProfiles = new LinkedHashMap<String, String>();
		List<Object[]> rawList = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName()
					+ "- [getProfileFromDictionary] - Entry ");
			if (SwtUtil.isEmptyOrNull(profileId) || profileId.equals("null")) {
				rawList = session.createQuery(
						"select d, p.nameTextId from FieldsProfile p, Dictionary d where p.nameTextId = d.id.textId"
								+ " and p.profileOn = :ruleType and p.moduleId = :moduleId and d.id.languageId = :languageId", Object[].class)
						.setParameter("ruleType", ruleType)
						.setParameter("moduleId", moduleId)
						.setParameter("languageId", languageId)
						.getResultList();
			} else {
				rawList = session.createQuery(
						"select d, p.nameTextId from FieldsProfile p, Dictionary d where p.nameTextId = d.id.textId"
								+ " and p.profileOn = :ruleType and p.profileId = :profileId and d.id.languageId = :languageId order by d.text", Object[].class)
						.setParameter("ruleType", ruleType)
						.setParameter("profileId", profileId)
						.setParameter("languageId", languageId)
						.getResultList();
			}
			for (Object[] su : rawList) {
				Dictionary d = (Dictionary) su[0];
				Integer nameTextId = (Integer) su[1];
				hashProfiles.put(String.valueOf(nameTextId), d.getText());
			}
			return hashProfiles;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getProfileFromDictionary] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getProfileFromDictionary", this.getClass());
		} finally {
			hashProfiles = null;
			log.debug(this.getClass().getName()
					+ "- [getProfileFromDictionary] - Exit ");
		}
	}

	public int simulateRule(Integer ruleId) throws SwtException{
		log.debug(this.getClass().getName()
				+ "- [ simulateRule ] - Entry");
		int n = 0;
		try (Connection conn = ((DataSource) SwtUtil.getBean("dataSource")).getConnection();
			 CallableStatement cstmt = conn.prepareCall("{call pkg_a_simulate_rule.simulate_rule(?,?)}")) {
			cstmt.setInt(1, ruleId);
			cstmt.registerOutParameter(2, oracle.jdbc.OracleTypes.NUMBER);
			cstmt.execute();
			n = (int) cstmt.getInt(2);
			conn.commit();
			return n;
		} catch (Exception ex) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [simulateRule] method : - "
							+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"simulateRule", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [simulateRule] - Exit");
		}
	}

	@SuppressWarnings("unchecked")
	public void saveSimulatedRule(RulesDefinition ruleDef) throws SwtException {
		ArrayList<RulesDefinition> simRules = new ArrayList<RulesDefinition>();
		String ruleName = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [saveSimulatedRule] - " + "Entry");
			simRules = (ArrayList<RulesDefinition>) session.createQuery(
					"SELECT rd from RulesDefinition rd WHERE rd.ruleName like :ruleName order by rd.ruleId desc", RulesDefinition.class)
					.setParameter("ruleName", ruleDef.getRuleName() + "%")
					.getResultList();
			Transaction tx = session.beginTransaction();
			try {
				if (simRules.size() > 0) {
					ruleDef.setRuleId(simRules.get(0).getRuleId() + 1);
					ruleName = String.valueOf(ruleDef.getRuleName() + (simRules.size() + 1));
					ruleDef.setRuleName(ruleName);
				} else {
					ruleDef.setRuleId(40);
					ruleName = ruleDef.getRuleName() + "1";
					ruleDef.setRuleName(ruleName);
				}
				session.save(ruleDef);
				tx.commit();
			} catch (Exception ex) {
				if (tx != null) tx.rollback();
				log.error(this.getClass().getName()
						+ " - Exception Caught in [saveSimulatedRule] method : - "
						+ ex.getMessage());
				throw SwtErrorHandler.getInstance().handleException(ex,
						"saveSimulatedRule", this.getClass());
			}
		} finally {
			simRules = null;
			log.debug(this.getClass().getName() + "- [saveSimulatedRule] - Exit ");
		}
	}

	@SuppressWarnings("unchecked")
	public int deleteSimilatedRule(int ruleId) throws SwtException {
		int n = 0;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Transaction tx = session.beginTransaction();
			try {
				session.createQuery("delete from RuleConditions rc where rc.id.ruleId = :ruleId")
						.setParameter("ruleId", ruleId)
						.executeUpdate();
				List<RulesDefinition> rules = session.createQuery(
						"select r from RulesDefinition r where r.ruleId = :ruleId", RulesDefinition.class)
						.setParameter("ruleId", ruleId)
						.getResultList();
				if (!rules.isEmpty()) {
					session.delete(rules.get(0));
				}
				tx.commit();
				n = 1;
			} catch (Exception exp) {
				if (tx != null) tx.rollback();
				log.error(this.getClass().getName()
						+ " - Exception Caught in [deleteSimilatedRule] method : - "
						+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"deleteSimilatedRule", this.getClass());
			}
		} finally {
			log.debug(this.getClass().getName() + "- [ deleteSimilatedRule ] - Exit");
		}
		return n;
	}

	@SuppressWarnings("unchecked")
	public void copyRulesCondFromOriginalRule(int ruleId, int simulatedRuleId) throws SwtException {
		List<RuleConditions> listCond = new ArrayList<RuleConditions>();
		RuleConditions.Id id = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName()
					+ " - [copyRulesCondFromOriginalRule] - " + "Entry");
			listCond = session.createQuery(
					"select rc from RuleConditions rc where rc.id.ruleId = :ruleId", RuleConditions.class)
					.setParameter("ruleId", ruleId)
					.getResultList();
			Transaction tx = session.beginTransaction();
			try {
				for (int i = 0; i < listCond.size(); i++) {
					id = new RuleConditions.Id();
					id.setRuleId(simulatedRuleId);
					id.setConditionId(listCond.get(i).getId().getConditionId());
					RuleConditions ruleCondition = new RuleConditions();
					ruleCondition.setId(id);
					ruleCondition.setFieldName(listCond.get(i).getFieldName());
					ruleCondition.setOperatorId(listCond.get(i).getOperatorId());
					ruleCondition.setLocalValue(listCond.get(i).getLocalValue());
					ruleCondition.setFieldValue(listCond.get(i).getFieldValue());
					ruleCondition.setDataType(listCond.get(i).getDataType());
					ruleCondition.setProfileField(listCond.get(i).getProfileField());
					ruleCondition.setTableName(listCond.get(i).getTableName());
					ruleCondition.setNextCondition(listCond.get(i).getNextCondition());
					ruleCondition.setProfileFieldValue(listCond.get(i).getProfileFieldValue());
					session.save(ruleCondition);
				}
				tx.commit();
			} catch (Exception exp) {
				if (tx != null) tx.rollback();
				log.error(this.getClass().getName()
						+ " - Exception Caught in [copyRulesCondFromOriginalRule] method : - "
						+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"copyRulesCondFromOriginalRule", this.getClass());
			}
		} finally {
			log.debug(this.getClass().getName()
					+ "- [ copyRulesCondFromOriginalRule ] - Exit");
		}
	}



	/**
	 * executeSelectQuery() Method to execute sQuery parameter as query
	 *
	 * @param squery
	 * @return List<Object[]>
	 * @throws SwtException
	 */
	public List<Object[]> executeSelectQuery(String squery) throws SwtException {
		log.debug(this.getClass().getName() + " - [executeSelectQuery] - Entering");

		List<Object[]> list = new ArrayList<>();

		try (Session session = SwtUtil.pcSessionFactory.openSession();
			 Connection conn = SwtUtil.connection(session);
			 Statement stmt = conn.createStatement();
			 ResultSet rs = stmt.executeQuery(squery)) {

			ResultSetMetaData rsmd = rs.getMetaData();
			int columnsNumber = rsmd.getColumnCount();

			while (rs.next()) {
				Object[] objects = new Object[columnsNumber];
				for (int i = 0; i < columnsNumber; i++) {
					objects[i] = rs.getObject(i + 1);
				}
				list.add(objects);
			}

			log.debug(this.getClass().getName() + " - [executeSelectQuery] - Retrieved " + list.size() + " records");

		} catch (Exception ex) {
			// Handle Oracle ORA-00942: Table or view does not exist
			if (ex.getMessage() != null && ex.getMessage().contains("ORA-00942")) {
				log.warn(this.getClass().getName() + " - [executeSelectQuery] - ORA-00942 detected, retrying with Hibernate");
				list = executeSelectHibernateQuery(squery);
			} else {
				log.error(this.getClass().getName() + " - Exception in [executeSelectQuery] method: ", ex);
				throw SwtErrorHandler.getInstance().handleException(ex, "executeSelectQuery", this.getClass());
			}
		}

		log.debug(this.getClass().getName() + " - [executeSelectQuery] - Exit");
		return list;
	}

	public List<RuleConditions> getCriteriaToBeChanged(int typeId, String ruleId, String languageId, Boolean showHidden, String moduleId)	throws SwtException {
		StringBuffer query = null;
		Connection conn = null;
		Statement stmt = null;
		ResultSet resultSet = null;
		List<RuleConditions> listCriteria = new ArrayList<RuleConditions>();
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getCriteriaToBeChanged] - Entry");
			session = SwtUtil.sessionFactory.openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement(); 
			query = new StringBuffer()
			.append("SELECT t.code code, t.type_code typeCode, t.type_enum typeEnum, c.table_name tableName, NVL (d.text, code) || t.label_plus label, c.condition_id conditionId, " +
					"c.field_name fieldName, c.operator_id operatorId, c.field_value fieldValue, c.next_condition nextCondition, " +
					"'N' profileField, t.column_value_upper columnValueUpper, t.hidden_operator hiddenOperator, 'N' profileFieldValue")
			.append("  FROM a_rule_condition c")
			.append("       INNER JOIN s_cfg_type_values t ON (c.field_name = t.code OR c.field_name = 'upper(' || t.code || ')' OR c.field_name = 'TRUNC(' || t.code || ')')")
			.append("       LEFT JOIN s_dictionary d ON d.text_id = t.label_text_id")
			.append(" WHERE rule_id = " + ruleId + " AND t.type_id = " + typeId + " AND language_id = '" + languageId + "' AND c.profile_field = 'N' " )
			.append(" UNION ALL ")
			.append("SELECT p.profile_code code, c.data_type typeCode, 0 typeEnum, c.table_name tableName, NVL (d.text, p.profile_name) label, c.condition_id conditionId, " +
					"c.field_name fieldName, c.operator_id operatorId, c.field_value fieldValue, c.next_condition nextCondition, " +
					"'Y' profileField, 'NN' columnValueUpper, '>,>,>=,<=' hiddenOperator, profile_field_value profileFieldValue")
			.append("  FROM a_rule_condition c")
			.append("       LEFT JOIN a_profile p ON p.profile_code = c.field_name")
			.append("       LEFT JOIN s_dictionary d ON (d.text_id = p.name_text_id AND d.language_id = '" + languageId + "')")
			.append(" WHERE c.rule_id = " + ruleId + " AND c.profile_field = 'Y' AND module_id = '" + moduleId + "'"); 
					
			resultSet = (ResultSet) stmt.executeQuery(query.toString());

			if (resultSet != null) {
				while (resultSet.next()) {
					RuleConditions ruleC = new RuleConditions();
					ruleC.setFieldCode(resultSet.getString("code"));
					ruleC.setOperatorId(resultSet.getString("operatorId"));
					ruleC.setFieldValue(resultSet.getString("fieldValue"));
					ruleC.setFieldName(resultSet.getString("fieldName"));
					ruleC.setNextCondition(resultSet.getString("nextCondition"));
					ruleC.setProfileFieldValue(resultSet.getString("profileFieldValue"));
					RuleConditions.Id id = new RuleConditions.Id();
					id.setRuleId(Integer.parseInt(ruleId));
					id.setConditionId(resultSet.getInt("conditionId"));
					ruleC.setId(id);
					listCriteria.add(ruleC);
				}
			}			
			
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCriteriaToBeChanged] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCriteriaToBeChanged", this.getClass());
		} finally {
			JDBCCloser.close(resultSet, stmt,conn, session);
			query = null;
			log.debug(this.getClass().getName() + " - [getCriteriaToBeChanged] - Exit");
		}
		
		return listCriteria;
	}

	public List<RulesDefinition> getListSecondaryRules(String moduleId) throws SwtException {

		List<RulesDefinition> lstSecondaryRules = null;
		Iterator<RulesDefinition> iterSecondaryRules = null;
		Session session = null; 
		try {
			log.debug(this.getClass().getName() + " - [getListSecondaryRules] - " + "Entry");
			session = SwtUtil.sessionFactory.openSession();
			iterSecondaryRules = (Iterator<RulesDefinition>) session.createQuery(
					"FROM RulesDefinition r where 1=2").getResultList().iterator();
			lstSecondaryRules = new ArrayList<RulesDefinition>();
			while (iterSecondaryRules.hasNext()) {
				RulesDefinition rulesDefinition = (RulesDefinition) iterSecondaryRules.next();
				lstSecondaryRules.add(rulesDefinition);
			}
		} catch (Exception ex) {
			log.error(this.getClass().getName() + " - Exception Caught in [getListSecondaryRules] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getListSecondaryRules", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName() + " - [getListSecondaryRules] - Exit");
		}
		return lstSecondaryRules;
	}

	/**
	 * validateQueryFromDataBase() Method to validate sQuery
	 *
	 * @param squery
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean validateQueryFromDataBase(String squery) throws SwtException {
		log.debug(this.getClass().getName() + " - [validateQueryFromDataBase] - Entry");

		boolean validQuery = true;

		try (Session session = SwtUtil.pcSessionFactory.openSession();
			 Connection conn = SwtUtil.connection(session);
			 Statement stmt = conn.createStatement();
			 ResultSet rs = stmt.executeQuery(squery.contains(" where ") ? squery + " and 1=2" : squery + " where 1=2")) {

			log.debug(this.getClass().getName() + " - [validateQueryFromDataBase] - Query validated successfully");

		} catch (Exception ex) {
			validQuery = false;
			log.error(this.getClass().getName() + " - Exception in [validateQueryFromDataBase] method: ", ex);
			throw SwtErrorHandler.getInstance().handleException(new SwtException(ex.getMessage()),
					"validateQueryFromDataBase", this.getClass());
		}

		log.debug(this.getClass().getName() + " - [validateQueryFromDataBase] - Exit with status: " + validQuery);
		return validQuery;
	}
	
	
	
	
}