/*
 * @(#)AccountAccessDAO.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR>
 * 
 */
public interface PaymentControlDAO extends DAO {

	public void saveNewDetailInRemoteDB() throws SwtException;
	
}
