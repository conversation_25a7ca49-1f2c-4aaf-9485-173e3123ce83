/*
 * @(#)SpreadProfilesMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.SpreadProfilesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.ProcessPointCategory;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.Type;
import org.hibernate.Session;







@Repository ("spreadProfilesMaintenanceDAO")
@Transactional
public class SpreadProfilesMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements SpreadProfilesMaintenanceDAO {
	public SpreadProfilesMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(SpreadProfilesMaintenanceDAOHibernate.class);


	public SpreadProfile getSpreadProfileDetails(String spreadProfileId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getSpreadProfileDetails] - Entry");

		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		SpreadProfile spreadProfile = null;

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			// Create a TypedQuery to fetch the spread profile by ID
			TypedQuery<SpreadProfile> query = session.createQuery(
					"FROM SpreadProfile s WHERE s.id.spreadProfileId = :spreadProfileId",
					SpreadProfile.class
			);
			query.setParameter("spreadProfileId", spreadProfileId);

			// Fetch single result safely
			spreadProfile = query.getResultStream().findFirst().orElse(null);
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getSpreadProfileDetails] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "getSpreadProfileDetails", AccountGroupsMaintenanceDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + " - [getSpreadProfileDetails] - Exit");
		return spreadProfile;
	}


	public List<SpreadProfile> getSpreadProfilesList(String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [getSpreadProfilesList] - Entry");

		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		List<SpreadProfile> records = new ArrayList<>();

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			// Define HQL Query
			String hql = SwtUtil.isEmptyOrNull(currencyCode) || SwtConstants.ALL_VALUE.equals(currencyCode)
					? "FROM SpreadProfile s"
					: "FROM SpreadProfile s WHERE s.currencyCode = :currencyCode ORDER BY UPPER(s.id.spreadProfileId) ASC";

			TypedQuery<SpreadProfile> query = session.createQuery(hql, SpreadProfile.class);

			if (!(SwtUtil.isEmptyOrNull(currencyCode) || SwtConstants.ALL_VALUE.equals(currencyCode))) {
				query.setParameter("currencyCode", currencyCode);
			}

			records = query.getResultList();
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in [getSpreadProfilesList] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getSpreadprofileDetailList", this.getClass());
		}

		log.debug(this.getClass().getName() + " - [getSpreadProfilesList] - Exit with " + records.size() + " records");
		return records;
	}

	public List<SpreadProcessPoint> getSpreadProcessPointList(String selectedSpreadProfileId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getSpreadProcessPointList] - Entry");

		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		List<SpreadProcessPoint> records = new ArrayList<>();

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			// Define HQL Query
			String hql = SwtUtil.isEmptyOrNull(selectedSpreadProfileId)
					? "FROM SpreadProcessPoint sp"
					: "FROM SpreadProcessPoint sp WHERE sp.spreadProfileId = :spreadProfileId";

			TypedQuery<SpreadProcessPoint> query = session.createQuery(hql, SpreadProcessPoint.class);

			if (!SwtUtil.isEmptyOrNull(selectedSpreadProfileId)) {
				query.setParameter("spreadProfileId", selectedSpreadProfileId);
			}

			records = query.getResultList();
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in [getSpreadProcessPointList] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getSpreadProcessPointList", this.getClass());
		}

		log.debug(this.getClass().getName() + " - [getSpreadProcessPointList] - Exit with " + records.size() + " records");
		return records;
	}



	public Collection getSpreadProfilesByCategoryId(String categoryId) throws SwtException {

		List spreadList =  new LinkedList<HashMap<String,Object>>();
		HashMap<String, String> obj =null;
		ArrayList spreadListDetails = null;
		SpreadProfile spreadProfile;
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getSpreadProfilesByCategoryId] - " + "Entry");

			// Query to get the priority detail list in order to priority code
			spreadList = PCMUtil.executeNamedSelectQuery("spread_profile_query", categoryId,categoryId);
			
			Iterator itr = spreadList.iterator();
			spreadListDetails = new ArrayList();
			while (itr.hasNext()) {
				obj = (HashMap<String, String>) (itr.next());
				spreadProfile = new SpreadProfile();
				spreadProfile.setSpreadProfileName(obj.get("name"));
				spreadProfile.getId().setSpreadProfileId(obj.get("spread_profile_id"));
				spreadProfile.setCurrencyCode(obj.get("currency_code"));
				spreadListDetails.add(spreadProfile);
			}
			log.debug(this.getClass().getName()
					+ " - [getSpreadProfilesByCategoryId] - " + "Exit");
			return spreadListDetails;
			
		} catch (Exception exp) {
			exp.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSpreadProfilesByCategoryId] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSpreadProfilesByCategoryId", this.getClass());
		} finally {
			// flush session
			getHibernateTemplate().flush();
			// Nullify object
			spreadList = null;
			spreadListDetails = null;
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getSpreadProfilesByCategoryId ] - Exit");
		}
	}

	public void deleteSpreadProfile(String selectedSpreadProfileId, Long maintEventId) throws SwtException {
		
		List records=null;
		Session session = null;
		Transaction tx = null;
		HashMap<String,String> exist= null;
		SwtInterceptor interceptor = null;
		
		try {
			log.debug(this.getClass().getName() + " - [deleteSpreadProfile] - "
					+ "Entry");
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session= SwtUtil.pcSessionFactory.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Create a TypedQuery to fetch the spread profile by ID
			TypedQuery<SpreadProfile> query = session.createQuery("from SpreadProfile s where s.id.spreadProfileId = :spreadProfileId", SpreadProfile.class);
			query.setParameter("spreadProfileId", selectedSpreadProfileId);

			// Execute the query and get the result
			records = query.getResultList();
			/* Condition to check list size */
			if (!records.isEmpty()) {
				SpreadProfile spreadProfile = (SpreadProfile) records.get(0);
				if(maintEventId != null) {
					spreadProfile.setMainEventId(""+maintEventId);
					for(SpreadProcessPoint element : spreadProfile.getSpreadProcessPoint()){
						element.setMainEventId(""+maintEventId);
					}
				}
				// Query to get the priority detail list in order to priority code
				records = PCMUtil.executeNamedSelectQuery("spread_integrity_query", selectedSpreadProfileId);
				if (!records.isEmpty()) {
					exist = (HashMap<String, String>) records.get(0);
					if (exist.get("exist").equals("Y")) {
						throw new SwtException("errors.DataIntegrityViolationExceptioninDelete");
					} else {
						session.delete(spreadProfile);
						if(maintEventId != null) {
							session.flush();
							tx.rollback();
						}
						else 
							tx.commit();
					}
				}
			} else {
				throw new SwtException("errors.SwtRecordNotExist");
			}
			log.debug(this.getClass().getName() + " - [deleteSpreadProfile] - "+ "Exit");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [deleteSpreadProfile] when rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteSpreadProfile] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCategory", this.getClass());
		} finally {
			JDBCCloser.close(session);
		}
		
	}

	public void saveSpreadProfile(SpreadProfile spreadProfile) throws SwtException {
		
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		
		try {
			log.debug(this.getClass().getName() + " - [saveSpreadProfile] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
            // Assuming you have a session object already created
			TypedQuery<SpreadProfile> query = session.createQuery("from SpreadProfile s where s.id.spreadProfileId = :spreadProfileId", SpreadProfile.class);
			query.setParameter("spreadProfileId", spreadProfile.getId().getSpreadProfileId());
			records = query.getResultList();
			// Condition to check list size
			if (records.isEmpty()) {
				tx = session.beginTransaction();
				if(spreadProfile.getMainEventId() != null) {
					spreadProfile.setRequireAuthorisation("Y");
				}
				session.save(spreadProfile);
				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName() + " - [saveSpreadProfile] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [saveSpreadProfile] when rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Catched in [saveSpreadProfile] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "saveSpreadProfile", this.getClass());
		} finally {
			JDBCCloser.close(session);

		}
		
	}

	public void updateSpreadProfile(SpreadProfile spreadProfile) throws SwtException {
		
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [updateSpreadProfile] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(spreadProfile);
			if(spreadProfile.getMainEventId() != null) {
				session.flush();
				tx.rollback();
			}
			else 
				tx.commit();
			log.debug(this.getClass().getName() + " - [updateSpreadProfile] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [updateSpreadProfile] when rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Catched in [updateSpreadProfile] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "updateSpreadProfile", this.getClass());
		} finally {
			JDBCCloser.close(session);

		}

	}

	public void crudSpreadProcessPoints(List<SpreadProcessPoint> listSpreadProcessPointAdd,
			List<SpreadProcessPoint> listSpreadProcessPointUpdate,
			List<SpreadProcessPoint> listSpreadProcessPointdelete, Long maintEventId) throws SwtException {
		
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		String[] categoriesList = null;
		
		try {
			log.debug(this.getClass().getName() + " - [crudSpreadProcessPoints] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if (!listSpreadProcessPointAdd.isEmpty()) {
				for (Iterator iterator = listSpreadProcessPointAdd.iterator(); iterator.hasNext();) {
					SpreadProcessPoint processI = (SpreadProcessPoint) iterator.next();
					if(maintEventId != null) {
						processI.setMainEventId(""+maintEventId);
					}
					session.save(processI);
					if (!SwtUtil.isEmptyOrNull(processI.getCategories())
							&& (!SwtConstants.ALL_LABEL.equals(processI.getCategories()))) {
						categoriesList = processI.getCategories().split(",");
						for (int i = 0; i < categoriesList.length; i++) {
							String categoryId = categoriesList[i];
							// Create a TypedQuery to fetch the category by ID
							TypedQuery<Category> query = session.createQuery("from Category c where c.id.categoryId = :categoryId", Category.class);
							query.setParameter("categoryId", categoryId);
							// Execute the query and get the result
							records = query.getResultList();
							if (!records.isEmpty()) {
								ProcessPointCategory processPointCat = new ProcessPointCategory();
								processPointCat.getId().setCategoryId(categoryId);
								processPointCat.getId().setProcessPointId(processI.getProcessPointId());
								if(maintEventId != null) {
									processPointCat.setMainEventId(""+maintEventId);
									processPointCat.setCategoryId(categoryId);
								 }
								
								session.save(processPointCat);
							} else {
								throw new SwtException("Category is already deleted!");
							}
						}
					}
				}
			}
			if (!listSpreadProcessPointUpdate.isEmpty()) {
				for (Iterator iterator = listSpreadProcessPointUpdate.iterator(); iterator.hasNext();) {
					SpreadProcessPoint processU = (SpreadProcessPoint) iterator.next();
					if(maintEventId != null) {
						processU.setMainEventId(""+maintEventId);
					}
					// Create a TypedQuery to fetch the categories for the specific process point
					TypedQuery<ProcessPointCategory> query = session.createQuery("from ProcessPointCategory p where p.id.processPointId = :processPointId", ProcessPointCategory.class);
					query.setParameter("processPointId", processU.getProcessPointId());

					// Execute the query and get the result
					records = query.getResultList();
					List<String> categoriesBeforeChangeList = new ArrayList();
					if (!records.isEmpty()) {
						for (Iterator iterator2 = records.iterator(); iterator2.hasNext();) {
							ProcessPointCategory processPointCategory = (ProcessPointCategory) iterator2.next();
							categoriesBeforeChangeList.add(processPointCategory.getId().getCategoryId());
						}
					}
					List<String> categoriesAfterChangeList = new ArrayList();
					if (!SwtUtil.isEmptyOrNull(processU.getCategories())) {
						// Check if the category is already exist in the list or not
						categoriesAfterChangeList = Arrays.asList(processU.getCategories().split(","));
						Set<ProcessPointCategory> categoriesToSaveList = new HashSet(); 
						for (String categoryId : categoriesAfterChangeList)
						{
							if (!categoriesBeforeChangeList.contains(categoryId)) {
								ProcessPointCategory categor = new ProcessPointCategory(processU.getProcessPointId(), categoryId);
								 if(maintEventId != null) {
									 categor.setMainEventId(""+maintEventId);
									 categor.setCategoryId(categoryId);
								 }
								 
								categoriesToSaveList.add(categor);
							}
						}
						processU.setProcessPointCategory(categoriesToSaveList);
					}
					// Remove the non existed categories in the list
					for (String categoryId : categoriesBeforeChangeList)
			        {
						if (!categoriesAfterChangeList.contains(categoryId)) {
							ProcessPointCategory categor = new ProcessPointCategory(processU.getProcessPointId(), categoryId);
							 if(maintEventId != null) {
								 categor.setMainEventId(""+maintEventId);
								 categor.setCategoryId(categoryId);
							 }
							 
							session.delete(categor);
						}
			        }
					session.update(processU);
				}
			}
			if (!listSpreadProcessPointdelete.isEmpty()) {
				for (Iterator iterator = listSpreadProcessPointdelete.iterator(); iterator.hasNext();) {
					SpreadProcessPoint processD = (SpreadProcessPoint) iterator.next();
					for (Iterator iterator2 = processD.getProcessPointCategory().iterator(); iterator2.hasNext();) {
						ProcessPointCategory processPointCategory = (ProcessPointCategory) iterator2.next();
						if(maintEventId != null) {
							processPointCategory.setCategoryId(processPointCategory.getId().getCategoryId());
						 }
						 ProcessPointCategory existingEntity =(ProcessPointCategory) session.get(ProcessPointCategory.class, processPointCategory.getId());
						    if(existingEntity != null) {
						    	if(maintEventId != null) {
									existingEntity.setMainEventId(""+maintEventId);
								}
						    	session.delete(existingEntity);
						    }
					}
					SpreadProcessPoint existingProcessD =(SpreadProcessPoint) session.get(SpreadProcessPoint.class, processD.getProcessPointId());
					if(existingProcessD != null) {
						if(maintEventId != null) {
							existingProcessD.setMainEventId(""+maintEventId);
						}
						session.delete(existingProcessD);
					}
				}
			}
			if(maintEventId != null) {
				session.flush();
				tx.rollback();
			}
			else 
				tx.commit();

			log.debug(this.getClass().getName() + " - [crudSpreadProcessPoints] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [crudSpreadProcessPoints] when rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Catched in [crudSpreadProcessPoints] method : - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "crudSpreadProcessPoints", this.getClass());
		} finally {
			JDBCCloser.close(session);
		}
	}
}