/*
 * @(#)AccountGroupsMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.util.*;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.ILMGeneralMaintenanceDAOHibernate;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.pcm.maintenance.dao.AccountGroupsMaintenanceDAO;
import org.swallow.pcm.maintenance.model.*;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;







@Repository ("accountGroupsMaintenanceDAO")
@Transactional
public class AccountGroupsMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements AccountGroupsMaintenanceDAO {
	public AccountGroupsMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(AccountGroupsMaintenanceDAOHibernate.class);

	public void saveAccountGroup(AccountGroup acctGroup)
			throws SwtException {
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		boolean requireAuthorisation = false;
		try {
			log.debug(this.getClass().getName() + " - [saveAcountGroup] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			if(acctGroup != null && !SwtUtil.isEmptyOrNull(acctGroup.getMainEventId())) {
				requireAuthorisation = true;
			}
			// Create a TypedQuery for AccountGroup
			TypedQuery<AccountGroup> query = session.createQuery(
					"from AccountGroup acctGroup where acctGroup.id.accGrpId = :accGrpId",
					AccountGroup.class
			);

			// Set the parameter
			query.setParameter("accGrpId", acctGroup.getId().getAccGrpId());

			// Get the result list
			records = query.getResultList();
			// Condition to check list size
			if (records.size() == 0) {
				tx = session.beginTransaction();
				if(requireAuthorisation) {
					acctGroup.setRequireAuthorisation("Y");
				}
				session.save(acctGroup);
//				if(requireAuthorisation) {
//					tx.rollback();
//					session.flush();
//				}
//				else

				tx.commit();

			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName() + " - [saveAcountGroup] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
							   + exception.getMessage());
			}
			log.error(this.getClass().getName()
					  + " - Exception Catched in [saveAccountGroup] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"saveAccountGroup",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}
	public void crudAccountsInGroup(ArrayList<AccountInGroup> accountsInsert, ArrayList<AccountInGroup> accountsDelete, Long maintEventId )
			throws SwtException {
		List records = null;
		List recordsDelete = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		AccountInGroup acctInGrp = new AccountInGroup();
		Iterator itr;

		try {
			log.debug(this.getClass().getName() + " - [crudAccountsInGroup] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(accountsInsert.size() > 0) {


				for (Iterator iterator = accountsInsert.iterator(); iterator.hasNext();) {
					AccountInGroup accountsI = (AccountInGroup) iterator.next();

					// Create a TypedQuery for AccountInGroup
					TypedQuery<AccountInGroup> query = session.createQuery(
							"from AccountInGroup acct where acct.id.accountId = :accountId and acct.id.hostId = :hostId and acct.id.entityId = :entityId",
							AccountInGroup.class
					);

					// Set the parameters
					query.setParameter("accountId", accountsI.getId().getAccountId());
					query.setParameter("hostId", accountsI.getId().getHostId());
					query.setParameter("entityId", accountsI.getId().getEntityId());

					// Get the result list
					records = query.getResultList();

					if (records.size() == 0) {
						if(maintEventId != null)
							accountsI.setMainEventId(""+maintEventId);
						session.save(accountsI);

					} else {
						throw new SwtException(
								"errors.DataIntegrityViolationExceptioninAdd");
					}

				}

			}
			if(accountsDelete.size() > 0 ) {
				for (Iterator iterator = accountsDelete.iterator(); iterator.hasNext();) {
					AccountInGroup accountsD = (AccountInGroup) iterator.next();

					// Create a TypedQuery for AccountInGroup
					TypedQuery<AccountInGroup> query = session.createQuery(
							"from AccountInGroup acct where acct.id.accountId = :accountId",
							AccountInGroup.class
					);

					// Set the parameter
					query.setParameter("accountId", accountsD.getId().getAccountId());

					// Get the result list
					recordsDelete = query.getResultList();

					if(recordsDelete != null){
						itr = recordsDelete.iterator();
						while(itr.hasNext()){
							acctInGrp = (AccountInGroup) itr.next();
							if(maintEventId != null)
								acctInGrp.setMainEventId(""+maintEventId);
							session.delete(acctInGrp);
						}
					}
				}
			}
			if(maintEventId != null) {
				session.flush();
				tx.rollback();
			}
			else
				tx.commit();

			// Condition to check list size

			log.debug(this.getClass().getName() + " - [crudReserve] - Exit");
		} catch (Exception e) {
			e.printStackTrace();

			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
							   + exception.getMessage());
			}
			log.error(this.getClass().getName()
					  + " - Exception Catched in [crudAccountsInGroup] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"crudAccountsInGroup",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
			recordsDelete = null;
		}
	}

	public void crudReserve(ArrayList<Reserve> reserveInsert, ArrayList<Reserve> reserveUpdate, ArrayList<Reserve> reserveDelete, Long maintEventId)
			throws SwtException {
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [crudReserve] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(reserveInsert.size() > 0) {
				for (Iterator iterator = reserveInsert.iterator(); iterator.hasNext();) {
					Reserve reserveI = (Reserve) iterator.next();
					if(maintEventId != null) {
						reserveI.setMainEventId(""+maintEventId);
					}
					// Create a TypedQuery for Reserve
					TypedQuery<Reserve> query = session.createQuery(
							"from Reserve res where res.accGrpId = :accGrpId and res.accGrpTime = :accGrpTime",
							Reserve.class
					);

					// Set the parameters
					query.setParameter("accGrpId", reserveI.getAccGrpId());
					query.setParameter("accGrpTime", reserveI.getAccGrpTime());

					// Get the result list
					records = query.getResultList();
					if (records.size() == 0) {
						session.save(reserveI);


					} else {
						throw new SwtException(
								"errors.DataIntegrityViolationExceptioninAdd");
					}

				}
			}
			if (reserveUpdate.size() > 0) {
				for (Iterator iterator = reserveUpdate.iterator(); iterator.hasNext();) {
					Reserve reserveU = (Reserve) iterator.next();
					if(maintEventId != null) {
						reserveU.setMainEventId(""+maintEventId);
					}
					session.update(reserveU);

				}
			}
			if (reserveDelete.size() > 0) {
				for (Iterator iterator = reserveDelete.iterator(); iterator.hasNext();) {
					Reserve reserveD = (Reserve) iterator.next();
					if(maintEventId != null) {
						reserveD.setMainEventId(""+maintEventId);
					}
					session.delete(reserveD);

				}
			}

			if(maintEventId != null) {
				session.flush();
				tx.rollback();
			}
			else
				tx.commit();

			// Condition to check list size

			log.debug(this.getClass().getName() + " - [crudReserve] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
							   + exception.getMessage());
			}
			log.error(this.getClass().getName()
					  + " - Exception Catched in [crudReserve] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"crudReserve",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}
	public void crudCutOff(ArrayList<AccountGroupCutoff> cutOffInsert, ArrayList<AccountGroupCutoff> cutOffUpdate, ArrayList<AccountGroupCutoff> cutOffDelete, Long maintEventId)
			throws SwtException {
		List records = null;
		List recordsCutOff = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		RulesDefinition ruleDefintion;
		RuleConditions.Id id = null;

		try {
			log.debug(this.getClass().getName() + " - [crudCutOff] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			if (!cutOffInsert.isEmpty()) {
				for (AccountGroupCutoff cutOffI : cutOffInsert) {
					ruleDefintion = cutOffI.getRulesDefinition();
					if (maintEventId != null) {
						ruleDefintion.setMainEventId("" + maintEventId);
					}
					session.save(ruleDefintion);
					cutOffI.setRuleId(ruleDefintion.getRuleId());
					if (maintEventId != null) {
						cutOffI.setMainEventId("" + maintEventId);
					}

					for (RuleConditions condition : ruleDefintion.getRuleConditions()) {
						condition.getId().setRuleId(ruleDefintion.getRuleId());
						condition.setIsActive(maintEventId != null ? "N" : "Y");
						session.save(condition);
					}
					session.save(cutOffI);
				}
			}

			if (!cutOffUpdate.isEmpty()) {
				for (AccountGroupCutoff cutOffU : cutOffUpdate) {
					TypedQuery<Integer> query = session.createQuery(
							"select acct.ruleId from AccountGroupCutoff acct where acct.cutOffRuleId = :cutOffRuleId",
							Integer.class);
					query.setParameter("cutOffRuleId", cutOffU.getCutOffRuleId());

					List<Integer> ruleIds = query.getResultList();
					if (!ruleIds.isEmpty() && ruleIds.size() == 1) {
						Integer ruleId = ruleIds.get(0);
						cutOffU.setRuleId(ruleId);
						cutOffU.getRulesDefinition().setRuleId(ruleId);
						if (maintEventId != null) {
							cutOffU.setMainEventId("" + maintEventId);
						}

						TypedQuery<RuleConditions> queryRule = session.createQuery(
								"from RuleConditions r where r.id.ruleId = :ruleId",
								RuleConditions.class);
						queryRule.setParameter("ruleId", ruleId);

						List<RuleConditions> recordsRuleConditions = queryRule.getResultList();
						if (recordsRuleConditions != null) {
							for (RuleConditions ruleCondition : recordsRuleConditions) {
								session.delete(ruleCondition);
							}
						}

						ruleDefintion = cutOffU.getRulesDefinition();
						if (maintEventId != null) {
							ruleDefintion.setMainEventId("" + maintEventId);
						}

						session.update(ruleDefintion);
						for (RuleConditions condition : ruleDefintion.getRuleConditions()) {
							id = new RuleConditions.Id();
							id.setRuleId(ruleId);
							id.setConditionId(condition.getId().getConditionId());
							condition.setId(id);
							condition.setIsActive(maintEventId != null ? "N" : "Y");
							session.save(condition);
						}

						session.update(cutOffU);
					}
				}
			}

			if (!cutOffDelete.isEmpty()) {
				for (AccountGroupCutoff cutOffD : cutOffDelete) {
					TypedQuery<Integer> query = session.createQuery(
							"select acct.ruleId from AccountGroupCutoff acct where acct.cutOffRuleId = :cutOffRuleId",
							Integer.class);
					query.setParameter("cutOffRuleId", cutOffD.getCutOffRuleId());

					List<Integer> ruleIds = query.getResultList();
					if (!ruleIds.isEmpty() && ruleIds.size() == 1) {
						Integer ruleId = ruleIds.get(0);

						TypedQuery<RuleConditions> queryRule = session.createQuery(
								"select rc from RuleConditions rc where rc.id.ruleId = :ruleId",
								RuleConditions.class);
						queryRule.setParameter("ruleId", ruleId);

						List<RuleConditions> recordsRuleConditions = queryRule.getResultList();
						if (recordsRuleConditions != null) {
							for (RuleConditions result : recordsRuleConditions) {
								session.delete(result);
							}
						}

						TypedQuery<RulesDefinition> queryRulesDef = session.createQuery(
								"select r from RulesDefinition r where r.ruleId = :ruleId",
								RulesDefinition.class);
						queryRulesDef.setParameter("ruleId", ruleId);

						List<RulesDefinition> recordsRulesDef = queryRulesDef.getResultList();
						if (recordsRulesDef != null) {
							for (RulesDefinition result : recordsRulesDef) {
								if (maintEventId != null) {
									result.setMainEventId("" + maintEventId);
								}
								session.delete(result);
							}
						}

						TypedQuery<AccountGroupCutoff> queryAcctGrpCutOff = session.createQuery(
								"SELECT r FROM AccountGroupCutoff r WHERE r.ruleId = :ruleId",
								AccountGroupCutoff.class);
						queryAcctGrpCutOff.setParameter("ruleId", ruleId);

						List<AccountGroupCutoff> recordsCut = queryAcctGrpCutOff.getResultList();
						if (recordsCut != null) {
							for (AccountGroupCutoff result : recordsCut) {
								if (maintEventId != null) {
									result.setMainEventId("" + maintEventId);
								}
								session.delete(result);
							}
						}
					}
				}
			}

			if (maintEventId != null) {
				session.flush();
				tx.rollback();
			} else {
				tx.commit();
			}

			log.debug(this.getClass().getName() + " - [crudCutOff] - Exit");
		} catch (Exception e) {
			e.printStackTrace();
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occurred in rolling back transaction. Cause: " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Caught in [crudCutOff] method: " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "crudCutOff", AccountGroupsMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}


	public List<AccountGroup> getAccountGroupDetailList(String selectedSpreadProfileId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountGroupDetailList] - Enter");
		List<AccountGroup> listAccts;
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<AccountGroup> query;

			if (SwtUtil.isEmptyOrNull(selectedSpreadProfileId)) {
				query = session.createQuery(
						"select acctGroup from AccountGroup acctGroup join PCMCurrency ccy " +
						"on acctGroup.currencyCode = ccy.id.currencyCode " +
						"where (acctGroup.requireAuthorisation = 'N' OR acctGroup.requireAuthorisation is null) " +
						"order by acctGroup.id.accGrpId, ccy.ordinal, acctGroup.ordinal",
						AccountGroup.class
				);
			} else {
				query = session.createQuery(
						"from AccountGroup acctGroup where acctGroup.spreadProfileId = :spreadProfileId " +
						"and (acctGroup.requireAuthorisation = 'N' OR acctGroup.requireAuthorisation is null) " +
						"order by acctGroup.id.accGrpId",
						AccountGroup.class
				);
				query.setParameter("spreadProfileId", selectedSpreadProfileId);
			}

			listAccts = query.getResultList();
			log.debug(this.getClass().getName() + " - [getAccountGroupDetailList] - Exit");
			return listAccts;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountGroupDetailList]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAccountGroupDetailList", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public AccountGroup getAcctGroupDetail(String acctGroupId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAcctGroupDetail] - Enter");
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<AccountGroup> query = session.createQuery(
					"from AccountGroup acctGrp where acctGrp.id.accGrpId = :accGrpId", AccountGroup.class);
			query.setParameter("accGrpId", acctGroupId);
			List<AccountGroup> records = query.getResultList();

			AccountGroup acctGroup = records.isEmpty() ? null : records.get(0);
			log.debug(this.getClass().getName() + " - [getAcctGroupDetail] - Exit");
			return acctGroup;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAcctGroupDetail]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAcctGroupDetail", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public Integer accountListCount(String groupId) throws SwtException {
		log.debug(this.getClass().getName() + "- [accountListCount] - Entering");
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<Long> typedQuery = session.createQuery(
					"select count(ac) from AccountInGroup ac where ac.accGrpId = :groupId", Long.class);
			typedQuery.setParameter("groupId", groupId);
			Integer accs = typedQuery.getSingleResult().intValue();
			log.debug(this.getClass().getName() + "- [accountListCount] - Exit");
			return accs;

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [AccountListCount]: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "accountListCount", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public void updateAcctGroup(AccountGroup acctGroup) throws SwtException {
		log.debug(this.getClass().getName() + " - [updateAcctGroup] - Enter");

		boolean requireAuthorisation = (acctGroup != null && !SwtUtil.isEmptyOrNull(acctGroup.getMainEventId()));
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			Transaction tx = session.beginTransaction();

			try {
				session.update(acctGroup);
				if (requireAuthorisation) {
					session.flush();
					tx.rollback();
				} else {
					tx.commit();
				}
			} catch (Exception e) {
				tx.rollback();
				log.error("Exception in [updateAcctGroup]: " + e.getMessage());
				throw e;
			}
		} catch (Exception e) {
			throw SwtErrorHandler.getInstance().handleException(e, "updateAcctGroup", AccountGroupsMaintenanceDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + " - [updateAcctGroup] - Exit");
	}




	public void deleteAcctGroup(AccountGroup acctGroup) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		boolean requireAuthorisation = false;

		try {
			log.debug(this.getClass().getName() + " - [deleteAcctGroup] - Enter");

			// Open a session with or without an interceptor
			if (!acctGroup.isForceNoLogs()) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			} else {
				session = getHibernateTemplate().getSessionFactory().openSession();
			}

			tx = session.beginTransaction();

			// Load the AccountGroup in the current session
			acctGroup = session.get(AccountGroup.class, acctGroup.getId());

			// Check if authorization is required
			if (!SwtUtil.isEmptyOrNull(acctGroup.getMainEventId())) {
				requireAuthorisation = true;
			}

			// Delete associated AccountInGroup entities
			if (acctGroup.getAccountInGroup() != null) {
				// Create a copy of the collection to avoid ConcurrentModificationException
				Set<AccountInGroup> accountInGroupCopy = new HashSet<>(acctGroup.getAccountInGroup());
				// Clear the collection first to prevent cascade issues
				acctGroup.getAccountInGroup().clear();
				// Now delete each entity
				for (AccountInGroup acctInGrp : accountInGroupCopy) {
					session.delete(acctInGrp);
				}
			}

			// Delete associated AccountGroupCutoff entities
			if (acctGroup.getAccountGroupCutoff() != null) {
				for (AccountGroupCutoff cutOffD : acctGroup.getAccountGroupCutoff()) {
					// Create a TypedQuery for ruleId
					TypedQuery<Integer> query = session.createQuery(
							"select acct.ruleId from AccountGroupCutoff acct where acct.cutOffRuleId = :cutOffRuleId",
							Integer.class
					);

					// Set the parameter
					query.setParameter("cutOffRuleId", cutOffD.getCutOffRuleId());

					// Get the result list
					List<Integer> records = query.getResultList();

					if (records.size() == 1) {
						Integer ruleId = records.get(0);

						// Delete associated RuleConditions
						TypedQuery<RuleConditions> queryRule = session.createQuery(
								"select rc from RuleConditions rc where rc.id.ruleId = :ruleId",
								RuleConditions.class
						);
						queryRule.setParameter("ruleId", ruleId);
						List<RuleConditions> recordsRulesConditions = queryRule.getResultList();
						for (RuleConditions result : recordsRulesConditions) {
							session.delete(result);
						}

						// Delete associated RulesDefinition
						TypedQuery<RulesDefinition> queryRulesDef = session.createQuery(
								"select r from RulesDefinition r where r.ruleId = :ruleId",
								RulesDefinition.class
						);
						queryRulesDef.setParameter("ruleId", ruleId);
						List<RulesDefinition> recordsRulesDef = queryRulesDef.getResultList();
						for (RulesDefinition resultRule : recordsRulesDef) {
							session.delete(resultRule);
						}
					}
				}
			}

			// Finally, delete the main AccountGroup entity
			session.delete(acctGroup);

			// Commit or rollback based on authorization requirement
			if (requireAuthorisation) {
				session.flush();
				tx.rollback();
			} else {
				tx.commit();
			}
			log.debug(this.getClass().getName() + " - [deleteAcctGroup] - Exit");
		} catch (Exception e) {
			e.printStackTrace();
			if (tx != null) {
				try {
					tx.rollback();
				} catch (HibernateException exception) {
					log.error("HibernateException occurred in rolling back transaction. Cause: " + exception.getMessage());
				}
			}
			log.error(this.getClass().getName() + " - Exception caught in [deleteAcctGroup] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "deleteAcctGroup", AccountGroupsMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				HibernateException hThrownException = JDBCCloser.close(session);

				if (hThrownException != null)
					throw new SwtException(hThrownException.getMessage());
			}
		}
	}

	public Collection<AcctMaintenance> getAccountListDetails(String currencyCode) throws SwtException {
		List<AcctMaintenance> accountList = new ArrayList<>();

		try {
			log.debug(this.getClass().getName() + " - [getAccountListDetails] - " + "Entry");

			TypedQuery<AcctMaintenance> query = getHibernateTemplate().getSessionFactory()
					.getCurrentSession()
					.createQuery(
							"SELECT a FROM AcctMaintenance a " +
							"JOIN Currency c ON c.id.hostId = a.id.hostId " +
							"AND c.id.entityId = a.id.entityId " +
							"AND c.id.currencyCode = a.currcode " +
							"WHERE c.preFlag = 'Y' " +
							"AND a.acctClass = 'N' " +
							"AND a.acctstatusflg = 'O' " +
							"AND a.currcode = :currencyCode " +
							"AND NOT EXISTS (SELECT 1 FROM AccountInGroup acctGrp " +
							"WHERE a.id.hostId = acctGrp.id.hostId " +
							"AND a.id.entityId = acctGrp.id.entityId " +
							"AND a.id.accountId = acctGrp.id.accountId) " +
							"ORDER BY a.id.accountId",
							AcctMaintenance.class
					);

			query.setParameter("currencyCode", currencyCode);
			accountList = query.getResultList();

			log.debug(this.getClass().getName() + " - [getAccountListDetails] - " + "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAccountListDetails] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAccountListDetails", ILMGeneralMaintenanceDAOHibernate.class);
		}

		return accountList;
	}


	public Collection getOrdinalByCurrency(String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [getOrdinalByCurrency] - Entry");
		List<AccountGroup> records;
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<AccountGroup> query = session.createQuery(
					"from AccountGroup acct where acct.currencyCode = :currencyCode", AccountGroup.class);
			query.setParameter("currencyCode", currencyCode);
			records = query.getResultList();

			log.debug(this.getClass().getName() + " - [getOrdinalByCurrency] - Exit");
			return records;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getOrdinalByCurrency]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getOrdinalByCurrency", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}


	public Collection getReserveDetails(String acctGroupId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getReserveDetails] - Enter");
		List<Reserve> records;
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<Reserve> query = session.createQuery(
					"from Reserve res where res.accGrpId = :acctGroupId", Reserve.class);
			query.setParameter("acctGroupId", acctGroupId);
			records = query.getResultList();

			log.debug(this.getClass().getName() + " - [getReserveDetails] - Exit");
			return records;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getReserveDetails]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getReserveDetails", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}
	public Collection getCutOffRulesDetails(String acctGroupId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getCutOffRulesDetails] - Enter");

		List<Object[]> records;
		List<RuleConditions> ruleConditionsList;
		List<AccountGroupCutoff> cutOffResult = new ArrayList<>();
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		// Using try-with-resources to ensure session is properly closed
		try (Session session = getHibernateTemplate().getSessionFactory()
				.withOptions().interceptor(interceptor).openSession()) {

			// Create a TypedQuery for the main query
			TypedQuery<Object[]> query = session.createQuery(
					"SELECT acctCut, rulesDef FROM AccountGroupCutoff acctCut " +
					"JOIN RulesDefinition rulesDef " +
					"ON rulesDef.ruleId = acctCut.ruleId " +
					"WHERE acctCut.accGrpId = :acctGroupId AND rulesDef.ruleType = 'C' " +
					"ORDER BY acctCut.ordinal ASC",
					Object[].class
			);

			// Set the parameter
			query.setParameter("acctGroupId", acctGroupId);

			// Get the result list
			records = query.getResultList();

			for (Object[] result : records) {
				AccountGroupCutoff cutOff = (AccountGroupCutoff) result[0];
				RulesDefinition rulesDef = (RulesDefinition) result[1];

				// Create a TypedQuery for RuleConditions
				TypedQuery<RuleConditions> queryRule = session.createQuery(
						"FROM RuleConditions rulesC WHERE rulesC.id.ruleId = :ruleId",
						RuleConditions.class
				);

				// Set the parameter correctly
				queryRule.setParameter("ruleId", rulesDef.getRuleId());

				// Get the result list safely
				ruleConditionsList = queryRule.getResultList();
				if (ruleConditionsList != null) {
					rulesDef.setRuleConditions((ArrayList<RuleConditions>) ruleConditionsList);
				}

				cutOff.setRulesDefinition(rulesDef);
				cutOffResult.add(cutOff);
			}

			log.debug(this.getClass().getName() + " - [getCutOffRulesDetails] - Exit");
			return cutOffResult;

		} catch (Exception e) {
			log.error(this.getClass().getName() +
					  " - Exception Caught in [getCutOffRulesDetails] method: " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getCutOffRulesDetails",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public Collection<AcctMaintenance> getAccountsInGroupList(String accountGrpId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountsInGroupList] - Entry");
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<AcctMaintenance> query = session.createQuery(
					"SELECT acct FROM AccountInGroup acctGrp JOIN AcctMaintenance acct " +
					"ON acctGrp.id.accountId = acct.id.accountId " +
					"AND acctGrp.id.hostId = acct.id.hostId " +
					"AND acctGrp.id.entityId = acct.id.entityId " +
					"WHERE acctGrp.accGrpId = :accountGrpId",
					AcctMaintenance.class);

			query.setParameter("accountGrpId", accountGrpId);
			List<AcctMaintenance> list = query.getResultList();
			log.debug(this.getClass().getName() + " - [getAccountsInGroupList] - Exit");
			return list;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountsInGroupList]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAccountsInGroupList", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}


	public AccountGroup getAccountGroupFromAccountId(String hostId, String entityId, String accountId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - Entry");
		List<AccountGroup> records;
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<AccountGroup> query = session.createQuery(
					"SELECT grp FROM AccountInGroup acctGrp JOIN AccountGroup grp " +
					"ON acctGrp.accGrpId = grp.id.accGrpId " +
					"WHERE acctGrp.id.accountId = :accountId " +
					"AND acctGrp.id.hostId = :hostId " +
					"AND acctGrp.id.entityId = :entityId",
					AccountGroup.class);

			query.setParameter("accountId", accountId);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			records = query.getResultList();
			AccountGroup group = records.isEmpty() ? null : records.get(0);

			log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - Exit");
			return group;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountGroupFromAccountId]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAccountGroupFromAccountId", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public HashMap checkAccountInPayment(ArrayList accountsId, ArrayList entity) throws SwtException {
		log.debug(this.getClass().getName() + " - [checkAccountInPayment] - Entry");
		HashMap<String, String> accountIdExist = new LinkedHashMap<>();
		String result;
		HashMap<String, String> exist;

		try {
			for (int i = 0; i < accountsId.size(); i++) {
				List recordsIntegrity = PCMUtil.executeNamedSelectQuery(
						"accountId_PayReq_query", accountsId.get(i).toString(), entity.get(i).toString());

				if (!recordsIntegrity.isEmpty()) {
					exist = (HashMap<String, String>) recordsIntegrity.get(0);
					result = exist.get("result");
					accountIdExist.put(accountsId.get(i).toString() + ";" + entity.get(i).toString(), result);
				}
			}

			log.debug(this.getClass().getName() + " - [checkAccountInPayment] - Exit");
			return accountIdExist;

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [checkAccountInPayment]: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "checkAccountInPayment", AccountGroupsMaintenanceDAOHibernate.class);
		} finally {
			accountIdExist = null;
			result = null;
			exist = null;
		}
	}

	@Override
	public Collection getAccountListDetailsMaintEvent(String currencyCode, String accountGroup) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountListDetailsMaintEvent] - Entry");
		List<Object[]> records;
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<Object[]> query = session.createQuery(
					"SELECT a, c FROM AcctMaintenance a JOIN Currency c " +
					"ON c.id.hostId = a.id.hostId AND c.id.entityId = a.id.entityId " +
					"WHERE c.id.currencyCode = a.currcode AND c.preFlag = 'Y' " +
					"AND a.acctClass = 'N' AND a.acctstatusflg = 'O' AND a.currcode = :currencyCode " +
					"AND NOT EXISTS (SELECT 1 FROM AccountInGroup acctGrp WHERE a.id.hostId = acctGrp.id.hostId " +
					"AND a.id.entityId = acctGrp.id.entityId AND a.id.accountId = acctGrp.id.accountId " +
					"AND acctGrp.accGrpId != :accountGroup) " +
					"ORDER BY a.id.accountId",
					Object[].class);

			query.setParameter("currencyCode", currencyCode);
			query.setParameter("accountGroup", accountGroup);

			records = query.getResultList();
			List<AcctMaintenance> accountList = new ArrayList<>();

			for (Object[] result : records) {
				AcctMaintenance account = (AcctMaintenance) result[0];
				accountList.add(account);
			}

			log.debug(this.getClass().getName() + " - [getAccountListDetailsMaintEvent] - Exit");
			return accountList;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountListDetailsMaintEvent]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAccountListDetailsMaintEvent", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}

	/**
	 * For Maintenance Event
	 * @param accountGroup
	 * @return
	 * @throws SwtException
	 */
	public void deleteRulesDefinitionForMaintEvent(String accountGroup) throws SwtException{

	}



}