/*
 * @(#)CategoryMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.*;
import jakarta.persistence.EntityManager;
import org.hibernate.type.StandardBasicTypes;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CategoryMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;






/**
 * <AUTHOR>
 * This is DAO class for Category screen
 */

@Repository ("categoryMaintenanceDAO")
public class CategoryMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements CategoryMaintenanceDAO {
	public CategoryMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(CategoryMaintenanceDAOHibernate.class);




	public Integer getMaxOrder() throws SwtException {
		log.debug("Entering getMaxOrder method");

		Interceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		Integer max = 0;

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<Integer> query = session.createQuery(
					"SELECT COALESCE(MAX(c.ordinal), 0) FROM Category c",
					Integer.class
			);

			max = query.getSingleResult(); // Directly retrieve the result
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in [getMaxOrder] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getMaxOrder", CurrencyMaintenanceDAOHibernate.class);
		}

		log.debug("Exiting getMaxOrder method with max order: " + max);
		return max;
	}



	public Boolean existPayRequest(String categoryId) throws SwtException {
		List  records =null;
		Boolean exist=false;
		HashMap<String, String>  result =null;
		try {
			records = PCMUtil.executeNamedSelectQuery("category_PayReq_query", categoryId);
			if (!records.isEmpty()) {
				result = (HashMap<String, String>) records.get(0);
				if(result.get("result").equals("Y")) {
					exist= true;
				}
			}
			
			return exist;
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMaxOrder] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMaxOrder", CurrencyMaintenanceDAOHibernate.class);
		}finally {
			records =null;

		}
		
	}
	
	
	

	/**
	 * Returns the collection of category detail list from category table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryDetailList()
			throws SwtException {
		// Variable List to hold list Category Rules
		Collection categoryList = new ArrayList();
		String query= null;
		Session session = null;
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getCategoryDetailList] - " + "Entry");

			session= SwtUtil.pcSessionFactory.openSession();
			
			/* Assigning the query statement to the string object */
			query = "from Category c order by LOWER(c.id.categoryId), c.ordinal  asc ";

			/* Query to get the category detail list in order to category code */			
			categoryList = session.createQuery(query).getResultList();
			log.debug(this.getClass().getName()
					+ " - [getCategoryDetailList] - " + "Exit");
			return categoryList;
			
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryDetailList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCategoryDetailList", this.getClass());
		} finally {
			// Nullify object
			categoryList = null;
			HibernateException hThrownException = JDBCCloser.close(session);
				if (hThrownException != null)
					throw new SwtException(hThrownException.getMessage());
			
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getCategoryDetailList ] - Exit");
		}
	}

	/**
	 * Returns the collection of category id and name from category table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryCombo()
			throws SwtException {
		// Variable List to hold list Category Rules
		Collection categoryList = new ArrayList();
		String query= null;
		Session session = null;
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getCategoryCombo] - " + "Entry");
			
			session= SwtUtil.pcSessionFactory.openSession();
			
			/* Assigning the query statement to the string object */
			query = "from Category c order by lower(c.id.categoryId) asc";
			
			/* Query to get the category detail list in order to category code */
			categoryList = session.createQuery(query).getResultList();

			log.debug(this.getClass().getName()
					+ " - [getCategoryCombo] - " + "Exit");
			return categoryList;
			
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryCombo] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCategoryCombo", this.getClass());
		} finally {
			// Nullify object
			categoryList = null;
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getCategoryCombo ] - Exit");
		}
	}
	


	/**
	 * Delete Category detail in the DataBase
	 * 
	 * @return none
	 * @throws SwtException
	 */
	public void deleteCategory(String categoryId) throws SwtException {
		List records=null;
		ArrayList<Object[]>  recordsCategoryRule = null;
		List  recordsIntegrity= null;
		Session session = null;
		Transaction tx = null;
		Category category= null;
		HashMap<String,String> exist= null;
		SwtInterceptor interceptor = null;
		List<RulesDefinition>  rulesDefinition = new ArrayList<RulesDefinition>();
		List<RuleConditions>  rulesCondition = new ArrayList<RuleConditions>();
		RulesDefinition rule = null;
		CategoryRule categoryRule= null;
		try {
			log.debug(this.getClass().getName() + " - [deleteCategory] - "
					+ "Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session= SwtUtil.pcSessionFactory.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Create a TypedQuery for Category
			TypedQuery<Category> query = session.createQuery("FROM Category c WHERE c.id.categoryId = :categoryId", Category.class);

			// Set the parameter
			query.setParameter("categoryId", categoryId);

			// Get the result list
			records = query.getResultList();
			if (records.size()> 0) {
				category = (Category) records.get(0);
				recordsIntegrity = PCMUtil.executeNamedSelectQuery("category_integrity_query", categoryId,categoryId,categoryId);
				if (recordsIntegrity.size()> 0) {
					exist= (HashMap<String, String>) recordsIntegrity.get(0);
					if(exist.get("exist").equals("Y")) {
						throw new SwtException("errors.DataIntegrityViolationExceptioninDelete");
					}else {
						TypedQuery<Object[]> ruleQuery = session.createQuery(
								"SELECT r, c FROM RulesDefinition r JOIN CategoryRule c ON r.ruleId = c.ruleId WHERE c.categoryId = :categoryId",
								Object[].class
						);

						// Set the parameter
						ruleQuery.setParameter("categoryId", categoryId);

						// Get the result list
						recordsCategoryRule = (ArrayList<Object[]>) ruleQuery.getResultList();

						if(recordsCategoryRule!= null && !recordsCategoryRule.isEmpty()) {
							for (Object[] result : recordsCategoryRule) {
								categoryRule = (CategoryRule) result[1];
								rule = (RulesDefinition) result[0];
								TypedQuery<RuleConditions> ruleCondQuery = session.createQuery(
										"FROM RuleConditions rc WHERE rc.id.ruleId = :ruleId",
										RuleConditions.class
								);

								// Set the parameter
								ruleCondQuery.setParameter("ruleId", rule.getRuleId());

								// Get the result list
								rulesCondition = ruleCondQuery.getResultList();
								if (rulesCondition!= null && !rulesCondition.isEmpty()) {
									for (Iterator it= rulesCondition.iterator(); it.hasNext();) {
										RuleConditions ruleCondition = (RuleConditions) it.next();
										session.delete(ruleCondition);
									}
								}
								session.delete(rule);
								//session.delete(categoryRule);			
							}	
						}
						session.delete(category);
						tx.commit(); 
					}
				}
			}else {
				throw new SwtException("errors.SwtRecordNotExist");
			}
			log.debug(this.getClass().getName() + " - [deleteCategory] - "+ "Exit");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteCategory] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCategory", CategoryMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
			category= null;
			exist= null;
		}

	}

	public Category getCategoryDetailById(String categoryId) throws SwtException {
		List records=null;
		Category category=null;
		ArrayList recordsCondition = new ArrayList();
		RulesDefinition rulesDefinition= null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getCategoryDetailById] - "
					+ "Entry");
			 session= SwtUtil.pcSessionFactory.openSession();
			
			/* Query to execute the records */
			TypedQuery<Category> query = session.createQuery(
					"FROM Category c WHERE c.id.categoryId = :categoryId",
					Category.class
			);

			// Set the parameter
			query.setParameter("categoryId", categoryId);

			// Get the result list
			records = query.getResultList();

			if(records != null && records.size()>0) {
				category=(Category) records.get(0);
		
				for (Iterator<CategoryRule> it = category.getCategoryRules().iterator(); it.hasNext();) {
					CategoryRule categoryRule = (CategoryRule) it.next();
					TypedQuery<RulesDefinition> ruleQuery = session.createQuery(
							"FROM RulesDefinition r WHERE r.ruleId = :ruleId",
							RulesDefinition.class
					);

					// Set the parameter
					ruleQuery.setParameter("ruleId", categoryRule.getRuleId());

					// Get the result list
					records = ruleQuery.getResultList();

					if(!records.isEmpty()) {
						rulesDefinition = (RulesDefinition) records.get(0);
					}


					TypedQuery<RuleConditions> ruleCondQuery = session.createQuery(
							"FROM RuleConditions rulesC WHERE rulesC.id.ruleId = :ruleId",
							RuleConditions.class
					);

					// Set the parameter
					ruleCondQuery.setParameter("ruleId", categoryRule.getRuleId());

					// Get the result list
					recordsCondition = (ArrayList) ruleCondQuery.getResultList();

					if(!recordsCondition.isEmpty()) {
						rulesDefinition.setRuleConditions(recordsCondition);
					}
					
					categoryRule.setRulesDefinition(rulesDefinition);		
				
				}
			}
			return category;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCategoryDetailById] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCategoryDetailById", CategoryMaintenanceDAOHibernate.class);
		}finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
		
	}


	public void crudCategoryRule(Category category, ArrayList<CategoryRule> categoryRuleInsert, ArrayList<CategoryRule> categoryRuleUpdate, ArrayList<CategoryRule> categoryRuleDelete, String screenName) throws SwtException {
		List records = null;
		List recordsIntegrity = null;
		HashMap<String, String> result = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		ArrayList<RuleConditions> ruleConditions = null;
		RuleConditions ruleCondition = null;

		try {
			log.debug(this.getClass().getName() + " - [crudCategoryRule] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			TypedQuery<Category> query = session.createQuery(
					"FROM Category c WHERE c.id.categoryId = :categoryId",
					Category.class
			);
			query.setParameter("categoryId", category.getId().getCategoryId());
			records = query.getResultList();

			if (records.size() == 0 && screenName.equals("add")) {
				session.save(category);
			} else if (screenName.equals("change")) {
				session.merge(category);
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

			if (categoryRuleInsert != null && !categoryRuleInsert.isEmpty()) {
				for (CategoryRule categoryI : categoryRuleInsert) {
					Integer ruleDefintionId = null;
					RulesDefinition ruleI = categoryI.getRulesDefinition();
					session.save(ruleI);
					ruleDefintionId = ruleI.getRuleId();
					categoryI.setRuleId(ruleDefintionId);

					ruleConditions = ruleI.getRuleConditions();
					if (ruleConditions != null && !ruleConditions.isEmpty()) {
						for (RuleConditions rc : ruleConditions) {
							rc.getId().setRuleId(ruleDefintionId);
							session.save(rc);
						}
					}

					recordsIntegrity = PCMUtil.executeNamedSelectQuery("category_rule_constraints_query_add",
							categoryI.getCategoryRuleName(), categoryI.getCategoryRuleName(), categoryI.getCategoryId());

					if (recordsIntegrity.size() > 0) {
						result = (HashMap<String, String>) recordsIntegrity.get(0);
						if ("NAME-EXIST".equals(result.get("result"))) {
							throw new SwtException("errors.DataIntegrityExceptioninRuleName");
						} else {
							session.save(categoryI);
						}
					}
				}
			}

			if (categoryRuleUpdate != null && !categoryRuleUpdate.isEmpty()) {
				for (CategoryRule categoryU : categoryRuleUpdate) {
					TypedQuery<RuleConditions> ruleQuery = session.createQuery(
							"FROM RuleConditions r WHERE r.id.ruleId = :ruleId",
							RuleConditions.class
					);
					ruleQuery.setParameter("ruleId", categoryU.getRuleId());
					records = ruleQuery.getResultList();

					for (Object obj : records) {
						session.delete(obj);
					}

					RulesDefinition ruleU = categoryU.getRulesDefinition();
					if (ruleU != null) {
						ruleConditions = ruleU.getRuleConditions();
						if (ruleConditions != null && !ruleConditions.isEmpty()) {
							for (RuleConditions rc : ruleConditions) {
								rc.getId().setRuleId(ruleU.getRuleId());
								session.save(rc);
							}
						}
						session.merge(ruleU);
					}

					recordsIntegrity = PCMUtil.executeNamedSelectQuery("category_rule_constraints_query_update",
							categoryU.getCategoryRuleName(), categoryU.getCategoryRuleName(),
							categoryU.getCategoryRuleId(), categoryU.getCategoryId());

					if (recordsIntegrity.size() > 0) {
						result = (HashMap<String, String>) recordsIntegrity.get(0);
						if ("NAME-EXIST".equals(result.get("result"))) {
							throw new SwtException("errors.DataIntegrityExceptioninRuleName");
						} else {
							session.merge(categoryU);
						}
					}
				}
			}

			if (categoryRuleDelete != null && !categoryRuleDelete.isEmpty()) {
				for (CategoryRule categoryD : categoryRuleDelete) {
					RulesDefinition ruleD = categoryD.getRulesDefinition();
					if (ruleD != null) {
						ruleConditions = ruleD.getRuleConditions();
						if (ruleConditions != null && !ruleConditions.isEmpty()) {
							for (RuleConditions rc : ruleConditions) {
								rc.getId().setRuleId(ruleD.getRuleId());
								session.delete(rc);
							}
						}
						session.delete(ruleD);
					}
					session.delete(categoryD);
				}
			}

			tx.commit();
			log.debug(this.getClass().getName() + " - [crudCategoryRule] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) tx.rollback();
			} catch (HibernateException he) {
				log.error("HibernateException occurred in rollback: " + he.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception in [crudCategoryRule]: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "crudCategoryRule", CategoryMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null) throw new SwtException(hThrownException.getMessage());
			records = null;
			recordsIntegrity = null;
		}
	}


	@Override
	public void reOrder(Integer value_from,Integer orderTo) throws SwtException {
		Session session = null;
		Connection conn = null;
		PreparedStatement pstmt=null;
		 String hqlUpdate = null;

		try {
			// Debug Message 
			log.debug(this.getClass().getName() + "- [ ReOrder ] - Enter");
			session= SwtUtil.pcSessionFactory.openSession();
			 conn = SwtUtil.connection(session);
			 //if(value_from > orderTo) {
				 hqlUpdate = "update PC_CATEGORY c  set c.ordinal = c.ordinal+1 Where c.ordinal >=? ";//and c.ordinal =< ?"; 
			// }else {
				 //orderTo = getMaxOrder() + 1;
				// hqlUpdate = "update PC_CATEGORY c  set c.ordinal = c.ordinal+1 Where c.ordinal < ?"; //and c.ordinal >=?";
			// }
			
			 pstmt = conn.prepareStatement(hqlUpdate);
			 pstmt.setInt(1, orderTo);
			 //pstmt.setInt(2, value_from);
			 int result= pstmt.executeUpdate();
			 conn.commit();
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"ReOrder", CurrencyMaintenanceDAOHibernate.class);
		}finally {
			JDBCCloser.close(null, null, conn, session);
			// Debug Message
			log.debug(this.getClass().getName() + "- [ ReOrder ] - Exit");
		}

	}

}