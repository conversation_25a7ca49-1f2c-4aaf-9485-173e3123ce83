/*
 * @(#)CategoryRulesMaintenanceDAO.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao;

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;

/**
 * <AUTHOR>
 * This is DAO interface for Category screen
 * 
 */
public interface CategoryMaintenanceDAO extends DAO {


	/**
	 * Returns the max ordinal from category table
	 * 
	 * @return Integer
	 * @throws SwtException
	 */
	
	public Integer getMaxOrder() throws SwtException;
	
	/**
	 * Returns the collection of category detail list from Category table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryDetailList() throws SwtException;
	
	/**
	 * Returns the collection of category combo list from Category table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryCombo() throws SwtException;
	
	/**
	 * Returns the collection of Category detail list from Category table
	 * 
	 * @param categoryId
	 * @return Category
	 * @throws SwtException
	 */
	public Category getCategoryDetailById(String categoryId) throws SwtException;


	/**
	 * Delete a Category from DB
	 * 
	 * @param category
	 * @return
	 * @throws SwtException
	 */
	public void deleteCategory(String categoryId)throws SwtException;
	
	
	/**
	 * This method is used to do crud operations in Category Rule table
	 * @param category, categoryRuleInsert categoryRuleUpdate,categoryRuleDelete
	 * return
	 * @throws SwtException
	 */
	public void crudCategoryRule(Category category, ArrayList<CategoryRule> categoryRuleInsert, ArrayList<CategoryRule> categoryRuleUpdate, ArrayList<CategoryRule> categoryRuleDelete,String screenName) throws SwtException;

	void reOrder(Integer value_from, Integer orderIni) throws SwtException;
	
	/**
	 * This method is used to do get if exist payment request for this categoryId 
	 * @param categoryId
	 * return Boolean
	 * @throws SwtException
	 */
	public Boolean existPayRequest(String categoryId) throws SwtException;
	
}
