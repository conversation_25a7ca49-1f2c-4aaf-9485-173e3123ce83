/*
 * @(#)AccountAccessDAO.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao;

import java.util.ArrayList;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.core.kv.KVType;

/**
 * <AUTHOR>
 * 
 */
public interface CommonDAO extends DAO {

	/**
	 * 
	 * @param userId
	 * @param operation
	 * @param tableName
	 * @param list
	 * @throws SwtException 
	 */
	public String doCrudOperation(String userId, String operation,String tableName, ArrayList<KVType> list, String tableLevel, String activityId) throws SwtException ;	
	
	
	
}
