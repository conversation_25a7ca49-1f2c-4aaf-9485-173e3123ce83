package org.swallow.pcm.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.Date;
import java.sql.CallableStatement;
import java.sql.SQLException;
import java.util.concurrent.Semaphore;

import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;


public class RunProcedureStopRule implements Runnable {

    
    private static Semaphore semaphore = new Semaphore(1);
    
    
    private SessionFactory sessionFactory;
    private String actionAsString = null;
    private String newStopRuleId;
    private String oldIsActive;
    private String newIsActive;
    private Date oldStartValueDate;
    private Date newStartValueDate;
    private Date oldEndValueDate;
    private Date newEndValueDate;
    private String newDeactivatedBy;
    
    
    public RunProcedureStopRule(SessionFactory sessionFactory, String actionAsString ,String newStopRuleId, String oldIsActive, String newIsActive,
            Date oldStartValueDate, Date newStartValueDate, Date oldEndValueDate,
            Date newEndValueDate, String newDeactivatedBy) {
    	this.sessionFactory = sessionFactory;
    	this.actionAsString = actionAsString;
		this.newStopRuleId = newStopRuleId;
		this.oldIsActive = oldIsActive;
		this.newIsActive = newIsActive;
		this.oldStartValueDate = oldStartValueDate;
		this.newStartValueDate = newStartValueDate;
		this.oldEndValueDate = oldEndValueDate;
		this.newEndValueDate = newEndValueDate;
		this.newDeactivatedBy = newDeactivatedBy;
}


    @Override
    public void run()  {
    	Session session = null;
    	Connection conn = null;
    	CallableStatement cstmt  =  null;
    	try {
            semaphore.acquire(); // Acquire the permit (or wait if another thread has it)
        } catch (InterruptedException e) {
            return; // If interrupted, return without executing the procedure
        }
        try {
        	
        	 session = sessionFactory.openSession();
        	 conn = SwtUtil.connection(session);
        	
        	  cstmt = conn.prepareCall("{call pkg_pc_check_rules.check_and_run_stop_process(?,?,?,?,?,?,?,?,?)}");
              // Set the IN parameters with appropriate values
        	  cstmt.setString(1, actionAsString);
              cstmt.setString(2, newStopRuleId);
              
              if (oldIsActive != null) {
                  cstmt.setString(3, oldIsActive);
              } else {
                  cstmt.setNull(3, java.sql.Types.VARCHAR);
              }
              
              if (newIsActive != null) {
                  cstmt.setString(4, newIsActive);
              } else {
                  cstmt.setNull(4, java.sql.Types.VARCHAR);
              }
              
              
              
              
              if (oldStartValueDate != null) {
                  cstmt.setDate(5, new java.sql.Date(oldStartValueDate.getTime()));
              } else {
                  cstmt.setNull(5, java.sql.Types.DATE);
              }
              
              if (newStartValueDate != null) {
                  cstmt.setDate(6, new java.sql.Date(newStartValueDate.getTime()));
              } else {
                  cstmt.setNull(6, java.sql.Types.DATE);
              }
              
              
              if (oldEndValueDate != null) {
                  cstmt.setDate(7, new java.sql.Date(oldEndValueDate.getTime()));
              } else {
                  cstmt.setNull(7, java.sql.Types.DATE);
              }

              if (newEndValueDate != null) {
                  cstmt.setDate(8, new java.sql.Date(newEndValueDate.getTime()));
              } else {
                  cstmt.setNull(8, java.sql.Types.DATE);
              }

              cstmt.setString(9, newDeactivatedBy);

              // Execute the procedure
              cstmt.execute();

        } catch (SQLException | HibernateException e) {
            // Handle the exception if needed
            e.printStackTrace();
        } finally {
        	semaphore.release(); // Release the permit, allowing the next waiting thread to proceed
        	// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"RunProcedureStopRule", StopRulesMaintenanceDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"RunProcedureStopRule", StopRulesMaintenanceDAOHibernate.class);
			
        }
    }
}