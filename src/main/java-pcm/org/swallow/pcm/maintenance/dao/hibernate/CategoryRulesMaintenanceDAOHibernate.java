/*
 * @(#)CategoryRulesMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Interceptor;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.type.StandardBasicTypes;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CategoryRulesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;





@Repository ("categoryRulesMaintenanceDAO")
@Transactional
public class CategoryRulesMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements CategoryRulesMaintenanceDAO {
	public CategoryRulesMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(CategoryRulesMaintenanceDAOHibernate.class);
	
	/**
	 * Returns the order max of Category  Rule detail list from category table
	 * 
	 * @return Integer
	 * @throws SwtException
	 */
	public Integer getMaxOrder(String categoryId) throws SwtException {
		log.debug("Entering getMaxOrder method");

		Interceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		Integer max = 0;

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			TypedQuery<Integer> query = session.createQuery(
					"SELECT COALESCE(MAX(c.ordinal), 0) FROM CategoryRule c",
					Integer.class
			);

			max = query.getSingleResult(); // Directly retrieve the result
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in [getMaxOrder] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getMaxOrder", CurrencyMaintenanceDAOHibernate.class);
		}

		log.debug("Exiting getMaxOrder method with max order: " + max);
		return max;
	}
	
	/**
	 * Returns the collection of category detail list from category table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryRuleDetailList()throws SwtException {
		// Variable List to hold list Rules
		Collection categoryRuleList = new ArrayList();
		String query= null;
		Session session = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCategoryRuleDetailList] - " + "Entry");

			session = SwtUtil.pcSessionFactory.openSession();
			
			/* Assigning the query statement to the string object */
			query = "from CategoryRule c ";

			/* Query to get the category detail list in order to category code */
			categoryRuleList = session.createQuery(query).getResultList();

			log.debug(this.getClass().getName()
					+ " - [getCategoryRuleDetailList] - " + "Exit");
			return categoryRuleList;
			
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryRuleDetailList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCategoryRuleDetailList", this.getClass());
		} finally {
			JDBCCloser.close(session);
			// Nullify object
			categoryRuleList = null;
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getCategoryRuleDetailList ] - Exit");
		}
	}

	@Override
	public Category getCategoryRuleDetailByCategoryId(Category category) throws SwtException {
		String query= null;
		CategoryRule categoryRule= null;
		ArrayList<Object[]> records = new ArrayList<Object[]>();
		List<CategoryRule> pcmResult = new ArrayList();
		List<RulesDefinition>  rulesDefinition = new ArrayList<RulesDefinition>();
		RulesDefinition rule = null;
		Session session = null;
		try {
			
			session = SwtUtil.pcSessionFactory.openSession();
			/* Assigning the query statement to the string object */
			query = "from RulesDefinition r, CategoryRule c where r.ruleId=c.ruleId  and c.categoryId =?1";
			/* Query to get the currency detail list in order to currency code */
			records = (ArrayList<Object[]>) session.createQuery(query, Object[].class).setParameter(1, category.getId().getCategoryId(), StandardBasicTypes.STRING).getResultList();;
		
			if(records!= null && records.size()> 0) {
				for (Object[] result : records) {
					rule = new RulesDefinition();
					categoryRule= new CategoryRule();
					
					categoryRule = (CategoryRule) result[1];
			        rule = (RulesDefinition) result[0];
			        
			        categoryRule.setRulesDefinition(rule);
			        category.getCategoryRules().add(categoryRule);
			        //pcmResult.add(categoryRule);
			      }
			}
			
			log.debug(this.getClass().getName()
					+ " - [getCategoryRuleDetailList] - " + "Exit");
			return category;
		} catch (Exception exp) {
			exp.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryRuleDetailByCategoryId] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCategoryRuleDetailByCategoryId", this.getClass());
		} finally {
			JDBCCloser.close(session);
			// Nullify object
			records = null;
			pcmResult = null;
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getCategoryRuleDetailByCategoryId ] - Exit");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public CategoryRule getCategoryRuleById(Long categoryRuleId) throws SwtException {
		CategoryRule categoryRule=null;
		ArrayList<Object[]> records = new ArrayList<Object[]>();
		String query= null;
		ArrayList<RuleConditions> ruleConditionsList = null;
		Session session = null;
		try {
			
			log.debug(this.getClass().getName()
					+ " - [getCategoryRuleById] - " + "Entry");

			session = SwtUtil.pcSessionFactory.openSession();
			/* Assigning the query statement to the string object */
			query = "from RulesDefinition r, CategoryRule c where r.ruleId=c.ruleId  and c.categoryRuleId =?1";
			/* Query to get the currency detail list in order to currency code */
			records = (ArrayList<Object[]>) session.createQuery(query, Object[].class).setParameter(1, categoryRuleId, StandardBasicTypes.LONG).getResultList();

			
			if (records!= null && records.size() > 0) {
				Object[] result= records.get(0);
				categoryRule= new CategoryRule();
				categoryRule = (CategoryRule) result[1];
		        RulesDefinition rulesDef = (RulesDefinition) result[0];
		    	ruleConditionsList =  (ArrayList<RuleConditions>) session.createQuery("from RuleConditions rulesC where rulesC.id.ruleId = ?1", RuleConditions.class)
		    	                .setParameter(1, rulesDef.getRuleId(), StandardBasicTypes.INTEGER);

				rulesDef.setRuleConditions(ruleConditionsList);
		        categoryRule.setRulesDefinition(rulesDef);
		      }
			
			log.debug(this.getClass().getName() + " - [getCategoryRuleById] - " + "Exit");
			
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryRuleById] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryRuleById", this.getClass());
		} finally {
			JDBCCloser.close(session);
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryRuleById] - Exit");
		}
		return categoryRule;
	}
	
}