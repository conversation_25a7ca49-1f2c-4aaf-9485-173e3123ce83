/*
 * @(#)CurrencyMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.pcm.maintenance.dao.CurrencyMaintenanceDAO;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.type.Type;
import org.hibernate.Session;






/**
 * <AUTHOR>
 * This is DAO class for Currency screen
 */

@Repository("currencyMaintenanceDAO")
@Transactional
public class CurrencyMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements CurrencyMaintenanceDAO {
    private final Log log = LogFactory.getLog(CurrencyMaintenanceDAOHibernate.class);

    public CurrencyMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, 
            @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    @SuppressWarnings("unchecked")
    public Collection getCurrencyDetailList() throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyDetailList] - Entry");
        
        try (Session session = SwtUtil.pcSessionFactory.openSession()) {
            String query = "select cm, c from CurrencyMaster cm, PCMCurrency c " +
                          "where c.id.currencyCode = cm.currencyCode order by c.ordinal";
            
            List<Object[]> results = session.createQuery(query, Object[].class).getResultList();
            List<PCMCurrency> pcmResult = new ArrayList<>();
            
            for (Object[] result : results) {
                PCMCurrency currency = (PCMCurrency) result[1];
                currency.setCurrencyMaster((CurrencyMaster) result[0]);
                pcmResult.add(currency);
            }
            
            return pcmResult;
        } catch (Exception e) {
            log.error("Exception in getCurrencyDetailList", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyDetailList", this.getClass());
        }
    }

    @SuppressWarnings("unchecked")
    public Collection getCurrencyCombo() throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyCombo] - Entry");
        
        try (Session session = SwtUtil.pcSessionFactory.openSession()) {
            String query = "select cm, c from CurrencyMaster cm, PCMCurrency c " +
                          "where c.id.currencyCode = cm.currencyCode " +
                          "and exists (select 1 from Currency c2 where c.id.currencyCode = c2.id.currencyCode " +
                          "and c2.preFlag = 'Y') order by c.id.currencyCode asc";
            
            List<Object[]> results = session.createQuery(query, Object[].class).getResultList();
            List<PCMCurrency> pcmResult = new ArrayList<>();
            
            for (Object[] result : results) {
                PCMCurrency currency = (PCMCurrency) result[1];
                currency.setCurrencyMaster((CurrencyMaster) result[0]);
                pcmResult.add(currency);
            }
            
            return pcmResult;
        } catch (Exception e) {
            log.error("Exception in getCurrencyCombo", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyCombo", this.getClass());
        }
    }

    public Collection getCurrencyMaintenanceCombo() throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyMaintenanceCombo] - Entry");
        
        try (Session session = SwtUtil.pcSessionFactory.openSession()) {
            String query = "from CurrencyMaster cm where cm.currencyCode != '*' " +
                          "and exists (select 1 from Currency c2 where cm.currencyCode = c2.id.currencyCode " +
                          "and c2.preFlag = 'Y') order by cm.currencyCode asc";
            
            return session.createQuery(query, CurrencyMaster.class).getResultList();
        } catch (Exception e) {
            log.error("Exception in getCurrencyMaintenanceCombo", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyMaintenanceCombo", this.getClass());
        }
    }

    public void saveCurrency(PCMCurrency currency) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveCurrency] - Entry");
        
        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            
            Long count = session.createQuery(
                "select count(c) from PCMCurrency c where c.id.currencyCode = :code", Long.class)
                .setParameter("code", currency.getId().getCurrencyCode())
                .uniqueResult();
                
            if (count == 0) {
                Transaction tx = session.beginTransaction();
                try {
                    session.save(currency);
                    tx.commit();
                } catch (Exception e) {
                    tx.rollback();
                    throw e;
                }
            } else {
                throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
            }
        } catch (Exception e) {
            log.error("Exception in saveCurrency", e);
            throw new SwtException(e.getMessage());
        }
    }

    public void updateCurrency(PCMCurrency currency) throws SwtException {
        log.debug(this.getClass().getName() + " - [updateCurrency] - Entry");
        
        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            
            Long count = session.createQuery(
                "select count(c) from PCMCurrency c where c.id.currencyCode = :code", Long.class)
                .setParameter("code", currency.getId().getCurrencyCode())
                .uniqueResult();
                
            if (count == 1) {
                Transaction tx = session.beginTransaction();
                try {
                    session.merge(currency);
                    tx.commit();
                } catch (Exception e) {
                    tx.rollback();
                    throw e;
                }
            }
        } catch (Exception e) {
            log.error("Exception in updateCurrency", e);
            throw SwtErrorHandler.getInstance().handleException(e, "updateCurrency", this.getClass());
        }
    }

    public void deleteCurrency(String currencyCode) throws SwtException {
        log.debug(this.getClass().getName() + " - [deleteCurrency] - Entry");
        
        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            
            PCMCurrency currency = session.createQuery(
                "from PCMCurrency c where c.id.currencyCode = :code", PCMCurrency.class)
                .setParameter("code", currencyCode)
                .uniqueResult();
                
            if (currency != null) {
                Transaction tx = session.beginTransaction();
                try {
                    session.delete(currency);
                    tx.commit();
                } catch (Exception e) {
                    tx.rollback();
                    throw e;
                }
            }
        } catch (Exception e) {
            log.error("Exception in deleteCurrency", e);
            throw SwtErrorHandler.getInstance().handleException(e, "deleteCurrency", this.getClass());
        }
    }

    public PCMCurrency getCurrencyDetailById(String currencyCode) throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyDetailById] - Entry");
        
        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            
            return session.createQuery(
                "from PCMCurrency c where c.id.currencyCode = :code", PCMCurrency.class)
                .setParameter("code", currencyCode)
                .uniqueResult();
        } catch (Exception e) {
            log.error("Exception in getCurrencyDetailById", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyDetailById", this.getClass());
        }
    }

    public Integer getMaxOrder() throws SwtException {
        log.debug(this.getClass().getName() + " - [getMaxOrder] - Entry");
        
        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            
            return session.createQuery(
                "select coalesce(max(c.ordinal), 0) from PCMCurrency c", Integer.class)
                .uniqueResult();
        } catch (Exception e) {
            log.error("Exception in getMaxOrder", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getMaxOrder", this.getClass());
        }
    }

    public void reOrder(Integer valueFrom, Integer orderTo) throws SwtException {
        log.debug(this.getClass().getName() + " - [reOrder] - Entry");
        
        try (Session session = SwtUtil.pcSessionFactory.openSession();
             Connection conn = SwtUtil.connection(session)) {
            
            String sql = "update PC_CCY set ordinal = ordinal + 1 where ordinal >= ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setInt(1, orderTo);
                stmt.executeUpdate();
                conn.commit();
            }
        } catch (Exception e) {
            log.error("Exception in reOrder", e);
            throw SwtErrorHandler.getInstance().handleException(e, "reOrder", this.getClass());
        }
    }
}