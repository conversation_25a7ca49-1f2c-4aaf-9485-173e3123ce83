/*
 * @(#)StopRulesMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.type.StandardBasicTypes;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.StopRulesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.StopRule;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;







@Repository ("stopRulesMaintenanceDAO")
@Transactional
public class StopRulesMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements StopRulesMaintenanceDAO {
	public StopRulesMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
	super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(StopRulesMaintenanceDAOHibernate.class);

	/**
	 * Returns the collection of category detail list from category table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public StopRule getStopRuleDetails(String stopRuleId) throws SwtException {
		// Variable List to hold list Rules
		StopRule stopRule = null;
		ArrayList<StopRule> stopRuleList = new ArrayList<StopRule>();
		ArrayList<RulesDefinition> ruleList = new ArrayList<RulesDefinition>();
		String query = null;
		RulesDefinition rule =null;
		Session session = null;
		List resultPayStop;
		try {
			log.debug(this.getClass().getName() + " - [getStopRulesDetailList] - " + "Entry");

			session = SwtUtil.pcSessionFactory.openSession();

			// Query to get the StopRule by ID
			TypedQuery<StopRule> stopRuleQuery = session.createQuery("from StopRule s where s.id.stopRuleId = :stopRuleId", StopRule.class);
			stopRuleQuery.setParameter("stopRuleId", stopRuleId);
			stopRuleList = (ArrayList<StopRule>) stopRuleQuery.getResultList(); // Use uniqueResult() since stopRuleId should be unique


			if (stopRuleList != null && stopRuleList.size() > 0) {
				stopRule = stopRuleList.get(0);
				if(stopRule.getRuleId() != null) {
					// Query to get the RulesDefinition by ruleId
					TypedQuery<RulesDefinition> ruleQuery = session.createQuery("from RulesDefinition r where r.ruleId = :ruleId", RulesDefinition.class);
					ruleQuery.setParameter("ruleId", stopRule.getRuleId());
					ruleList = (ArrayList<RulesDefinition>) ruleQuery.getResultList();

					if (!ruleList.isEmpty()) {
						stopRule.setRule(ruleList.get(0));
					}

					// Query to count PaymentStop linked to the StopRule
					TypedQuery<Long> countQuery = session.createQuery("select count(*) from PaymentStop s where s.id.stopRuleId = :stopRuleId", Long.class);
					countQuery.setParameter("stopRuleId", stopRule.getId().getStopRuleId());
					Long numOfPR = countQuery.getSingleResult();
					stopRule.setNumberOfLinkedPR(numOfPR != null ? numOfPR.intValue() : 0);

				}


			}
			log.debug(this.getClass().getName() + " - [getStopRulesDetailList] - " + "Exit");
			return stopRule;

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getStopRulesDetailList] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getStopRulesDetailList",
					StopRulesMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	public Collection getStopRulesDetailList(String selectedStatus) throws SwtException {
		// Variable List to hold list Rules
		ArrayList<StopRule> stopList = new ArrayList<StopRule>();
		String query = null;
		Session session = null;
		List resultPayStop  = null;
		int numOfPR  = 0;

		try {
			log.debug(this.getClass().getName() + " - [getStopRulesDetailList] - " + "Entry");
			session = SwtUtil.pcSessionFactory.openSession();
			if(selectedStatus.equals("A")) {
				query = "from StopRule s where s.isActive='Y' order by  s.activateOnDate ";
			}else if(selectedStatus.equals("I")) {
				query = "from StopRule s where s.isActive='N' order by s.activateOnDate ";
			}else {
				query = "from StopRule s order by s.isActive desc, s.activateOnDate ";

			}
			/* Assigning the query statement to the string object */

			/* Query to get the category detail list in order to category code */
			stopList = (ArrayList<StopRule>) session.createQuery(query).getResultList();

			for (int i = 0; i < stopList.size(); i++) {
				/* Assigning the query statement to the string object */
				query = "select count(*) from PaymentStop s where s.id.stopRuleId= :stopRuleId";
				/* Query to get the category detail list in order to category code */
				resultPayStop =  session.createQuery(query, Object.class)
						.setParameter("stopRuleId",  stopList.get(i).getId().getStopRuleId(), StandardBasicTypes.STRING)
						.getResultList();
				numOfPR =  ((Long) resultPayStop.get(0)).intValue();

				stopList.get(i).setNumberOfLinkedPR(numOfPR);
			}
			log.debug(this.getClass().getName() + " - [getStopRulesDetailList] - " + "Exit");
			return stopList;

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getStopRulesDetailList] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getStopRulesDetailList",
					StopRulesMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	@Override
	public void saveStopRule(StopRule stopRule, boolean isSaveAction) throws SwtException {
		/* Methods local variable declaration */
		List<StopRule>  records = null;
		Session session = null;
		Transaction tx = null;
		StopRule oldStopRule = null;
		SwtInterceptor interceptor = null;
		String stopRuleId = null;
		Integer ruleDefintionId = null;
		RulesDefinition ruleDefintion;
		boolean requireAuthorisation = false;
		String actionAsString = null; 
		try {
			log.debug(this.getClass().getName() + " - [saveStopRule] - " + "Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();

			TypedQuery<StopRule> query = session.createQuery("from StopRule s where s.id.stopRuleId = :stopRuleId", StopRule.class);
			query.setParameter("stopRuleId", stopRule.getId().getStopRuleId());
			records = query.getResultList();

			if(stopRule != null && !SwtUtil.isEmptyOrNull(stopRule.getMainEventId())) {
				requireAuthorisation = true;
			}

			tx = session.beginTransaction();
			if(isSaveAction || records.size() == 0) {
				/* Condition to check list size */
				if (records.size() == 0) {
					actionAsString = "I";
					if(stopRule.getRule() != null && !SwtUtil.isEmptyOrNull(stopRule.getRule().getRuleQuery())) {
						ruleDefintion = stopRule.getRule();
						if(!SwtUtil.isEmptyOrNull(stopRule.getMainEventId())) {
							ruleDefintion.setMainEventId(stopRule.getMainEventId());
						}
						ruleDefintionId  = (Integer) session.save(ruleDefintion);
						stopRule.setRuleId(ruleDefintionId);
						
						if(ruleDefintion.getRuleConditions() != null && ruleDefintion.getRuleConditions().size()>0) {
							for (int i = 0; i < ruleDefintion.getRuleConditions().size(); i++) {
								ruleDefintion.getRuleConditions().get(i).getId().setRuleId(ruleDefintionId);
								session.save(ruleDefintion.getRuleConditions().get(i));
							}
						}
					}
					
					session.save(stopRule);
					log.debug(this.getClass().getName() + " - [saveStopRule] - " + "Exit");
				} else {
					throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
				}
			}else {
				if (records.size() > 0) {
					actionAsString = "U";
					oldStopRule = (StopRule) records.get(0);
					session.merge(stopRule);
					log.debug(this.getClass().getName() + " - [saveStopRule] - " + "Exit");
				} else {
					throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
				}
			}
			if(requireAuthorisation) {
				session.flush();
				tx.rollback();
			}
			else {
				tx.commit();
			
				try {
				    Thread procedureThread = new Thread(new RunProcedureStopRule(getHibernateTemplate().getSessionFactory(),actionAsString, stopRule.getId().getStopRuleId(),
				    		oldStopRule != null?oldStopRule.getIsActive():null, stopRule.getIsActive(), oldStopRule != null?SwtUtil.truncateDateTime(oldStopRule.getActivateOnDate()):null, SwtUtil.truncateDateTime(stopRule.getActivateOnDate()),
				    				oldStopRule != null?SwtUtil.truncateDateTime(oldStopRule.getDeactivateOnDate()):null, SwtUtil.truncateDateTime(stopRule.getDeactivateOnDate()), stopRule.getDeactivatedBy()));
			         procedureThread.start();
				}catch(Exception e) {
				} 
				
				 
			}
				
			
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveStopRule] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveStopRule",
					StopRulesMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}


	@Override
	public void deleteStopRule(String stopRuleId, Long maintEventId) throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		List recordsRules = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		StopRule stopRule = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteStopRule] - " + "Entry");
			// Open a session with interceptor
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Query to find the StopRule
			TypedQuery<StopRule> query = session.createQuery("from StopRule s where s.id.stopRuleId = :stopRuleId", StopRule.class);
			query.setParameter("stopRuleId", stopRuleId);
			stopRule = query.getSingleResult();

			if (stopRule != null) {
				if (maintEventId != null) {
					stopRule.setMainEventId(String.valueOf(maintEventId));
					if (stopRule.getRule() != null) {
						stopRule.getRule().setMainEventId(String.valueOf(maintEventId));
					}
				}

				// Delete the StopRule
				session.delete(stopRule);

				// If the StopRule has a RuleId, delete the corresponding RulesDefinition
				if (stopRule.getRuleId() != null) {
					TypedQuery<RulesDefinition> ruleQuery = session.createQuery("from RulesDefinition r where r.ruleId = :ruleId", RulesDefinition.class);
					ruleQuery.setParameter("ruleId", stopRule.getRuleId());
					RulesDefinition rule = ruleQuery.getSingleResult();
					if (rule != null) {
						session.delete(rule);
					}
				}

				log.debug(this.getClass().getName() + " - [deleteStopRule] - " + "Exit");
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}
			if(maintEventId != null) {
				session.flush();
				tx.rollback();
			}
			else
				tx.commit();

		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteStopRule] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteStopRule",
					StopRulesMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}
	
	public  boolean checkProcessStopRule(String stopRuleId) throws SwtException {
		log.debug("Enter into checkProcessStopRule method");
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String value = null;
		try {
				String query = "SELECT PKG_PC_CHECK_RULES.is_stop_process_running (?) expression FROM DUAL";
				session = SwtUtil.pcSessionFactory.openSession();
				conn = SwtUtil.connection(session);
				statement = conn.prepareStatement(query);
				statement.setString(1, stopRuleId);
				statement.execute();
				resultSet = statement.getResultSet();
				
				if (resultSet != null) {
					resultSet.next();
					value = resultSet.getString(1);
				}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(hibernateException, "getAllEntityOption",
					StopRulesMaintenanceDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getAllEntityOption",
					StopRulesMaintenanceDAOHibernate.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"checkProcessStopRule", StopRulesMaintenanceDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"checkProcessStopRule", StopRulesMaintenanceDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		log.debug("Exiting StopRulesMaintenanceDAOHibernate.checkProcessStopRule()");
		return "Y".equals(value);
		
	}

}