/*
 * @(#)categoryRulesMaintenanceDAO.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;

/**
 * <AUTHOR>
 * 
 */
public interface CategoryRulesMaintenanceDAO extends DAO {
	
	
	/**
	 * Returns the max ordinal from category table
	 * 
	 * @return Integer
	 * @throws SwtException
	 */
	
	public Integer getMaxOrder(String categoryId) throws SwtException;
	

	/**
	 * Returns the collection of category rule detail list from CategoryRule table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryRuleDetailList() throws SwtException;
	
	
	/**
	 * Returns the collection of category rule detail list from CategoryRule table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Category getCategoryRuleDetailByCategoryId(Category category) throws SwtException;
	
	
	/**
	 * Returns the CategoryRule of categorRuleId from CategoryRule table
	 * 
	 * @return categoryRuleId
	 * @throws SwtException
	 */
	public CategoryRule getCategoryRuleById(Long categoryRuleId) throws SwtException;
	
}
