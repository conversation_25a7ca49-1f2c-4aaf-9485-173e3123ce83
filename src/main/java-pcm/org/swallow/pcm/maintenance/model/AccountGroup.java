/*
 * @(#)AccountGroup.java 1.0 2019-02-25
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Set;

import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.ILMTransactionSetDTL;
import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class AccountGroup extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.accGrpId","Account Group Id");
		logTable.put("currencyCode","Currency");
		logTable.put("description","Account Group Name");
		logTable.put("ordinal","Processing Order");
		logTable.put("quickCategoryId","Quick Category ID");
		logTable.put("defaultCategoryId","Default Category ID");
		logTable.put("kickoff","kick off");
		logTable.put("eodPhaseBegins","End of Intraday Release Phase");
		logTable.put("cobCutoff","Close of Business");
		logTable.put("spreadProfileId","Spread Profile");
		logTable.put("targetPaymentPercentMethod","Target Release Calc. Method");
	}
	
	
	public static ArrayList<String>  excludedAttributes = new ArrayList<String>();
	static {
		excludedAttributes.add("requireAuthorisation");
	}
	
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private String description;
	private Integer ordinal;
	private String currencyCode;
	private String kickoff;
	private String eodPhaseBegins;
	private String cobCutoff;
	private String targetPaymentPercentMethod;
	private String spreadProfileId;
	private String quickCategoryId;
	private String defaultCategoryId;
	private Set<AccountInGroup> numberOfAccounts = new HashSet<AccountInGroup>(0);
	private AcctMaintenance acctMaintenance = null;
	private Set<Reserve> reserve = null;
	private Set<AccountGroupCutoff> accountGroupCutoff = null;
	private Set<AccountInGroup> accountInGroup = null;
	private PCMCurrency ccy = null;
	private RulesDefinition rulesDefinition = null;
	private String requireAuthorisation = null;
	
	
	public Id getId() {
		return id;
	}
	
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String accGrpId;
		
		public Id(String accGrpId) {
			this.accGrpId = accGrpId;
		}

		public Id() {
		}

		public String getAccGrpId() {
			return accGrpId;
		}
		public void setAccGrpId(String accGrpId) {
			this.accGrpId = accGrpId;
		}
	}
	
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Integer getOrdinal() {
		return ordinal;
	}
	public void setOrdinal(Integer ordinal) {
		this.ordinal = ordinal;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getKickoff() {
		return kickoff;
	}
	public void setKickoff(String kickoff) {
		this.kickoff = kickoff;
	}
	public String getEodPhaseBegins() {
		return eodPhaseBegins;
	}
	public void setEodPhaseBegins(String eodPhaseBegins) {
		this.eodPhaseBegins = eodPhaseBegins;
	}
	public String getCobCutoff() {
		return cobCutoff;
	}
	public void setCobCutoff(String cobCutoff) {
		this.cobCutoff = cobCutoff;
	}
	public String getTargetPaymentPercentMethod() {
		return targetPaymentPercentMethod;
	}
	public void setTargetPaymentPercentMethod(String targetPaymentPercentMethod) {
		this.targetPaymentPercentMethod = targetPaymentPercentMethod;
	}
	public String getSpreadProfileId() {
		return spreadProfileId;
	}
	
	public void setSpreadProfileId(String spreadProfileId) {
		this.spreadProfileId = spreadProfileId;
	}
	public String getQuickCategoryId() {
		return quickCategoryId;
	}
	public void setQuickCategoryId(String quickCategoryId) {
		this.quickCategoryId = quickCategoryId;
	}
	public String getDefaultCategoryId() {
		return defaultCategoryId;
	}
	public void setDefaultCategoryId(String defaultCategoryId) {
		this.defaultCategoryId = defaultCategoryId;
	}
	public Set<AccountInGroup> getNumberOfAccounts() {
		return numberOfAccounts;
	}

	public void setNumberOfAccounts(Set<AccountInGroup> numberOfAccounts) {
		this.numberOfAccounts = numberOfAccounts;
	}
	public AcctMaintenance getAcctMaintenance() {
		return acctMaintenance;
	}
	public void setAcctMaintenance(AcctMaintenance acctMaintenance) {
		this.acctMaintenance = acctMaintenance;
	}

	public Set<Reserve> getReserve() {
		return reserve;
	}

	public void setReserve(Set<Reserve> reserve) {
		this.reserve = reserve;
	}
	

	public Set<AccountGroupCutoff> getAccountGroupCutoff() {
		return accountGroupCutoff;
	}

	public void setAccountGroupCutoff(Set<AccountGroupCutoff> accountGroupCutoff) {
		this.accountGroupCutoff = accountGroupCutoff;
	}

	public Set<AccountInGroup> getAccountInGroup() {
		return accountInGroup;
	}

	public void setAccountInGroup(Set<AccountInGroup> accountInGroup) {
		this.accountInGroup = accountInGroup;
	}

	public PCMCurrency getCcy() {
		return ccy;
	}

	public void setCcy(PCMCurrency ccy) {
		this.ccy = ccy;
	}

	public RulesDefinition getRulesDefinition() {
		return rulesDefinition;
	}

	public void setRulesDefinition(RulesDefinition rulesDefinition) {
		this.rulesDefinition = rulesDefinition;
	}

	public String getRequireAuthorisation() {
		return requireAuthorisation;
	}

	public void setRequireAuthorisation(String requireAuthorisation) {
		this.requireAuthorisation = requireAuthorisation;
	}
}
