<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.StopRule" table="PC_STOP_RULE">
    	<composite-id class="org.swallow.pcm.maintenance.model.StopRule$Id" name="id" unsaved-value="any">
		   <key-property name="stopRuleId" access="field" column="STOP_RULE_ID"/>
		</composite-id>
    	
		<property name="ruleType" column="RULE_TYPE" not-null="false"/>	
		<property name="stopReasonText" column="STOP_REASON_TEXT" not-null="false"/>	
		<property name="activateOnDate" column="START_VALUE_DATE" not-null="false"/>	
		<property name="activatedOnDate" column="ACTIVATED_ON_DATE" not-null="false"/>	
		<property name="activatedBy" column="ACTIVATED_BY" not-null="false"/>	
		<property name="deactivateOnDate" column="END_VALUE_DATE" not-null="false"/>	
		<property name="deactivatedOnDate" column="DEACTIVATED_ON_DATE" not-null="false"/>	
		<property name="deactivatedBy" column="DEACTIVATED_BY" not-null="false"/>	
		<property name="isActive" column="IS_ACTIVE" not-null="false"/>	
		<property name="ruleId" column="RULE_ID" not-null="false"/>	
		<property name="actionOnDeactivation" column="ACTION_ON_DEACTIVATION" not-null="false"/>	
	</class>
</hibernate-mapping>