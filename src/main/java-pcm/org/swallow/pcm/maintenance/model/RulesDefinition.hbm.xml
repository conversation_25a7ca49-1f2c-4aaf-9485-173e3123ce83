<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.pcm.maintenance.model.RulesDefinition"
		table="P_RULE">
		<id name="ruleId" column="rule_id">
			<generator class="sequence">
				<param name="sequence_name">SEQ_P_RULE</param>
			<param name="increment_size">1</param>
			</generator>
		</id>	
		<property name="ruleType" column="rule_type" />
		<property name="ruleQuery" column="rule_query" />
		<property name="ruleText" column="rule_text" />
	</class>
</hibernate-mapping>