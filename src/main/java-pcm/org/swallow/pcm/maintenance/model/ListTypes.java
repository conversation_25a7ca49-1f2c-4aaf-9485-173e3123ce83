/**
 * 
 */
package org.swallow.pcm.maintenance.model;

import java.util.ArrayList;
import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;

/**
 * ListTypes.java
 * 
 * Class for List of types mapping.
 * 
 * <AUTHOR> Tunisia - Ezzeddine
 * @version V 1.04 August 2013
 * 
 */
public class ListTypes extends BaseObject implements AuditComponent {

	/**
	 * long variable to hold serial versionId
	 */
	private static final long serialVersionUID = 1L;

	// Instance type Id
	private Integer typeId;
	// Variable String to hold the name
	private String name;
	// Variable String to hold narrative
	private String narrative;
	// Variable String to hold module ID
	private String moduleId;
	// Variable String to hold if lis is editable by user
	private String editable;

	private Integer maxCodeLength;
	// to hold typeValue list in case of sub report
	private ArrayList<TypeValues> typeValueList;
	/* String variable to hold class name */
	public static String className = "ListTypes";
	/* String variable to hold module id */
	public static String module_Id = "SYSTEM";
	/* Creates an instance for HashTable */
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	/* Static block for logs */
	static {
		logTable.put("typeId", "TYPE_ID");
		logTable.put("name", "NAME");
		logTable.put("narrative", "NARRATIVE");
		logTable.put("moduleId", "MODULE_ID");
		logTable.put("editable", "EDITABLE");
		logTable.put("maxCodeLength", "MAX_CODE_LEN");
	}

	/**
	 * @return the typeId
	 */
	public Integer getTypeId() {
		return typeId;
	}

	/**
	 * @param typeId the typeId to set
	 */
	public void setTypeId(Integer typeId) {
		this.typeId = typeId;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @param name the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the narrative
	 */
	public String getNarrative() {
		return narrative;
	}

	/**
	 * @param narrative the narrative to set
	 */
	public void setNarrative(String narrative) {
		this.narrative = narrative;
	}

	/**
	 * @return the moduleId
	 */
	public String getModuleId() {
		return moduleId;
	}

	/**
	 * @param moduleId the moduleId to set
	 */
	public void setModuleId(String moduleId) {
		this.moduleId = moduleId;
	}

	/**
	 * @return the editable
	 */
	public String getEditable() {
		return editable;
	}

	/**
	 * @param editable the editable to set
	 */
	public void setEditable(String editable) {
		this.editable = editable;
	}

	public Integer getMaxCodeLength() {
		return maxCodeLength;
	}

	public void setMaxCodeLength(Integer maxCodeLength) {
		this.maxCodeLength = maxCodeLength;
	}

	/**
	 * @return the typeValueList
	 */
	public ArrayList<TypeValues> getTypeValueList() {
		return typeValueList;
	}

	/**
	 * @param typeValueList the typeValueList to set
	 */
	public void setTypeValueList(ArrayList<TypeValues> typeValueList) {
		this.typeValueList = typeValueList;
	}

}
