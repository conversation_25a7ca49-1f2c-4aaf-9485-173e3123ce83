/*
 * @(#)ProcessPointPriority.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class ProcessPointCategory extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private String categoryId = null;
	
	public static Hashtable  logTable = new Hashtable();
	
	public ProcessPointCategory() {
	}

	public ProcessPointCategory(Long processPointId, String categoryId) {
		this.id.setProcessPointId(processPointId);
		this.id.setCategoryId(categoryId);
		this.categoryId = categoryId;
	}

	public Id getId() {
		return id;
	}
	
	public void setId(Id id) {
		this.id = id;
	}
	
	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public static class Id extends BaseObject {
		
		private Long processPointId;
		private String categoryId;
		
		public Id(Long processPointId, String categoryId) {
			this.processPointId = processPointId;
			this.categoryId = categoryId;
		}

		public Id() {
		}

		public Long getProcessPointId() {
			return processPointId;
		}

		public void setProcessPointId(Long processPointId) {
			this.processPointId = processPointId;
		}

		public String getCategoryId() {
			return categoryId;
		}

		public void setCategoryId(String categoryId) {
			this.categoryId = categoryId;
		}
	}
}
