<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.SpreadProcessPoint" table="PC_SPREAD_PROCESS_POINT">
    	<id name="processPointId" type="long" column="PROCESS_POINT_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_SPREAD_PROCESS_POINT</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
		<property name="spreadProfileId" column="SPREAD_PROFILE_ID" not-null="true"/>	
		<property name="processPointTime" column="TIME" not-null="true"/>	
		<property name="targetPaymentPercent" column="TARGET_PAYMENT_PERCENT" not-null="false"/>
		<property name="processCategories" column="PROCESS_CATEGORIES" not-null="false"/>		
		<property name="processName" column="NAME" not-null="false"/>		
		<set name="processPointCategory" inverse="true" lazy="false" cascade="all" table="PC_PROCESS_POINT_CATEGORY">
			<key>
			    <column name="PROCESS_POINT_ID" not-null="true" />
			</key>
			<one-to-many class="org.swallow.pcm.maintenance.model.ProcessPointCategory" />
		</set>
	</class>
	
</hibernate-mapping>