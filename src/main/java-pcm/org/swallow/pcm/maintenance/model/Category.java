/*
 * @(#)Category.java 1.0 2019-02-25
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.HashSet;
import java.util.Hashtable;
import java.util.Set;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class Category extends BaseObject  implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.categoryId","Category Id");
		logTable.put("categoryName","Category Name");
		logTable.put("ordinal","Processing order");
		logTable.put("isActive","Active");
		logTable.put("urgentSpreadInd","Type");
		logTable.put("useLiqCheck","Use Liquidity Check");
		logTable.put("assignmentMethod","Assignment Method");
		logTable.put("inclTargetPayReleased","Incl. in Spr. Release Target");
		logTable.put("inclInAvailableLiqCalc","Incl. in avail. Liquidity");
		logTable.put("setReleaseTime","Release time");
		logTable.put("releaseValueDateOffset","Release value date offset");
		logTable.put("ruleAssignPriority","Rule Assignment Priority");
	}
	
	private Id id = new Id();

	private String categoryName;
	private Integer ordinal;
	private String isActive;
	private String urgentSpreadInd;
	private String useLiqCheck;
	private String assignmentMethod;
	private String inclTargetPayReleased;
	private String inclInAvailableLiqCalc;
	private String specifiedReleaseTime;
	private String setReleaseTime;
	private Integer releaseValueDateOffset;
	private Integer ruleAssignPriority;
	private Set<CategoryRule> categoryRules = new HashSet<CategoryRule>(0);
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String categoryId;
		
		public Id(String categoryId) {
			this.categoryId = categoryId;
		}

		public Id() {
		}

		public String getCategoryId() {
			return categoryId;
		}

		public void setCategoryId(String categoryId) {
			this.categoryId = categoryId;
		}
	}
	
	public String getCategoryName() {
		return categoryName;
	}
	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}
	public Integer getOrdinal() {
		return ordinal;
	}
	public void setOrdinal(Integer ordinal) {
		this.ordinal = ordinal;
	}
	
	public String getIsActive() {
		return isActive;
	}
	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
	public String getUrgentSpreadInd() {
		return urgentSpreadInd;
	}
	public void setUrgentSpreadInd(String urgentSpreadInd) {
		this.urgentSpreadInd = urgentSpreadInd;
	}
	public String getUseLiqCheck() {
		return useLiqCheck;
	}
	public void setUseLiqCheck(String useLiqCheck) {
		this.useLiqCheck = useLiqCheck;
	}
	
	public String getAssignmentMethod() {
		return assignmentMethod;
	}
	public void setAssignmentMethod(String assignmentMethod) {
		this.assignmentMethod = assignmentMethod;
	}
	public String getInclTargetPayReleased() {
		return inclTargetPayReleased;
	}
	public void setInclTargetPayReleased(String inclTargetPayReleased) {
		this.inclTargetPayReleased = inclTargetPayReleased;
	}
	
	public String getInclInAvailableLiqCalc() {
		return inclInAvailableLiqCalc;
	}
	public void setInclInAvailableLiqCalc(String inclInAvailableLiqCalc) {
		this.inclInAvailableLiqCalc = inclInAvailableLiqCalc;
	}
	
	public String getSpecifiedReleaseTime() {
		return specifiedReleaseTime;
	}
	public void setSpecifiedReleaseTime(String specifiedReleaseTime) {
		this.specifiedReleaseTime = specifiedReleaseTime;
	}
	public String getSetReleaseTime() {
		return setReleaseTime;
	}
	public void setSetReleaseTime(String setReleaseTime) {
		this.setReleaseTime = setReleaseTime;
	}
	public Set<CategoryRule> getCategoryRules() {
		return categoryRules;
	}
	public void setCategoryRules(Set<CategoryRule> categoryRules) {
		this.categoryRules = categoryRules;
	}
	public Integer getReleaseValueDateOffset() {
		return releaseValueDateOffset;
	}
	public void setReleaseValueDateOffset(Integer releaseValueDateOffset) {
		this.releaseValueDateOffset = releaseValueDateOffset;
	}
	public Integer getRuleAssignPriority() {
		return ruleAssignPriority;
	}
	public void setRuleAssignPriority(Integer ruleAssignPriority) {
		this.ruleAssignPriority = ruleAssignPriority;
	}
	
}
