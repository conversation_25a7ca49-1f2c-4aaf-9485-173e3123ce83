/*
 * @(#)StopRule.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class StopRule extends BaseObject implements org.swallow.model.AuditComponent{
	
	private static final long serialVersionUID = 1L;
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.stopRuleId","Stop Id");
		logTable.put("stopReasonText","Reason Text");
		logTable.put("ruleType","Rule Type");
		logTable.put("isActive","Is Active");
		logTable.put("deactivateOnDate","Deactivate On Date");
		logTable.put("activatedOnDate","Activate On Date");
	}
	
	
	private Id id = new Id();
	private String stopReasonText;
	private Date activateOnDate;
	private Date activatedOnDate;
	private String activatedBy;
	private Date deactivateOnDate;
	private Date deactivatedOnDate;
	private String deactivatedBy;
	private Integer ruleId;
	private String ruleType;
	private String isActive;
	private RulesDefinition rule;
	private String actionOnDeactivation;
	private int numberOfLinkedPR;
	
	public Id getId() {
		return id;
	}
	
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String stopRuleId;
		
		public Id(String stopRuleId) {
			this.stopRuleId = stopRuleId;
		}

		public Id() {
		}

		public String getStopRuleId() {
			return stopRuleId;
		}

		public void setStopRuleId(String stopRuleId) {
			this.stopRuleId = stopRuleId;
		}
	}

	public String getStopReasonText() {
		return stopReasonText;
	}

	public void setStopReasonText(String stopReasonText) {
		this.stopReasonText = stopReasonText;
	}

	public Date getActivateOnDate() {
		return activateOnDate;
	}

	public void setActivateOnDate(Date activateOnDate) {
		this.activateOnDate = activateOnDate;
	}

	public Date getActivatedOnDate() {
		return activatedOnDate;
	}

	public void setActivatedOnDate(Date activatedOnDate) {
		this.activatedOnDate = activatedOnDate;
	}

	public String getActivatedBy() {
		return activatedBy;
	}

	public void setActivatedBy(String activatedBy) {
		this.activatedBy = activatedBy;
	}

	public Date getDeactivateOnDate() {
		return deactivateOnDate;
	}

	public void setDeactivateOnDate(Date deactivateOnDate) {
		this.deactivateOnDate = deactivateOnDate;
	}

	public Date getDeactivatedOnDate() {
		return deactivatedOnDate;
	}

	public void setDeactivatedOnDate(Date deactivatedOnDate) {
		this.deactivatedOnDate = deactivatedOnDate;
	}

	public String getDeactivatedBy() {
		return deactivatedBy;
	}

	public void setDeactivatedBy(String deactivatedBy) {
		this.deactivatedBy = deactivatedBy;
	}

	public Integer getRuleId() {
		return ruleId;
	}

	public void setRuleId(Integer ruleId) {
		this.ruleId = ruleId;
	}

	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	public String getIsActive() {
		return isActive;
	}

	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}

	public RulesDefinition getRule() {
		return rule;
	}

	public void setRule(RulesDefinition rule) {
		this.rule = rule;
	}

	public String getActionOnDeactivation() {
		return actionOnDeactivation;
	}

	public void setActionOnDeactivation(String actionOnDeactivation) {
		this.actionOnDeactivation = actionOnDeactivation;
	}

	public int getNumberOfLinkedPR() {
		return numberOfLinkedPR;
	}

	public void setNumberOfLinkedPR(int numberOfLinkedPR) {
		this.numberOfLinkedPR = numberOfLinkedPR;
	}
}
