package org.swallow.pcm.maintenance.model.core.kv;

import java.io.Serializable;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import org.swallow.exception.SwtException;


//XmLElementWrapper generates a wrapper element around XML representation
@XmlRootElement
public class OperationsList implements Serializable{
	public static String INSERT_OPERATION = "I";
	public static String UPDATE_OPERATION = "U";
	public static String DELETE_OPERATION = "D";
	
	public static String MASTER_LEVEL = "M";
	public static String CHILD_LEVEL = "S";
	
	private ArrayList<TabKVType> elementList;

	
	public OperationsList(){
		
	}
	
	public OperationsList(String operListAsXML){
		try {
			this.elementList = OperationsList.xmlToOperationsList(operListAsXML).getElementList();
			sortOperationList(this);
		} catch (SwtException e) {
			this.elementList = null;
		}
	}
	
	
	public OperationsList(String tableName, String operationType, String tableLevel, Map<String, Object> columnValues) {
		this.addOperation(tableName, operationType, tableLevel, columnValues);
		sortOperationList(this);
	}
	
	public ArrayList<TabKVType> getElementList() {
		if(elementList == null)
			elementList = new ArrayList<TabKVType>();
		return elementList;
	}

	@XmlElement(name = "tabKVType")
	public void setElementList(ArrayList<TabKVType> elementList) {
		this.elementList = elementList;
	}

	/**
	 * Returns the orginal XML
	 * @return
	 */
	public String toXmlString(){
		try {
			return operationsListToXML(this);
		} catch (Exception e) {
			return null;
		}
	}
	
	/**
	 * Returns the orginal XML
	 * @return
	 * @throws SwtException 
	 */
	public String operationsListToXML(OperationsList object) throws SwtException{
		JAXBContext context; 
    	Marshaller ma;
    	StringWriter sw;
    	try {
    		context = JAXBContext.newInstance(OperationsList.class);
    		ma = context.createMarshaller();
    		sw = new StringWriter();
    		// Marshall into XML
    		ma.marshal(object, sw);
    		return sw.toString();
		} catch (Exception e) {
			throw new SwtException("Error marshalling the OperationsList into XML string", e);
		}
	}
	
	
	/**
	 * XML to OperationsList object
	 * @param input
	 * @return
	 * @throws SwtException
	 */
	public static OperationsList xmlToOperationsList(String input) throws SwtException{
		OperationsList operationList = null;
		JAXBContext context; 
		// 
    	Unmarshaller um;
    	try {
    		context = JAXBContext.newInstance(OperationsList.class);
    		um = context.createUnmarshaller();
    		//Unmarshal the coming XML to OperationsList object
    		operationList = (OperationsList) um.unmarshal(new StringReader(input));
    		
    		// Sort
    		sortOperationList(operationList);
		} catch (Exception e) {
			throw new SwtException("Error unmarshalling the KV type XML into OperationsList", e);
		}
		return operationList;
	}
	
	/**
	 * Sorts the operationList so that Master tables are placed the first
	 * @param operationList
	 */
	private static void sortOperationList(OperationsList operationList){
		Collections.sort(operationList.elementList, new Comparator<TabKVType>() {
			@Override
			public int compare(TabKVType o1, TabKVType o2) {
				if(o1.getTableLevel().equalsIgnoreCase(o2.getTableLevel()))
					return 0;
				else if(o1.getTableLevel().equalsIgnoreCase(OperationsList.MASTER_LEVEL)){
					return -1;
				}else{
					return 1;
				}
			}
		});
	}
	
	/**
	 * Injects a new operation
	 * @param tableName
	 * @param operationType
	 * @param tableLevel
	 */
	public void addOperation(String tableName, String operationType, String tableLevel, Map<String, Object> columnValues){
		TabKVType tab = new TabKVType();
		tab.setTableName(tableName);
		tab.setTableLevel(tableLevel);
		tab.setOperation(operationType);
		if(columnValues != null)
		{
			int i=0;
			for(String columnName:columnValues.keySet()){
				KVType kv = new KVType(columnName, columnValues.get(columnName));
				kv.setIndex(i++);
				tab.getElementList().add(kv);
			}
		}
		this.getElementList().add(tab);
		sortOperationList(this);
	}
	
	/**
	 * Removes an operation indexed by table name
	 * @param tableName
	 */
	public void removeOperation(String tableName){
		for(TabKVType tvk:getElementList()){
			if(tvk.getTableName().equalsIgnoreCase(tableName)){
				this.getElementList().remove(tvk);
			}
		}
		sortOperationList(this);
	}
	
	/**
	 * Gets an operation @TabKVType based on table name
	 * @param tableName
	 * @return
	 */
	public TabKVType getOperationByTableName(String tableName){
		for(TabKVType tvk:getElementList()){
			if(tvk.getTableName().equalsIgnoreCase(tableName)){
				return tvk;
			}
		}
		return null;
	}
	
	@Override
	public String toString() {
		return this.toXmlString();
	}
	
	public static void main(String[] args) throws SwtException {
		String xmlString = 
				"<operationsList>"
				+ "<tabKVType tableName=\"G_REC_UNIT_GROUP\" operation=\"I\" tableLevel=\"S\">"
				+ "<kvType> " + "<key>REC_GROUP_ID</key>"
				+ "<value>" + "1" + "</value>"
				+ "</kvType>"
				
                +"<kvType> " + "<key>ACTIVITY_ID</key>"
                + "<value>1</value>"
                + "</kvType>"
                
                +"<kvType> " + "<key>CREATOR_TYPE</key>"
                + "<value>" + "U" + "</value>"
                + "</kvType>"
                
                +"<kvType> " + "<key>ENTITY_ID</key>"
                + "<value>1</value>"
                + "</kvType>"
                
                +"<kvType> " + "<key>NAME</key>"
                + "<value>1</value>"
                + "</kvType>"
                
                +"<kvType> " + "<key>FULL_NAME</key>"
                + "<value>Full Name</value>"
                + "</kvType>"
                
                
                +"<kvType> " + "<key>TYPE</key>"
                + "<value> a type</value>"
                + "</kvType>"
                
				+ "</tabKVType>"
				+ "</operationsList>";
		
		OperationsList oplist = new OperationsList(xmlString);
		oplist.addOperation("T2", OperationsList.INSERT_OPERATION, OperationsList.MASTER_LEVEL, new HashMap<String, Object>(){{
													put("col1", "val1");
													put("col2", new Date());
													put("col1", 1255);
											}});
		oplist.addOperation("T3", OperationsList.INSERT_OPERATION, OperationsList.CHILD_LEVEL, new HashMap<String, Object>(){{
			put("col1", "val1");
			put("col2", new Date());
			put("col1", 1255);
		}});
		//oplist.removeOperation("G_REC_UNIT_GROUP");
		//oplist.setElementList(elementList)
		
		/*System.out.println(CommonUtil.kvTabToXmlString("T3", OperationsList.INSERT_OPERATION, OperationsList.MASTER_LEVEL, new HashMap<String, Object>(){{
													put("col1", "val1111");
													put("col2", new Date());
													put("col3", 123456789);
											}}));*/
		
	}
}
