package org.swallow.pcm.maintenance.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;


/**
 * <p>
 * Class used to configure the different rules that will be used by
 * <b>RulesDefiniton</b>
 * </p>
 * 
 * <AUTHOR>
 * @version V 1.0.2
 * 
 */

public class RulesDefinition extends BaseObject implements
		AuditComponent {

	/**
	 * long variable to hold serial versionId
	 */
	private static final long serialVersionUID = 1L;

	private Integer ruleId;
	private String ruleName;
	private String ruleStatus;
	private String ruleType;
	private float levelCode;
	private String description;
	private Integer periodValue;
	private String period;
	private String editable;
	private String ruleQuery;
	private String ruleText;
	private String blocked;
	private String updateAlert;
	private String updateAlertLabel;
	private String updateUser;
	private Date updateDate;
	private String updateDateAsString;
	private Integer nameTextId;
	private Integer descTextId;
	private Date expiryDate;
	private String expiryDateString;
	private String moduleId;
	private String isRiskFactor;
	private String levelCodeAsString;
	// String variable to hold class name
	public static String className = "RulesDefinition";
	// String variable to hold module id
	public static String module_Id = "AML";
	// Variable String to hold sign of transactions
	private String sign;
	// Variable String to indicate if the check will be launched by account or
	// by all client accounts
	private String allCltAccount;
	private String levelCodeToDisplay;
	private String handleProcessedAlerts;


	// new column missing info to define the rules of missing info
	private String missingInfo;
	
	// new column rule level type to define the level type of rule (Primary or Secondary)
	private String levelType;
	// rule used like as secondary criteria for principal rule
	private Integer acceptRuleId;
	// Variable String to define Sub Type 
	private String subType;
	//Variable to hold ruleConditions
	private ArrayList<RuleConditions> ruleConditions = new ArrayList<RuleConditions>();
	
	// save the query to execute with separator to allow parsing at the next time
	private String modifiableRuleQuery;
			
	// save the query to display with separator to allow parsing at the next time
	private String modifiableRuleText;
	
		
	// Creates an instance for Hashtable
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	// Static blocked for logs
	static {
		logTable.put("ruleId", "RULE_ID");
		logTable.put("ruleName", "RULE_NAME");
		logTable.put("ruleStatus", "RULE_STATUS");
		logTable.put("ruleType", "RULE_TYPE");
		logTable.put("levelCode", "LEVEL_CODE");
		logTable.put("description", "DESCRIPTION");
		logTable.put("periodType", "PERIOD_TYPE");
		logTable.put("periodeMultiple", "PERIOD_MULTIPLE");
		logTable.put("periodValue", "PERIOD_VALUE");
		logTable.put("editable", "EDITABLE");
		logTable.put("ruleQuery", "RULE_QUERY");
		logTable.put("blocked", "BLOCKED");
		logTable.put("ruleText", "RULE_TEXT");
		logTable.put("updateUser", "UPDATE_USER");
		logTable.put("updateDate", "UPDATE_DATE");
		logTable.put("expiryDtae", "EXPIRY_DATE");
		logTable.put("moduleId", "MODULE_ID");
		logTable.put("sign", "SIGN");
		logTable.put("allCltAccount", "ALL_CLIENT_ACCOUNT");
		logTable.put("missingInfo", "MISSING_INFO");
		logTable.put("isRiskFactor", "IS_RISK_FACTOR");
		logTable.put("handleProcessedAlerts", "HANDLE_PROCESSED_ALERTS");
	}

	/**
	 * @return the ruleId
	 */
	public Integer getRuleId() {
		return ruleId;
	}

	/**
	 * @param ruleId
	 *            the ruleId to set
	 */
	public void setRuleId(Integer ruleId) {
		this.ruleId = ruleId;
	}

	
	/**
	 * @return the ruleName
	 */
	public String getRuleName() {
		return ruleName;
	}

	/**
	 * @param ruleName
	 *            the ruleName to set
	 */
	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	/**
	 * @return the ruleStatus
	 */
	public String getRuleStatus() {
		return ruleStatus;
	}

	/**
	 * @param ruleStatus
	 *            the ruleStatus to set
	 */
	public void setRuleStatus(String ruleStatus) {
		this.ruleStatus = ruleStatus;
	}

	/**
	 * @return the ruleType
	 */
	public String getRuleType() {
		return ruleType;
	}

	/**
	 * @param ruleType
	 *            the ruleType to set
	 */
	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	/**
	 * @return the levelCode
	 */
	public float getLevelCode() {
		return levelCode;
	}

	/**
	 * @param levelCode
	 *            the levelCode to set
	 */
	public void setLevelCode(float levelCode) {
		this.levelCode = levelCode;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description
	 *            the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * @return the periodValue
	 */
	public Integer getPeriodValue() {
		return periodValue;
	}

	/**
	 * @param periodValue
	 *            the periodValue to set
	 */
	public void setPeriodValue(Integer periodValue) {
		this.periodValue = periodValue;
	}

	/**
	 * @return the editable
	 */
	public String getEditable() {
		return editable;
	}

	/**
	 * @param editable
	 *            the editable to set
	 */
	public void setEditable(String editable) {
		this.editable = editable;
	}

	/**
	 * @return the ruleQuery
	 */
	public String getRuleQuery() {
		return ruleQuery;
	}

	/**
	 * @param ruleQuery
	 *            the ruleQuery to set
	 */
	public void setRuleQuery(String ruleQuery) {
		this.ruleQuery = ruleQuery;
	}

	/**
	 * @return the ruleText
	 */
	public String getRuleText() {
		return ruleText;
	}

	/**
	 * @param ruleText
	 *            the ruleText to set
	 */
	public void setRuleText(String ruleText) {
		this.ruleText = ruleText;
	}

	/**
	 * @return the blocked
	 */
	public String getBlocked() {
		return blocked;
	}

	/**
	 * @param blocked
	 *            the blocked to set
	 */
	public void setBlocked(String blocked) {
		this.blocked = blocked;
	}

	/**
	 * @return the updateUser
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            the updateUser to set
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return the nameTextId
	 */
	public Integer getNameTextId() {
		return nameTextId;
	}

	/**
	 * @param nameTextId
	 *            the nameTextId to set
	 */
	public void setNameTextId(Integer nameTextId) {
		this.nameTextId = nameTextId;
	}

	/**
	 * @return the descTextId
	 */
	public Integer getDescTextId() {
		return descTextId;
	}

	/**
	 * @param descTextId
	 *            the descTextId to set
	 */
	public void setDescTextId(Integer descTextId) {
		this.descTextId = descTextId;
	}

	/**
	 * @return the expiryDate
	 */
	public Date getExpiryDate() {
		return expiryDate;
	}

	/**
	 * @param expiryDate
	 *            the expiryDate to set
	 */
	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	/**
	 * @return the expiryDateString
	 */
	public String getExpiryDateString() {
		return expiryDateString;
	}

	/**
	 * @param expiryDateString
	 *            the expiryDateString to set
	 */
	public void setExpiryDateString(String expiryDateString) {
		this.expiryDateString = expiryDateString;
	}

	/**
	 * @return the sign
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * @param sign
	 *            the sign to set
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * @return the moduleId
	 */
	public String getModuleId() {
		return moduleId;
	}
	/**
	 * @param moduleId the moduleId to set
	 */
	public void setModuleId(String moduleId) {
		this.moduleId = moduleId;
	}
	/**
	 * @return the period
	 */
	public String getPeriod() {
		return period;
	}

	/**
	 * @param period
	 *            the period to set
	 */
	public void setPeriod(String period) {
		this.period = period;
	}

	/**
	 * @return the allCltAccount
	 */
	public String getAllCltAccount() {
		return allCltAccount;
	}

	/**
	 * @param allCltAccount
	 *            the allCltAccount to set
	 */
	public void setAllCltAccount(String allCltAccount) {
		this.allCltAccount = allCltAccount;
	}

	/**
	 * @return the updateAlert
	 */
	public String getUpdateAlert() {
		return updateAlert;
	}

	/**
	 * @param updateAlert
	 *            the updateAlert to set
	 */
	public void setUpdateAlert(String updateAlert) {
		this.updateAlert = updateAlert;
	}

	/**
	 * @return the updateAlertLabel
	 */
	public String getUpdateAlertLabel() {
		return updateAlertLabel;
	}

	/**
	 * @param updateAlertLabel
	 *            the updateAlertLabel to set
	 */
	public void setUpdateAlertLabel(String updateAlertLabel) {
		this.updateAlertLabel = updateAlertLabel;
	}

	/**
	 * @return the missingInfo
	 */
	public String getMissingInfo() {
		return missingInfo;
	}

	/**
	 * @param missingInfo
	 *            the missingInfo to set
	 */
	public void setMissingInfo(String missingInfo) {
		this.missingInfo = missingInfo;
	}

	/**
	 * @return the updateDateAsString
	 */
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}

	/**
	 * @param updateDateAsString the updateDateAsString to set
	 */
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}

	/**
	 * @return the levelType
	 */
	public String getLevelType() {
		return levelType;
	}

	/**
	 * @param levelType the levelType to set
	 */
	public void setLevelType(String levelType) {
		this.levelType = levelType;
	}

	/**
	 * @return the acceptRuleId
	 */
	public Integer getAcceptRuleId() {
		return acceptRuleId;
	}

	/**
	 * @param acceptRuleId the acceptRuleId to set
	 */
	public void setAcceptRuleId(Integer acceptRuleId) {
		this.acceptRuleId = acceptRuleId;
	}

	/**
	 * @return the isRiskFactor
	 */
	public String getIsRiskFactor() {
		return isRiskFactor;
	}

	/**
	 * @param isRiskFactor the isRiskFactor to set
	 */
	public void setIsRiskFactor(String isRiskFactor) {
		this.isRiskFactor = isRiskFactor;
	}
	
	/**
	 * @return the levelCodeToDisplay
	 */
	public String getLevelCodeToDisplay() {
		return levelCodeToDisplay;
	}
	
	/**
	 * @param levelCodeToDisplay the levelCodeToDisplay to set
	 */
	public void setLevelCodeToDisplay(String levelCodeToDisplay) {
		this.levelCodeToDisplay = levelCodeToDisplay;
	}

	public String getLevelCodeAsString() {
		return levelCodeAsString;
	}

	public void setLevelCodeAsString(String levelCodeAsString) {
		this.levelCodeAsString = levelCodeAsString;
	}

	/**
	 * @return the subType
	 */
	public String getSubType() {
		return subType;
	}

	/**
	 * @param subType the subType to set
	 */
	public void setSubType(String subType) {
		this.subType = subType;
	}
	/**
	 * @return the handleProcessedAlerts
	 */
	public String gethandleProcessedAlerts() {
		return handleProcessedAlerts;
	}
	/**
	 * @param handleProcessedAlerts the handleProcessedAlerts to set
	 */
	public void sethandleProcessedAlerts(String handleProcessedAlerts) {
		this.handleProcessedAlerts = handleProcessedAlerts;
	}

	public ArrayList<RuleConditions> getRuleConditions() {
		return ruleConditions;
	}

	public void setRuleConditions(ArrayList<RuleConditions> ruleConditions) {
		this.ruleConditions = ruleConditions;
	}

	public String getModifiableRuleQuery() {
		return modifiableRuleQuery;
	}

	public void setModifiableRuleQuery(String modifiableRuleQuery) {
		this.modifiableRuleQuery = modifiableRuleQuery;
	}

	public String getModifiableRuleText() {
		return modifiableRuleText;
	}

	public void setModifiableRuleText(String modifiableRuleText) {
		this.modifiableRuleText = modifiableRuleText;
	}
	
	
	
}
