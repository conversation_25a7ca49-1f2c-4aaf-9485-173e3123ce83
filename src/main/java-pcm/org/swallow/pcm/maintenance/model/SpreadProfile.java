/*
 * @(#)SpreadProfile.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.HashSet;
import java.util.Hashtable;
import java.util.Set;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class SpreadProfile extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.spreadProfileId","Spread Profile Id");
		logTable.put("spreadProfileName","spread Profile Name");
		logTable.put("currencyCode","currency Code");
	}
	private Id id = new Id();
	private String spreadProfileName;
	private String currencyCode;
	private Set<SpreadProcessPoint> spreadProcessPoint = new HashSet<SpreadProcessPoint>(0);
	private String requireAuthorisation = null;
	public Id getId() {
		return id;
	}
	
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String spreadProfileId;
		
		public Id(String spreadProfileId) {
			this.spreadProfileId = spreadProfileId;
		}

		public Id() {
		}

		public String getSpreadProfileId() {
			return spreadProfileId;
		}

		public void setSpreadProfileId(String spreadProfileId) {
			this.spreadProfileId = spreadProfileId;
		}
	}

	public String getSpreadProfileName() {
		return spreadProfileName;
	}

	public void setSpreadProfileName(String spreadProfileName) {
		this.spreadProfileName = spreadProfileName;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public Set<SpreadProcessPoint> getSpreadProcessPoint() {
		return spreadProcessPoint;
	}

	public void setSpreadProcessPoint(Set<SpreadProcessPoint> spreadProcessPoint) {
		this.spreadProcessPoint = spreadProcessPoint;
	}

	public String getRequireAuthorisation() {
		return requireAuthorisation;
	}

	public void setRequireAuthorisation(String requireAuthorisation) {
		this.requireAuthorisation = requireAuthorisation;
	}
	
}
