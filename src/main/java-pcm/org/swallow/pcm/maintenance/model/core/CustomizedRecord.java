package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;

public class CustomizedRecord implements Serializable{

	private Object value=null;
	private String type=null;
	private String visualType=null;
	private int size=4000;
	private boolean negative=false;
	private boolean holiday=false;
	private boolean clickable= false;
	private boolean visible = true;
	private boolean visibleDEFAULT = true;
	private boolean editable=false;
	private String columnName=null;
	private String dataprovider = null;
	private String dataUrl = null;
	private String dataId = null;
	private String dataParams = null;
	private String format = null;
	
	
	public CustomizedRecord() {
	}


	public CustomizedRecord(Object value, String columnName, String type,String visualType) {
		super();
		this.value = value;
		this.columnName = columnName;
		this.type = type;
		this.visualType = visualType;
	}
	
	
	public Object getValue() {
		return value;
	}
	public void setValue(Object value) {
		this.value = value;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public int getSize() {
		return size;
	}
	public void setSize(int size) {
		this.size = size;
	}
	public boolean isNegative() {
		return negative;
	}
	public void setNegative(boolean negative) {
		this.negative = negative;
	}
	public boolean isHoliday() {
		return holiday;
	}
	public void setHoliday(boolean holiday) {
		this.holiday = holiday;
	}
	public String getColumnName() {
		return columnName;
	}
	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}


	public String getVisualType() {
		return visualType;
	}


	public void setVisualType(String visualType) {
		this.visualType = visualType;
	}


	public boolean isEditable() {
		return editable;
	}


	public void setEditable(boolean editable) {
		this.editable = editable;
	}


	public String getDataprovider() {
		return dataprovider;
	}


	public void setDataprovider(String dataprovider) {
		this.dataprovider = dataprovider;
	}


	public boolean isVisible() {
		return visible;
	}


	public void setVisible(boolean visible) {
		this.visible = visible;
	}


	public String getDataId() {
		return dataId;
	}


	public void setDataId(String dataId) {
		this.dataId = dataId;
	}


	public String getDataParams() {
		return dataParams;
	}


	public void setDataParams(String dataParams) {
		this.dataParams = dataParams;
	}


	public boolean isClickable() {
		return clickable;
	}


	public void setClickable(boolean clickable) {
		this.clickable = clickable;
	}


	public String getFormat() {
		return format;
	}


	public void setFormat(String format) {
		this.format = format;
	}


	public boolean isVisibleDEFAULT() {
		return visibleDEFAULT;
	}


	public void setVisibleDEFAULT(boolean visibleDEFAULT) {
		this.visibleDEFAULT = visibleDEFAULT;
	}


	public String getDataUrl() {
		return dataUrl;
	}


	public void setDataUrl(String dataUrl) {
		this.dataUrl = dataUrl;
	}
	
	
}
