/*
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.pcm.maintenance.model;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * 
 * This class has getters and setters related to PCMCurrency Details<br>
 */
public class PCMCurrencyDetailsVO implements Serializable {
	private Collection currencyList;
	// Holds the collection of currency
	private Collection<PCMCurrency> currencyListDetails;

	public PCMCurrencyDetailsVO() {
	}

	public PCMCurrencyDetailsVO(Collection currencyList,
			Collection<PCMCurrency> currencyListDetails) {
		this.currencyList = currencyList;
		this.currencyListDetails = currencyListDetails;
	}

	/**
	 * @return Returns the currencyList.
	 */
	public Collection getCurrencyList() {
		return currencyList;
	}

	/**
	 * @param currencyList
	 *            The currencyList to set.
	 */
	public void setCurrencyList(Collection currencyList) {
		this.currencyList = currencyList;
	}

	/**
	 * @return the currencyListDetails
	 */
	public Collection<PCMCurrency> getCurrencyListDetails() {
		return currencyListDetails;
	}

	/**
	 * @param currencyListDetails
	 *            the currencyListDetails to set
	 */
	public void setCurrencyListDetails(Collection<PCMCurrency> currencyListDetails) {
		this.currencyListDetails = currencyListDetails;
	}

}
