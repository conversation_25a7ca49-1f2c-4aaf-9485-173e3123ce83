<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.AccountInGroup" table="PC_ACCOUNT_IN_GROUP">
		<composite-id name="id" class="org.swallow.pcm.maintenance.model.AccountInGroup$Id" unsaved-value="any">
			 <key-property name="hostId" access="field" column="HOST_ID"/>
			 <key-property name="entityId" access="field" column="ENTITY_ID"/>
			 <key-property name="accountId" access="field" column="ACCOUNT_ID"/>
		</composite-id>
<!-- 		<property name="checkAccountLiquidity" column="CHECK_ACCOUNT_LIQUIDITY" not-null="false"/>	 -->
		<property name="accGrpId" column="ACC_GRP_ID" not-null="false"/>
	    </class>
</hibernate-mapping>