<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.AccountGroup" table="PC_ACCOUNT_GROUP">
    	<composite-id class="org.swallow.pcm.maintenance.model.AccountGroup$Id" name="id" unsaved-value="any">
		   <key-property name="accGrpId" access="field" column="ACC_GRP_ID"/>
		</composite-id>
    	
		<property name="description" column="DESCRIPTION" not-null="true"/>	
		<property name="ordinal" column="ORDINAL" not-null="false"/>	
		<property name="currencyCode" column="CURRENCY_CODE" not-null="true"/>			
		<property name="kickoff" column="KICKOFF" not-null="false"/>
		<property name="eodPhaseBegins" column="EOD_PHASE_BEGINS" not-null="false"/>		
		<property name="cobCutoff" column="COB_CUTOFF" not-null="true"/>		
		<property name="targetPaymentPercentMethod" column="TARGET_PAYMENT_PERCENT_METHOD" not-null="false"/>		
		<property name="spreadProfileId" column="SPREAD_PROFILE_ID" not-null="false"/>		
		<property name="quickCategoryId" column="QUICK_CATEGORY_ID" not-null="false"/>				
		<property name="defaultCategoryId" column="DEFAULT_CATEGORY_ID" not-null="false"/>	
		<property name="requireAuthorisation" column="REQUIRE_AUTHORISATION" not-null="false"/>	
		<set name="reserve" inverse="true" lazy="false"  cascade="delete" table="PC_RESERVE" >
      <key>
            
            <column name="ACC_GRP_ID" not-null="true" />
      </key>
      <one-to-many class="org.swallow.pcm.maintenance.model.Reserve" />
   </set>
		<set name="accountInGroup" inverse="true" lazy="false"  cascade="all" table="PC_ACCOUNT_IN_GROUP" >
      <key>
            <column name="ACC_GRP_ID" not-null="true" />
      </key>
      <one-to-many class="org.swallow.pcm.maintenance.model.AccountInGroup" />
   </set>
		<set name="accountGroupCutoff" inverse="true" lazy="false"  cascade="all" table="PC_ACCOUNT_GRP_CUTOFF" >
      <key>
            <column name="ACC_GRP_ID" not-null="true" />
      </key>
      <one-to-many class="org.swallow.pcm.maintenance.model.AccountGroupCutoff" />
   </set>
		
	
<!-- 		<bag name="reserve" -->
<!--        table="PC_RESERVE" -->
<!--        lazy="true" -->
<!--        cascade="all"> -->
<!--         <key column="ACC_GRP_ID"/> -->
<!--         <one-to-many class="org.swallow.pcm.maintenance.model.Reserve" /> -->
<!--   </bag>	 -->
		
	</class>
</hibernate-mapping>