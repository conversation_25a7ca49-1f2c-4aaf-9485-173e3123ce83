<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.RunningTotals" table="PC_RUNNING_TOTALS">
    	<composite-id name="id" class="org.swallow.pcm.maintenance.model.RunningTotals$Id" >
			 <key-property name="accGrpId" access="field" column="ACC_GRP_ID"/>
			 <key-property name="priorityId" access="field" column="PRIORITY_ID"/>
		</composite-id>
		<property name="totalPaidForValueDate" column="TOTAL_PAID_FOR_VALUE_DATE" not-null="false"/>	
		<property name="totalPaidSinceLastProcPt" column="TOTAL_PAID_SINCE_LAST_PROC_PT" not-null="false"/>	
		<property name="valueDate" column="VALUE_DATE" not-null="false"/>
	</class>
</hibernate-mapping>