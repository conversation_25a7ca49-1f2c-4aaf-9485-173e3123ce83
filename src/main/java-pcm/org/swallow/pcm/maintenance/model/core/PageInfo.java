/**
 * @(#)PageInfo.java / 1.0 / 18 Aug 2009 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;

/**
 * PageInfo.java
 * 
 * This java bean has getters & setters for paging details
 * 
 * <AUTHOR>
 * @version New SMART-1.0
 * @date 18 Aug 2009
 */
public class PageInfo implements Serializable {

	// Total no of pages
	private int maxPage;
	// current page no
	private int currentPage;

	/**
	 * Default constructor
	 */
	public PageInfo() {
	}

	/**
	 * Constructor
	 * 
	 * @param maxPage
	 * @param currentPage
	 */
	public PageInfo(int maxPage, int currentPage) {
		// set properties
		this.maxPage = maxPage;
		this.currentPage = currentPage;
	}

	/**
	 * Getter method of maxPage
	 * 
	 * @return the maxPage
	 */
	public int getMaxPage() {
		return maxPage;
	}

	/**
	 * Setter method of maxPage
	 * 
	 * @param maxPage
	 *            the maxPage to set
	 */
	public void setMaxPage(int maxPage) {
		this.maxPage = maxPage;
	}

	/**
	 * Getter method of currentPage
	 * 
	 * @return the currentPage
	 */
	public int getCurrentPage() {
		return currentPage;
	}

	/**
	 * Setter method of currentPage
	 * 
	 * @param currentPage
	 *            the currentPage to set
	 */
	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}
}