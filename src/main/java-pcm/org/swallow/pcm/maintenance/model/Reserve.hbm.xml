<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.Reserve" table="PC_RESERVE">
    
    <id name="reserveId" type="long" column="RESERVE_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_RESERVE</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
		<property name="accGrpId" column="ACC_GRP_ID" not-null="false"/>	
		<property name="accGrpTime" column="TIME" not-null="false"/>	
		<property name="reserveBalance" column="RESERVED_BALANCE" not-null="false"/>	
		<property name="useCreditLine" column="USE_CREDIT_LINE" not-null="false"/>	
	</class>
</hibernate-mapping>