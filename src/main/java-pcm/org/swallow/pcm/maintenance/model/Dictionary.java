/**
 * @(#)Dictionary.java / 1.0 / 03 Apr 2009 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.io.Serializable;

import org.swallow.model.BaseObject;


/**
 * Dictionary.java
 * 
 * Hibernate Mapping file associated with S_Dictionary table
 * 
 * <AUTHOR>
 * @date April 03, 2009
 * @version New Predict
 */
public class Dictionary implements Serializable {

	/**
	 * Variable to hold serial version
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * String variable to hold text for this class
	 */
	private String text = null;
	/**
	 * Initializing Id object for this class
	 */
	private Id id = new Id();

	/**
	 * Id class to define composite key variables
	 */
	public static class Id extends BaseObject {
		/**
		 * Variable to hold serial version
		 */
		private static final long serialVersionUID = 1L;
		/**
		 * String variable to hold language id
		 */
		private String languageId = null;
		/**
		 * String variable to hold text id
		 */
		private String textId = null;

		/**
		 * Getter method to get language
		 * 
		 * @return String - returns - languageId
		 */
		public String getLanguageId() {
			return languageId;
		}

		/**
		 * Setter method to set languageId
		 * 
		 * @param languageId -
		 *            passing languageId as String
		 * @return none
		 */
		public void setLanguageId(String languageId) {
			this.languageId = languageId;
		}

		/**
		 * Getter method to get textId
		 * 
		 * @return String - return textId
		 */
		public String getTextId() {
			return textId;
		}

		/**
		 * Setter method to set textId
		 * 
		 * @param textId -
		 *            passing textId as String
		 * @return none
		 */
		public void setTextId(String textId) {
			this.textId = textId;
		}
	}

	/**
	 * Getter method to get text
	 * 
	 * @return String - return text
	 */
	public String getText() {
		return text;
	}

	/**
	 * Setter method to set text
	 * 
	 * @param text -
	 *            passing text as String
	 * @return none
	 */
	public void setText(String text) {
		this.text = text;
	}

	/**
	 * Getter method to get Id instance
	 * 
	 * @return Id - returns Id instance
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method to set Id instance
	 * 
	 * @param id -
	 *            Passing Id instance
	 * @return none
	 */
	public void setId(Id id) {
		this.id = id;
	}

}
