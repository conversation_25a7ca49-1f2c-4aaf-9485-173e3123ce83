<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.pcm.maintenance.model.TypeValues" table="S_CFG_TYPE_VALUES">
		<composite-id name="id" class="org.swallow.pcm.maintenance.model.TypeValues$Id" unsaved-value="any">
			<key-property name="typeId" column="TYPE_ID" access="field"
				type="integer" />
			<key-property name="valueId" column="VALUE_ID" access="field"
				type="integer" />
		</composite-id>
		<property name="modif" column="MODIF" />
		<property name="trans" column="TRANS" />
		<property name="code" column="CODE" />
		<property name="labelTextId" column="LABEL_TEXT_ID" not-null="false"/>
		<property name="labelPlus" column="LABEL_PLUS" />
		<property name="typeCode" column="TYPE_CODE" />
		<property name="typeEnum" column="TYPE_ENUM" />
		<property name="squery" column="squery" />
		<property name="tableName" column="table_name" />
		<property name="columnValueUpper" column="COLUMN_VALUE_UPPER" />
		<property name="internalLabel" column="INTERNAL_LABEL" />
		<property name="typeList" column="TYPE_LIST" />
		<property name="macroColumn" column="macro_column" />
		<property name="hiddenOperator" column="hidden_operator" />
	</class>
</hibernate-mapping>