/*
 * @(#)SpreadProcessPoint.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.HashSet;
import java.util.Hashtable;
import java.util.Set;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class SpreadProcessPoint extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("processPointId","Process Point Id");
		logTable.put("spreadProfileId","Spread Profile Id");
		logTable.put("processName","Process Name");
		logTable.put("processPointTime","Process Point Time");
		logTable.put("targetPaymentPercent","Target Payment Percent");
		logTable.put("processCategories","Process Categories");
		logTable.put("categories","Categories");
	}
	private Long processPointId;
	private String spreadProfileId;
	private String processPointTime;
	private Long targetPaymentPercent;
	private String processCategories;
	private String processCategoriesText;
	private String processName;
	private String categories;
	private Set<ProcessPointCategory> processPointCategory = new HashSet<ProcessPointCategory>(0);
	public final static String CLASSNAME = "SpreadProcessPoint";
	
	public Long getProcessPointId() {
		return processPointId;
	}
	public void setProcessPointId(Long processPointId) {
		this.processPointId = processPointId;
	}
	public String getSpreadProfileId() {
		return spreadProfileId;
	}
	public void setSpreadProfileId(String spreadProfileId) {
		this.spreadProfileId = spreadProfileId;
	}
	public String getProcessPointTime() {
		return processPointTime;
	}
	public void setProcessPointTime(String processPointTime) {
		this.processPointTime = processPointTime;
	}
	public Long getTargetPaymentPercent() {
		return targetPaymentPercent;
	}
	public void setTargetPaymentPercent(Long targetPaymentPercent) {
		this.targetPaymentPercent = targetPaymentPercent;
	}
	public String getProcessCategories() {
		return processCategories;
	}
	public void setProcessCategories(String processCategories) {
		this.processCategories = processCategories;
	}
	public String getProcessCategoriesText() {
		return processCategoriesText;
	}
	public void setProcessCategoriesText(String processCategoriesText) {
		this.processCategoriesText = processCategoriesText;
	}
	public String getProcessName() {
		return processName;
	}
	public void setProcessName(String processName) {
		this.processName = processName;
	}
	public String getCategories() {
		return categories;
	}
	public void setCategories(String categories) {
		this.categories = categories;
	}
	public Set<ProcessPointCategory> getProcessPointCategory() {
		return processPointCategory;
	}
	public void setProcessPointCategory(Set<ProcessPointCategory> processPointCategory) {
		this.processPointCategory = processPointCategory;
	}
	
	
}
