/*
 * @(#)Reserve.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class Reserve extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("accGrpId","Account Group Id");
		logTable.put("reserveBalance","Reserve Balance");
		logTable.put("accGrpTime","Time");
		logTable.put("useCreditLine","Use Credit Line");
	}
	
	private static final long serialVersionUID = 1L;

	private Long reserveId;
	private Long reserveBalance;	
	private String useCreditLine;	
	private String accGrpId;
	private String accGrpTime;
	public final static String CLASSNAME = "Reserve";


	public Long getReserveBalance() {
		return reserveBalance;
	}
	
	public void setReserveBalance(Long reserveBalance) {
		this.reserveBalance = reserveBalance;
	}
	
	public String getUseCreditLine() {
		return useCreditLine;
	}
	
	public void setUseCreditLine(String useCreditLine) {
		this.useCreditLine = useCreditLine;
	}
	public Long getReserveId() {
		return reserveId;
	}
	public void setReserveId(Long reserveId) {
		this.reserveId = reserveId;
	}

	public String getAccGrpId() {
		return accGrpId;
	}

	public void setAccGrpId(String accGrpId) {
		this.accGrpId = accGrpId;
	}

	public String getAccGrpTime() {
		return accGrpTime;
	}

	public void setAccGrpTime(String accGrpTime) {
		this.accGrpTime = accGrpTime;
	}
}
