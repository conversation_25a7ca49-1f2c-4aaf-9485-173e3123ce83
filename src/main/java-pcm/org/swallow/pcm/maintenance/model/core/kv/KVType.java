package org.swallow.pcm.maintenance.model.core.kv;


import java.io.Serializable;
import java.util.Date;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

 
@XmlRootElement
public class KVType implements Serializable{
	int index;
	String key;
	String value;
	String value1;
	String value2;
	
	public KVType() {
		
	}

	public KVType(String key, Object value) {
		this.key = key;
		this.value = valueToString(value);
	}
	
	public KVType(Object index, Object key, Object value, Object value1, Object value2) {
		this.index = Integer.parseInt(index.toString());
		this.key = valueToString(key);
		this.value = valueToString(value);
		this.value1 = valueToString(value1);
		this.value2 = valueToString(value2);
	}
	
	/**
	 * A value object to String, Dates are converted into ISO date format
	 * @param value
	 * @return
	 */
	private String valueToString(Object value){
		try {
			return value instanceof Date ? SwtUtil.formatDate((Date)value, SwtConstants.ISO_DATE_FORMAT):new String(value.toString());
		} catch (Exception e) {
			return null;
		}
	}
	
	public void updateValue(Object value){
		this.value = valueToString(value);
	}
	
	public int getIndex() {
		return index;
	}
	@XmlElement
	public void setIndex(int index) {
		this.index = index;
	}
	public String getKey() {
		return key;
	}
	@XmlElement
	public void setKey(String key) {
		this.key = key;
	}
	public String getValue() {
		return value;
	}
	@XmlElement
	public void setValue(String value) {
		this.value = value;
	}
	public String getValue1() {
		return value1;
	}
	@XmlElement
	public void setValue1(String value1) {
		this.value1 = value1;
	}
	public String getValue2() {
		return value2;
	}
	@XmlElement
	public void setValue2(String value2) {
		this.value2 = value2;
	}
}