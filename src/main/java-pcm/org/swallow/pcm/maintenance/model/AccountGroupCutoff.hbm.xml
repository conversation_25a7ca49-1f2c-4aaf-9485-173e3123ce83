<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.AccountGroupCutoff" table="PC_ACCOUNT_GRP_CUTOFF">
    	<id name="cutOffRuleId" type="long" column="CUTOFF_RULE_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_ACC_GROUP_CUTOFF</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
		<property name="accGrpId" column="ACC_GRP_ID" not-null="true"/>	
		<property name="ordinal" column="ORDINAL" not-null="true"/>	
		<property name="cutoffTime" column="CUTOFF_TIME" not-null="true"/>	
		<property name="logText" column="LOG_TEXT" not-null="false"/>		
		<property name="ruleId" column="RULE_ID" not-null="true"/>		
	</class>
</hibernate-mapping>