/**
 * @(#)OptionInfo.java / 1.0 / 18 Aug 2009 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;
import java.util.HashMap;

/**
 * OptionInfo.java
 * 
 * This java bean has getters & setters for form dropdown option
 * 
 * <AUTHOR>
 * @version New SMART-1.0
 * @date 18 Aug 2009
 */
public class OptionInfo implements Serializable{

	// option value
	private String value = null;
	// display value
	private String text = null;
	// selected flag
	private boolean selected;
	// Added by Bala.dnr 110909 for option attribute
	// id attribute value
	private String id = null;
	// Added by <PERSON><PERSON>di 26072013
	//list of additional attributes if all columns is set to true
	private HashMap<String, String> params = new HashMap<String, String>();

	// Added by Bala.dnr 110909 for option attribute
// to get the type of data
	private String type = null;
	//
	private String label =null;

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	/**
	 * Default constructor
	 */
	public OptionInfo() {
	}

	/**
	 * Constructor
	 * 
	 * @param value
	 * @param text
	 * @param selected
	 */
	public OptionInfo(String value, String text, boolean selected) {
		// Set properties
		this.value = value;
		this.text = text;
		this.selected = selected;
	}

	/**
	 * Constructor
	 * 
	 * @param value
	 * @param text
	 * @param selected
	 * @param id
	 */
	public OptionInfo(String value, String text, boolean selected, String id) {
		// Set properties
		this.value = value;
		this.text = text;
		this.selected = selected;
		this.id = id;
	}

	//Add new constructor with value By Amani on 12/10/2013 START
	/**
	 * Constructor
	 * 
	 * @param value
	 * @param text
	 * @param selected
	 * @param id
	 * @param type
	 */
	public OptionInfo(String type, String text, boolean selected, String id, String value) {
		// Set properties		
		this.type = type;
		this.text = text;
		this.selected = selected;
		this.id = id;
		this.value = value;
	}
        //Add new constructor with value By Amani on 12/10/2013 END

	/**
	 * Constructor
	 * 
	 * @param value
	 * @param text
	 * @param selected
	 */
	public OptionInfo(String value, String text,String label, boolean selected) {
		// Set properties
		this.value = value;
		this.text = text;
		this.label=label;
		this.selected = selected;
	}




	/**
	 * Getter method of value
	 * 
	 * @return the value
	 */
	public String getValue() {
		return value;
	}

	/**
	 * Setter method of value
	 * 
	 * @param value
	 *            the value to set
	 */
	public void setValue(String value) {
		this.value = value;
	}

	/**
	 * Getter method of text
	 * 
	 * @return the text
	 */
	public String getText() {
		return text;
	}

	/**
	 * Setter method of text
	 * 
	 * @param text
	 *            the text to set
	 */
	public void setText(String text) {
		this.text = text;
	}

	/**
	 * Getter method of selected
	 * 
	 * @return the selected
	 */
	public boolean isSelected() {
		return selected;
	}

	/**
	 * Setter method of selected
	 * 
	 * @param selected
	 *            the selected to set
	 */
	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	/**
	 * Getter method of id
	 * 
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * Setter method of id
	 * 
	 * @param id
	 *            the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

        public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public HashMap<String, String> getParams() {
		return params;
	}

	public void setParams(HashMap<String, String> params) {
		this.params = params;
	}
}