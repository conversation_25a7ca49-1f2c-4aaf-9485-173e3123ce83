package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;


/**
 * <p>
 * Class used to configure the different conditions of rule that will be used by
 * <b>RulesDefiniton</b>
 * </p>
 * 
 * <AUTHOR>
 * @version V 1.0.2
 * 
 */
public class RuleConditions extends BaseObject {

	
	/**
	 * long variable to hold serial versionId
	 */
	private static final long serialVersionUID = 1L;
	
	// Variable for Id
	public Id id;
	private String fieldName;
	private String operatorId;
	private String localValue;
	private String fieldValue;
	private String dataType;
	private String profileField;
	private String tableName;
	private String nextCondition;
	private String profileFieldValue;
	private String fieldCode;
	private Integer typeEnum ;
	private String label;
	private String allCondition;
	private String isActive = "Y";
	
	
	// Creates an instance for Hashtable
	public static Hashtable<String,String> logTable = new Hashtable<String, String>();
	// Static block for logs
	static {
		logTable.put("ruleId", "RULE_ID");
		logTable.put("conditionId", "CONDITION_ID");
		logTable.put("fieldName", "FIELD_NAME");
		logTable.put("operatorId", "OPERATOR_ID");
		logTable.put("localValue", "LOCAL_VALUE");
		logTable.put("fieldValue", "FIELD_VALUE");
		logTable.put("dataType", "DATA_TYPE");
		logTable.put("profileField", "PROFILE_FIELD");
		logTable.put("tableName", "TABLE_NAME");
		logTable.put("nextCondition", "NEXT_CONDITION");
		logTable.put("profileFieldValue", "PROFILE_FIELD_VALUE");
	}
	
	@SuppressWarnings("serial")
	public static class Id extends BaseObject {

		// Variable Integer to hold ruleId
		private Integer ruleId;
		// Variable Integer to hold conditionId
		private Integer conditionId;
		/**
		 * @return the ruleId
		 */
		public Integer getRuleId() {
			return ruleId;
		}
		/**
		 * @param ruleId the ruleId to set
		 */
		public void setRuleId(Integer ruleId) {
			this.ruleId = ruleId;
		}
		/**
		 * @return the conditionId
		 */
		public Integer getConditionId() {
			return conditionId;
		}
		/**
		 * @param conditionId the conditionId to set
		 */
		public void setConditionId(Integer conditionId) {
			this.conditionId = conditionId;
		}

	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the fieldName
	 */
	public String getFieldName() {
		return fieldName;
	}

	/**
	 * @param fieldName the fieldName to set
	 */
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	/**
	 * @return the operatorId
	 */
	public String getOperatorId() {
		return operatorId;
	}

	/**
	 * @param operatorId the operatorId to set
	 */
	public void setOperatorId(String operatorId) {
		this.operatorId = operatorId;
	}

	/**
	 * @return the localValue
	 */
	public String getLocalValue() {
		return localValue;
	}

	/**
	 * @param localValue the localValue to set
	 */
	public void setLocalValue(String localValue) {
		this.localValue = localValue;
	}

	/**
	 * @return the fieldValue
	 */
	public String getFieldValue() {
		return fieldValue;
	}

	/**
	 * @param fieldValue the fieldValue to set
	 */
	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}

	/**
	 * @return the dataType
	 */
	public String getDataType() {
		return dataType;
	}

	/**
	 * @param dataType the dataType to set
	 */
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	/**
	 * @return the profileField
	 */
	public String getProfileField() {
		return profileField;
	}

	/**
	 * @param profileField the profileField to set
	 */
	public void setProfileField(String profileField) {
		this.profileField = profileField;
	}

	/**
	 * @return the tableName
	 */
	public String getTableName() {
		return tableName;
	}

	/**
	 * @param tableName the tableName to set
	 */
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	/**
	 * @return the nextCondition
	 */
	public String getNextCondition() {
		return nextCondition;
	}

	/**
	 * @param nextCondition the nextCondition to set
	 */
	public void setNextCondition(String nextCondition) {
		this.nextCondition = nextCondition;
	}


	/**
	 * @return the profileFieldValue
	 */
	public String getProfileFieldValue() {
		return profileFieldValue;
	}

	/**
	 * @param profileFieldValue the profileFieldValue to set
	 */
	public void setProfileFieldValue(String profileFieldValue) {
		this.profileFieldValue = profileFieldValue;
	}
	
	
	

	/**
	 * @return the fieldCode
	 */
	public String getFieldCode() {
		return fieldCode;
	}

	/**
	 * @param fieldCode the fieldCode to set
	 */
	public void setFieldCode(String fieldCode) {
		this.fieldCode = fieldCode;
	}

	/**
	 * @param id
	 * @param fieldName
	 * @param operatorId
	 * @param localValue
	 * @param fieldValue
	 * @param dataType
	 * @param profileField
	 * @param tableName
	 * @param nextCondition
	 * @param profileFieldValue
	 */
	public RuleConditions(Id id, String fieldName, String operatorId,
			String localValue, String fieldValue, String dataType,
			String profileField, String tableName, String nextCondition, String profileFieldValue) {
		super();
		this.id = id;
		this.fieldName = fieldName;
		this.operatorId = operatorId;
		this.localValue = localValue;
		this.fieldValue = fieldValue;
		this.dataType = dataType;
		this.profileField = profileField;
		this.tableName = tableName;
		this.nextCondition = nextCondition;
		this.profileFieldValue = profileFieldValue;
	}

	/**
	 * 
	 */
	public RuleConditions() {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * @return the typeEnum
	 */
	public Integer getTypeEnum() {
		return typeEnum;
	}

	/**
	 * @param typeEnum the typeEnum to set
	 */
	public void setTypeEnum(Integer typeEnum) {
		this.typeEnum = typeEnum;
	}

	/**
	 * @return the label
	 */
	public String getLabel() {
		return label;
	}

	/**
	 * @param label the label to set
	 */
	public void setLabel(String label) {
		this.label = label;
	}

	public String getAllCondition() {
		return allCondition;
	}

	public void setAllCondition(String allCondition) {
		this.allCondition = allCondition;
	}

	public String getIsActive() {
		return isActive;
	}

	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
	
	


}
