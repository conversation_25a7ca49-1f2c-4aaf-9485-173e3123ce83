/**
 * @(#)SelectInfo.java / 1.0 / 18 Aug 2009 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;
import java.util.List;

/**
 * SelectInfo.java
 * 
 * This java bean has getters & setters for form select(dropdown) control
 * 
 * <AUTHOR>
 * @version New SMART-1.0
 * @date 18 Aug 2009
 */
public class SelectInfo implements Serializable{

	// Select control id
	private String id = null;
	// Select control options
	private List<OptionInfo> options = null;

	private String selectData = null;

	private String visualType = "C";

	/**
	 * Default constructor
	 */
	public SelectInfo() {
	}

	/**
	 * selectData is the whole select data in string format
	 */
	public SelectInfo(String selectData) {
		this.selectData = selectData;
	}

	
	/**
	 * Constructor
	 * 
	 * @param id
	 * @param options
	 */
	public SelectInfo(String id, List<OptionInfo> options) {
		// set properties
		this.id = id;
		this.options = options;
	}

	/**
	 * Getter method of id
	 * 
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * Setter method of id
	 * 
	 * @param id
	 *            the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * Getter method of options
	 * 
	 * @return the options
	 */
	public List<OptionInfo> getOptions() {
		return options;
	}

	/**
	 * Setter method of options
	 * 
	 * @param options
	 *            the options to set
	 */
	public void setOptions(List<OptionInfo> options) {
		this.options = options;
	}
	
	/**
	 * Getter method for selectData
	 * @return selectData: the XML representation of the select
	 */
	public String getSelectData() {
		return selectData;
	}

	/**
	 * Setter method for selectData
	 * @param selectData :XML representation of the select
	 */
	public void setSelectData(String selectData) {
		this.selectData = selectData;
	}

	public String getVisualType() {
		return visualType;
	}

	public void setVisualType(String visualType) {
		this.visualType = visualType;
	}
	
	
}