/*
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.pcm.maintenance.model;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * 
 * This class has getters and setters related to PCMCurrency Details<br>
 */
public class ListChangesCategory implements Serializable{
	private ArrayList<CategoryRule> listCategoryRuleDelete;
	private ArrayList<CategoryRule> listCategoryRuleUpdate;
	private ArrayList<CategoryRule> listCategoryRuleAdd ;

	public ListChangesCategory() {
	}

	public ListChangesCategory( ArrayList<CategoryRule> listCategoryRuleDelete,
			 ArrayList<CategoryRule> listCategoryRuleUpdate, ArrayList<CategoryRule> listCategoryRuleAdd) {
		this.listCategoryRuleDelete = listCategoryRuleDelete;
		this.listCategoryRuleUpdate = listCategoryRuleUpdate;
		this.listCategoryRuleAdd = listCategoryRuleAdd;
	}

	public ArrayList<CategoryRule> getListCategoryRuleDelete() {
		return listCategoryRuleDelete;
	}

	public void setListCategoryRuleDelete(ArrayList<CategoryRule> listCategoryRuleDelete) {
		this.listCategoryRuleDelete = listCategoryRuleDelete;
	}

	public ArrayList<CategoryRule> getListCategoryRuleUpdate() {
		return listCategoryRuleUpdate;
	}

	public void setListCategoryRuleUpdate(ArrayList<CategoryRule> listCategoryRuleUpdate) {
		this.listCategoryRuleUpdate = listCategoryRuleUpdate;
	}

	public ArrayList<CategoryRule> getListCategoryRuleAdd() {
		return listCategoryRuleAdd;
	}

	public void setListCategoryRuleAdd(ArrayList<CategoryRule> listCategoryRuleAdd) {
		this.listCategoryRuleAdd = listCategoryRuleAdd;
	}



}
