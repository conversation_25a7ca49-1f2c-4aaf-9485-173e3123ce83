/**
 *
 */
package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;

/**
 * Class used for the concept of multi-lingual
 *
 * <AUTHOR> Tunisia - Ezzeddine
 * @version V 1.0.2 January 2009
 *
 */
public class TypeValues extends BaseObject implements org.swallow.model.AuditComponent {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Id id = new Id();
	private String modif;
	private String trans;
	private String code;
	private Integer labelTextId;
	private String labelPlus;
	private String typeCode;
	private String typeEnum;
	private String squery;
	private String labelFromDict;
	private String tableName;
	private String columnValueUpper;
	private String internalLabel;
	private String typeList;
	private String macroColumn;
	private String hiddenOperator;
	public static String className = "TypeValues";
	public static String module_Id = "SYSTEM";
	/* Creates an instance for HashTable */
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();

	/* Static block for logs */
	static {
		logTable.put("typeId", "TYPE_ID");
		logTable.put("valueId", "VALUE_ID");
		logTable.put("modif", "MODIF");
		logTable.put("trans", "TRANS");
		logTable.put("code", "CODE");
		logTable.put("labelTextId", "LABEL_TEXT_ID");
		logTable.put("labelPlus", "LABEL_PLUS");
		logTable.put("typeCode", "TYPE_CODE");
		logTable.put("typeEnum", "TYPE_ENUM");
		logTable.put("squery", "squery");
		logTable.put("tableName", "table_name");
		logTable.put("columnValueUpper", "COLUMN_VALUE_UPPER");
		logTable.put("internalLabel", "INTERNAL_LABEL");
		logTable.put("typeList", "TYPE_LIST");
		logTable.put("macroColumn", "macro_column");
		logTable.put("hiddenOperator", "hidden_operator");
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the modif
	 */
	public String getModif() {
		return modif;
	}

	/**
	 * @param modif the modif to set
	 */
	public void setModif(String modif) {
		this.modif = modif;
	}

	/**
	 * @return the trans
	 */
	public String getTrans() {
		return trans;
	}

	/**
	 * @param trans the trans to set
	 */
	public void setTrans(String trans) {
		this.trans = trans;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the labelTextId
	 */
	public Integer getLabelTextId() {
		return labelTextId;
	}

	/**
	 * @param labelTextId the labelTextId to set
	 */
	public void setLabelTextId(Integer labelTextId) {
		this.labelTextId = labelTextId;
	}

	/**
	 * @return the labelPlus
	 */
	public String getLabelPlus() {
		return labelPlus;
	}

	/**
	 * @param labelPlus the labelPlus to set
	 */
	public void setLabelPlus(String labelPlus) {
		this.labelPlus = labelPlus;
	}

	/**
	 * @return the typeCode
	 */
	public String getTypeCode() {
		return typeCode;
	}

	/**
	 * @param typeCode the typeCode to set
	 */
	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}

	/**
	 * @return the typeEnum
	 */
	public String getTypeEnum() {
		return typeEnum;
	}

	/**
	 * @param typeEnum the typeEnum to set
	 */
	public void setTypeEnum(String typeEnum) {
		this.typeEnum = typeEnum;
	}

	/**
	 * @return the squery
	 */
	public String getSquery() {
		return squery;
	}

	/**
	 * @param squery the squery to set
	 */
	public void setSquery(String squery) {
		this.squery = squery;
	}

	/**
	 *
	 */
	public TypeValues() {
		// TODO Auto-generated constructor stub
	}

	/**
	 * @return the labelFromDict
	 */
	public String getLabelFromDict() {
		return labelFromDict;
	}

	/**
	 * @param labelFromDict the labelFromDict to set
	 */
	public void setLabelFromDict(String labelFromDict) {
		this.labelFromDict = labelFromDict;
	}

	/**
	 * @return the tableName
	 */
	public String getTableName() {
		return tableName;
	}

	/**
	 * @param tableName the tableName to set
	 */
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	/**
	 * @return the columnValueUpper
	 */
	public String getColumnValueUpper() {
		return columnValueUpper;
	}

	/**
	 * @return the internalLabel
	 */
	public String getInternalLabel() {
		return internalLabel;
	}

	/**
	 * @param internalLabel the internalLabel to set
	 */
	public void setInternalLabel(String internalLabel) {
		this.internalLabel = internalLabel;
	}

	/**
	 * @param columnValueUpper the columnValueUpper to set
	 */
	public void setColumnValueUpper(String columnValueUpper) {
		this.columnValueUpper = columnValueUpper;
	}

	/**
	 * @return the typeList
	 */
	public String getTypeList() {
		return typeList;
	}

	/**
	 * @param typeList the typeList to set
	 */
	public void setTypeList(String typeList) {
		this.typeList = typeList;
	}

	/**
	 * @return the macroColumn
	 */
	public String getMacroColumn() {
		return macroColumn;
	}

	/**
	 * @param macroColumn the macroColumn to set
	 */
	public void setMacroColumn(String macroColumn) {
		this.macroColumn = macroColumn;
	}

	/**
	 * @return the hiddenOperator
	 */
	public String getHiddenOperator() {
		return hiddenOperator;
	}

	/**
	 * @param hiddenOperator the hiddenOperator to set
	 */
	public void setHiddenOperator(String hiddenOperator) {
		this.hiddenOperator = hiddenOperator;
	}

	/**
	 * @param id
	 * @param modif
	 * @param trans
	 * @param code
	 * @param labelTextId
	 * @param labelPlus
	 * @param typeCode
	 * @param typeEnum
	 */
	public TypeValues(Id id, String modif, String trans, String code, Integer labelTextId, String labelPlus,
			String typeCode, String typeEnum) {
		this.id = id;
		this.modif = modif;
		this.trans = trans;
		this.code = code;
		this.labelTextId = labelTextId;
		this.labelPlus = labelPlus;
		this.typeCode = typeCode;
		this.typeEnum = typeEnum;
	}

	public static class Id extends BaseObject {

		/**
		 *
		 */
		private static final long serialVersionUID = 1L;
		private Integer typeId;
		private Integer valueId;

		/**
		 * @return the typeId
		 */
		public Integer getTypeId() {
			return typeId;
		}

		/**
		 * @param typeId the typeId to set
		 */
		public void setTypeId(Integer typeId) {
			this.typeId = typeId;
		}

		/**
		 * @return the valueId
		 */
		public Integer getValueId() {
			return valueId;
		}

		/**
		 * @param valueId the valueId to set
		 */
		public void setValueId(Integer valueId) {
			this.valueId = valueId;
		}

		/**
		 *
		 */
		public Id() {
			// TODO Auto-generated constructor stub
		}

		/**
		 * @param typeId
		 * @param valueId
		 */
		public Id(Integer typeId, Integer valueId) {
			this.typeId = typeId;
			this.valueId = valueId;
		}

	}

}
