/*
 * @(#)CategoryRule.java 1.0 2019-02-25
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;
import java.util.Hashtable;

import org.swallow.model.BaseObject;



/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class CategoryRule extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("categoryRuleId","Category Rule Id");
		logTable.put("categoryRuleName","Category Rule Name");
		logTable.put("ordinal","Processing Order");
		logTable.put("applyOnlyToSource","Apply to Source");
		logTable.put("applyOnlyToEntity","Apply to Entity");
		logTable.put("applyOnlyToCcy","Apply to Ccy");
		logTable.put("ruleId","Rule ID");
	}

	private Long categoryRuleId;
	private String categoryId;
	private String categoryRuleName;
	private Integer ordinal;
	private String applyOnlyToSource;
	private String applyOnlyToCcy;
	private String applyOnlyToEntity;
	private Integer ruleId;
	private RulesDefinition rulesDefinition = null;
	public final static String CLASSNAME = "CategoryRule";
	
	public Long getCategoryRuleId() {
		return categoryRuleId;
	}
	public void setCategoryRuleId(Long categoryRuleId) {
		this.categoryRuleId = categoryRuleId;
	}

	public String getCategoryId() {
		return categoryId;
	}
	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}
	public String getCategoryRuleName() {
		return categoryRuleName;
	}
	public void setCategoryRuleName(String categoryRuleName) {
		this.categoryRuleName = categoryRuleName;
	}
	
	public Integer getOrdinal() {
		return ordinal;
	}
	public void setOrdinal(Integer ordinal) {
		this.ordinal = ordinal;
	}
	public String getApplyOnlyToSource() {
		return applyOnlyToSource;
	}
	public void setApplyOnlyToSource(String applyOnlyToSource) {
		this.applyOnlyToSource = applyOnlyToSource;
	}
	public String getApplyOnlyToCcy() {
		return applyOnlyToCcy;
	}
	public void setApplyOnlyToCcy(String applyOnlyToCcy) {
		this.applyOnlyToCcy = applyOnlyToCcy;
	}
	public String getApplyOnlyToEntity() {
		return applyOnlyToEntity;
	}
	public void setApplyOnlyToEntity(String applyOnlyToEntity) {
		this.applyOnlyToEntity = applyOnlyToEntity;
	}
	public Integer getRuleId() {
		return ruleId;
	}
	public void setRuleId(Integer ruleId) {
		this.ruleId = ruleId;
	}
	public RulesDefinition getRulesDefinition() {
		return rulesDefinition;
	}
	public void setRulesDefinition(RulesDefinition rulesDefinition) {
		this.rulesDefinition = rulesDefinition;
	}
}
