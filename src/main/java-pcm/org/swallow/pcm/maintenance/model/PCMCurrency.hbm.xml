<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.PCMCurrency" table="PC_CCY" >
		<composite-id class="org.swallow.pcm.maintenance.model.PCMCurrency$Id" name="id" unsaved-value="any">
		   <key-property name="currencyCode" access="field" column="CURRENCY_CODE"/>
		</composite-id>
		<property name="ordinal" column="ORDINAL" not-null="false"/>
		<property name="displayMultiplier" column="DISPLAY_MULTIPLIER" not-null="false"/>
<!-- 		<property name="ccyEntityForTimeframe" column="S_CCY_ENTITY_FOR_TIMEFRAME" not-null="false"/> -->
		<property name="largeAmountThreshold" column="LARGE_AMOUNT_THRESHOLD" not-null="false"/>	
    </class>
</hibernate-mapping>