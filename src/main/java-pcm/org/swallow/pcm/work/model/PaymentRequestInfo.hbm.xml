<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.PaymentRequestInfo" table="VW_PC_PAYMENT_REQUEST_INFO_ALL">
		
		<id name="payReqId" type="java.lang.Long">
			<column name="PAYREQ_ID" />
			<generator class="foreign">
				<param name="property">paymentRequestInfo</param>
			</generator>
		</id>
    	
		<property name="paymentMessage" column="PAYMENT_MESSAGE" not-null="false"/>
		<property name="orderingCustName" column="ORDERING_CUST_NAME" not-null="false"/>
		<property name="orderingInstName" column="ORDERING_INST_NAME" not-null="false"/>
		<property name="sendersCorresName" column="SENDERS_CORRES_NAME" not-null="false"/>
		<property name="receiversCorresName" column="RECEIVERS_CORRES_NAME" not-null="false"/>
		<property name="thirdReimbsmntName" column="THIRD_REIMBSMNT_NAME" not-null="false"/>
		<property name="intermediaryName" column="INTERMEDIARY_NAME" not-null="false"/>
		<property name="accountWithInstName" column="ACCOUNT_WITH_INST_NAME" not-null="false"/>
		<property name="beneficiaryInstName" column="BENEFICIARY_INST_NAME" not-null="false"/>				
		<property name="beneficiaryCustName" column="BENEFICIARY_CUST_NAME" not-null="false"/>	
		<property name="deliveryAgentName" column="DELIVERY_AGENT_NAME" not-null="false"/>
		<property name="deliverersCustodianName" column="DELIVERERS_CUSTODIAN_NAME" not-null="false"/>
		<property name="placeOfSettlementName" column="PLACE_OF_SETTLEMENT_NAME" not-null="false"/>	
		<property name="sellerName" column="SELLER_NAME" not-null="false"/>	
	</class>
</hibernate-mapping>