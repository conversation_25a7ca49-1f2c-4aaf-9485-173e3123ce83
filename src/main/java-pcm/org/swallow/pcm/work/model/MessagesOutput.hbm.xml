<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.MessagesOutput" table="VW_PC_MESSAGES_OUTPUT_ALL">
    	<id name="messageOutId" type="long" column="MESSAGE_OUT_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_MESSAGES_OUTPUT</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
    	
		<property name="messageArcId" column="MESSAGE_ARC_ID" not-null="false"/>
		<property name="payreqId" column="PAYREQ_ID" not-null="false"/>
		<property name="sourceId" column="SOURCE_ID" not-null="false"/>
		<property name="responsesStatus" column="RESPONSES_STATUS" not-null="false"/>
		<property name="messageBody" column="MESSAGE_BODY" not-null="false"/>
		<property name="sentDate" column="SENT_DATE" not-null="false"/>
	</class>
</hibernate-mapping>