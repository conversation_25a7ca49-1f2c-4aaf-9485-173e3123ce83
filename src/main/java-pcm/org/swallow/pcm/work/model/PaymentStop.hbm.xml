<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.PaymentStop" table="VW_PC_PAYMENT_STOP_ALL">
    	<composite-id class="org.swallow.pcm.work.model.PaymentStop$Id" name="id" unsaved-value="any">
		   <key-property name="payReqId" access="field" column="PAYREQ_ID"/>
		   <key-property name="stopRuleId" access="field" column="STOP_RULE_ID"/>
		</composite-id>
    	
		<property name="stopDate" column="STOP_DATE" not-null="false"/>
		<property name="unstopDate" column="UNSTOP_DATE" not-null="true"/>
		<property name="unstopBy" column="UNSTOP_BY" not-null="false"/>
			
		<many-to-one name="stopRule" class="org.swallow.pcm.maintenance.model.StopRule" lazy="false"
            column="STOP_RULE_ID" insert="false" update="false"
            cascade="all" />
	</class>
</hibernate-mapping>