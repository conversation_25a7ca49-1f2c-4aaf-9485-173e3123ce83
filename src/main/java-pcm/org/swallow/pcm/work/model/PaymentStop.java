/*
 * @(#)PaymentStop.java 1.0 2019-05-23
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
import org.swallow.pcm.maintenance.model.StopRule;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class PaymentStop extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.payReqId","payment Request Id");
	}
	public final static String CLASSNAME = "PaymentStop";
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private Date stopDate;
	private Date unstopDate;
	private String unstopBy;
	private StopRule stopRule;
	
	public Id getId() {
		return id;
	}
	
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private Long payReqId;
		private String stopRuleId;

		public Id(Long payReqId) {
			this.payReqId = payReqId;
		}

		public Id() {
		}

		public Long getPayReqId() {
			return payReqId;
		}

		public void setPayReqId(Long payreqId) {
			this.payReqId = payreqId;
		}
		
		public String getStopRuleId() {
			return stopRuleId;
		}

		public void setStopRuleId(String stopRuleId) {
			this.stopRuleId = stopRuleId;
		}

	}


	public Date getStopDate() {
		return stopDate;
	}

	public void setStopDate(Date stopDate) {
		this.stopDate = stopDate;
	}

	public Date getUnstopDate() {
		return unstopDate;
	}

	public void setUnstopDate(Date unstopDate) {
		this.unstopDate = unstopDate;
	}

	public String getUnstopBy() {
		return unstopBy;
	}

	public void setUnstopBy(String unstopBy) {
		this.unstopBy = unstopBy;
	}

	public StopRule getStopRule() {
		return stopRule;
	}

	public void setStopRule(StopRule stopRule) {
		this.stopRule = stopRule;
	}

}
