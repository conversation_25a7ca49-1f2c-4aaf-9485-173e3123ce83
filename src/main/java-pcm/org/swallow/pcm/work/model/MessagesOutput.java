/*
 * @(#)MessagesOutput.java 1.0 2019-05-23
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class MessagesOutput extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("messageOutId","message Output Id");
	}
	
	private static final long serialVersionUID = 1L;
	private Long messageOutId;
	private Long messageArcId;
	private Long payreqId;
	private String sourceId;
	private String responsesStatus;
	private String messageBody;
	private Date sentDate;
	
	public Long getMessageOutId() {
		return messageOutId;
	}
	public void setMessageOutId(Long messageOutId) {
		this.messageOutId = messageOutId;
	}
	public Long getMessageArcId() {
		return messageArcId;
	}
	public void setMessageArcId(Long messageArcId) {
		this.messageArcId = messageArcId;
	}
	public Long getPayreqId() {
		return payreqId;
	}
	public void setPayreqId(Long payreqId) {
		this.payreqId = payreqId;
	}
	public String getSourceId() {
		return sourceId;
	}
	public void setSourceId(String sourceId) {
		this.sourceId = sourceId;
	}
	public String getResponsesStatus() {
		return responsesStatus;
	}
	public void setResponsesStatus(String responsesStatus) {
		this.responsesStatus = responsesStatus;
	}
	public String getMessageBody() {
		return messageBody;
	}
	public void setMessageBody(String messageBody) {
		this.messageBody = messageBody;
	}
	public Date getSentDate() {
		return sentDate;
	}
	public void setSentDate(Date sentDate) {
		this.sentDate = sentDate;
	}
}
