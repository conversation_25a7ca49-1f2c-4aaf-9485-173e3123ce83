<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.PaymentMessage" table="VW_PC_PAYMENT_MESSAGE_ALL">
    	<id name="sequenceNo" type="long" column="SEQUENCE_NO">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_PAYMENT_MESSAGE</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
    	
		<property name="messageId" column="MESSAGE_ID" not-null="true"/>
		<property name="payreqId" column="PAYREQ_ID" not-null="true"/>
		<property name="messageStatus" column="MESSAGE_STATUS" not-null="true"/>
		<property name="inputDate" column="INPUT_DATE" not-null="true"/>
		<property name="location" column="LOCATION" />
		<property name="dbLink" column="DB_LINK" />
	</class>
</hibernate-mapping>