<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.MessageArchivePCM" table="I_MESSAGE_ARCHIVE">
    <id name="seqNo" column="SEQ_NO" unsaved-value="null">
		<generator class="assigned"/>
	</id>
		<property name="mediumType" column="MEDIUM_TYPE" not-null="false"/>	
		<property name="mediumInstance" column="MEDIUM_INSTANCE" not-null="false"/>
		<property name="messageDirection" column="MESSAGE_DIRECTION" not-null="false"/>	
		<property name="messageFormat" column="MESSAGE_FORMAT" not-null="false"/>	
		<property name="messageType" column="MESSAGE_TYPE" not-null="false"/>	
		<property name="startTime" column="START_TIME" not-null="false"/>
		<property name="finishTime" column="FINISH_TIME" not-null="false"/>	
		<property name="status" column="STATUS" not-null="false"/>
		<property name="statusNotes" column="STATUS_NOTES" not-null="false"/>	
		<property name="messageBody" column="MESSAGE_BODY" not-null="false" type="java.sql.Clob"/>
		<property name="messageCategory" column="MESSAGE_CATEGORY" not-null="false"/>	
		<property name="transactionType" column="TRANSACTION_TYPE" not-null="false"/>	
		<property name="transactionReferenceNumber1" column="TRANSACTION_REFERENCE_NUMBER1" not-null="false"/>	
		<property name="transactionReferenceNumber2" column="TRANSACTION_REFERENCE_NUMBER2" not-null="false"/>
		<property name="transactionReferenceNumber3" column="TRANSACTION_REFERENCE_NUMBER3" not-null="false"/>	
		<property name="transactionReferenceNumber4" column="TRANSACTION_REFERENCE_NUMBER4" not-null="false"/>	
		<property name="valueDate" column="VALUE_DATE" not-null="false"/>	
		<property name="currencyCode1" column="CURRENCY_CODE1" not-null="false"/>
		<property name="currencyCode2" column="CURRENCY_CODE2" not-null="false"/>	
		<property name="currencyCode3" column="CURRENCY_CODE3" not-null="false"/>	
		<property name="currencyCode4" column="CURRENCY_CODE4" not-null="false"/>	
		<property name="amount1" column="AMOUNT_1" type="java.lang.Double" not-null="false"/>
		<property name="amount2" column="AMOUNT_2" type="java.lang.Double" not-null="false"/>	
		<property name="amount3" column="AMOUNT_3" type="java.lang.Double" not-null="false"/>
		<property name="amount4" column="AMOUNT_4" type="java.lang.Double"  not-null="false"/>	
		<property name="amountSign1" column="AMOUNT_SIGN_1" not-null="false"/>
		<property name="amountSign2" column="AMOUNT_SIGN_2" not-null="false"/>	
		<property name="amountSign3" column="AMOUNT_SIGN_3" not-null="false"/>	
		<property name="amountSign4" column="AMOUNT_SIGN_4" not-null="false"/>	
		<property name="rate1" column="RATE_1" type="java.lang.Integer"  not-null="false"/>
		<property name="rate2" column="RATE_2" type="java.lang.Integer" not-null="false"/>	
		<property name="rate3" column="RATE_3" type="java.lang.Integer" not-null="false"/>
		<property name="rate4" column="RATE_4" type="java.lang.Integer"  not-null="false"/>
		<property name="orderingInstitution" column="ORDERING_INSTITUTION" not-null="false"/>	
		<property name="sendersCorrespondent" column="SENDERS_CORRESPONDENT" not-null="false"/>
		<property name="receiversCorrespondent" column="RECEIVERS_CORRESPONDENT" not-null="false"/>
		<property name="accountWithInstitution" column="ACCOUNT_WITH_INSTITUTION" not-null="false"/>	
		<property name="senderToReceiverInformation" column="SENDER_TO_RECEIVER_INFORMATION" not-null="false"/>
		<property name="OrderingCustomer" column="ORDERING_CUSTOMER" not-null="false"/>
		<property name="party1" column="PARTY_1" not-null="false"/>	
		<property name="party2" column="PARTY_2" not-null="false"/>
		<property name="party3" column="PARTY_3" not-null="false"/>	
		<property name="party4" column="PARTY_4" not-null="false"/>
		<property name="pcPaymentId" column="PC_PAYMENT_ID" not-null="false"/>
		<property name="accountId" column="ACCOUNT_ID" not-null="false"/>
			
			
    </class>
</hibernate-mapping>