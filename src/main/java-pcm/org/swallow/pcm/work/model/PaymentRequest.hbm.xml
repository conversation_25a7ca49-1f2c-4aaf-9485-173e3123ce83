<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.PaymentRequest" table="VW_PC_PAYMENT_REQUEST_ALL">
    	<id name="payReqId" type="long" column="PAYREQ_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_PAYMENT_REQUEST</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
    	
		<property name="sourceId" column="SOURCE_ID" not-null="true"/>
		<property name="sourceReference" column="SOURCE_REFERENCE" not-null="false"/>
		<property name="status" column="STATUS" not-null="false"/>
		<property name="inputDate" column="INPUT_DATE" not-null="false"/>
		<property name="creationMsgId" column="CREATION_MSG_ID" not-null="false"/>
		<property name="cancelDate" column="CANCEL_DATE" not-null="false"/>
		<property name="cancellationMsgId" column="CANCELLATION_MSG_ID" not-null="false"/>
		<property name="releaseDate" column="RELEASE_DATE" not-null="false"/>
		<property name="confirmReleaseMsgId" column="CONFIRM_RELEASE_MSG_ID" not-null="false"/>				
		<property name="confirmReleaseDate" column="CONFIRM_RELEASE_DATE" not-null="false"/>	
		<property name="categoryId" column="CATEGORY_ID" not-null="false"/>
		<property name="categoryRuleName" column="CATEGORY_RULE_NAME" not-null="false"/>
		<property name="requiredReleaseTime" column="REQUIRED_RELEASE_TIME" not-null="false"/>	
		<property name="stopDate" column="STOP_DATE" not-null="false"/>	
		<property name="unstopDate" column="UNSTOP_DATE" not-null="false"/>	
		<property name="blockReasonCode" column="BLOCK_REASON_CODE" not-null="false"/>
		<property name="blockDate" column="BLOCK_DATE" not-null="false"/>	
		<property name="hostId" column="HOST_ID" not-null="false"/>
		<property name="entityId" column="ENTITY_ID" not-null="false"/>
		<property name="accountId" column="ACCOUNT_ID" not-null="false"/>	
		<property name="currencyCode" column="CURRENCY_CODE" not-null="false"/>
		<property name="valueDate" column="VALUE_DATE" not-null="false"/>
		<property name="amount" column="AMOUNT" not-null="false"/>
		<property name="cutoffTime" column="CUTOFF_TIME" not-null="false"/>
		<property name="entitySubId" column="ENTITY_SUB_ID" not-null="false"/>
		<property name="messageType" column="MESSAGE_TYPE" not-null="false"/>
		<property name="action" column="ACTION" not-null="false"/>
		<property name="originatingSystem" column="ORIGINATING_SYSTEM" not-null="false"/>
		<property name="paymentType" column="PAYMENT_TYPE" not-null="false"/>
		<property name="department" column="DEPARTMENT" not-null="false"/>
		<property name="sourceReqReleaseTime" column="SOURCE_REQ_RELEASE_TIME" not-null="false"/>
		<property name="sourceUrgencyIndicator" column="SOURCE_URGENCY_INDICATOR" not-null="false"/>
		<property name="gpi" column="GPI" not-null="false"/>
		<property name="foReference" column="FO_REFERENCE" not-null="false"/>
		<property name="boReference" column="BO_REFERENCE" not-null="false"/>
		<property name="paymentReference" column="PAYMENT_REFERENCE" not-null="false"/>	
		<property name="relatedReference" column="RELATED_REFERENCE" not-null="false"/>
		<property name="senderBic" column="SENDER_BIC" not-null="false"/>
		<property name="receiverBic" column="RECEIVER_BIC" not-null="false"/>
		<property name="orderingCustBic" column="ORDERING_CUST_BIC" not-null="false"/>
		<property name="orderingCustAccount" column="ORDERING_CUST_ACCOUNT" not-null="false"/>
		<property name="orderingInstBic" column="ORDERING_INST_BIC" not-null="false"/>
		<property name="orderingInstAccount" column="ORDERING_INST_ACCOUNT" not-null="false"/>
		<property name="sendersCorresBic" column="SENDERS_CORRES_BIC" not-null="false"/>
		<property name="sendersCorresAccount" column="SENDERS_CORRES_ACCOUNT" not-null="false"/>
		<property name="receiversCorresBic" column="RECEIVERS_CORRES_BIC" not-null="false"/>
		<property name="receiversCorresAccount" column="RECEIVERS_CORRES_ACCOUNT" not-null="false"/>
		<property name="thirdReimbsmntBic" column="THIRD_REIMBSMNT_BIC" not-null="false"/>
		<property name="thirdReimbsmntAccount" column="THIRD_REIMBSMNT_ACCOUNT" not-null="false"/>
		<property name="intermediaryBic" column="INTERMEDIARY_BIC" not-null="false"/>
		<property name="intermediaryAccount" column="INTERMEDIARY_ACCOUNT" not-null="false"/>
		<property name="accountWithInstBic" column="ACCOUNT_WITH_INST_BIC" not-null="false"/>
		<property name="accountWithInstAccount" column="ACCOUNT_WITH_INST_ACCOUNT" not-null="false"/>
		<property name="beneficiaryInstBic" column="BENEFICIARY_INST_BIC" not-null="false"/>
		<property name="beneficiaryInstAccount" column="BENEFICIARY_INST_ACCOUNT" not-null="false"/>
		<property name="beneficiaryCustBic" column="BENEFICIARY_CUST_BIC" not-null="false"/>
		<property name="beneficiaryCustAccount" column="BENEFICIARY_CUST_ACCOUNT" not-null="false"/>
		<property name="senderReceiverInfo" column="SENDER_RECEIVER_INFO" not-null="false"/>
		<property name="deliveryAgentBic" column="DELIVERY_AGENT_BIC" not-null="false"/>
		<property name="deliveryAgentAccount" column="DELIVERY_AGENT_ACCOUNT" not-null="false"/>
		<property name="deliverersCustodianBic" column="DELIVERERS_CUSTODIAN_BIC" not-null="false"/>
		<property name="deliverersCustodianAccount" column="DELIVERERS_CUSTODIAN_ACCOUNT" not-null="false"/>
		<property name="placeOfSettlementBic" column="PLACE_OF_SETTLEMENT_BIC" not-null="false"/>
		<property name="placeOfSettlementAccount" column="PLACE_OF_SETTLEMENT_ACCOUNT" not-null="false"/>
		<property name="sellerBic" column="SELLER_BIC" not-null="false"/>
		<property name="sellerAccount" column="SELLER_ACCOUNT" not-null="false"/>
		<property name="releaseMethod" column="RELEASE_METHOD" not-null="false"/>
		<property name="updatedBy" column="UPDATED_BY" not-null="false"/>
		<property name="location" column="LOCATION"  not-null="false"/>
		<property name="dbLink" column="DB_LINK" />
		<property name="accountGroupId" column="ACC_GRP_ID"  not-null="false"/>
		
		<one-to-one  name="paymentRequestInfo"   class="org.swallow.pcm.work.model.PaymentRequestInfo"
			cascade="save-update"></one-to-one>
	</class>
</hibernate-mapping>