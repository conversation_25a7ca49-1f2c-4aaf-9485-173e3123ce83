<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.work.model.PaymentLog" table="VW_PC_PAYMENT_LOG_ALL">
    	<id name="logSeq" type="long" column="LOG_SEQ">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_PAYMENT_LOG</param>
			<param name="increment_size">1</param>
			</generator>
		</id>
    	
		<property name="payreqId" column="PAYREQ_ID" not-null="true"/>
		<property name="logDate" column="LOG_DATE" not-null="false"/>
		<property name="logUser" column="LOG_USER" not-null="true"/>
		<property name="logDetails" column="LOG_DETAILS" not-null="false"/>
	</class>
</hibernate-mapping>