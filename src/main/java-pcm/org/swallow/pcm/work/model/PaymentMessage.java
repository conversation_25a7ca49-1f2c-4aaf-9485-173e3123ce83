/*
 * @(#)PaymentMessage.java 1.0 2019-05-27
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
import org.swallow.work.model.InputExceptionsDataModel;
import org.swallow.work.model.MessageArchive;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class PaymentMessage extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("messageOutId","message Output Id");
	}
	
	private static final long serialVersionUID = 1L;
	private Long sequenceNo; 
	private Long messageId;
	private Long payreqId;
	private String messageStatus;
	private Date inputDate;
	private String messageBody = null;
	private String location = null;
	private String dbLink = null;
	private InputExceptionsDataModel message;
	public Long getSequenceNo() {
		return sequenceNo;
	}
	public void setSequenceNo(Long sequenceNo) {
		this.sequenceNo = sequenceNo;
	}
	public Long getMessageId() {
		return messageId;
	}
	public void setMessageId(Long messageId) {
		this.messageId = messageId;
	}
	public Long getPayreqId() {
		return payreqId;
	}
	public void setPayreqId(Long payreqId) {
		this.payreqId = payreqId;
	}
	public String getMessageStatus() {
		return messageStatus;
	}
	public void setMessageStatus(String messageStatus) {
		this.messageStatus = messageStatus;
	}
	public Date getInputDate() {
		return inputDate;
	}
	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}
	public String getMessageBody() {
		return messageBody;
	}
	public void setMessageBody(String messageBody) {
		this.messageBody = messageBody;
	}
	public InputExceptionsDataModel getMessage() {
		return message;
	}
	public void setMessage(InputExceptionsDataModel message) {
		this.message = message;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getDbLink() {
		return dbLink;
	}
	public void setDbLink(String dbLink) {
		this.dbLink = dbLink;
	}
	
	
	
}
