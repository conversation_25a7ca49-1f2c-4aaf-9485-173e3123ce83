/*
 * @(#)PaymentRequest.java 1.0 2019-05-23
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class PaymentRequest extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("payReqId","payment Request Id");
	}
	public final static String CLASSNAME = "PaymentRequest";
	private static final long serialVersionUID = 1L;
	private Long payReqId;
	private String sourceId;
	private String sourceReference;
	private String status;
	private Date inputDate;
	private String creationMsgId;
	private Date cancelDate;
	private String cancellationMsgId;
	private Date releaseDate;
	private String confirmReleaseMsgId;
	private Date confirmReleaseDate;
	private String categoryId;
	private String categoryRuleName;
	private String requiredReleaseTime;
	private Date stopDate;
	private Date unstopDate;
	private String blockReasonCode;
	private Date blockDate;
	private String hostId;
	private String entityId;
	private String accountId;
	private String accountGroupId;
	private String currencyCode;
	private Date valueDate;
	private Double amount;
	private String cutoffTime;
	private String entitySubId;
	private String messageType;
	private String action;
	private String originatingSystem;
	private String paymentType;
	private String department;
	private Date sourceReqReleaseTime;
	private String sourceUrgencyIndicator;
	private String gpi;
	private String foReference;
	private String boReference;
	private String paymentReference;
	private String relatedReference;
	private String senderBic;
	private String receiverBic;
	private String orderingCustBic;
	private String orderingCustAccount;
	private String orderingInstBic;
	private String orderingInstAccount;
	private String sendersCorresBic;
	private String sendersCorresAccount;
	private String receiversCorresBic;
	private String receiversCorresAccount;
	private String thirdReimbsmntBic;
	private String thirdReimbsmntAccount;
	private String intermediaryBic;
	private String intermediaryAccount;
	private String accountWithInstBic;
	private String accountWithInstAccount;
	private String beneficiaryInstBic;
	private String beneficiaryInstAccount;
	private String beneficiaryCustBic;
	private String beneficiaryCustAccount;
	private String senderReceiverInfo;
	private String deliveryAgentBic;
	private String deliveryAgentAccount;
	private String deliverersCustodianBic;
	private String deliverersCustodianAccount;
	private String placeOfSettlementBic;
	private String placeOfSettlementAccount;
	private String sellerBic;
	private String sellerAccount;
	private String releaseMethod;
	private String updatedBy;
	private PaymentRequestInfo paymentRequestInfo = null;
	private String location;
	private String dbLink = null;

	public Long getPayReqId() {
		return payReqId;
	}

	public void setPayReqId(Long payreqId) {
		this.payReqId = payreqId;
	}
	
	public String getSourceId() {
		return sourceId;
	}

	public void setSourceId(String sourceId) {
		this.sourceId = sourceId;
	}

	public String getSourceReference() {
		return sourceReference;
	}

	public void setSourceReference(String sourceReference) {
		this.sourceReference = sourceReference;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getInputDate() {
		return inputDate;
	}

	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}

	public String getCreationMsgId() {
		return creationMsgId;
	}

	public void setCreationMsgId(String creationMsgId) {
		this.creationMsgId = creationMsgId;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public String getCancellationMsgId() {
		return cancellationMsgId;
	}

	public void setCancellationMsgId(String cancellationMsgId) {
		this.cancellationMsgId = cancellationMsgId;
	}

	public Date getReleaseDate() {
		return releaseDate;
	}

	public void setReleaseDate(Date releaseDate) {
		this.releaseDate = releaseDate;
	}

	public String getConfirmReleaseMsgId() {
		return confirmReleaseMsgId;
	}

	public void setConfirmReleaseMsgId(String confirmReleaseMsgId) {
		this.confirmReleaseMsgId = confirmReleaseMsgId;
	}

	public Date getConfirmReleaseDate() {
		return confirmReleaseDate;
	}

	public void setConfirmReleaseDate(Date confirmReleaseDate) {
		this.confirmReleaseDate = confirmReleaseDate;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getCategoryRuleName() {
		return categoryRuleName;
	}

	public void setCategoryRuleName(String categoryRuleName) {
		this.categoryRuleName = categoryRuleName;
	}

	public String getRequiredReleaseTime() {
		return requiredReleaseTime;
	}

	public void setRequiredReleaseTime(String requiredReleaseTime) {
		this.requiredReleaseTime = requiredReleaseTime;
	}

	public Date getStopDate() {
		return stopDate;
	}

	public void setStopDate(Date stopDate) {
		this.stopDate = stopDate;
	}

	public Date getUnstopDate() {
		return unstopDate;
	}

	public void setUnstopDate(Date unstopDate) {
		this.unstopDate = unstopDate;
	}

	public String getBlockReasonCode() {
		return blockReasonCode;
	}

	public void setBlockReasonCode(String blockReasonCode) {
		this.blockReasonCode = blockReasonCode;
	}

	public Date getBlockDate() {
		return blockDate;
	}

	public void setBlockDate(Date blockDate) {
		this.blockDate = blockDate;
	}

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public Date getValueDate() {
		return valueDate;
	}

	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getCutoffTime() {
		return cutoffTime;
	}

	public void setCutoffTime(String cutoffTime) {
		this.cutoffTime = cutoffTime;
	}

	public String getEntitySubId() {
		return entitySubId;
	}

	public void setEntitySubId(String entitySubId) {
		this.entitySubId = entitySubId;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getOriginatingSystem() {
		return originatingSystem;
	}

	public void setOriginatingSystem(String originatingSystem) {
		this.originatingSystem = originatingSystem;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public Date getSourceReqReleaseTime() {
		return sourceReqReleaseTime;
	}

	public void setSourceReqReleaseTime(Date sourceReqReleaseTime) {
		this.sourceReqReleaseTime = sourceReqReleaseTime;
	}

	public String getSourceUrgencyIndicator() {
		return sourceUrgencyIndicator;
	}

	public void setSourceUrgencyIndicator(String sourceUrgencyIndicator) {
		this.sourceUrgencyIndicator = sourceUrgencyIndicator;
	}

	public String getGpi() {
		return gpi;
	}

	public void setGpi(String gpi) {
		this.gpi = gpi;
	}

	public String getFoReference() {
		return foReference;
	}

	public void setFoReference(String foReference) {
		this.foReference = foReference;
	}

	public String getBoReference() {
		return boReference;
	}

	public void setBoReference(String boReference) {
		this.boReference = boReference;
	}

	public String getPaymentReference() {
		return paymentReference;
	}

	public void setPaymentReference(String paymentReference) {
		this.paymentReference = paymentReference;
	}

	public String getRelatedReference() {
		return relatedReference;
	}

	public void setRelatedReference(String relatedReference) {
		this.relatedReference = relatedReference;
	}

	public String getSenderBic() {
		return senderBic;
	}

	public void setSenderBic(String senderBic) {
		this.senderBic = senderBic;
	}

	public String getReceiverBic() {
		return receiverBic;
	}

	public void setReceiverBic(String receiverBic) {
		this.receiverBic = receiverBic;
	}

	public String getOrderingCustBic() {
		return orderingCustBic;
	}

	public void setOrderingCustBic(String orderingCustBic) {
		this.orderingCustBic = orderingCustBic;
	}

	public String getOrderingCustAccount() {
		return orderingCustAccount;
	}

	public void setOrderingCustAccount(String orderingCustAccount) {
		this.orderingCustAccount = orderingCustAccount;
	}

	public String getOrderingInstBic() {
		return orderingInstBic;
	}

	public void setOrderingInstBic(String orderingInstBic) {
		this.orderingInstBic = orderingInstBic;
	}

	public String getOrderingInstAccount() {
		return orderingInstAccount;
	}

	public void setOrderingInstAccount(String orderingInstAccount) {
		this.orderingInstAccount = orderingInstAccount;
	}

	public String getSendersCorresBic() {
		return sendersCorresBic;
	}

	public void setSendersCorresBic(String sendersCorresBic) {
		this.sendersCorresBic = sendersCorresBic;
	}

	public String getSendersCorresAccount() {
		return sendersCorresAccount;
	}

	public void setSendersCorresAccount(String sendersCorresAccount) {
		this.sendersCorresAccount = sendersCorresAccount;
	}

	public String getReceiversCorresBic() {
		return receiversCorresBic;
	}

	public void setReceiversCorresBic(String receiversCorresBic) {
		this.receiversCorresBic = receiversCorresBic;
	}

	public String getReceiversCorresAccount() {
		return receiversCorresAccount;
	}

	public void setReceiversCorresAccount(String receiversCorresAccount) {
		this.receiversCorresAccount = receiversCorresAccount;
	}

	public String getThirdReimbsmntBic() {
		return thirdReimbsmntBic;
	}

	public void setThirdReimbsmntBic(String thirdReimbsmntBic) {
		this.thirdReimbsmntBic = thirdReimbsmntBic;
	}

	public String getThirdReimbsmntAccount() {
		return thirdReimbsmntAccount;
	}

	public void setThirdReimbsmntAccount(String thirdReimbsmntAccount) {
		this.thirdReimbsmntAccount = thirdReimbsmntAccount;
	}

	public String getIntermediaryBic() {
		return intermediaryBic;
	}

	public void setIntermediaryBic(String intermediaryBic) {
		this.intermediaryBic = intermediaryBic;
	}

	public String getIntermediaryAccount() {
		return intermediaryAccount;
	}

	public void setIntermediaryAccount(String intermediaryAccount) {
		this.intermediaryAccount = intermediaryAccount;
	}

	public String getAccountWithInstBic() {
		return accountWithInstBic;
	}

	public void setAccountWithInstBic(String accountWithInstBic) {
		this.accountWithInstBic = accountWithInstBic;
	}

	public String getAccountWithInstAccount() {
		return accountWithInstAccount;
	}

	public void setAccountWithInstAccount(String accountWithInstAccount) {
		this.accountWithInstAccount = accountWithInstAccount;
	}

	public String getBeneficiaryInstBic() {
		return beneficiaryInstBic;
	}

	public void setBeneficiaryInstBic(String beneficiaryInstBic) {
		this.beneficiaryInstBic = beneficiaryInstBic;
	}

	public String getBeneficiaryInstAccount() {
		return beneficiaryInstAccount;
	}

	public void setBeneficiaryInstAccount(String beneficiaryInstAccount) {
		this.beneficiaryInstAccount = beneficiaryInstAccount;
	}

	public String getBeneficiaryCustBic() {
		return beneficiaryCustBic;
	}

	public void setBeneficiaryCustBic(String beneficiaryCustBic) {
		this.beneficiaryCustBic = beneficiaryCustBic;
	}

	public String getBeneficiaryCustAccount() {
		return beneficiaryCustAccount;
	}

	public void setBeneficiaryCustAccount(String beneficiaryCustAccount) {
		this.beneficiaryCustAccount = beneficiaryCustAccount;
	}

	public String getSenderReceiverInfo() {
		return senderReceiverInfo;
	}

	public void setSenderReceiverInfo(String senderReceiverInfo) {
		this.senderReceiverInfo = senderReceiverInfo;
	}

	public String getDeliveryAgentBic() {
		return deliveryAgentBic;
	}

	public void setDeliveryAgentBic(String deliveryAgentBic) {
		this.deliveryAgentBic = deliveryAgentBic;
	}

	public String getDeliveryAgentAccount() {
		return deliveryAgentAccount;
	}

	public void setDeliveryAgentAccount(String deliveryAgentAccount) {
		this.deliveryAgentAccount = deliveryAgentAccount;
	}

	public String getDeliverersCustodianBic() {
		return deliverersCustodianBic;
	}

	public void setDeliverersCustodianBic(String deliverersCustodianBic) {
		this.deliverersCustodianBic = deliverersCustodianBic;
	}

	public String getDeliverersCustodianAccount() {
		return deliverersCustodianAccount;
	}

	public void setDeliverersCustodianAccount(String deliverersCustodianAccount) {
		this.deliverersCustodianAccount = deliverersCustodianAccount;
	}

	public String getPlaceOfSettlementBic() {
		return placeOfSettlementBic;
	}

	public void setPlaceOfSettlementBic(String placeOfSettlementBic) {
		this.placeOfSettlementBic = placeOfSettlementBic;
	}

	public String getPlaceOfSettlementAccount() {
		return placeOfSettlementAccount;
	}

	public void setPlaceOfSettlementAccount(String placeOfSettlementAccount) {
		this.placeOfSettlementAccount = placeOfSettlementAccount;
	}

	public String getSellerBic() {
		return sellerBic;
	}

	public void setSellerBic(String sellerBic) {
		this.sellerBic = sellerBic;
	}

	public String getSellerAccount() {
		return sellerAccount;
	}

	public void setSellerAccount(String sellerAccount) {
		this.sellerAccount = sellerAccount;
	}

	public PaymentRequestInfo getPaymentRequestInfo() {
		return paymentRequestInfo;
	}

	public void setPaymentRequestInfo(PaymentRequestInfo paymentRequestInfo) {
		this.paymentRequestInfo = paymentRequestInfo;
	}

	public String getReleaseMethod() {
		return releaseMethod;
	}

	public void setReleaseMethod(String releaseMethod) {
		this.releaseMethod = releaseMethod;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getAccountGroupId() {
		return accountGroupId;
	}

	public void setAccountGroupId(String accountGroupId) {
		this.accountGroupId = accountGroupId;
	}

	public String getDbLink() {
		return dbLink;
	}

	public void setDbLink(String dbLink) {
		this.dbLink = dbLink;
	}
}
