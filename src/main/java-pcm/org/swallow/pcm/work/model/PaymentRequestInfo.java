/*
 * @(#)PaymentRequestInfo.java 1.0 2019-05-23
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class PaymentRequestInfo extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.payReqId","payment Request Id");
	}
	
	private static final long serialVersionUID = 1L;
	private Long payReqId;

	private String paymentMessage;
	private String orderingCustName;
	private String orderingInstName;
	private String sendersCorresName;
	private String receiversCorresName;
	private String thirdReimbsmntName;
	private String intermediaryName;
	private String accountWithInstName;
	private String beneficiaryInstName;
	private String beneficiaryCustName;
	private String deliveryAgentName;
	private String deliverersCustodianName;
	private String placeOfSettlementName;
	private String sellerName;
	

	public Long getPayReqId() {
		return payReqId;
	}

	public void setPayReqId(Long payreqId) {
		this.payReqId = payreqId;
	}
	public String getPaymentMessage() {
		return paymentMessage;
	}

	public void setPaymentMessage(String paymentMessage) {
		this.paymentMessage = paymentMessage;
	}

	public String getOrderingCustName() {
		return orderingCustName;
	}

	public void setOrderingCustName(String orderingCustName) {
		this.orderingCustName = orderingCustName;
	}

	public String getSendersCorresName() {
		return sendersCorresName;
	}

	public void setSendersCorresName(String sendersCorresName) {
		this.sendersCorresName = sendersCorresName;
	}

	public String getReceiversCorresName() {
		return receiversCorresName;
	}

	public void setReceiversCorresName(String receiversCorresName) {
		this.receiversCorresName = receiversCorresName;
	}

	public String getThirdReimbsmntName() {
		return thirdReimbsmntName;
	}

	public void setThirdReimbsmntName(String thirdReimbsmntName) {
		this.thirdReimbsmntName = thirdReimbsmntName;
	}

	public String getIntermediaryName() {
		return intermediaryName;
	}

	public void setIntermediaryName(String intermediaryName) {
		this.intermediaryName = intermediaryName;
	}

	public String getAccountWithInstName() {
		return accountWithInstName;
	}

	public void setAccountWithInstName(String accountWithInstName) {
		this.accountWithInstName = accountWithInstName;
	}

	public String getBeneficiaryInstName() {
		return beneficiaryInstName;
	}

	public void setBeneficiaryInstName(String beneficiaryInstName) {
		this.beneficiaryInstName = beneficiaryInstName;
	}

	public String getBeneficiaryCustName() {
		return beneficiaryCustName;
	}

	public void setBeneficiaryCustName(String beneficiaryCustName) {
		this.beneficiaryCustName = beneficiaryCustName;
	}

	public String getDeliveryAgentName() {
		return deliveryAgentName;
	}

	public void setDeliveryAgentName(String deliveryAgentName) {
		this.deliveryAgentName = deliveryAgentName;
	}

	public String getDeliverersCustodianName() {
		return deliverersCustodianName;
	}

	public void setDeliverersCustodianName(String deliverersCustodianName) {
		this.deliverersCustodianName = deliverersCustodianName;
	}

	public String getPlaceOfSettlementName() {
		return placeOfSettlementName;
	}

	public void setPlaceOfSettlementName(String placeOfSettlementName) {
		this.placeOfSettlementName = placeOfSettlementName;
	}

	public String getSellerName() {
		return sellerName;
	}

	public void setSellerName(String sellerName) {
		this.sellerName = sellerName;
	}

	public String getOrderingInstName() {
		return orderingInstName;
	}

	public void setOrderingInstName(String orderingInstName) {
		this.orderingInstName = orderingInstName;
	}
}
