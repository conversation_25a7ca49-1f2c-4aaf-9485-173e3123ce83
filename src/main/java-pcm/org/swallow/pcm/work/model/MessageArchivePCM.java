/*
 * @(#)InputExceptionsDataModel.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */package org.swallow.pcm.work.model;


import java.sql.Clob;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.swallow.model.BaseObject;
import org.swallow.work.model.InputExceptionsDataModel;

public class MessageArchivePCM extends InputExceptionsDataModel {

	private String pcPaymentId  				= "";
	private String accountId  					= "";
	

	public String getPcPaymentId() {
		return pcPaymentId;
	}
	public void setPcPaymentId(String pcPaymentId) {
		this.pcPaymentId = pcPaymentId;
	}
	public String getAccountId() {
		return accountId;
	}
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
}
