/*
 * @(#)PaymentDisplayDAO.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao;

import java.util.Collection;
import java.util.Date;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.pcm.work.model.MessagesOutput;
import org.swallow.pcm.work.model.PaymentMessage;
import org.swallow.pcm.work.model.PaymentRequest;

/**
 * <AUTHOR>
 * 
 */
public interface PaymentDisplayDAO extends DAO {

	public Collection getPaymentMessages(int payReqId) throws SwtException;
	
	public PaymentMessage getPaymentMessageDetails(int messageId) throws SwtException;
	public Collection getStopRulesList(int payReqId) throws SwtException;
	public String getStopUnstopReason(int payReqId, boolean stopReason, String dateFormat) throws SwtException;
	public PaymentRequest getPaymentRequestDetails(int payReqId) throws SwtException;
	
	public Collection getPaymentResponses(int payReqId) throws SwtException;
	public MessagesOutput getPaymentResponseDetails(int messageId) throws SwtException;
	public Collection getPayementRequestLogs(int payReqId) throws SwtException;
}
