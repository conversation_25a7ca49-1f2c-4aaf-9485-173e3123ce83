/*
 * @(#)PaymentSearchDAO.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao;

import java.util.ArrayList;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Party;

/**
 * <AUTHOR>
 * 
 */
public interface PaymentSearchDAO extends DAO {

	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId,
			int pageSize, int currentPage, String selectedsort) throws SwtException;
	/**
	 * 
	 * @param partyName
	 * @param partyId
	 * @param entityId
	 * @return int
	 * @throws SwtException
	 */
	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException;

}
