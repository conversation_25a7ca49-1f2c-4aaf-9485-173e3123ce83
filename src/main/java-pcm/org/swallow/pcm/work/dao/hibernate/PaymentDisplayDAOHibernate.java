/*
 * @(#)PaymentDisplayDAOHibernate.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.work.dao.PaymentDisplayDAO;
import org.swallow.pcm.work.model.MessagesOutput;
import org.swallow.pcm.work.model.PaymentLog;
import org.swallow.pcm.work.model.PaymentMessage;
import org.swallow.pcm.work.model.PaymentRequest;
import org.swallow.pcm.work.model.PaymentStop;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.hibernate.GenericDisplayMonitorDAOHibernate;
import org.swallow.work.service.InputExceptionsMessagesManager;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.hibernate.Transaction;
import org.hibernate.Session;






@Repository ("paymentDisplayDAO")
@Transactional
public class PaymentDisplayDAOHibernate extends CustomHibernateDaoSupport implements PaymentDisplayDAO {
	public PaymentDisplayDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(PaymentDisplayDAOHibernate.class);

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public PaymentMessage getPaymentMessageDetails(int messageId) throws SwtException {
		PaymentMessage message = null;
		try {
			log.debug(this.getClass().getName() + " - [getPaymentMessageDetails] - " + "Entry");

			try (Session session = sessionFactory.openSession()) {
				String queryString = "from PaymentMessage m where m.messageId = :messageId";
				TypedQuery<PaymentMessage> query = session.createQuery(queryString, PaymentMessage.class);
				query.setParameter("messageId", messageId);
				List<PaymentMessage> messagesList = query.getResultList();

				if (!messagesList.isEmpty()) {
					message = messagesList.get(0);
				}
			}

			log.debug(this.getClass().getName() + " - [getPaymentMessageDetails] - Exit");
			return message;

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getPaymentMessageDetails] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymentMessageDetails", PaymentDisplayDAOHibernate.class);
		}
	}

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public MessagesOutput getPaymentResponseDetails(int messageId) throws SwtException {
		MessagesOutput messagesOutput = null;
		String queryString = "from MessagesOutput m where m.messageOutId = :messageId";

		try {
			log.debug(this.getClass().getName() + " - [getPaymentResponseDetails] - Entry");

			try (Session session = sessionFactory.openSession()) {
				TypedQuery<MessagesOutput> query = session.createQuery(queryString, MessagesOutput.class);
				query.setParameter("messageId", messageId);
				List<MessagesOutput> messagesOutputList = query.getResultList();

				if (!messagesOutputList.isEmpty()) {
					messagesOutput = messagesOutputList.get(0);
				}
			}

			log.debug(this.getClass().getName() + " - [getPaymentResponseDetails] - Exit");
			return messagesOutput;

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentResponseDetails] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymentResponseDetails", PaymentDisplayDAOHibernate.class);
		}
	}

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getPaymentMessages(int payReqId) throws SwtException {
		String queryString = "from PaymentMessage m where m.payreqId = :payReqId order by m.sequenceNo desc";
		String messageText = null;
		String dbLink = null;

		try {
			log.debug(this.getClass().getName() + " - [getPaymentMessages] - Entry");

			try (Session session = sessionFactory.openSession()) {
				InputExceptionsMessagesManager inputExceptionsMessagesManager = (InputExceptionsMessagesManager) SwtUtil.getBean("inputExceptionsMessagesManager");

				TypedQuery<PaymentMessage> query = session.createQuery(queryString, PaymentMessage.class);
				query.setParameter("payReqId", (long) payReqId);
				List<PaymentMessage> resultList = query.getResultList();

				for (PaymentMessage msg : resultList) {
					if ("H".equals(msg.getLocation())) {
						dbLink = msg.getDbLink();
						try {
							messageText = getPaymenMessageBodyFromArchiveSchema(payReqId, dbLink);
						} catch (Exception e) {
							messageText = "";
						}
						msg.setMessageBody(messageText);
					} else {
						try {
							messageText = inputExceptionsMessagesManager.getMessageBody(String.valueOf(msg.getMessageId()), new OpTimer(), true);
						} catch (Exception e) {
							messageText = "";
						}
						msg.setMessageBody(messageText);
					}
				}

				return new ArrayList<>(resultList);
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentMessages] method: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymentMessages", PaymentDisplayDAOHibernate.class);
		}
	}

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getPaymentResponses(int payReqId) throws SwtException {
		String queryString = "from MessagesOutput m where m.payreqId = :payReqId order by m.sentDate desc";

		try {
			log.debug(this.getClass().getName() + " - [getPaymentResponses] - Entry");

			try (Session session = sessionFactory.openSession()) {
				TypedQuery<MessagesOutput> query = session.createQuery(queryString, MessagesOutput.class);
				query.setParameter("payReqId", (long) payReqId);
				List<MessagesOutput> resultList = query.getResultList();
				return new ArrayList<>(resultList);
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentResponses] method: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymentResponses", PaymentDisplayDAOHibernate.class);
		}
	}

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getStopRulesList(int payReqId) throws SwtException {
		String queryString = "from PaymentStop s where s.id.payReqId = :payReqId order by s.unstopDate desc";

		try {
			log.debug(this.getClass().getName() + " - [getStopRulesList] - Entry");

			try (Session session = sessionFactory.openSession()) {
				TypedQuery<PaymentStop> query = session.createQuery(queryString, PaymentStop.class);
				query.setParameter("payReqId", (long) payReqId);
				List<PaymentStop> resultList = query.getResultList();
				return new ArrayList<>(resultList);
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getStopRulesList] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getStopRulesList", PaymentDisplayDAOHibernate.class);
		}
	}

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getPayementRequestLogs(int payReqId) throws SwtException {
		String queryString = "from PaymentLog s where s.payreqId = :payReqId order by s.logSeq desc";

		try {
			log.debug(this.getClass().getName() + " - [getPaymentRequestLogs] - Entry");

			try (Session session = sessionFactory.openSession()) {
				TypedQuery<PaymentLog> query = session.createQuery(queryString, PaymentLog.class);
				query.setParameter("payReqId", (long) payReqId);
				List<PaymentLog> resultList = query.getResultList();
				return new ArrayList<>(resultList);
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentRequestLogs] method: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymentRequestLogs", PaymentDisplayDAOHibernate.class);
		}
	}


	public String getStopUnstopReason(int payReqId, boolean stopReason, String dateFormat) throws SwtException {
		String reason = null;

		try {
			log.debug(this.getClass().getName() + " - [getStopReason] - Entry");

			if (SwtUtil.isEmptyOrNull(dateFormat)) {
				dateFormat = SwtUtil.getCurrentDateFormat(null);
			}

			dateFormat = dateFormat.toUpperCase() + " HH24:MI:SS";

			try (Session session = sessionFactory.openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement cstmt = stopReason ?
						 conn.prepareCall("{ ? = call PKG_PC_Tools.GetStopReason(?, 'Y', ?)}") :
						 conn.prepareCall("{ ? = call pkg_pc_tools.GetUnstopReason(?)}")) {

				cstmt.setInt(2, payReqId);
				if (stopReason) {
					cstmt.setString(3, dateFormat);
				}
				cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.VARCHAR);
				cstmt.execute();
				reason = cstmt.getString(1);
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getStopReason] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getStopReason", PaymentDisplayDAOHibernate.class);
		}

		return reason;
	}

	/**
	 * Returns the collection of category detail list from category table
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public PaymentRequest getPaymentRequestDetails(int payReqId) throws SwtException {
		PaymentRequest paymentRequest = null;
		String queryString = "from PaymentRequest p where p.payReqId = :payReqId";
		String paymentMessage = null;
		String dbLink = null;

		try {
			log.debug(this.getClass().getName() + " - [getPaymentRequestDetails] - Entry");

			try (Session session = sessionFactory.openSession()) {
				TypedQuery<PaymentRequest> query = session.createQuery(queryString, PaymentRequest.class);
				query.setParameter("payReqId", (long) payReqId);
				List<PaymentRequest> resultList = query.getResultList();

				if (!resultList.isEmpty()) {
					paymentRequest = resultList.get(0);

					if (paymentRequest.getPaymentRequestInfo() != null && "H".equals(paymentRequest.getLocation())) {
						dbLink = paymentRequest.getDbLink();
						paymentMessage = getPaymenMessageBodyFromArchiveSchema(payReqId, dbLink);
						if (!SwtUtil.isEmptyOrNull(paymentMessage)) {
							paymentRequest.getPaymentRequestInfo().setPaymentMessage(paymentMessage);
						}
					}
				}
			}

			log.debug(this.getClass().getName() + " - [getPaymentRequestDetails] - Exit");
			return paymentRequest;

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentRequestDetails] method: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymentRequestDetails", PaymentDisplayDAOHibernate.class);
		}
	}

	private String getPaymenMessageBodyFromArchiveSchema(int payReqId, String dbLink) throws SwtException {
		String paymentMessage = null;

		try {
			try (Session session = sessionFactory.openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement cstmt = conn.prepareCall("{call pkg_pc_screen_utility.sp_get_msg_body_from_dblink(?,?,?)}")) {

				cstmt.setInt(1, payReqId);
				cstmt.setString(2, dbLink);
				cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.CLOB);
				cstmt.execute();
				paymentMessage = cstmt.getString(3);
				conn.commit();
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getPaymenMessageBodyFromArchiveSchema] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPaymenMessageBodyFromArchiveSchema", PaymentDisplayDAOHibernate.class);
		}

		return paymentMessage;
	}

}