/*
 * @(#)DashboardDAOHibernate.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.hibernate.MaintenanceLogDAOHibernate;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.model.ScreenInfo;
import org.swallow.pcm.maintenance.dao.hibernate.AccountGroupsMaintenanceDAOHibernate;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.core.dashboardResult;
import org.swallow.pcm.work.dao.DashboardDAO;
import org.swallow.pcm.work.model.PaymentRequest;
import org.swallow.pcm.work.model.PaymentStop;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.Session;
import jakarta.persistence.TypedQuery;







@Repository ("dashboardDAO")
@Transactional
public class DashboardDAOHibernate extends CustomHibernateDaoSupport implements
		DashboardDAO {
	public DashboardDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(DashboardDAOHibernate.class);


	/**
	 * This is used to fetches details for the currency dashboard
	 *
	 * @param listEntities
	 * @param valueDate
	 * @param applyCurrencyThreshold
	 * @param spreadOnly
	 * @param userId
	 * @param pv_xml_out
	 * @return Collection
	 * @throws SwtException
	 */

	public String[] currencyDashboardDetails(String listEntities, String valueDate,
											 String applyCurrencyThreshold, String spreadOnly, String userId, String volume, String multiplier) throws SwtException {
		String[] resultArray = new String[3];
		try {
			log.debug(this.getClass().getName() + " - [currencyDashboardDetails] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement pstmt = conn.prepareCall("{call PKG_PC_DASHBOARD.sp_currency_dashboard(?,?,?,?,?,?,?,?,?, ?)}")) {

				pstmt.setString(1, listEntities);
				pstmt.setString(2, valueDate);
				pstmt.setString(3, applyCurrencyThreshold);
				pstmt.setString(4, spreadOnly);
				pstmt.setString(5, userId);
				pstmt.setString(6, volume);
				pstmt.setString(7, multiplier);
				pstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CLOB);
				pstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CHAR);
				pstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.DATE);

				pstmt.execute();

				resultArray[0] = pstmt.getString(8);
				resultArray[1] = pstmt.getString(9);
				resultArray[2] = pstmt.getDate(10).toString();
			}

			log.debug(this.getClass().getName() + " - [currencyDashboardDetails] - Exit");
			return resultArray;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [currencyDashboardDetails] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "currencyDashboardDetails", DashboardDAOHibernate.class);
		}
	}



	public dashboardResult dataGridDetails(String listEntities, String valueDate, String highValue, String currencyMultiplier, String status,  String blockedReason, String currencyCode,
										   String accountGroupId, String accountId, String userId, String initialFilter, String userFilter, String refFilter, String spreadOnly,
										   String order, String ascDesc, String  rowbegin, String rowEnd, String archive, String ccyTime, String inputSince) throws SwtException {
		dashboardResult result = new dashboardResult();
		try {
			log.debug(this.getClass().getName() + " - [dataGridDetails] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement pstmt = conn.prepareCall("{call PKG_PC_DASHBOARD.sp_pr_datagrid(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}")) {

				pstmt.setString(1, listEntities);
				pstmt.setString(2, valueDate);
				pstmt.setString(3, highValue);
				pstmt.setString(4, currencyMultiplier);
				pstmt.setString(5, status);
				pstmt.setString(6, blockedReason);
				pstmt.setString(7, currencyCode);
				pstmt.setString(8, accountGroupId);
				pstmt.setString(9, accountId);
				pstmt.setString(10, userId);
				pstmt.setString(11, initialFilter);
				pstmt.setString(12, userFilter);
				pstmt.setString(13, refFilter);
				pstmt.setString(14, spreadOnly);
				pstmt.setString(15, order);
				pstmt.setString(16, ascDesc);
				pstmt.setString(17, rowbegin);
				pstmt.setString(18, rowEnd);
				pstmt.setString(19, archive);
				pstmt.setString(20, ccyTime);
				pstmt.setString(21, inputSince);

				pstmt.registerOutParameter(21, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(22, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(23, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(24, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(25, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(26, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(27, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(28, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(29, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.registerOutParameter(30, oracle.jdbc.OracleTypes.CLOB);

				pstmt.execute();

				result.setInputSince(pstmt.getString(21));
				result.setSodBalance(pstmt.getString(22));
				result.setConfirmedCredit(pstmt.getString(23));
				result.setCreditLine(pstmt.getString(24));
				result.setReleasedPayments(pstmt.getString(25));
				result.setFcastDr(pstmt.getString(26));
				result.setAvailableLiqEx(pstmt.getString(27));
				result.setAvailabelLiqInc(pstmt.getString(28));
				result.setReservedBalance(pstmt.getString(29));
				result.setXmlOut((SwtUtil.isEmptyOrNull(pstmt.getString(30)) ? "" : pstmt.getString(30)));
			}

			log.debug(this.getClass().getName() + " - [dataGridDetails] - Exit");
			return result;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [dataGridDetails] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "dataGridDetails", DashboardDAOHibernate.class);
		}
	}

	public Collection unStopReleasePayment(ArrayList paymentReqId, ArrayList previousStatusId, String paymentAction, String userId) throws SwtException {
		Collection errCodeId = new ArrayList();
		try {
			log.debug(this.getClass().getName() + " - [unStopReleasePayment] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement pstmt = conn.prepareCall("{call PKG_PC_SCREEN_UTILITY.sp_release_unstop_paymt(?,?,?,?,?)}")) {

				for (int i = 0; i < paymentReqId.size(); i++) {
					int paymentReqIdNumber = Integer.parseInt(paymentReqId.get(i).toString());
					String previousStatus = previousStatusId.get(i).toString();

					pstmt.setInt(1, paymentReqIdNumber);
					pstmt.setString(2, previousStatus);
					pstmt.setString(3, paymentAction);
					pstmt.setString(4, userId);
					pstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CLOB);

					pstmt.execute();

					String result = pstmt.getString(5);
					if (result.equals("ERR#NOT_FOUND") || result.equals("ERR#LOCK") ||
						result.equals("ERR#STATUS") || result.equals("ERR#ACTION")) {
						errCodeId.add(new LabelValueBean(paymentReqId.get(i).toString(), result));
					}
				}
			}

			log.debug(this.getClass().getName() + " - [unStopReleasePayment] - Exit");
			return errCodeId;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [unStopReleasePayment] method : - " + e.getMessage());
			throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
		}
	}


	public HashMap isBlockedStopped(ArrayList paymentReqId, String formatDate) throws SwtException {
		HashMap<String, String> idRule = new LinkedHashMap<String, String>();
		try {
			log.debug(this.getClass().getName() + " - [isBlockedStopped] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement pstmt = conn.prepareCall("{call PKG_PC_CHECK_RULES.is_blocked_pr_stopped(?,?,?)}")) {

				for (int i = 0; i < paymentReqId.size(); i++) {
					int paymentReqIdInt = Integer.parseInt(paymentReqId.get(i).toString());

					pstmt.setInt(1, paymentReqIdInt);
					pstmt.setString(3, formatDate);
					pstmt.registerOutParameter(2, Types.VARCHAR);

					pstmt.execute();

					String result = pstmt.getString(2);
					if (result.indexOf("Y") > -1 || result.indexOf("P") > -1) {
						idRule.put(paymentReqId.get(i).toString(), result);
					} else if (result.equals("N")) {
						idRule.put(paymentReqId.get(i).toString(), "N");
					} else {
						idRule.put(paymentReqId.get(i).toString(), "L");
					}
				}
			}

			log.debug(this.getClass().getName() + " - [isBlockedStopped] - Exit");
			return idRule;

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [isBlockedStopped] method : - " + e.getMessage());
			throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
		}
	}
	public String checkStopProcess() throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [checkStopProcess] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession();
				 Connection conn = SwtUtil.connection(session);
				 CallableStatement pstmt = conn.prepareCall("{? = call PKG_PC_CHECK_RULES.is_stop_process_running()}")) {

				pstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.execute();

				String result = pstmt.getString(1);
				log.debug(this.getClass().getName() + " - [checkStopProcess] - Exit");
				return result;
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [checkStopProcess] method : - " + e.getMessage());
			throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
		}
	}


	public Collection getAccountGrpDetails(String currency) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getAccountGrpDetails] - Entry");

			try (Session session = SwtUtil.pcSessionFactory.openSession()) {
				String query = !currency.equals("All")
						? "from AccountGroup acct where acct.currencyCode = :currency and (acct.requireAuthorisation = 'N' OR acct.requireAuthorisation is null) order by lower(acct.id.accGrpId) asc"
						: "from AccountGroup acct where (acct.requireAuthorisation = 'N' OR acct.requireAuthorisation is null) order by lower(acct.id.accGrpId) asc";

				Collection accountGrpList = !currency.equals("All")
						? session.createQuery(query, AccountGroup.class)
						.setParameter("currency", currency)
						.getResultList()
						: session.createQuery(query, AccountGroup.class)
						.getResultList();

				log.debug(this.getClass().getName() + " - [getAccountGrpDetails] - Exit");
				return accountGrpList;
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountGrpDetails] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getAccountGrpDetails", this.getClass());
		}
	}

	public Collection getAccountsDetails(String accountGrp, String currency) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getAccountsDetails] - Entry");

			try (Session session = SwtUtil.pcSessionFactory.openSession()) {
				String query;
				Collection accountsList;

				if (!accountGrp.equals("All") && currency.equals("All")) {
					query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
					accountsList = session.createQuery(query, AcctMaintenance.class)
							.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
							.getResultList();
				} else if (currency.equals("All") && !accountGrp.equals("All")) {
					query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
					accountsList = session.createQuery(query, AcctMaintenance.class)
							.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
							.getResultList();
				} else if (!currency.equals("All") && !accountGrp.equals("All")) {
					query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where accGrp.currencyCode = :currency and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
					accountsList = session.createQuery(query, AcctMaintenance.class)
							.setParameter("currency", currency, StandardBasicTypes.STRING)
							.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
							.getResultList();
				} else {
					query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where accGrp.currencyCode = :currency order by acct.id.accountId asc";
					accountsList = session.createQuery(query, AcctMaintenance.class)
							.setParameter("currency", currency, StandardBasicTypes.STRING)
							.getResultList();
				}

				log.debug(this.getClass().getName() + " - [getAccountsDetails] - Exit");
				return accountsList;
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountsDetails] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getAccountsDetails", this.getClass());
		}
	}

	public Collection getAccountsDetailsByEntity(String entity, String accountGrp, String currency) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getAccountsDetailsByEntity] - Entry");

			try (Session session = SwtUtil.pcSessionFactory.openSession()) {
				String query;
				Collection accountsList;

				if (entity.equals("All")) {
					if (!accountGrp.equals("All") && currency.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else if (!currency.equals("All") && accountGrp.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else if (!currency.equals("All") && !accountGrp.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where accGrp.currencyCode = :currency and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("currency", currency, StandardBasicTypes.STRING)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where accGrp.currencyCode = :currency order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("currency", currency, StandardBasicTypes.STRING)
								.getResultList();
					}
				} else if (entity.contains(",")) {
					List<String> entitiesList = Arrays.asList(entity.split(","));

					if (!accountGrp.equals("All") && currency.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acct.id.entityId in (:entity) and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entitiesList)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else if (!currency.equals("All") && accountGrp.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acct.id.entityId in (:entity) and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entitiesList)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else if (!currency.equals("All") && !accountGrp.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where acct.id.entityId in (:entity) and accGrp.currencyCode = :currency and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entitiesList)
								.setParameter("currency", currency, StandardBasicTypes.STRING)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where acct.id.entityId in (:entity) and accGrp.currencyCode = :currency order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entitiesList)
								.setParameter("currency", currency, StandardBasicTypes.STRING)
								.getResultList();
					}
				} else {
					if (!accountGrp.equals("All") && currency.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acct.id.entityId =:entity and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entity, StandardBasicTypes.STRING)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else if (!currency.equals("All") && accountGrp.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acct.id.entityId =:entity and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entity, StandardBasicTypes.STRING)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else if (!currency.equals("All") && !accountGrp.equals("All")) {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where acct.id.entityId =:entity and accGrp.currencyCode = :currency and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entity, StandardBasicTypes.STRING)
								.setParameter("currency", currency, StandardBasicTypes.STRING)
								.setParameter("accountGrp", accountGrp, StandardBasicTypes.STRING)
								.getResultList();
					} else {
						query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where acct.id.entityId =:entity and accGrp.currencyCode = :currency order by acct.id.accountId asc";
						accountsList = session.createQuery(query, AcctMaintenance.class)
								.setParameter("entity", entity, StandardBasicTypes.STRING)
								.setParameter("currency", currency, StandardBasicTypes.STRING)
								.getResultList();
					}
				}

				log.debug(this.getClass().getName() + " - [getAccountsDetailsByEntity] - Exit");
				return accountsList;
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountsDetailsByEntity] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getAccountsDetailsByEntity", this.getClass());
		}
	}
	public void updatePaymentCategory(String paymentReqId, String categoryId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [updatePaymentCategory] - Entry");

			try (Session session = SwtUtil.pcSessionFactory.openSession();
				 Connection conn = SwtUtil.connection(session);
				 PreparedStatement stmt = conn.prepareStatement("UPDATE PC_PAYMENT_REQUEST SET CATEGORY_ID = ? WHERE PAYREQ_ID = ?")) {

				stmt.setString(1, categoryId);
				stmt.setInt(2, Integer.parseInt(paymentReqId));
				stmt.executeUpdate();

				conn.commit();

				try {
					Transaction tx = session.beginTransaction();
					Long paymentReqIdLong = Long.parseLong(paymentReqId);
					String query = "from PaymentRequest payReq where payReq.payReqId = :paymentReqId";
					List<PaymentRequest> records = session.createQuery(query, PaymentRequest.class)
							.setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
							.getResultList();

					PaymentRequest payRequest = null;
					if (records.size() == 1) {
						payRequest = records.get(0);
					}

					MaintenanceLogDAOHibernate maintenanceLogDAOHibernate = (MaintenanceLogDAOHibernate) (SwtUtil.getBean("maintenanceLogDAO"));
					MaintenanceLog mainLog = new MaintenanceLog();
					mainLog.setHostId(SwtUtil.getCurrentHostId());
					mainLog.setuserId(UserThreadLocalHolder.getUser());
					mainLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
					mainLog.setAction(SwtConstants.ACTION_UPDATE);
					mainLog.setTableName("Payment Request");
					mainLog.setColumnName("Category");
					mainLog.setOldValue(payRequest != null ? payRequest.getCategoryId() : "");
					mainLog.setNewValue(categoryId);
					mainLog.setReference(paymentReqId);
					mainLog.setLogDate(new Date());
					maintenanceLogDAOHibernate.logMaintenanceAudit(mainLog);

				} catch (Exception e) {
					log.error(this.getClass().getName() + " - Exception Caught in [updatePaymentCategory] maintenance log : - " + e.getMessage());
				}
			}

			log.debug(this.getClass().getName() + " - [updatePaymentCategory] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [updatePaymentCategory] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "updatePaymentCategory", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}


	public void releasePayment(ArrayList paymentReqId, String userId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [releasePayment] - Entry");

			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				Transaction tx = session.beginTransaction();
				try {
					for (int i = 0; i < paymentReqId.size(); i++) {
						String query = "from PaymentRequest payReq where payReq.payReqId = :paymentReqId";
						Long paymentReqIdLong = Long.parseLong(paymentReqId.get(i).toString());
						List<PaymentRequest> records = session.createQuery(query, PaymentRequest.class)
								.setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
								.getResultList();

						if (records.size() == 1) {
							PaymentRequest payRequest = records.get(0);
							payRequest.setReleaseDate(SwtUtil.getSysParamDate());

							if (payRequest.getStatus().equals("S")) {
								payRequest.setReleaseMethod("P");
							} else if (payRequest.getStatus().equals("B")) {
								payRequest.setReleaseMethod("B");
							} else if (payRequest.getStatus().equals("W")) {
								payRequest.setReleaseMethod("M");
							}

							payRequest.setStatus("R");
							payRequest.setUpdatedBy(userId);
							session.update(payRequest);
						} else {
							throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
						}
					}

					tx.commit();
					log.debug(this.getClass().getName() + " - [releasePayment] - Exit");

				} catch (Exception e) {
					if (tx != null) {
						tx.rollback();
					}
					throw e;
				}
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [releasePayment] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "releasePayment", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}
	public AccountGroup getSpreadId(String accountGroupId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getSpreadId] - Entry");

			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				Transaction tx = session.beginTransaction();
				try {
					String query = "from AccountGroup acct where acct.id.accGrpId = :accountGroupId";
					List<AccountGroup> records = session.createQuery(query, AccountGroup.class)
							.setParameter("accountGroupId", accountGroupId, StandardBasicTypes.STRING)
							.getResultList();

					AccountGroup acctGrp = null;
					if (records != null && !records.isEmpty()) {
						acctGrp = records.get(0);
					}

					tx.commit();
					log.debug(this.getClass().getName() + " - [getSpreadId] - Exit");
					return acctGrp;

				} catch (Exception e) {
					if (tx != null) {
						tx.rollback();
					}
					throw e;
				}
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [getSpreadId] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getSpreadId", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}
	public void unStopPayment(String paymentReqId, String userId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [unStopPayment] - Entry");

			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				Transaction tx = session.beginTransaction();
				try {
					String query = "from PaymentRequest payReq where payReq.payReqId = :paymentReqId";
					String query2 = "from PaymentStop payStop where payStop.id.payReqId = :paymentReqId";
					Long paymentReqIdLong = Long.parseLong(paymentReqId);

					List<PaymentRequest> records = session.createQuery(query, PaymentRequest.class)
							.setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
							.getResultList();

					List<PaymentStop> records2 = session.createQuery(query2, PaymentStop.class)
							.setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
							.getResultList();

					if (records.size() == 1) {
						PaymentRequest payRequest = records.get(0);
						payRequest.setUnstopDate(SwtUtil.getSysParamDate());
						payRequest.setStatus("W");
						payRequest.setUpdatedBy(userId);
						session.update(payRequest);
					} else {
						throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
					}

					for (PaymentStop payStop : records2) {
						payStop.setUnstopDate(SwtUtil.getSysParamDate());
						payStop.setUnstopBy(userId);
						session.update(payStop);
					}

					tx.commit();
					log.debug(this.getClass().getName() + " - [unStopPayment] - Exit");

				} catch (Exception e) {
					if (tx != null) {
						tx.rollback();
					}
					throw e;
				}
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [unStopPayment] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "unStopPayment", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public void saveScreenInfo(ArrayList<ScreenInfo> screenInfoList) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [saveScreenInfo] - Entry");

			if (screenInfoList.size() > 0) {
				try (Session session = SwtUtil.pcSessionFactory.openSession()) {
					Transaction tx = session.beginTransaction();
					try {
						for (ScreenInfo screenInfo : screenInfoList) {
							String queryString = "from ScreenInfo screenInfo where screenInfo.id.hostId = :hostId " +
												 "and screenInfo.id.userId = :userId " +
												 "and screenInfo.id.entityId = :entityId " +
												 "and screenInfo.id.screenId = :screenId " +
												 "and screenInfo.id.clsId = :clsId " +
												 "and screenInfo.id.propertyName = :propertyName";

							TypedQuery<ScreenInfo> query = session.createQuery(queryString, ScreenInfo.class)
									.setParameter("hostId", screenInfo.getId().getHostId())
									.setParameter("userId", screenInfo.getId().getUserId())
									.setParameter("entityId", screenInfo.getId().getEntityId())
									.setParameter("screenId", screenInfo.getId().getScreenId())
									.setParameter("clsId", "display")
									.setParameter("propertyName", screenInfo.getId().getPropertyName());

							List<ScreenInfo> records = query.getResultList();

							if (records.isEmpty()) {
								session.save(screenInfo);
							} else {
								session.update(screenInfo);
							}
						}

						tx.commit();
						log.debug(this.getClass().getName() + " - [saveScreenInfo] - Exit");

					} catch (Exception e) {
						if (tx != null) {
							tx.rollback();
						}
						throw e;
					}
				}
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Caught in [saveScreenInfo] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "saveScreenInfo", AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

	public void unStopReleasePayment(String paymentId, String userId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [unStopReleasePayment] - Enter");

			try (Session session = SwtUtil.pcSessionFactory.openSession()) {
				Transaction tx = session.beginTransaction();
				try {
					session.doWork(connection -> {
						try (CallableStatement callableStatement = connection.prepareCall("{call PCM_UNSTOP_RELEASE_PAYMENT(?,?)}")) {
							callableStatement.setString(1, paymentId);
							callableStatement.setString(2, userId);
							callableStatement.execute();
						}
					});

					tx.commit();
				} catch (Exception e) {
					if (tx != null) {
						tx.rollback();
					}
					throw e;
				}
			}

			log.debug(this.getClass().getName() + " - [unStopReleasePayment] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Caught in [unStopReleasePayment] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"unStopReleasePayment",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
	}

}