/*
 * @(#)PaymentSearchManagerImpl.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.work.service.impl;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Party;
import org.swallow.pcm.work.dao.PaymentSearchDAO;
import org.swallow.pcm.work.service.PaymentSearchManager;
@Component("paymentSearchManager")
public class PaymentSearchManagerImpl implements PaymentSearchManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(PaymentSearchManagerImpl.class);

	private PaymentSearchDAO paymentSearchDAO = null;

	public void setPaymentSearchDAO(PaymentSearchDAO paymentSearchDAO) {
		this.paymentSearchDAO = paymentSearchDAO;
	}

	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId, int pageSize,
			int currentPage, String selectedsort) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPartySearchResult ] - " + "Entry");
			return paymentSearchDAO.getPartySearchResult(partyId, partyName, entityId,
					pageSize, currentPage, selectedsort);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchResult", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - Exit");
		}
		// return result as list
	}

	@Override
	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPartySearchResult ] - " + "Entry");
			return paymentSearchDAO.getTotalCount(partyName, partyId, entityId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchResult", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - Exit");
		}
	}

}
