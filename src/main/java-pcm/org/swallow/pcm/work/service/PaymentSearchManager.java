/*
 * @(#)PaymentSearchManager.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.service;

import java.util.ArrayList;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Party;
import org.swallow.pcm.work.dao.PaymentSearchDAO;

public interface PaymentSearchManager {

	public void setPaymentSearchDAO(PaymentSearchDAO paymentSearchDAO);

	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId,
			int pageSize, int currentPage, String selectedsort) throws SwtException;

	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException;

}
