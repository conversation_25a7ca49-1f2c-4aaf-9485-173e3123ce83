/*
 * @(#)DashboardManagerImpl.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.ScreenInfo;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.core.dashboardResult;
import org.swallow.pcm.work.dao.DashboardDAO;
import org.swallow.pcm.work.service.DashboardManager;
@Component("dashboardManager")
public class DashboardManagerImpl implements DashboardManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(DashboardManagerImpl.class);
	@Autowired
	private DashboardDAO dashboardDAO = null;

	public void setDashboardDAO(DashboardDAO dashboardDAO) {
		this.dashboardDAO = dashboardDAO;
	}

	

	@Override
	public String[] currencyDashboardDetails(String listEntities, String valueDate, String applyCurrencyThreshold,
			String spreadOnly, String userId, String volume, String multiplier) throws SwtException {
		String [] result = null;
		try {
			log.debug(this.getClass().getName() + " - [ currencyDashboardDetails ] - " + "Entry");
			result = dashboardDAO.currencyDashboardDetails(listEntities, valueDate, applyCurrencyThreshold, spreadOnly, userId, volume, multiplier);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [currencyDashboardDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountGrpDetails", this.getClass());

		}finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [currencyDashboardDetails] - Exit");
		}
		return result;
	}
	
	
	public Collection getAccountGrpDetails(String currency) throws SwtException{
		// Variable List to hold list RulesDefinition
		List accountGrpList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAccountGrpDetails ] - " + "Entry");
			accountGrpList =  (List) dashboardDAO.getAccountGrpDetails(currency);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getAccountGrpDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountGrpDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getAccountGrpDetails] - Exit");
		}
		// return result as list
		return  accountGrpList;
	}

	public Collection getAccountsDetails(String accountGrp, String currency) throws SwtException{
		// Variable List to hold list RulesDefinition
		List accountList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAccountsDetails ] - " + "Entry");
			accountList =  (List) dashboardDAO.getAccountsDetails(accountGrp, currency);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getAccountsDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountsDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getAccountsDetails] - Exit");
		}
		// return result as list
		return  accountList;
	}


	public Collection getAccountsDetailsByEntity(String entity, String accountGrp, String currency) throws SwtException{
		// Variable List to hold list RulesDefinition
		List accountList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAccountsDetails ] - " + "Entry");
			accountList =  (List) dashboardDAO.getAccountsDetailsByEntity(entity, accountGrp, currency);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getAccountsDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountsDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getAccountsDetails] - Exit");
		}
		// return result as list
		return  accountList;
	}

	
	public void updatePaymentCategory(String paymentReqId, String categoryId)
			throws SwtException {
		try {
			dashboardDAO.updatePaymentCategory(paymentReqId, categoryId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updatePaymentCategory] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updatePaymentCategory",
					DashboardManagerImpl.class);
		}

	}
	
	public Collection unStopReleasePayment(ArrayList paymentReqId, ArrayList previousStatus, String paymentAction,  String userId)
			throws SwtException {
		Collection result = null;
		try {
			result = dashboardDAO.unStopReleasePayment(paymentReqId, previousStatus, paymentAction, userId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [unStopReleasePayment] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"unStopReleasePayment",
					DashboardManagerImpl.class);
		}
		return result;
	}
	public void unStopPayment(String paymentReqId, String userId)
			throws SwtException {
		try {
			dashboardDAO.unStopPayment(paymentReqId, userId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [unStopPayment] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"unStopPayment",
					DashboardManagerImpl.class);
		}
		
	}
	public dashboardResult dataGridDetails(String listEntities, String valueDate, String highValue, String currencyMultiplier, String status, String blockedReason, String currencyCode, 
			String accountGroupId, String accountId, String userId, String initialFilter, String userFilter, String refFilter, String spreadOnly,
			String order, String ascDesc, String  rowbegin, String rowEnd, String archive, String ccyTime, String inputSince) throws SwtException {
		dashboardResult resultData = new dashboardResult();
		try {
		
			resultData = dashboardDAO.dataGridDetails(listEntities, valueDate, highValue, currencyMultiplier,  status, blockedReason,  currencyCode, 
					 accountGroupId,  accountId,  userId,  initialFilter,  userFilter, refFilter, spreadOnly,
					 order,  ascDesc,   rowbegin,  rowEnd, archive, ccyTime, inputSince );
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [unStopPayment] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"unStopPayment",
					DashboardManagerImpl.class);
		}
		return resultData;
		
	}
	
	public AccountGroup getSpreadId(String accountGroupId) throws SwtException{
		AccountGroup accountList = null;
		
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getSpreadId ] - " + "Entry");
			accountList =  dashboardDAO.getSpreadId(accountGroupId);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSpreadId] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSpreadId", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getSpreadId] - Exit");
		}
		// return result as list
		return  accountList;
	}
	
	public HashMap isBlockedStopped(ArrayList paymentReqId, String formatDate)
			throws SwtException {
		HashMap result = new LinkedHashMap<String, String>();
		try {
			result = dashboardDAO.isBlockedStopped(paymentReqId, formatDate);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [isBlockedStopped] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"isBlockedStopped",
					DashboardManagerImpl.class);
		}
		return result;
	}
	public void saveScreenInfo(ArrayList<ScreenInfo> screenInfoList)
			throws SwtException {
		try {
			dashboardDAO.saveScreenInfo(screenInfoList);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveScreenInfo] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveScreenInfo",
					DashboardManagerImpl.class);
		}
		
	}
	public String checkStopProcess() throws SwtException {
		String result = null;
		try {
			result = dashboardDAO.checkStopProcess();
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [checkStopProcess] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"checkStopProcess",
					DashboardManagerImpl.class);
		}
		return result;
		
	}
	
	
	
	
}
