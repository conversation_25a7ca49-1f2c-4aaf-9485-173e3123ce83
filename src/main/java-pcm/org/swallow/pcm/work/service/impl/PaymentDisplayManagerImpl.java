/*
 * @(#)PaymentDisplayManagerImpl.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.work.service.impl;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.work.dao.PaymentDisplayDAO;
import org.swallow.pcm.work.model.MessagesOutput;
import org.swallow.pcm.work.model.PaymentMessage;
import org.swallow.pcm.work.model.PaymentRequest;
import org.swallow.pcm.work.service.PaymentDisplayManager;
@Component("paymentDisplayManager")
public class PaymentDisplayManagerImpl implements PaymentDisplayManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(PaymentDisplayManagerImpl.class);
	@Autowired
	private PaymentDisplayDAO paymentDisplayDAO = null;

	public void setPaymentDisplayDAO(PaymentDisplayDAO paymentDisplayDAO) {
		this.paymentDisplayDAO = paymentDisplayDAO;
	}


	@SuppressWarnings("unchecked")
	public Collection getPaymentMessages(int payReqId) throws SwtException {
		// Variable List to hold list RulesDefinition
		List messageList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			messageList = (List) paymentDisplayDAO.getPaymentMessages(payReqId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentMessage] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPaymentMessage", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPaymentMessage] - Exit");
		}
		// return result as list
		return messageList;
	}

	@SuppressWarnings("unchecked")
	public Collection getPaymentResponses(int payReqId) throws SwtException {
		// Variable List to hold list RulesDefinition
		List messageList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			messageList = (List) paymentDisplayDAO.getPaymentResponses(payReqId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentMessage] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPaymentMessage", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPaymentMessage] - Exit");
		}
		// return result as list
		return messageList;
	}

	@SuppressWarnings("unchecked")
	public Collection getStopRulesList(int payReqId) throws SwtException {
		// Variable List to hold list stopRulesList
		List stopRulesList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getStopRulesList ] - " + "Entry");
			stopRulesList = (List) paymentDisplayDAO.getStopRulesList(payReqId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getStopRulesList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getStopRulesList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getStopRulesList] - Exit");
		}
		// return result as list
		return stopRulesList;
	}
	
	
	public String getStopUnstopReason(int payReqId, boolean stopReason, String dateFormat) throws SwtException{
		// Variable List to hold list stopRulesList
				String reason = null;
				try {
					// log debug message
					log.debug(this.getClass().getName() + " - [ getStopReason ] - " + "Entry");
					reason =  paymentDisplayDAO.getStopUnstopReason(payReqId, stopReason, dateFormat);
				} catch (Exception ex) {
					// log error message
					log.error(this.getClass().getName() + " - Exception Caught in [getStopReason] method : - "
							+ ex.getMessage());
					throw SwtErrorHandler.getInstance().handleException(ex, "getStopReason", this.getClass());
				} finally {
					// log debug message
					log.debug(this.getClass().getName() + " - [getStopReason] - Exit");
				}
				// return result as list
				return reason;
	}
	
	@SuppressWarnings("unchecked")
	public Collection getPayementRequestLogs(int payReqId) throws SwtException {
		// Variable List to hold list stopRulesList
		List logs = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPayementRequestLogs ] - " + "Entry");
			logs = (List) paymentDisplayDAO.getPayementRequestLogs(payReqId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPayementRequestLogs] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPayementRequestLogs", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPayementRequestLogs] - Exit");
		}
		// return result as list
		return logs;
	}

	@SuppressWarnings("unchecked")
	public PaymentMessage getPaymentMessageDetails(int messageId) throws SwtException {
		// Variable List to hold list RulesDefinition
		PaymentMessage messages = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			messages = paymentDisplayDAO.getPaymentMessageDetails(messageId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentMessageDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPaymentMessageDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPaymentMessageDetails] - Exit");
		}
		// return result as list
		return messages;
	}

	@SuppressWarnings("unchecked")
	public MessagesOutput getPaymentResponseDetails(int messageId) throws SwtException {
		// Variable List to hold list RulesDefinition
		MessagesOutput messagesOutput = null;
		try {
			
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			messagesOutput = paymentDisplayDAO.getPaymentResponseDetails(messageId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentMessageDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPaymentMessageDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPaymentMessageDetails] - Exit");
		}
		// return result as list
		return messagesOutput;
	}

	@SuppressWarnings("unchecked")
	public PaymentRequest getPaymentRequestDetails(int payReqId) throws SwtException {
		// Variable List to hold list RulesDefinition
		PaymentRequest paymentRequest = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			paymentRequest = paymentDisplayDAO.getPaymentRequestDetails(payReqId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPaymentMessageDetails] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPaymentMessageDetails", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPaymentMessageDetails] - Exit");
		}
		// return result as list
		return paymentRequest;
	}

}
