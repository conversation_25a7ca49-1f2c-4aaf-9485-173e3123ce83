/*
 * @(#)DashboardAction.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.control.model.Archive;
import org.swallow.control.service.ArchiveManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.model.MenuItem;
import org.swallow.model.ScreenInfo;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.model.core.TabInfo;
import org.swallow.pcm.maintenance.model.core.dashboardResult;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.pcm.work.model.PaymentRequest;
import org.swallow.pcm.work.service.DashboardManager;
import org.swallow.pcm.work.service.PaymentDisplayManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;



/**
 * <AUTHOR>
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/dashboardPCM", "/dashboardPCM.do"})
public class DashboardAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/pc/monitor/dashboarddetailsPCM");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/monitor/dashboardPCM");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("successDetails", "jsp/pc/monitor/dashboarddetailsPCM");
		viewMap.put("spreadDisplay", "jsp/pc/maintenance/spreadprofilesadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		if (!SwtUtil.isEmptyOrNull(method)) {
			int commaIndex = method.indexOf(",");
			if (commaIndex > 0) {
				method = method.substring(0, commaIndex).trim();
			}
		}


		switch (method) {
			case "unspecified":
				return unspecified();
			case "dashboardDetails":
				return dashboardDetails();
			case "spreadDisplay":
				return spreadDisplay();
			case "dashboardAdd":
				return dashboardAdd();
			case "display":
				return display();
			case "saveLogError":
				return saveLogError();
			case "saveRefreshRate":
				return saveRefreshRate();
			case "saveColumnWidthBreakDownScreen":
				return saveColumnWidthBreakDownScreen();
			case "saveColumnOrderBreakDownScreen":
				return saveColumnOrderBreakDownScreen();
			case "saveColumnOrder":
				return saveColumnOrder();
			case "getDashboardDetails":
				return getDashboardDetails();
			case "getSpreadId":
				return getSpreadId();
			case "updatePaymentCategory":
				return updatePaymentCategory();
			case "unStopReleasePayment":
				return unStopReleasePayment();
			case "checkBlockedStoppedPay":
				return checkBlockedStoppedPay();
			case "checkStopProcess":
				return checkStopProcess();
		}


		return unspecified();
	}




	@Autowired
	private DashboardManager dashboardManager = null;

	// Menu item id (Menu id for Dashboard screen)
	private String menuItemId = String
			.valueOf(SwtConstants.MENU_ITEM_PCM_BREKDOWN_DASHBOARD);

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(DashboardAction.class);

	public void setDashboardManager(
			DashboardManager dashboardManager) {
		this.dashboardManager = dashboardManager;
	}


	protected String unspecified() throws SwtException {
		return getView("success");
	}

	public String dashboardDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("method", "dashboardDetails");
		return getView("successDetails");
	}

	public String spreadDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("method", "spreadDisplay");
		return getView("spreadDisplay");
	}

	public String dashboardAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "dashboardAdd");
		return getView("add");
	}

	/**
	 * This function is called from the unspecified() and displays the details
	 * corresponding to default value of entityId
	 *
	 * @return - ActionForward object
	 * @throws SwtException - SwtException object
	 */
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String entityId = null;
		String entity = null;
		String applyCurrencyThreshold = null;
		String applyCurrencyMultiplier= null;
		String spreadOnly = null;
		String systemStatus = null;
		Boolean  tabIsChanged = null;
		Boolean  entityIsChanged = false;
		String  tabChanged = null;
		String  entityChanged = null;
		String volume = null;
		Date systemDate = null;
		String systemDateAsString = null;
		String formatDate = null;
		Date systemDBDate = null;
		String message = null;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		ArrayList<LabelValueBean> allEntity = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		HttpSession session = null;
		SystemFormats systemFormats = null;
		CommonDataManager cdm = null;
		int menuAccessId = 2;
		String listEntities = null;
		String pv_xml_out = null;
		String refreshRate = null;
		String selectedDateAsISO = null;
		String selectedDate = null;
		String[] resultArray = new String[3];
		String inputSince = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - Entry");

			session = request.getSession();
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PCM_DASHBOARD+"", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = PCMConstant.DASHBOARD;
			refreshRate = getRefreshRate(request);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.addAttribute(PCMConstant.LAST_REF_TIME,  SwtUtil.getLastRefTimeOnGMTOffset(request,entityId));
			xmlWriter.addAttribute(PCMConstant.REF_RATE, refreshRate);
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			lstSelect = new ArrayList<SelectInfo>();
			lstOptions = new ArrayList<OptionInfo>();

			entity = request.getParameter("entity");

			if (!SwtUtil.isEmptyOrNull(entity) && !entity.equals("All")) {
				listEntities = convertListToInParameterForQuery(entity);
			}else {
				listEntities= "All";
				entity = "All";
			}

			/*
			 * the entity Dropdown
			 */
			if(entity.indexOf(",") != -1) {
				lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", true));
			}else {
				lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			}

			if(entity.equals("All")) {
				lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE,SwtConstants.ALL_LABEL, true));
			}else {
				lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE,SwtConstants.ALL_LABEL, false));
			}

			allEntity = (ArrayList<LabelValueBean>) SwtUtil
					.convertEntityAcessCollectionLVL(SwtUtil
							.getUserEntityAccessList(session), session);
			Iterator j = allEntity.iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue().equals("") && row.getLabel().equals("")) {
					row =(LabelValueBean) j.next();
				}
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), entity.equals(row.getValue())));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));
			lstOptions = new ArrayList<OptionInfo>();
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));
			responseConstructor.formSelect(lstSelect);

			entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			applyCurrencyThreshold = request.getParameter("applyCurrencyThreshold");
			if (SwtUtil.isEmptyOrNull(applyCurrencyThreshold)) {
				applyCurrencyThreshold = SwtConstants.NO;
			}

			applyCurrencyMultiplier = request.getParameter("applyCurrencyMultiplier");
			if (SwtUtil.isEmptyOrNull(applyCurrencyMultiplier)) {
				applyCurrencyMultiplier = SwtConstants.NO;
			}

			/* Reading the spreadOnly value from the request */
			spreadOnly = request.getParameter("spreadOnly");
			if (spreadOnly == null) {
				spreadOnly = SwtConstants.NO;
			}

			/* Reading the Volume/Value from the request */
			volume = request.getParameter("volume");
			if (volume == null) {
				volume = SwtConstants.NO;
			}

			/* Reading the Volume/Value from the request */
			tabChanged = request.getParameter("tabIsChanged");
			if (SwtUtil.isEmptyOrNull(tabChanged)) {
				tabIsChanged = false;
			} else {
				tabIsChanged = (!tabChanged.equals("true")) ? true : false;
			}

			entityChanged = request.getParameter("entityChanged");
			if (SwtUtil.isEmptyOrNull(entityChanged)) {
				entityIsChanged = false;
			} else {
				entityIsChanged = (entityChanged.equals("true")) ? true : false;
			}

			if(SwtUtil.isEmptyOrNull(selectedDate) ) {
				selectedDate = systemDateAsString;

			}
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);

			if(!entityIsChanged)
				selectedDate = request.getParameter("dateAsString");

			formatDate = request.getParameter("formatDate");

			systemDateAsString = SwtUtil.getSysDateWithFmt(systemDBDate);

			if (SwtUtil.isEmptyOrNull(selectedDate)) {
				selectedDate = PCMUtil.getDefaultDateForPCMMonitor(session, entity);
			}

			systemDate = SwtUtil.getDBSysDatewithTime(systemDBDate);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			formatDate = SwtUtil.getCurrentDateFormat(request.getSession());
			if(!SwtUtil.isEmptyOrNull(selectedDate)) {
				Date date = SwtUtil.parseDate(selectedDate, formatDate);
				selectedDateAsISO = sdf.format(date);
			}else {
				selectedDateAsISO = null;
			}

			resultArray = dashboardManager.currencyDashboardDetails(listEntities,selectedDateAsISO, applyCurrencyThreshold,spreadOnly,userId,volume,applyCurrencyMultiplier);

			/* Reading the spreadingNotCompletedFlag value BD */
			systemStatus = resultArray[1];
			if (SwtUtil.isEmptyOrNull(systemStatus)) {
				systemStatus = "G";
			}
			pv_xml_out = resultArray[0];
			if (!SwtUtil.isEmptyOrNull(resultArray[2]))
				inputSince = SwtUtil.formatDate(resultArray[2].toString(), formatDate);
			else
				inputSince = "";
			xmlWriter.appendText("<advancedGrid>"+pv_xml_out+"</advancedGrid>");


			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.SELECTED_ENTITY, entity);
			responseConstructor.createElement(PCMConstant.CURRENCYTHRESHOLD, applyCurrencyThreshold);
			responseConstructor.createElement(PCMConstant.CURRENCYMULTIPLIER, applyCurrencyMultiplier);
			responseConstructor.createElement(PCMConstant.SPREADONLY, spreadOnly);
			responseConstructor.createElement(PCMConstant.VOLUME, volume);
			responseConstructor.createElement(PCMConstant.SYSTEM_STATUS, systemStatus);
			responseConstructor.createElement(PCMConstant.DATE_FROM_SESSION, systemDateAsString);
			responseConstructor.createElement(PCMConstant.VALUE_DATE, selectedDate);
			responseConstructor.createElement(PCMConstant.INPUT_SINCE_DATE, inputSince);
			responseConstructor.createElement(PCMConstant.TAB_IS_CHANGED, Boolean.toString(tabIsChanged));
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(componentId);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			log.error(this.getClass().getName()
					  + "- [ display ] - " + message);
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			return getView("statechange");
		} catch (Exception e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			log.error(this.getClass().getName()
					  + "- [ PCM Monitor - display ] - " + message);
			request.setAttribute("reply_status_ok", "false");
			if (e.getMessage() != null)
				request.setAttribute("reply_message",e.getMessage());
			else
				request.setAttribute("reply_message","ERROR_PCM_MONITOR");
			return getView("statechange");
		} finally {
			// nullify objects
			entityId = null;
			applyCurrencyThreshold = null;
			systemDate = null;
			systemDateAsString = null;
			systemFormats = null;
			systemDBDate = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ PCM Monitor - display ] - Exit");
		}
	}


	/**
	 * Method to save the reset refresh rate for the given screen and user id
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveLogError() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String error = null;
		String text = null;
		SwtException swtexp = new SwtException();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveLogError ] - Entry ");
			error = request.getParameter("error");
			text = request.getParameter("text");
			swtexp.setUserId(SwtUtil.getCurrentUserId(request.getSession()));
			swtexp.setErrorLogFlag("Y");
			if(SwtUtil.isEmptyOrNull(text) && text.length()>=4000) {
				text = text.substring(0, 3985);
			}

			swtexp.setErrorDesc("PCM MONITOR:"+ text);
			swtexp.setErrorId(error);
			SwtUtil.logErrorInDatabase(swtexp);
			//return getView("statechange");
		} catch (Exception exp) {
			log.debug("Exception Catch in DashboardAction.'saveLogError' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveLogError", DashboardAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message",exp.getMessage());

		}finally {
			log.debug("existing 'saveLogError' method");
		}
		return null;
	}

	/**
	 * This function get the refresh rate to a user defined value
	 *
	 * @throws SwtException -
	 *             SwtException object
	 */
	private String getRefreshRate(HttpServletRequest request) throws SwtException {
		ScreenOptionManager screenOptionManager = null;
		ScreenOption screenOption = null;
		String refreshResult = null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getRefreshRate ] - Entry ");
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			screenOption = new ScreenOption();
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			screenOption.getId().setUserId(
					SwtUtil.getCurrentUserId(request.getSession()));
			screenOption.getId().setScreenId(SwtConstants.DASHBOARD_PCM_ID);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			if (!SwtUtil.isEmptyOrNull(screenOption.getPropertyValue())){
				refreshResult = screenOption.getPropertyValue();
			}else {
				refreshResult = "30";
			}
			return refreshResult;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - [getRefreshRate] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getRefreshRate", DashboardAction.class);
		} finally {
			// nullify object(s)
			screenOptionManager = null;
			screenOption = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getRefreshRate ] - Exit ");
		}
	}

	/**
	 * Method to save the reset refresh rate for the given screen and user id
	 *
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveRefreshRate() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		ScreenOptionManager screenOptionManager = null;
		ScreenOption screenOption = null;
		String refreshRate= null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveRefreshRate ] - Entry ");
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			screenOption = new ScreenOption();
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			screenOption.getId().setUserId(
					SwtUtil.getCurrentUserId(request.getSession()));
			screenOption.getId().setScreenId(SwtConstants.DASHBOARD_PCM_ID);
			refreshRate = request.getParameter("refresh");

			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			if (!SwtUtil.isEmptyOrNull(refreshRate)){
				screenOption.setPropertyValue(refreshRate);
				screenOptionManager.saveRefreshRate(screenOption);
			}
			return display();
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in DashboardAction.'saveRefreshRate' method : "
						   + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message","ERROR_REFRESH_RATE");
		} catch (Exception exp) {
			log.debug("Exception Catch in DashboardAction.'saveRefreshRate' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveRefreshRate", DashboardAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message","ERROR_REFRESH_RATE");

		}finally {
			log.debug("existing 'saveRefreshRate' method");
		}

		return getView("statechange");
	}



	/**
	 * This method is used to bind the column order in request object
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @throws SwtException
	 */
	private void bindColumnOrderInRequest(HttpServletRequest request,
										  String entityId) throws SwtException {
		// To get column order from DB (Comma separated value)
		String columnOrder = null;
		// To hold grid column order
		ArrayList<String> alColumnOrder = null;
		// Property value (split by Comma)
		String[] props = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ bindColumnOrderInRequest ] - Entry ");
			// Get column order from DB (User preference)
			columnOrder = SwtUtil.getPropertyValue(request, entityId,
					menuItemId, "display", "column_order");
			// If user preference not found in DB, set default order
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				columnOrder = "currency,total,balance,waiting,stopped,blocked,cancelled,released,repair,supress,rejected,status";
			}
			/*
			 * Comma special character is used to split and put in String array
			 * variable
			 */
			props = columnOrder.split(",");
			// Initialize list to hold grid column order
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				/* Adding the Column values to ArrayList */
				alColumnOrder.add(prop);
			}
			/*
			 * Setting the Column orders value in request object to show in
			 * screen
			 */
			request.setAttribute("column_order", alColumnOrder);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - [bindColumnOrderInRequest] - Exception - "
					  + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnOrderInRequest", DashboardAction.class);
		} finally {
			// nullify objects
			columnOrder = null;
			alColumnOrder = null;
			props = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ bindColumnOrderInRequest ] - Exit ");
		}
	}

	/**
	 * This method is used to bind the column width in request object
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @throws SwtException
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request,
										  String entityId) throws SwtException {
		// Column name and width in comma separated value
		String columnWidth = null;
		// Hold column width (column name as key and width as value)
		HashMap<String, String> hmColumnWidth = null;
		// Property value (split by Comma)
		String[] props = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ bindColumnWidthInRequest ] - Entry ");
			// Initialize map to store column name and width
			hmColumnWidth = new HashMap<String, String>();
			// Get column name/width from database
			columnWidth = SwtUtil.getPropertyValue(request, entityId,
					menuItemId, "display", "column_width");
			// If user preference not found in DB, set default order
			if (SwtUtil.isEmptyOrNull(columnWidth)) {
				columnWidth = "currency=150,total=140,balance=150,waiting=150,stopped=95,blocked=95,cancelled=100,released=100,repair=100,supress=100,rejected=100,status=114";
			}
			props = columnWidth.split(",");

			for (String prop : props) {
				if (prop.indexOf("=") != -1) {
					String[] propVal = prop.split("=");
					hmColumnWidth.put(propVal[0], propVal[1]);
				}
			}
			/* Setting the HashMap in request object */
			request.setAttribute("column_width", hmColumnWidth);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - Exception - "
					  + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnWidthInRequest", DashboardAction.class);
		} finally {
			// nullify objects
			columnWidth = null;
			hmColumnWidth = null;
			props = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ bindColumnWidthInRequest ] - Exit ");
		}
	}

	/**
	 * This method is used to save the dataGrid's column width in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnWidthBreakDownScreen() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Column width - comma separated value
		String columnWidth = null;
		// Entity id
		String entityId = null;
		HttpSession session = null;
		Collection allEntity = null;
		ScreenInfo screenInfo = new ScreenInfo();
		String userId = null;
		String status = null;
		ArrayList<ScreenInfo> screenInfoList = new ArrayList<ScreenInfo>();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Entry ");
			session = request.getSession();
			status = request.getParameter("status");
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			columnWidth = request.getParameter("width");
			if ((columnWidth = request.getParameter("width")) != null
				&& (entityId = request.getParameter("entityid")) != null) {
				if(entityId.equals("All")) {
					allEntity = SwtUtil
							.convertEntityAcessCollectionLVL(SwtUtil
									.getUserEntityAccessList(session), session);
					Iterator j = allEntity.iterator();
					LabelValueBean row = null;
					String entities = "";
					while (j.hasNext()) {
						row = (LabelValueBean) j.next();
						if(row.getValue() != null)
							entities = entities + row.getValue() + ',';

					}
					entityId = entities.substring(0, entities.length()-1);
				}

				ArrayList entityIdList= new ArrayList(Arrays.asList(entityId.split(",")));

				for (int i = 0; i < entityIdList.size(); i++) {
					screenInfo = new ScreenInfo();
					entityId = entityIdList.get(i).toString();
					screenInfo.getId().setUserId(userId);
					screenInfo.getId().setEntityId(entityId);
					screenInfo.getId().setHostId(SwtUtil.getCurrentHostId());
					screenInfo.getId().setScreenId(menuItemId);
					screenInfo.getId().setPropertyName("column_width_"+status);
					screenInfo.getId().setClsId("display");
					screenInfo.setPropertyValue(columnWidth);
					screenInfoList.add(screenInfo);
					SwtUtil.setPropertyValue(request, entityId, menuItemId,"display", "column_width_"+status, columnWidth);
				}
				dashboardManager.saveScreenInfo(screenInfoList);

			}
			/* Setting the reply_status_ok,reply_message value in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName()
					  + "- [ saveColumnWidth() ] - " + e);
			/*
			 * Setting the reply_status_ok,reply_message value,reply_location in
			 * request object
			 */
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify objects
			columnWidth = null;
			entityId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Exit");
		}
		/* Return Type of this Struts Action and returns to a JSP page */
		return getView("statechange");
	}

	/**
	 * This method is used to save the dataGrid's column order in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnOrderBreakDownScreen() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Column width - comma separated value
		String columnOrder = null;
		// Entity id
		String entityId = null;
		HttpSession session = null;
		Collection allEntity = null;
		String userId = null;
		String status = null;
		ScreenInfo screenInfo = new ScreenInfo();
		ArrayList<ScreenInfo> screenInfoList = new ArrayList<ScreenInfo>();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrderBreakDownScreen ] - Entry ");
			session = request.getSession();
			status = request.getParameter("status");
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			columnOrder = request.getParameter("order");
			if ((columnOrder = request.getParameter("order")) != null
				&& (entityId = request.getParameter("entityid")) != null) {
				if(entityId.equals("All")) {
					allEntity = SwtUtil
							.convertEntityAcessCollectionLVL(SwtUtil
									.getUserEntityAccessList(session), session);
					Iterator j = allEntity.iterator();
					LabelValueBean row = null;
					String entities = "";
					while (j.hasNext()) {
						row = (LabelValueBean) j.next();
						if(row.getValue() != null)
							entities = entities + row.getValue() + ',';

					}
					entityId = entities.substring(0, entities.length()-1);
				}

				ArrayList entityIdList= new ArrayList(Arrays.asList(entityId.split(",")));
				for (int i = 0; i < entityIdList.size(); i++) {
					screenInfo = new ScreenInfo();
					entityId = entityIdList.get(i).toString();
					screenInfo.getId().setUserId(userId);
					screenInfo.getId().setEntityId(entityId);
					screenInfo.getId().setHostId(SwtUtil.getCurrentHostId());
					screenInfo.getId().setScreenId(menuItemId);
					screenInfo.getId().setPropertyName("column_width_" + status);
					screenInfo.getId().setClsId("display");
					screenInfo.setPropertyValue(columnOrder);
					screenInfoList.add(screenInfo);
					SwtUtil.setPropertyValue(request, entityId, menuItemId,"display", "column_width_" + status, columnOrder);
				}
				dashboardManager.saveScreenInfo(screenInfoList);


			}
			/* Setting the reply_status_ok,reply_message value in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName()
					  + "- [ saveColumnWidth() ] - " + e);
			/*
			 * Setting the reply_status_ok,reply_message value,reply_location in
			 * request object
			 */
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify objects
			columnOrder = null;
			entityId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Exit");
		}
		/* Return Type of this Struts Action and returns to a JSP page */
		return getView("statechange");
	}


	/**
	 * This method is used to save the dataGrid column's order in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnOrder() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To hold column order (comma separated value)
		String columnOrder = null;
		// Entity id
		String entityId = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Entry ");
			if ((columnOrder = request.getParameter("order")) != null
				&& (entityId = request.getParameter("entityid")) != null) {
				SwtUtil.setPropertyValue(request, entityId, menuItemId,
						"display", "column_order", columnOrder);
			}
			/* Setting the reply_status_ok,reply_message in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify object(s)
			columnOrder = null;
			entityId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Exit ");
		}
		return getView("statechange");
	}
	/**
	 * This method gets dashboard 2 details.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String getDashboardDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String errorMessage = null;
		String languageId = null;
		SystemFormats systemFormats = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		PCMCurrencyDetailsVO currencyList;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		ArrayList<LabelValueBean> allEntity = null;
		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		String selectedStatus= null;
		String fromScreen= null;
		String selectedEntity = null;
		String selectedAccount = null;
		String selectedAcctWithoutEntity = null;
		String selectedDate = null;
		String applyCcyThreshold = null;
		String applyAbsoluteAmount = null;
		Collection accountGroupList;
		Collection accountsList;
		Collection statusList;
		Collection blockedList;
		Collection categoryList;
		Date systemDBDate = null;
		HttpSession session = null;
		ArrayList<TabInfo> tabs = null;
		String systemDateAsString = null;
		String selectedDateAsISO = null;
		String blockedReason = null;
		String sodBalance = null;
		String confirmedCredit = null;
		String creditLine = null;
		String otherPay = null;
		String releasePay = null;
		String exCreditLine = null;
		String incCreditLine = null;
		String reserve = null;
		String ascDesc = null;
		String order = null;
		String userFilter = null;
		String initialFilter = null;
		String refFilter = null;
		String spreadOnly = null;
		Number pageSize = 0;
		Number refreshRate = 0;
		String rowBegin = null;
		String rowEnd = null;
		String archive = null;
		String displayMultiplier = null;
		String hostId = null;
		Collection<Archive> dbList = null;
		String listEntities = null;
		String timeFrame = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String inputSince = null;
		String inputSinceIso = null;
		String accountEntity= null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getDashboardDetails ] - " + "Entry");
			session = request.getSession();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = PCMConstant.DASHBOARD_LEVEL2;

			selectedCurrencyCode = request.getParameter("currencyCode");
			selectedEntity = request.getParameter("entity");
			accountEntity= request.getParameter("accountEntity");
			selectedAccountGroup = request.getParameter("accountGroup");
			selectedAccount = request.getParameter("account");
			selectedStatus = request.getParameter("status");
			selectedDate = request.getParameter("date");
			applyCcyThreshold = request.getParameter("applyCurrencyThreshold");
			fromScreen = request.getParameter("fromScreen");
			applyAbsoluteAmount = request.getParameter("absoluteAmount");
			blockedReason = request.getParameter("blockedReason");
			userFilter = request.getParameter("userFilter");
			initialFilter = request.getParameter("initialFilter");
			refFilter = request.getParameter("refFilter");
			spreadOnly = request.getParameter("spreadOnly");
			order = request.getParameter("order");

			ascDesc = request.getParameter("ascDesc");
			rowBegin = request.getParameter("rowBegin");
			rowEnd = request.getParameter("rowEnd");
			archive = request.getParameter("archive");
			timeFrame = request.getParameter("timeFrame");
			inputSince = request.getParameter("inputSince");
			String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());

			if (!SwtUtil.isEmptyOrNull(selectedAccount)  && selectedAccount.indexOf('(') != -1 && selectedAccount.indexOf(')') != -1) {
				// Find the index of the first '('
				int indexOfParenthesis = selectedAccount.indexOf('(');
				// Extract the substring from the beginning to '('
				selectedAcctWithoutEntity = selectedAccount.substring(0, indexOfParenthesis).trim();

			}
			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PCM_BREKDOWN_DASHBOARD+"", cdm.getUser());
			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
			//get DBLink
			hostId = putHostIdListInReq(request);
			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
//			if(SwtUtil.isEmptyOrNull(archive)) {
//				archive = archiveManager.getActiveDBLink(hostId, "PCM");
//			}

			if(!SwtUtil.isEmptyOrNull(archive)) {
				archive = archiveManager.getDBlink(archive);
			}
			//currency multiplier
			if(!selectedCurrencyCode.equals("All") &&  !SwtUtil.isEmptyOrNull(selectedCurrencyCode) ) {
				CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
				displayMultiplier = currencyMaintenanceManager.getCurrencyDetailById(selectedCurrencyCode).getDisplayMultiplier();
			}
			//default refresh rate
			refreshRate = SwtUtil.geDefaultRefreshrate();
			//PageSize
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.DEFAULT_SYSTEM_SCREEN_PAGE_SIZE);
			if(SwtUtil.isEmptyOrNull(rowBegin) || SwtUtil.isEmptyOrNull(rowEnd)) {
				rowBegin = "0";
				rowEnd = String.valueOf(pageSize);

			}
			// To get the tabs date as String
			String yesterday = null;
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(accountEntity);
			Calendar cal = Calendar.getInstance();
			cal.setTime(systemDBDate);
			cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) , cal.get(Calendar.DAY_OF_MONTH)-1);
			yesterday = SwtUtil.formatDate(cal.getTime(),
					SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());


			systemDateAsString = SwtUtil.getSysDateWithFmt(systemDBDate);
			if((!SwtUtil.isEmptyOrNull(selectedStatus) && "B".equals(selectedStatus) && !SwtUtil.isEmptyOrNull(blockedReason) && "BV".equals(blockedReason))) {
				selectedDate = "";
				//inputSince = systemDateAsString;

			}
			else if(SwtUtil.isEmptyOrNull(selectedDate) && !fromScreen.equals("search")) {
				selectedDate = systemDateAsString;

			} else if((SwtUtil.isEmptyOrNull(selectedDate) && fromScreen.equals("search")) ) {
				selectedDate = "";
			}
			/******iso convert***/

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			if(!SwtUtil.isEmptyOrNull(selectedDate)) {
				Date date = SwtUtil.parseDate(selectedDate, formatDate );
				selectedDateAsISO = sdf.format(date);
			}
			if(!SwtUtil.isEmptyOrNull(inputSince)) {
				inputSinceIso = SwtUtil.formatDateToIso(inputSince, formatDate);
			}




			if(selectedEntity == null) {
				selectedEntity = SwtUtil.getUserCurrentEntity(session);
			}

			if(accountEntity == null) {
				accountEntity = SwtUtil.getUserCurrentEntity(session);
			}
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, formatDate);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			/****************************Selects********************/
			/****CurrencyCombo***********/
			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedCurrencyCode))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));



			}

			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/****entity Combo***********/
			lstOptions = new ArrayList<OptionInfo>();
			if(selectedEntity.indexOf(",") != -1) {
				lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", true));
			}else {
				lstOptions.add(new OptionInfo("<Multiple values>", "<<Multiple Values>>", false));
			}

			if(selectedEntity.equals("All")) {
				lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE,SwtConstants.ALL_LABEL, true));
			}else {
				lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE,SwtConstants.ALL_LABEL, false));
			}

			allEntity = (ArrayList<LabelValueBean>) SwtUtil
					.convertEntityAcessCollectionLVL(SwtUtil
							.getUserEntityAccessList(session), session);
//			allEntity.add(0, new LabelValueBean(SwtConstants.ALL_LABEL,
//					SwtConstants.ALL_VALUE));

			j = allEntity.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedEntity))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));

			/*****Account Group Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			accountGroupList = getAccountGroupCombo(selectedCurrencyCode);
			j = accountGroupList.iterator();
			row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedAccountGroup))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));



			/*****Account Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			accountsList = getAccountsCombo(selectedEntity, selectedAccountGroup, selectedCurrencyCode);
			j = accountsList.iterator();
			row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedAccount))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_LIST, lstOptions));

			/*****Status Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			statusList = getStatusList();
			j = statusList.iterator();
			row = null;
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedStatus))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.STATUS_LIST, lstOptions));

			/*****Blocked Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			blockedList = getBlockedList();
			j = blockedList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(blockedReason))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.BLOCKED_LIST, lstOptions));

			/*****Category Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			categoryList = getCategoryList();
			j = categoryList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CATEGORY_LIST, lstOptions));

			responseConstructor.formSelect(lstSelect);

			/****************dataGrid******************/
			String dataGrid = null;
			dashboardResult result = new dashboardResult();
			if (!SwtUtil.isEmptyOrNull(accountEntity) && !accountEntity.equals("All")) {
				listEntities = convertListToInParameterForQuery(accountEntity);
			}else {
				listEntities= "All";
				selectedEntity = "All";
			}
			//dataGrid = dashboardManager.dataGridDetails(selectedEntity, selectedDate, applyCcyThreshold, applyAbsoluteAmount, 'W' ,blockedReason,  selectedCurrencyCode, selectedAccountGroup, selectedAccount, userId, initialFilter, userFilter, order, ascDesc, rowbegin, rowEnd);
			result = dashboardManager.dataGridDetails(listEntities, selectedDateAsISO, applyCcyThreshold, applyAbsoluteAmount, selectedStatus ,blockedReason, selectedCurrencyCode, selectedAccountGroup, selectedAcctWithoutEntity, userId, initialFilter, userFilter, refFilter, spreadOnly, order, ascDesc, rowBegin, rowEnd, archive, timeFrame, inputSinceIso);

			dataGrid = result.getXmlOut();
			sodBalance = result.getSodBalance();
			confirmedCredit = result.getConfirmedCredit();
			creditLine = result.getCreditLine();
			releasePay = result.getReleasedPayments();
			otherPay = result.getFcastDr();
			incCreditLine = result.getAvailabelLiqInc();
			exCreditLine = result.getAvailableLiqEx();
			reserve = result.getReservedBalance();
			if(!SwtUtil.isEmptyOrNull(result.getInputSince()))
				inputSince = SwtUtil.formatDate(result.getInputSince(), formatDate);
			xmlWriter.appendText(dataGrid);

			/*************************************Singletons******************************/
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.CURRENCYCODE, selectedCurrencyCode);
			responseConstructor.createElement(PCMConstant.SELECTED_ENTITY, selectedEntity);
			responseConstructor.createElement(PCMConstant.STATUS, selectedStatus);
			responseConstructor.createElement(PCMConstant.DATE_FROM_SESSION,  systemDateAsString);
			responseConstructor.createElement(PCMConstant.VALUE_DATE, selectedDate);
			responseConstructor.createElement(PCMConstant.INPUT_SINCE_DATE, inputSince);
			if(!SwtUtil.isEmptyOrNull(displayMultiplier)) {
				responseConstructor.createElement(PCMConstant.MULTIPLIER, (!displayMultiplier.equals("N") )?  "("+displayMultiplier+")" : "");
			}

			responseConstructor.createElement(PCMConstant.SOD_BALANCE, sodBalance);
			responseConstructor.createElement(PCMConstant.CONFIRMED_CREDIT, !SwtUtil.isEmptyOrNull(confirmedCredit) ?  confirmedCredit : "");
			responseConstructor.createElement(PCMConstant.CREDIT_LINE, creditLine);
			responseConstructor.createElement(PCMConstant.RELEASE_PAYMENT, releasePay);
			responseConstructor.createElement(PCMConstant.OTHER_PAYMENTS, otherPay);
			responseConstructor.createElement(PCMConstant.INC_CREDIT_LINE, incCreditLine);
			responseConstructor.createElement(PCMConstant.EX_CREDIT_LINE, exCreditLine);
			responseConstructor.createElement(PCMConstant.RESERVE_BALANCED, reserve);
			responseConstructor.createElement(PCMConstant.PAGE_SIZE, pageSize.toString());
			responseConstructor.createElement(PCMConstant.REF_RATE, refreshRate.toString());

			xmlWriter.endElement(SwtConstants.SINGLETONS);


			xmlWriter.endElement(componentId);
			responseHandler.sendResponse(response, xmlWriter.getData());

			//sendDisplayResponse(request, mapping, categoryListDetails, languageId, systemFormats);

		} catch (SwtException swtexp) {
			log.error(
					this.getClass().getName() + " - Exception Catched in [getDashboardDetails] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [getDashboardDetails] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getDashboardDetails] - Exit");
			pageSize = 0;

		}

		return null;
	}


	/**
	 * This method get spread i by accountgroup id.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getSpreadId() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String accountGroupId = null;
		AccountGroup result = new AccountGroup();
		String spreadId = null;
		SwtXMLWriter xmlWriter = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		String componentId = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [getSpreadId] - Entry");
			accountGroupId = request.getParameter("accountGroupId");
			result = dashboardManager.getSpreadId(accountGroupId);
			if(result != null) {
				spreadId =result.getSpreadProfileId();
			} else {
				spreadId = "";
			}

			responseHandler = new ResponseHandler();
			componentId = PCMConstant.DASHBOARD_LEVEL2;
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.SPREAD_ID, spreadId);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			xmlWriter.endElement(componentId);

			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "SUCCESS");
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [getSpreadId] - Exception -" + ex.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(ex, "getSpreadId", DashboardAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			return getView("fail");

		} finally {
			log.debug(this.getClass().getName() + "- [getSpreadId] - Exit");
		}

	}

	/**
	 * This method updates PaymentCategory into the DB.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String updatePaymentCategory() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String paymentReqId = null;
		String categoryId = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [updatePaymentCategory] - Entry");
			paymentReqId = request.getParameter("paymentId");
			categoryId = request.getParameter("categoryId");


			dashboardManager.updatePaymentCategory(paymentReqId, categoryId);

			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "SUCCESS");

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [updatePaymentCategory] - Exception -" + ex.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(ex, "updatePaymentCategory", DashboardAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");

		} finally {
			log.debug(this.getClass().getName() + "- [updatePaymentCategory] - Exit");
		}
		return getView("statechange");
	}




	/**
	 * This method unstop or release the payment request.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unStopReleasePayment() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String paymentReqId = null;
		String paymentAction = null;
		String previousStatus = null;
		String userId= null;
		Collection resultUnStopRelease =null ;
		Iterator iterator;
		PaymentRequest paymentRequest = null;
		List<PaymentRequest> paymentRequests = new ArrayList<PaymentRequest>();
		List listOfStatusCode = new ArrayList();
		LabelValueBean row = new LabelValueBean("", "");
		int index = 0;
		int payId;
		String returnCode = null;

		try {
			// log debug message
			paymentRequest = new PaymentRequest();
			log.debug(this.getClass().getName() + "- [unStopReleasePayment] - Entry");
			paymentReqId = request.getParameter("paymentId");
			paymentAction = request.getParameter("paymentAction");
			previousStatus = request.getParameter("previousStatus");
			if(!SwtUtil.isEmptyOrNull(previousStatus) && !SwtUtil.isEmptyOrNull(paymentReqId)) {
				userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
				ArrayList paymentIdList= new ArrayList(Arrays.asList(paymentReqId.split(",")));
				ArrayList statusList= new ArrayList(Arrays.asList(previousStatus.split(",")));
				resultUnStopRelease = dashboardManager.unStopReleasePayment(paymentIdList,statusList, paymentAction, userId);
				if(resultUnStopRelease.size()> 0) {
					iterator = resultUnStopRelease.iterator();
					while (iterator.hasNext()) {
						row = (LabelValueBean) iterator.next();
						payId = Integer.parseInt(row.getLabel());
						returnCode = row.getValue();
						PaymentDisplayManager paymentDisplayManager = (PaymentDisplayManager) SwtUtil.getBean("paymentDisplayManager");
						paymentRequest = paymentDisplayManager.getPaymentRequestDetails(payId);
						if(paymentRequest != null) {
							paymentRequests.add(paymentRequest);
							listOfStatusCode.add(returnCode);

						}
						index++;

					}

				}
				return paymentErrorGrid(request,paymentRequests,listOfStatusCode, paymentAction);
			}

		} catch (SwtException swtExp) {
			// log error message
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"ERROR_PAYMENT_ENGINE");
			}else {
				request.setAttribute("reply_message",
						"GENERIC_ERROR");
			}


		} finally {
			log.debug(this.getClass().getName() + "- [unStopReleasePayment] - Exit");
		}
		return getView("statechange");
	}


	/**
	 * This method is to check if Back valued is stopped or not.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkBlockedStoppedPay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String paymentReqId = null;
		HashMap<String, String> result = new LinkedHashMap<String, String>();
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [checkBlockedStoppedPay] - Entry");
			paymentReqId = request.getParameter("paymentId");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "payIdStopRule";
			xmlWriter.startElement(componentId);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			if(!SwtUtil.isEmptyOrNull(paymentReqId)) {
				ArrayList paymentIdList= new ArrayList(Arrays.asList(paymentReqId.split(",")));
				result = dashboardManager.isBlockedStopped(paymentIdList, formatDate);

				String resultMap ="";
				for (Map.Entry<String, String> entry : result.entrySet()) {
					String key = entry.getKey();
					String value = entry.getValue();
					resultMap+=value+";";
					// ...
				}
				if(resultMap.length()>0) {
					resultMap = resultMap.substring(0, resultMap.length()-1);
				}
				responseConstructor.createElement(PCMConstant.PAYID_RULE, resultMap);

			} else {
				responseConstructor.createElement(PCMConstant.PAYID_RULE, "");
			}
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(componentId);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException swtExp) {
			// log error message
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"ERROR_PAYMENT_ENGINE");
			}else {
				request.setAttribute("reply_message",
						"GENERIC_ERROR");
			}


		} finally {
			log.debug(this.getClass().getName() + "- [unStopReleasePayment] - Exit");
		}
		return getView("statechange");
	}
	/**
	 * This method is to check if stop process is running or not.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkStopProcess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String result = null;


		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [checkStopProcess] - Entry");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "stopProcess";
			xmlWriter.startElement(componentId);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			result = dashboardManager.checkStopProcess();
			responseConstructor.createElement(PCMConstant.IS_STOPPROCESS_RUNNING, result);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(componentId);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException swtExp) {
			request.setAttribute("reply_message",
					"GENERIC_ERROR");

		} finally {
			log.debug(this.getClass().getName() + "- [checkStopProcess] - Exit");
		}
		return getView("statechange");
	}
	public String paymentErrorGrid(HttpServletRequest request, List<PaymentRequest> paymentRequests, List listOfStatusCode, String paymentAction)  {
		// To send response to client
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		int index = 0;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		try {

			log.debug(this.getClass().getName() + " - [ paymentErrorGrid ] - " + "Entry");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "ErrorsOfUpdate";
			xmlWriter.startElement(componentId);
			if(paymentRequests.size()> 0) {
				responseConstructor.formGridStart();
				responseConstructor.formPaging(null);
				responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns));

				responseConstructor.formRowsStart(paymentRequests.size());
				for (Iterator<PaymentRequest> it = paymentRequests.iterator(); it.hasNext();) {
					// Obtain rules definition tag from iterator
					PaymentRequest payReq = (PaymentRequest) it.next();
					responseConstructor.formRowStart();

					String amountFormatted = payReq.getAmount()== null ? "": SwtUtil.formatCurrency(payReq.getCurrencyCode(), payReq.getAmount().doubleValue());
					String dateFormatted = SwtUtil.formatDate(payReq.getValueDate().toString(), SwtUtil.getCurrentDateFormat(request.getSession()));
					responseConstructor.createRowElement(PCMConstant.STATUS, decodeStatus(payReq.getStatus()));
					responseConstructor.createRowElement(PCMConstant.PAYMENT_ID, payReq.getPayReqId().toString());
					responseConstructor.createRowElement(PCMConstant.CATEGORY_ID, payReq.getCategoryId());
					responseConstructor.createRowElement(PCMConstant.ENTITY, payReq.getEntityId());
					responseConstructor.createRowElement(PCMConstant.ACCOUNT, payReq.getAccountId());
					responseConstructor.createRowElement(PCMConstant.AMOUNT, amountFormatted);
					responseConstructor.createRowElement(PCMConstant.VALUE_DATE, dateFormatted);
					responseConstructor.createRowElement(PCMConstant.SOURCE, payReq.getSourceId());
					if(listOfStatusCode.get(index).toString().equals("ERR#STATUS")) {
						responseConstructor.createRowElement(PCMConstant.REASON, "Payment status has changed by a running process or by another user");
					} else if(listOfStatusCode.get(index).toString().equals("ERR#ACTION")) {
						responseConstructor.createRowElement(PCMConstant.REASON, "Inappropriate action for this status");
					} else if(listOfStatusCode.get(index).toString().equals("ERR#LOCK")) {
						responseConstructor.createRowElement(PCMConstant.REASON, "Payment is locked");
					} else if(listOfStatusCode.get(index).toString().equals("ERR#NOT_FOUND")) {
						responseConstructor.createRowElement(PCMConstant.REASON, "Payment is not found");
					}

					responseConstructor.formRowEnd();
					index ++;
				}

				responseConstructor.formRowsEnd();
				responseConstructor.formGridEnd();
			} else {
				//Payements are OK
				request.setAttribute("reply_status_ok", "true");
				request.setAttribute("reply_message", "SUCCESS");
			}

			xmlWriter.endElement(componentId);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		}catch (SwtException ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName()
					  + " - SwtException Catched in [paymentErrorGrid] method : - "
					  + ex.getMessage());
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName()
					  + " - Exception Catched in [paymentErrorGrid] method : - "
					  + ex.getMessage());
			ex.printStackTrace();
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ paymentErrorGrid ] - Exit");
		}
		return null;

	}
	public String decodeStatus(String status) {
		String statusCode = null ;
		switch (status) {
			case "S":
				statusCode = "Stopped";
				break;
			case "W":
				statusCode = "Waiting";
				break;
			case "B":
				statusCode = "Blocked";
				break;
			case "R":
				statusCode = "Released";
				break;
			case "C":
				statusCode = "Cancelled";
				break;

			default:
				statusCode = "All";
				break;
		}

		return statusCode;

	}
	/**
	 * This method creates sample column details
	 *
	 * @param width
	 *            - passing columns widths
	 * @param columnOrder
	 *            - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		String[] columnOrderProp = null;
		Iterator<String> columnOrderItr = null;
		LinkedHashMap<String, String> widths = null;
		String[] columnWidthProperty = null;
		List<ColumnInfo> lstColumns = null;
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		ArrayList<String> lstHiddenColunms = null;
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getRulesDefGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				width =   PCMConstant.STATUS + "=100,"
						  +PCMConstant.PAYMENT_ID + "=120,"
						  +PCMConstant.CATEGORY_ID + "=150,"
						  + PCMConstant.ENTITY + "=100,"
						  + PCMConstant.ACCOUNT + "=130,"
						  + PCMConstant.AMOUNT + "=120,"
						  + PCMConstant.VALUE_DATE + "=120,"
						  + PCMConstant.SOURCE + "=100,"
						  + PCMConstant.REASON + "=400";
			}

			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				columnOrder =   PCMConstant.STATUS + ","
								+PCMConstant.PAYMENT_ID + ","
								+PCMConstant.CATEGORY_ID + ","
								+ PCMConstant.ENTITY + ","
								+ PCMConstant.ACCOUNT + ","
								+ PCMConstant.AMOUNT + ","
								+ PCMConstant.VALUE_DATE  + ","
								+ PCMConstant.SOURCE  + ","
								+ PCMConstant.REASON ;
			}
			orders = new ArrayList<String>();
			columnOrderProp = columnOrder.split(",");
			for (int i = 0; i < columnOrderProp.length; i++) {
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					//boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if(columnKey.equals(lstHiddenColunms.get(j))){
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				if (order.equals(PCMConstant.STATUS))
					lstColumns.add(new ColumnInfo(
							PCMConstant.STATUS_COLUMN_HEADER,
							PCMConstant.STATUS,
							PCMConstant.COLUMN_TYPE_STRING,
							0,
							Integer.parseInt(widths.get(PCMConstant.STATUS)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.STATUS)));
				if (order.equals(PCMConstant.PAYMENT_ID))
					lstColumns.add(new ColumnInfo(
							PCMConstant.PAYMENT_ID_COLUMN_HEADER,
							PCMConstant.PAYMENT_ID,
							PCMConstant.COLUMN_TYPE_NUMBER,
							1,
							Integer.parseInt(widths.get(PCMConstant.PAYMENT_ID)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.PAYMENT_ID)));
				if (order.equals(PCMConstant.CATEGORY_ID))
					lstColumns.add(new ColumnInfo(
							PCMConstant.CATEGORY_ID_HEADER,
							PCMConstant.CATEGORY_ID,
							PCMConstant.COLUMN_TYPE_STRING,
							2,
							Integer.parseInt(widths.get(PCMConstant.CATEGORY_ID)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.CATEGORY_ID)));
				if (order.equals(PCMConstant.ENTITY))
					lstColumns.add(new ColumnInfo(
							PCMConstant.ENTITY_COLUMN_HEADER,
							PCMConstant.ENTITY,
							PCMConstant.COLUMN_TYPE_STRING,
							3,
							Integer.parseInt(widths.get(PCMConstant.ENTITY)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.ENTITY)));
				if (order.equals(PCMConstant.ACCOUNT))
					lstColumns.add(new ColumnInfo(
							PCMConstant.ACCOUNT_COLUMN_HEADER,
							PCMConstant.ACCOUNT,
							PCMConstant.COLUMN_TYPE_STRING,
							4,
							Integer.parseInt(widths.get(PCMConstant.ACCOUNT)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.ACCOUNT)));
				if (order.equals(PCMConstant.AMOUNT))
					lstColumns.add(new ColumnInfo(
							PCMConstant.AMOUNT_COLUMN_HEADER,
							PCMConstant.AMOUNT,
							PCMConstant.COLUMN_TYPE_NUMBER,
							5,
							Integer.parseInt(widths.get(PCMConstant.AMOUNT)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.AMOUNT)));
				if (order.equals(PCMConstant.VALUE_DATE))
					lstColumns.add(new ColumnInfo(
							PCMConstant.VALUE_DATE_COLUMN_HEADER,
							PCMConstant.VALUE_DATE,
							PCMConstant.COLUMN_TYPE_STRING,
							6,
							Integer.parseInt(widths.get(PCMConstant.VALUE_DATE)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.VALUE_DATE)));
				if (order.equals(PCMConstant.SOURCE))
					lstColumns.add(new ColumnInfo(
							PCMConstant.SOURCE_COLUMN_HEADER,
							PCMConstant.SOURCE,
							PCMConstant.COLUMN_TYPE_STRING,
							7,
							Integer.parseInt(widths.get(PCMConstant.SOURCE)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.SOURCE)));
				if (order.equals(PCMConstant.REASON))
					lstColumns.add(new ColumnInfo(
							PCMConstant.REASON_COLUMN_HEADER,
							PCMConstant.REASON,
							PCMConstant.COLUMN_TYPE_STRING,
							8,
							Integer.parseInt(widths.get(PCMConstant.REASON)),
							true,
							true, hiddenColumnsMap.get(PCMConstant.REASON)));

			}
		}
		catch (Exception ex) {
			// log error message
			log
					.error(this.getClass().getName()
						   + " - Exception Catched in [getGridColumns] method : - "
						   + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/* Method to collect the account group list
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getAccountGroupCombo(String currency) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Entry");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection accountColl;
		Iterator accountItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		/* Collect the multiplier list from the Cache manager */
		accountColl = (Collection) dashboardManager.getAccountGrpDetails(currency);

		/* Iterate the multiplierlist */
		accountItr =  accountColl.iterator();
		accountColl = new ArrayList();
		L1 = new LabelValueBean("", "");
		AccountGroup row = null;
		while (accountItr.hasNext()) {
			row =(AccountGroup) accountItr.next();
			L1 = new LabelValueBean(row.getDescription(), row.getId().getAccGrpId());
			accountColl.add(L1); // None
			index++;

		}
		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Exit");
		return accountColl;

	}
	/* Method to collect the accounts  list of account group
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getAccountsCombo(String entity, String accountGroup, String currency) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountsCombo] - "
				  + "Entry");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection accountColl;
		Iterator accountItr = null;
		LabelValueBean L1;
		/* Collect the multiplier list from the Cache manager */
		accountColl = (Collection) dashboardManager.getAccountsDetailsByEntity(entity, accountGroup, currency);

		/* Iterate the multiplierlist */
		accountItr =  accountColl.iterator();
		accountColl = new ArrayList();
		L1 = new LabelValueBean("", "");
		AcctMaintenance row = null;
		while (accountItr.hasNext()) {
			row =(AcctMaintenance) accountItr.next();
			L1 = new LabelValueBean(row.getAcctname(), row.getId().getAccountId()+"("+row.getId().getEntityId()+")");
			accountColl.add(L1); // None
			index++;

		}
		log.debug(this.getClass().getName() + " - [getAccountsCombo] - "
				  + "Exit");
		return accountColl;

	}
	/**
	 * Get the Status list
	 *
	 * @param
	 * @return
	 * @throws SwtException
	 */
	private Collection getStatusList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getStatusList] - "
				  + "Entry");

		/* Method's local variable and class instance declaration */
		Collection statusColl;
		LabelValueBean L1;
		LabelValueBean L2;
		LabelValueBean L3;
		LabelValueBean L4;
		LabelValueBean L5;

		statusColl = new ArrayList();
		L1 = new LabelValueBean( PCMConstant.WAITING_STATUS, "W");
		L2 = new LabelValueBean(PCMConstant.RELEASED_STATUS, "R");
		L3 = new LabelValueBean(PCMConstant.STOPPED_STATUS, "S");
		L4 = new LabelValueBean(PCMConstant.CANCELLED_STATUS, "C");
		L5 = new LabelValueBean(PCMConstant.BLOCKED_STATUS, "B");

		statusColl.add(L1);
		statusColl.add(L2);
		statusColl.add(L3);
		statusColl.add(L4);
		statusColl.add(L5);


		log.debug(this.getClass().getName() + " - [getStatusList] - "
				  + "Exit");
		return statusColl;

	}

	/**
	 * Get the blocked list
	 *
	 * @param
	 * @return
	 * @throws SwtException
	 */
	private Collection getBlockedList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getBlockedList] - "
				  + "Entry");

		/* Method's local variable and class instance declaration */
		Collection blockedColl;
		LabelValueBean L0;
		LabelValueBean L1;
		LabelValueBean L2;
		LabelValueBean L3;
		LabelValueBean L4;

		blockedColl = new ArrayList();
		L0 = new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE);
		L1 = new LabelValueBean( PCMConstant.BACK_VALUED, "BV");
		L2 = new LabelValueBean( PCMConstant.BLOCKED_INPUT, "BI");
		L3 = new LabelValueBean(PCMConstant.BLOCKED_CUTOFF, "BC");
		L4 = new LabelValueBean(PCMConstant.BLOCKED_STOPPED, "BS");

		blockedColl.add(L0);
		blockedColl.add(L1);
		blockedColl.add(L2);
		blockedColl.add(L3);
		blockedColl.add(L4);

		log.debug(this.getClass().getName() + " - [getBlockedList] - "
				  + "Exit");
		return blockedColl;

	}
	private Collection getCategoryList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getDefaultCategoryList] - "
				  + "Entry");
		CategoryMaintenanceManager categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil.getBean("categoryMaintenanceManager");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection categoryColl;
		Iterator categoryItr = null;
		LabelValueBean L1;
		categoryColl = (Collection) categoryMaintenanceManager.getCategoryCombo();

		categoryItr =  categoryColl.iterator();
		categoryColl = new ArrayList();
		L1 = new LabelValueBean("", "");
		Category row = null;
		while (categoryItr.hasNext()) {
			row =(Category) categoryItr.next();
			L1 = new LabelValueBean(row.getId().getCategoryId(), row.getCategoryName());
			categoryColl.add(L1); // None
			index++;

		}

		log.debug(this.getClass().getName() + " - [getCategoryList] - "
				  + "Exit");
		return categoryColl;

	}


	private static String convertListToInParameterForQuery(String listAsString) {
		String result = "";
		if (!SwtUtil.isEmptyOrNull(listAsString)) {
			if (listAsString.substring(listAsString.length() - 1).equals(",")) {
				listAsString = listAsString.substring(0, (listAsString.length() - 1));
			}
			String[] listArray = listAsString.split(",(\\s)?");
			result = StringUtils.join(listArray, "','");
			result = "'" + result + "'";

		}

		return result;
	}
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putHostIdListInReq' method");
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		log.debug("exiting 'putHostIdListInReq' method");
		return hostId;

	}

}

