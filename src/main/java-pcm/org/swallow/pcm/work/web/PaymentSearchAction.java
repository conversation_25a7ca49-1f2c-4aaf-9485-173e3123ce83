
/*
 * @(#)PaymentSearchAction.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.service.PartyManager;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.PCMCurrency;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;
import org.swallow.pcm.maintenance.service.CurrencyMaintenanceManager;
import org.swallow.pcm.maintenance.web.CurrencyMaintenanceAction;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.pcm.work.service.DashboardManager;
import org.swallow.pcm.work.service.PaymentSearchManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/paymentSearchPCM", "/paymentSearchPCM.do"})
public class PaymentSearchAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("displayArchive", "jsp/pc/work/paymentarchivesearch");
		viewMap.put("success", "jsp/pc/work/paymentsearch");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("partysearch", "jsp/pc/work/partysearch");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "displayArchive":
				return displayArchive();
			case "partysearch":
				return partysearch();
			case "display":
				return display();
			case "partySearch":
				return partySearch();
			case "partySearchDisplay":
				return partySearchDisplay();
			case "partySearchResult":
				return partySearchResult();
		}


		return unspecified();
	}




	@Autowired
	private PaymentSearchManager paymentSearchManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(PaymentSearchAction.class);

	public void setPaymentSearchManager(PaymentSearchManager paymentSearchManager) {
		this.paymentSearchManager = paymentSearchManager;
	}

	protected String unspecified() throws SwtException {
		return getView("success");
	}



	public String displayArchive() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "displayArchive");
		return getView("displayArchive");
	}


	public String partysearch() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "partysearch");
		return getView("partysearch");
	}

	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 *
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String display() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();



		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String languageId = null;
		PCMCurrencyDetailsVO currencyList;
		Collection countryList;
		Collection sourcesList;
		Collection messageTypesList;
		Collection accountGroupList;
		SystemFormats systemFormats = null;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;
		Collection accountsList;

		Collection statusList;
		Collection blockedList;
		Collection categoryList;
		Collection partiesList;
		Collection archiveList;


		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		String selectedStatus= null;
		String selectedEntity = null;
		String selectedAccount = null;
		String selectedCategory = null;

		String selectedSource = null;
		String selectedMessageType = null;


		try {

			log.debug(this.getClass().getName() + " - [display] - " + "Entry");

			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());

			selectedCurrencyCode = request.getParameter("currencyCode");
			selectedAccountGroup = request.getParameter("accountGroup");
			selectedAccount = request.getParameter("account");
			selectedEntity = request.getParameter("entity");

			if(SwtUtil.isEmptyOrNull(selectedEntity)) {
				selectedEntity = "All";
			}
			if(SwtUtil.isEmptyOrNull(selectedAccountGroup)) {
				selectedAccountGroup = "All";
			}
			if(SwtUtil.isEmptyOrNull(selectedAccount)) {
				selectedAccount = "All";
			}
			if(SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				selectedCurrencyCode = "All";
			}

			selectedCategory = request.getParameter("category");
			selectedSource = request.getParameter("source");
			selectedMessageType = request.getParameter("message");


			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "paymentSearch";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(PCMConstant.FOUR_EYES_REQUIRED, !SwtUtil.getMaintainAnyPCFeature(userId));
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.addAttribute(PCMConstant.USER_DEFAULT_ENTITY, SwtUtil.getUserCurrentEntity(request.getSession()));
			xmlWriter.startElement(PCMConstant.PAYMENT_SEARCH_ROOT_TAG);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.HELPURL, "swf/system/help/Help.swf?message=");
			responseConstructor.createElement(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			responseConstructor.createElement(PCMConstant.CURRENCYCODE, selectedCurrencyCode);
			responseConstructor.createElement(PCMConstant.ACC_GP_ID, selectedAccountGroup);
			responseConstructor.createElement(PCMConstant.ACCOUNT, selectedAccount);
			responseConstructor.createElement(PCMConstant.ENTITY, selectedEntity);
			responseConstructor.createElement(PCMConstant.STATUS, selectedStatus);


//			String partiesAsXML = getPartiesCombo(SwtUtil.getCurrentHostId(), "RABONL2U");
//			responseConstructor.createElement("partiesAsJSON", SwtUtil.xmlToJson(partiesAsXML));

			//System.err.println(SwtUtil.xmlToJson(partiesAsXML));
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE, false));

			CurrencyMaintenanceManager currencyMaintenanceManager = (CurrencyMaintenanceManager) SwtUtil.getBean("currencyMaintenanceManager");
			currencyList = currencyMaintenanceManager.getCurrencyCombo();
			Iterator j = ((List<PCMCurrency>) currencyList.getCurrencyList()).iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedCurrencyCode))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/*
			 * the sources Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("All", "All", false));

			sourcesList = getSourceList();
			j = sourcesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedSource))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.SOURCESLIST, lstOptions));
			/*
			 * the message types Dropdown
			 */

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("All", "All", false));
			messageTypesList = getMessageTypesList();
			j = messageTypesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedMessageType))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.MESSAGE_TYPESLIST, lstOptions));
			/*
			 * the accountGroups types Dropdown
			 */
//
//			// options drop down list
//			lstOptions = new ArrayList<OptionInfo>();
//			lstOptions.add(new OptionInfo("All", "All", false));
//			accountGroupList = getAccountGroupsList();
//			j = accountGroupList.iterator();
//			row = null;
//			while (j.hasNext()) {
//				row = (LabelValueBean) j.next();
//				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
//			}
//			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));

			/*****Account Group Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			accountGroupList = getAccountGroupCombo(selectedCurrencyCode);
			j = accountGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedAccountGroup))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));



			ArrayList<LabelValueBean> allEntity = null;

			/****entity Combo***********/

			allEntity = (ArrayList<LabelValueBean>) SwtUtil
					.convertEntityAcessCollectionLVL(SwtUtil
							.getUserEntityAccessList(request.getSession()), request.getSession());
			allEntity.add(0, new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			lstOptions = new ArrayList<OptionInfo>();
			j = allEntity.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedEntity))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));



			/*****Account Combo***********/
			lstOptions = new ArrayList<OptionInfo>();
			accountsList = getAccountsCombo(selectedAccountGroup, selectedCurrencyCode);
			j = accountsList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedAccount))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_LIST, lstOptions));


			/*****Status Combo***********/

		/*	lstOptions = new ArrayList<OptionInfo>();
			statusList = getStatusList();
			j = statusList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.STATUS_LIST, lstOptions));*/

			/*****Blocked Combo***********/

		/*	lstOptions = new ArrayList<OptionInfo>();
			blockedList = getBlockedList();
			j = blockedList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.BLOCKED_LIST, lstOptions));/*

			/*****Category Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			categoryList = getCategoryList();
			j = categoryList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedCategory))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.CATEGORY_LIST, lstOptions));

			/*****archive Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			archiveList = getArchiveList();
			j = archiveList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedCategory))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ARCHIVE_LIST, lstOptions));

			/*****Parties Combo***********/

			lstOptions = new ArrayList<OptionInfo>();

//			String partiesAsXML = getPartiesCombo(SwtUtil.getCurrentHostId(), "RABONL2U");
//			lstSelect.add(new SelectInfo(partiesAsXML));


			// Add the selects node
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(PCMConstant.PAYMENT_SEARCH_ROOT_TAG);

//			System.err.println("xmlWriter.getData()= "+ xmlWriter.getData());
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [display] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [display] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "display", CurrencyMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}


	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCountryList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = CacheManager.getInstance().getCountries();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCounterPartyList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = new ArrayList<>();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getSourceList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = PCMUtil.getSourceList();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getMessageTypesList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = PCMUtil.getMessageTypeList();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getAccountGroupsList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		List<AccountGroup> accountGroupsDetails = new ArrayList<AccountGroup>();
		ArrayList<LabelValueBean> listAccountsGroup = null;
		try {
			log.debug(this.getClass().getName() + " - [displayAccountGroupsGrid] - " + "Entry");
			LabelValueBean bean = null;
			listAccountsGroup = new ArrayList<LabelValueBean>();
			// Retrieves current system formats
			AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
					.getBean("accountGroupsMaintenanceManager");
			accountGroupsDetails = accountGroupsMaintenanceManager.getAccountGroupDetailList(null);
			for (int i = 0; i < accountGroupsDetails.size(); i++) {
				bean = new LabelValueBean(accountGroupsDetails.get(i).getDescription() != null?accountGroupsDetails.get(i).getDescription():"",
						""+accountGroupsDetails.get(i).getId().getAccGrpId());
				listAccountsGroup.add(bean);
			}

		} catch (SwtException e) {

		}

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return listAccountsGroup;
	}

	/**
	 * Method to collect the currency list from cache manager and set the currency
	 * list to the request attribute
	 *
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private Collection getCurrencyList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = CacheManager.getInstance().getCurrencies();

		log.debug(this.getClass().getName() + " - [getCurrencyList] - " + "Exit");
		return coll;
	}

	/* Method to collect the accounts  list of account group
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getAccountsCombo(String accountGroup, String currencyCode) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountsCombo] - "
				  + "Entry");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection accountColl;
		Iterator accountItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		DashboardManager dashboardManager = (DashboardManager) SwtUtil.getBean("dashboardManager");
		/* Collect the multiplier list from the Cache manager */
		if(SwtUtil.isEmptyOrNull(currencyCode))
			currencyCode = "all";

		if(SwtUtil.isEmptyOrNull(accountGroup))
			accountGroup = "all";


		accountColl = (Collection) dashboardManager.getAccountsDetails(accountGroup, currencyCode);

		/* Iterate the multiplierlist */
		accountItr =  accountColl.iterator();
		accountColl = new ArrayList();
		L1 = new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE);
		L2 = new LabelValueBean("", "");
		AcctMaintenance row = null;
		accountColl.add(L1);
		while (accountItr.hasNext()) {
			row =(AcctMaintenance) accountItr.next();
			L2 = new LabelValueBean (row.getAcctname(), row.getId().getAccountId());
			accountColl.add(L2); // None
			index++;

		}
		log.debug(this.getClass().getName() + " - [getAccountsCombo] - "
				  + "Exit");
		return accountColl;

	}

	/**
	 * Get the Status list
	 *
	 * @param
	 * @return
	 * @throws SwtException
	 */
	private Collection getStatusList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getStatusList] - "
				  + "Entry");

		/* Method's local variable and class instance declaration */
		Collection statusColl;
		LabelValueBean L1;
		LabelValueBean L2;
		LabelValueBean L3;
		LabelValueBean L4;
		LabelValueBean L5;

		statusColl = new ArrayList();
		L1 = new LabelValueBean(PCMConstant.WAITING_STATUS, "W");
		L2 = new LabelValueBean(PCMConstant.RELEASED_STATUS, "R");
		L3 = new LabelValueBean(PCMConstant.STOPPED_STATUS, "S");
		L4 = new LabelValueBean(PCMConstant.CANCELLED_STATUS, "C");
		L5 = new LabelValueBean(PCMConstant.BLOCKED_STATUS, "B");

		statusColl.add(L1);
		statusColl.add(L2);
		statusColl.add(L3);
		statusColl.add(L4);
		statusColl.add(L5);


		log.debug(this.getClass().getName() + " - [getStatusList] - "
				  + "Exit");
		return statusColl;

	}

	/**
	 * Get the blocked list
	 *
	 * @param
	 * @return
	 * @throws SwtException
	 */
	private Collection getBlockedList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getBlockedList] - "
				  + "Entry");

		/* Method's local variable and class instance declaration */
		Collection blockedColl;
		LabelValueBean L1;
		LabelValueBean L2;
		LabelValueBean L3;

		blockedColl = new ArrayList();
		L1 = new LabelValueBean(PCMConstant.BACK_VALUED, "BV");
		L2 = new LabelValueBean(PCMConstant.BLOCKED_INPUT, "BI");
		L3 = new LabelValueBean(PCMConstant.BLOCKED_STOPPED, "BS");

		blockedColl.add(L1);
		blockedColl.add(L2);
		blockedColl.add(L3);

		log.debug(this.getClass().getName() + " - [getBlockedList] - "
				  + "Exit");
		return blockedColl;

	}
	private Collection getCategoryList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getDefaultCategoryList] - "
				  + "Entry");
		CategoryMaintenanceManager categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil.getBean("categoryMaintenanceManager");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection categoryColl;
		Iterator categoryItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		categoryColl = (Collection) categoryMaintenanceManager.getCategoryCombo();

		categoryItr =  categoryColl.iterator();
		categoryColl = new ArrayList();
		L1 = new LabelValueBean("All", "All");
		Category row = null;
		categoryColl.add(L1);
		while (categoryItr.hasNext()) {
			row =(Category) categoryItr.next();
			L2 = new LabelValueBean( row.getCategoryName(), row.getId().getCategoryId());
			categoryColl.add(L2); // None
			index++;

		}

		log.debug(this.getClass().getName() + " - [getCategoryList] - "
				  + "Exit");
		return categoryColl;

	}


	/* Method to collect the account group list
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getAccountGroupCombo(String currency) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Entry");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection accountColl;
		Iterator accountItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		/* Collect the multiplier list from the Cache manager */
		DashboardManager dashboardManager = (DashboardManager) SwtUtil.getBean("dashboardManager");
		if(SwtUtil.isEmptyOrNull(currency))
			currency = "All";
		accountColl = (Collection) dashboardManager.getAccountGrpDetails(currency);

		/* Iterate the multiplierlist */
		accountItr =  accountColl.iterator();
		accountColl = new ArrayList();
		L1 = new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE);
		AccountGroup row = null;
		accountColl.add(L1);
		while (accountItr.hasNext()) {
			row =(AccountGroup) accountItr.next();
			L2 = new LabelValueBean(row.getDescription(),row.getId().getAccGrpId());
			accountColl.add(L2); // None
			index++;

		}
		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Exit");
		return accountColl;

	}
	/* Method to collect the account group list
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private Collection getArchiveList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Entry");
		/* Method's local variable and class instance declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = PCMUtil.getArchiveList();

		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Exit");
		return coll;

	}
	/* Method to collect the account group list
	 *
	 * @param request
	 * @return
	 * @return
	 * @throws SwtException
	 *
	 */

	private String getPartiesCombo(String hostId, String entityId) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Entry");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection parties = null;
		Iterator partiesItr = null;
		LabelValueBean L1;
		LabelValueBean L2;
		/* Collect the multiplier list from the Cache manager */
		PartyManager partyManager = (PartyManager) SwtUtil.getBean("partyManager");
		//parties = (Collection) partyManager.getParties(hostId, entityId);
//		String partyXML = "<select id=\"partiesList\">"+partyManager.getPartiesAsXML(hostId, entityId)+"  </select>";
		String partyXML = partyManager.getPartiesAsXML(hostId, entityId);


		/* Iterate the multiplierlist */
		/*partiesItr =  parties.iterator();
		parties = new ArrayList();
		L1 = new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE);
		AccountGroup row = null;
		parties.add(L1);
		while (accountItr.hasNext()) {
			row =(AccountGroup) accountItr.next();
			L2 = new LabelValueBean(row.getId().getAccGrpId(), row.getDescription());
			accountColl.add(L2); // None
			index++;

		}*/
		log.debug(this.getClass().getName() + " - [getAccountGroupCombo] - "
				  + "Exit");
		return partyXML;

	}

	public String partySearch() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		return getView("partysearch");
	}

	public String partySearchDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String errorMessage = null;
		SystemFormats systemFormats = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;

		try {

			log.debug(this.getClass().getName() + " - [partySearchDisplay] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "PartySearch";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.startElement(PCMConstant.PARTY_SEARCH);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getPartySearchGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(0);
			responseConstructor.formRowEnd();

			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.PARTY_SEARCH);
			request.setAttribute("data", xmlWriter.getData());

			// build XML response
			return getView("data");

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
						   + ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [partySearchDisplay] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
												   + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [partySearchDisplay] - Exit");
		}
		return null;
	}

	public String partySearchResult() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		List<Party> partiesDetails = new ArrayList<Party>();
		String errorMessage = null;
		SystemFormats systemFormats = null;
		String partyId = null;
		String partyName = null;
		String entityId = null;
		String selectedsort = null;
		int maxPage = 0;
		int currentPage = 0;
		int pageSize = 0;
		int totalCount = 0;
		String currPageStr = null;
		ArrayList<Party> partiesCollection = null;
		HashMap<String, Object> totalMap = null;
		try {

			log.debug(this.getClass().getName() + " - [display] - " + "Entry");
			partiesCollection = new ArrayList<Party>();
			currentPage = 1;
			partyId = request.getParameter("partyId");
			partyName = request.getParameter("partyName");
			entityId = request.getParameter("entityId");
			selectedsort = request.getParameter("selectedsort");
			entityId = "RABONL2U";
			currPageStr = request.getParameter("currentPage");
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			if (!SwtUtil.isEmptyOrNull(currPageStr)) {
				currentPage = Integer.parseInt(currPageStr);
			}
			totalCount = paymentSearchManager.getTotalCount(partyName, partyId, entityId);
			maxPage = setMaxPageAttribute(totalCount, pageSize);

			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			partiesCollection = paymentSearchManager.getPartySearchResult(partyId, partyName, entityId, pageSize, currentPage, selectedsort);
			request.setAttribute("spreadProfilesDetailsList", partiesDetails);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// build XML response
			if (maxPage == 0) {
				currentPage = 0;
			}
			return sendDisplayResponse(request, partiesCollection, systemFormats, currentPage, maxPage);

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
						   + ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
												   + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			partiesDetails = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
		}
		return null;
	}

	/**
	 * This method forms the xml for displaying the parties list.
	 *
	 * @param partiesDetails   - passing parties details
	 * @param systemFormats - passing system formats date
	 * @param currentPage
	 * @param maxPage
	 * @return
	 */
	public String sendDisplayResponse(HttpServletRequest request, List<Party> partiesDetails, SystemFormats systemFormats, int currentPage, int maxPage) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;

		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "PartySearch";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.PARTY_SEARCH);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(new PageInfo(maxPage,currentPage));
			// form column details
			responseConstructor.formColumn(getPartySearchGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(partiesDetails.size());
			// Iterating rules definition details
			for (Iterator<Party> it = partiesDetails.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				Party party = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PARTY_ID, party.getId().getPartyId());
				responseConstructor.createRowElement(PCMConstant.PARTY_NAME, party.getPartyName());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.PARTY_SEARCH);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();
			// getExceptionXmlData(ex.getMessage());
			// log exception in database
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();
			// logs exception in data base
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	private List<ColumnInfo> getPartySearchGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {

		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		// Hash map to hold column hidden_Columns
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		// Array list to hold hidden Column array
		ArrayList<String> lstHiddenColunms = null;
		// String array variable to hold hidden columns property
		String[] hiddenColumnsProp = null;

		try {
			log.debug(this.getClass().getName() + " - [ getPartySearchGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PARTY_ID + "=200," + PCMConstant.PARTY_NAME + "=300";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PARTY_ID + "," + PCMConstant.PARTY_NAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Party Id
				if (order.equals(PCMConstant.PARTY_ID))
					lstColumns.add(new ColumnInfo(PCMConstant.PARTY_ID_COLUMN_HEADER, PCMConstant.PARTY_ID,
							PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get(PCMConstant.PARTY_ID)),
							false, true, hiddenColumnsMap.get(PCMConstant.PARTY_ID)));

				// Party Name
				if (order.equals(PCMConstant.PARTY_NAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PARTY_NAME_COLUMN_HEADER, PCMConstant.PARTY_NAME,
							PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get(PCMConstant.PARTY_NAME)), false,
							false, hiddenColumnsMap.get(PCMConstant.PARTY_NAME)));
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getPartySearchGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/**
	 * This function is used to sets maximum Page The maximum page count is done
	 * by calculation ie. The total record value got from the DB and the
	 * pageSize is got from the properties file.
	 *
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				  + "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage;
		int remainder;
		maxPage = (totalCount) / (pageSize);
		remainder = totalCount % pageSize;
		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName()
				  + "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}

}
