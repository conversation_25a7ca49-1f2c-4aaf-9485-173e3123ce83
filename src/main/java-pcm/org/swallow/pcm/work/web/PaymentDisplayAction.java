/*
 * @(#)PaymentDisplayAction.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.web;

import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;
import org.swallow.pcm.maintenance.service.SpreadProfilesMaintenanceManager;
import org.swallow.pcm.maintenance.web.CurrencyMaintenanceAction;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.pcm.work.model.MessagesOutput;
import org.swallow.pcm.work.model.PaymentLog;
import org.swallow.pcm.work.model.PaymentMessage;
import org.swallow.pcm.work.model.PaymentRequest;
import org.swallow.pcm.work.model.PaymentRequestInfo;
import org.swallow.pcm.work.model.PaymentStop;
import org.swallow.pcm.work.service.DashboardManager;
import org.swallow.pcm.work.service.PaymentDisplayManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/paymentDisplayPCM", "/paymentDisplayPCM.do"})
public class PaymentDisplayAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("paymentDisplay", "jsp/pc/work/paymentdisplay");
		viewMap.put("fail", "error");
		viewMap.put("messageList", "jsp/pc/work/messageList");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/pc/work/paymentdisplay");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("stopRulesList", "jsp/pc/work/stopRulesList");
		viewMap.put("logs", "jsp/pc/work/logs");
		viewMap.put("messageOut", "jsp/pc/work/messageOut");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "messageOut":
				return messageOut();
			case "messageList":
				return messageList();
			case "stopRulesList":
				return stopRulesList();
			case "logs":
				return logs();
			case "getMessageList":
				return getMessageList();
			case "getResonsesList":
				return getResonsesList();
			case "getStopRulesList":
				return getStopRulesList();
			case "logsDisplay":
				return logsDisplay();
			case "unStopReleasePayment":
				return unStopReleasePayment();
			case "view":
				return view();
			case "checkBlockedStoppedPay":
				return checkBlockedStoppedPay();
			case "paymentDisplay":
				return paymentDisplay();
		}


		return unspecified();
	}




	@Autowired
	private PaymentDisplayManager paymentDisplayManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(PaymentDisplayAction.class);

	public void setPaymentDisplayManager(PaymentDisplayManager paymentDisplayManager) {
		this.paymentDisplayManager = paymentDisplayManager;
	}

	protected String unspecified() throws SwtException {
		return getView("success");
	}


	public String messageOut() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("method", "messageOut");
		return getView("messageOut");
	}
	public String messageList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("method", "messageList");
		return getView("messageList");
	}
	public String stopRulesList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("method", "stopRulesList");
		return getView("stopRulesList");
	}
	public String logs() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("method", "logs");
		return getView("logs");
	}

	/**
	 * This method gets currency maintenance screen display it.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getMessageList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To build error message and log the same
		String errorMessage = null;
		PCMCurrencyDetailsVO currencyDetailsVO;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<PaymentMessage> paymentMessages = null;
		int payReqId = 0;
		String payReqIdAsString = null;
//71
		try {

			payReqIdAsString = (String) request.getParameter("payReqId");
			try {
				if (!SwtUtil.isEmptyOrNull(payReqIdAsString)) {
					payReqId = Integer.parseInt(payReqIdAsString);
				}
			} catch (Exception e) {
			}

			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			paymentMessages = new ArrayList<PaymentMessage>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
			paymentMessages = (List<PaymentMessage>) paymentDisplayManager.getPaymentMessages(payReqId);

			// build XML response
			return sendDisplayResponseMessage(request,  paymentMessages, languageId, systemFormats);

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();
			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}

	/**
	 * This method gets currency maintenance screen display it.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getResonsesList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To build error message and log the same
		String errorMessage = null;
		PCMCurrencyDetailsVO currencyDetailsVO;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<MessagesOutput> messagesOutputs = null;
		int payReqId = 0;
		String payReqIdAsString = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");

			payReqIdAsString = (String) request.getParameter("payReqId");
			try {
				if (!SwtUtil.isEmptyOrNull(payReqIdAsString)) {
					payReqId = Integer.parseInt(payReqIdAsString);
				}
			} catch (Exception e) {
			}

			// Initializing array list to hold rules details
			messagesOutputs = new ArrayList<MessagesOutput>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
			messagesOutputs = (List<MessagesOutput>) paymentDisplayManager.getPaymentResponses(payReqId);

			// build XML response
			return sendDisplayResponseMessageOut(request,  messagesOutputs, languageId, systemFormats);

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
		} catch (Exception ex) {

			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();
			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}

	/**
	 * This method gets currency maintenance screen display it.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getStopRulesList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To build error message and log the same
		String errorMessage = null;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<PaymentStop> stopRules = null;
		int payReqId = 0;
		String payReqIdAsString = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");

			payReqIdAsString = (String) request.getParameter("payReqId");
			try {
				if (!SwtUtil.isEmptyOrNull(payReqIdAsString)) {
					payReqId = Integer.parseInt(payReqIdAsString);
				}
			} catch (Exception e) {
			}

			// Initializing array list to hold rules details
			stopRules = new ArrayList<PaymentStop>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
			stopRules = (List<PaymentStop>) paymentDisplayManager.getStopRulesList(payReqId);

			// build XML response
			return sendDisplayResponseStopRules(request,  stopRules, languageId, systemFormats);

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();
			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}

	/**
	 * This method gets currency maintenance screen display it.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String logsDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To build error message and log the same
		String errorMessage = null;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<PaymentLog> logs = null;
		int payReqId = 0;
		String payReqIdAsString = null;


		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			logs = new ArrayList<PaymentLog>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();

			payReqIdAsString = (String) request.getParameter("payReqId");

			try {
				if (!SwtUtil.isEmptyOrNull(payReqIdAsString)) {
					payReqId = Integer.parseInt(payReqIdAsString);
				}
			} catch (Exception e) {
			}



			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
			logs = (List<PaymentLog>) paymentDisplayManager.getPayementRequestLogs(payReqId);

			// build XML response
			return sendDisplayResponseLogs(request,  logs, languageId, systemFormats, payReqId);

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();
			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}

	/**
	 * This method forms the xml for displaying the currency list.
	 *
	 * @param languageId      - passing languageId
	 * @param systemFormats   - passing system formats date
	 * @return
	 */
	public String sendDisplayResponseLogs(HttpServletRequest request,
										  List<PaymentLog> logs, String languageId, SystemFormats systemFormats, int payReqId) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "messageSummary";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement(PCMConstant.MESSAGE_OUT);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getLogSummaryColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(logs.size());

			// Iterating currency details
			for (Iterator<PaymentLog> it = logs.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				PaymentLog log = (PaymentLog) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME, "" + log.getLogSeq());
				responseConstructor.createRowElement(PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME, SwtUtil.formatDate(
						log.getLogDate(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createRowElement(PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME,
						"" + log.getLogUser());
				responseConstructor.createRowElement(PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME,
						"" + log.getLogDetails());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			responseConstructor.createElement(SwtConstants.DATE_FORMAT,
					SwtUtil.getCurrentDateFormat(request.getSession()));
			responseConstructor.createElement(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_PAY_REQ_TAGNAME, payReqId);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.MESSAGE_OUT);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					  + exp.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	/**
	 * This method forms the xml for displaying the currency list.
	 *
	 * @param languageId      - passing languageId
	 * @param systemFormats   - passing system formats date
	 * @return
	 */
	public String sendDisplayResponseMessageOut(HttpServletRequest request,
												List<MessagesOutput> messagesOutputs, String languageId, SystemFormats systemFormats) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "messageSummary";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement(PCMConstant.MESSAGE_OUT);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getMessageSummaryColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(messagesOutputs.size());

			// Iterating currency details
			for (Iterator<MessagesOutput> it = messagesOutputs.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				MessagesOutput message = (MessagesOutput) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME,
						SwtUtil.formatDate(message.getSentDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME,
						message.getSourceId());
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME,
						"" + message.getMessageOutId());
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME,
						"" + message.getMessageArcId());
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME,
						message.getResponsesStatus());
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_BODY_TAGNAME,
						message.getMessageBody());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.MESSAGE_OUT);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					  + exp.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	/**
	 * This method forms the xml for displaying the currency list.
	 *
	 * @param languageId      - passing languageId
	 * @param systemFormats   - passing system formats date
	 * @return
	 */
	public String sendDisplayResponseMessage(HttpServletRequest request,
											 List<PaymentMessage> messages, String languageId, SystemFormats systemFormats) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "messageSummary";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement(PCMConstant.MESSAGE_OUT);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getMessageSummaryColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(messages.size());

			// Iterating currency details
			for (Iterator<PaymentMessage> it = messages.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				PaymentMessage message = (PaymentMessage) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME,
						SwtUtil.formatDate(message.getInputDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME,
						"" + message.getMessageId());
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME,
						message.getMessageStatus());
				responseConstructor.createRowElement(PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_BODY_TAGNAME,
						message.getMessageBody());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.MESSAGE_OUT);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					  + exp.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	/**
	 * This method forms the xml for displaying the currency list.
	 *
	 * @return
	 */
	public String sendDisplayResponseStopRules(HttpServletRequest request,
											   List<PaymentStop> paymentStopList, String languageId, SystemFormats systemFormats) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "stopSummary";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement(PCMConstant.PR_STOP_RULES);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getStopRulesColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(paymentStopList.size());

			// Iterating currency details
			for (Iterator<PaymentStop> it = paymentStopList.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				PaymentStop stop = (PaymentStop) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME,
						stop.getId().getStopRuleId());
				responseConstructor.createRowElement(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME,
						stop.getStopRule() != null ? stop.getStopRule().getStopReasonText():"");
				responseConstructor.createRowElement(PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME, SwtUtil.formatDate(
						stop.getStopDate(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createRowElement(PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME,
						SwtUtil.formatDate(stop.getUnstopDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createRowElement(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME, stop.getUnstopBy());
				responseConstructor.createRowElement(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_PAY_REQ_TAGNAME,
						stop.getUnstopBy());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.PR_STOP_RULES);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException exp) {
			exp.printStackTrace();
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					  + exp.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	/**
	 * This method unstop or release the payment request.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unStopReleasePayment() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String location = null;
		String paymentReqId = null;
		String paymentAction = null;
		String previousStatus = null;
		String userId= null;
		Collection resultUnStopRelease =null ;
		Iterator iterator;
		PaymentRequest paymentRequest = null;
		List<PaymentRequest> paymentRequests = new ArrayList<PaymentRequest>();
		List listOfStatusCode = new ArrayList();
		LabelValueBean row = new LabelValueBean("", "");
		int index = 0;
		int payId;
		String returnCode = null;

		try {
			log.debug(this.getClass().getName() + "- [unStopReleasePayment] - Entry");
			location = request.getParameter("location");
			if(location.equals("L") ) {
				paymentRequest = new PaymentRequest();
				paymentReqId = request.getParameter("paymentId");
				paymentAction = request.getParameter("paymentAction");
				previousStatus = request.getParameter("previousStatus");
				if(!SwtUtil.isEmptyOrNull(previousStatus) && !SwtUtil.isEmptyOrNull(paymentReqId)) {
					userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
					ArrayList paymentIdList= new ArrayList(Arrays.asList(paymentReqId.split(",")));
					ArrayList statusList= new ArrayList(Arrays.asList(previousStatus.split(",")));
					DashboardManager dashboardManager = (DashboardManager) SwtUtil.getBean("dashboardManager");
					resultUnStopRelease = dashboardManager.unStopReleasePayment(paymentIdList,statusList, paymentAction, userId);
					request.setAttribute("reply_status_ok", "true");
					request.setAttribute("reply_message", "SUCCESS");
				}else {
					request.setAttribute("reply_status_ok", "false");
					request.setAttribute("reply_message", "INCORRECT_PREVIOUS_STATUS");
				}

			}else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message", "ARCHIVE_LOCATION");
			}
		} catch (SwtException swtExp) {
			// log error message
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"ERROR_PAYMENT_ENGINE");
			}else {
				request.setAttribute("reply_message",
						"GENERIC_ERROR");
			}


		} finally {
			log.debug(this.getClass().getName() + "- [unStopReleasePayment] - Exit");
		}
		return getView("statechange");
	}



	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getMessageSummaryColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME + "=150,"
						+ PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME + "=130,"
						+ PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME + "=120";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME + ","
							  + PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME + ","
							  + PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// CURRENCY CODE type column
				if (order.equals(PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_HEADING,
							PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 0,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME)));

				// Ordinal column
				if (order.equals(PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_HEADING,
							PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME, PCMConstant.COLUMN_TYPE_NUMBER, 2,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME)));

				// MULTIPLIER column
				if (order.equals(PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_SUMMARY_STATUS_HEADING,
							PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_SUMMARY_STATUS_TAGNAME)));

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getCurrencyMaintenanceGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCurrencyMaintenanceGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getLogSummaryColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME + "=130,"
						+ PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME + "=150,"
						+ PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME + "=130,"
						+ PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME + "=200";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME + ","
							  + PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME + ","
							  + PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME + ","
							  + PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				if (order.equals(PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_HEADING,
							PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME, PCMConstant.COLUMN_TYPE_NUMBER, 0,
							Integer.parseInt(widths.get(PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_LOG_SUMMARY_LOG_SEQ_TAGNAME)));

				// CURRENCY CODE type column
				if (order.equals(PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_LOG_SUMMARY_LOG_DATE_HEADING,
							PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 1,
							Integer.parseInt(widths.get(PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.PR_LOG_SUMMARY_LOG_DATE_TAGNAME)));

				// Ordinal column
				if (order.equals(PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_LOG_SUMMARY_LOG_USER_HEADING,
							PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME, PCMConstant.COLUMN_TYPE_NUMBER, 2,
							Integer.parseInt(widths.get(PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_LOG_SUMMARY_LOG_USER_TAGNAME)));

				// MULTIPLIER column
				if (order.equals(PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_HEADING,
							PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME)));

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getCurrencyMaintenanceGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCurrencyMaintenanceGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getResponsesSummaryColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME + "=150,"
						+ PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME + "=130,"
						+ PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME + "=130,"
						+ PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME + "=130,"
						+ PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME + "=120";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME + ","
							  + PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME + ","
							  + PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME + ","
							  + PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME + ","
							  + PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// CURRENCY CODE type column
				if (order.equals(PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_HEADING,
							PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 0,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME)));

				// CURRENCY NAME type column
				if (order.equals(PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_HEADING,
							PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME)));

				// Ordinal column
				if (order.equals(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_HEADING,
							PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME, PCMConstant.COLUMN_TYPE_NUMBER, 2,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME)), true,
							true, hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME)));
				if (order.equals(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_HEADING,
							PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME, PCMConstant.COLUMN_TYPE_NUMBER, 2,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME)), true,
							true, hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME)));

				// MULTIPLIER column
				if (order.equals(PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_HEADING,
							PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME)));

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getCurrencyMaintenanceGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCurrencyMaintenanceGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getStopRulesColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME + "=150,"
						+ PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME + "=160,"
						+ PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME + "=160,"
						+ PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME + "=160,"
						+ PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME + "=130";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME + ","
							  + PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME + ","
							  + PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME + ","
							  + PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME + ","
							  + PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// CURRENCY CODE type column
				if (order.equals(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_HEADING,
							PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME)));

				if (order.equals(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_HEADING,
							PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME)), false, true,
							hiddenColumnsMap.get(PCMConstant.PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME)));

				// CURRENCY NAME type column
				if (order.equals(PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_STOP_SUMMARY_STOP_DATE_HEADING,
							PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 2,
							Integer.parseInt(widths.get(PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_STOP_SUMMARY_STOP_DATE_TAGNAME)));

				// Ordinal column
				if (order.equals(PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_HEADING,
							PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 3,
							Integer.parseInt(widths.get(PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME)));

				if (order.equals(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_HEADING,
							PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME)), true, true,
							hiddenColumnsMap.get(PCMConstant.PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME)));

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getCurrencyMaintenanceGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCurrencyMaintenanceGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCurrencyMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * This method gets currency maintenance screen display it.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String view() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String languageId = null;
		SystemFormats systemFormats = null;
		String payReqId = null;
		int payReqIdInt = 0;
		PaymentRequest paymentRequest = null;
		ArrayList<PaymentLog> paymentMessageList = null;
		PaymentLog messageEntry = null;
		String lastAction = "";
		String senderReceiverDetails = "";
		String stopReason = "";
		AccountGroup accountGroup = null;
		String blockReason = "";
		String releaseReason = "";
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		Collection categoryList;
		List<PaymentMessage> paymentMessages = null;
		String selectedTimeFrame = null;
		String cuttOffTime = "";
		String requiredReleaseTime = "";
		String dateFormat = null;
		Integer offset = null;
		Category category =  null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		try {


			log.debug(this.getClass().getName() + " - [add] - " + "Entry");

			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat = SwtUtil.getCurrentDateFormat(request.getSession());

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "paymentRequest";

			payReqId = (String) request.getParameter("payReqId");

			selectedTimeFrame = (String) request.getParameter("selectedTimeFrame");
			try {
				payReqIdInt = Integer.parseInt(payReqId);
			} catch (Exception e) {
			}
			paymentRequest = paymentDisplayManager.getPaymentRequestDetails(payReqIdInt);

			paymentMessageList = (ArrayList<PaymentLog>) paymentDisplayManager.getPayementRequestLogs(payReqIdInt);
			if (paymentMessageList != null && paymentMessageList.size() > 0) {
				messageEntry = paymentMessageList.get(0);
			}
			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.PAYMENT_REQUEST_ROOT_TAG);

			xmlWriter.clearAttribute();
			// xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.DATE_FORMAT,
					SwtUtil.getCurrentDateFormat(request.getSession()));
			responseConstructor.createElement(SwtConstants.HELPURL, "swf/system/help/Help.swf?message=");
			if (paymentRequest != null) {
				responseConstructor.createElement("accountid", paymentRequest.getAccountId());
				if (!SwtUtil.isEmptyOrNull(paymentRequest.getAccountId())) {

					SystemInfo systemInfo = new SystemInfo();
					AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
							.getBean("acctMaintenanceManager");

					AcctMaintenance account = acctMaintenanceManager.getEditableDataDetailList(
							paymentRequest.getEntityId(), SwtUtil.getCurrentHostId(), paymentRequest.getAccountId(),
							systemInfo, SwtUtil.getCurrentSystemFormats(request.getSession()));
					String accountClassAsString = null;
					if (account.getAccttype().equals("C")) {
						accountClassAsString = "Cash";
					} else {
						accountClassAsString = "Custodian";
					}

					responseConstructor.createElement("accounttypeLabel", accountClassAsString);

				} else {
					responseConstructor.createElement("accounttypeLabel", "");

				}
				responseConstructor.createElement("accountwithinstaccount", paymentRequest.getAccountWithInstAccount());
				responseConstructor.createElement("accountwithinstbic", paymentRequest.getAccountWithInstBic());
				responseConstructor.createElement("action", paymentRequest.getAction());
				responseConstructor.createElement("amount",
						paymentRequest.getAmount() != null ? SwtUtil.formatCurrency(paymentRequest.getCurrencyCode(),
								new BigDecimal(paymentRequest.getAmount())) : "");
				responseConstructor.createElement("boreference", paymentRequest.getBoReference());
				responseConstructor.createElement("beneficiarycustaccount", paymentRequest.getBeneficiaryCustAccount());
				responseConstructor.createElement("beneficiarycustbic", paymentRequest.getBeneficiaryCustBic());
				responseConstructor.createElement("beneficiaryinstaccount", paymentRequest.getBeneficiaryInstAccount());
				responseConstructor.createElement("beneficiaryinstbic", paymentRequest.getBeneficiaryInstBic());
				responseConstructor.createElement("currencycode", paymentRequest.getCurrencyCode());
				responseConstructor.createElement("delivererscustodianaccount",
						paymentRequest.getDeliverersCustodianAccount());
				responseConstructor.createElement("delivererscustodianbic", paymentRequest.getDeliverersCustodianBic());
				responseConstructor.createElement("deliveryagentaccount", paymentRequest.getDeliveryAgentAccount());
				responseConstructor.createElement("deliveryagentbic", paymentRequest.getDeliveryAgentBic());
				responseConstructor.createElement("department", paymentRequest.getDepartment());
				responseConstructor.createElement("entityid", paymentRequest.getEntityId());
				responseConstructor.createElement("foreference", paymentRequest.getFoReference());
				responseConstructor.createElement("creationMsgId", paymentRequest.getCreationMsgId());
				responseConstructor.createElement("gpi", paymentRequest.getGpi());

				responseConstructor.createElement("intermediaryaccount", paymentRequest.getIntermediaryAccount());
				responseConstructor.createElement("intermediarybic", paymentRequest.getIntermediaryBic());
				responseConstructor.createElement("messagetype", paymentRequest.getMessageType());
				responseConstructor.createElement("orderingcustaccount", paymentRequest.getOrderingCustAccount());
				responseConstructor.createElement("orderingcustbei", paymentRequest.getOrderingCustBic());
				responseConstructor.createElement("orderinginstaccount", paymentRequest.getOrderingInstAccount());
				responseConstructor.createElement("orderinginstbic", paymentRequest.getOrderingInstBic());
				responseConstructor.createElement("originatingsystem", paymentRequest.getOriginatingSystem());
				responseConstructor.createElement("paymenttype", paymentRequest.getPaymentType());
				responseConstructor.createElement("urgentSpreadable", paymentRequest.getSourceUrgencyIndicator());

				responseConstructor.createElement("placeofsettlementaccount",
						paymentRequest.getPlaceOfSettlementAccount());
				responseConstructor.createElement("placeofsettlementbic", paymentRequest.getPlaceOfSettlementBic());
				responseConstructor.createElement("receiverbic", paymentRequest.getReceiverBic());
				responseConstructor.createElement("receiverscorresaccount", paymentRequest.getReceiversCorresAccount());
				responseConstructor.createElement("receiverscorresbic", paymentRequest.getReceiversCorresBic());
				responseConstructor.createElement("paymentreference", paymentRequest.getPaymentReference());
				responseConstructor.createElement("relatedreference", paymentRequest.getRelatedReference());
				responseConstructor.createElement("selleraccount", paymentRequest.getSellerAccount());

				responseConstructor.createElement("categoryRulelabel", paymentRequest.getCategoryRuleName());
				responseConstructor.createElement("categoryIDlabel", paymentRequest.getCategoryId());

				responseConstructor.createElement("location", paymentRequest.getLocation());
				//Commited and changed to get account group from the payment request
//				accountGroup = getAccountGroupFromAccountId(paymentRequest.getHostId(), paymentRequest.getEntityId(), paymentRequest.getAccountId());
				accountGroup = getAccountGroupById(paymentRequest.getAccountGroupId());
				responseConstructor.createElement("accountGroupLabel", accountGroup != null ?accountGroup.getId().getAccGrpId():"");
				responseConstructor.createElement("accountGroupNameLabel", accountGroup != null ?accountGroup.getDescription():"");
				responseConstructor.createElement("accountGroupSpreadId", accountGroup != null ?accountGroup.getSpreadProfileId():"");

				if(accountGroup != null && !SwtUtil.isEmptyOrNull(accountGroup.getSpreadProfileId())){

					SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil.getBean("spreadProfilesMaintenanceManager");
					SpreadProfile  spreadProfile = spreadProfilesMaintenanceManager.getSpreadProfileDetails(accountGroup.getSpreadProfileId());
					responseConstructor.createElement("accountGroupSpreadName", spreadProfile.getSpreadProfileName());

				}else {
					responseConstructor.createElement("accountGroupSpreadName", "");

				}
				responseConstructor.createElement("accountGroupSpreadName", accountGroup != null ?accountGroup.getDescription():"");



				if (messageEntry != null) {
					String status = paymentRequest.getStatus();
					responseConstructor.createElement("lastActionValue", status);

					if(SwtUtil.isEmptyOrNull(status))
						lastAction= "W";


					if (status.equals("W")) {
						lastAction = PCMConstant.WAITING_STATUS;
					} else if (status.equals("S")){
						lastAction = PCMConstant.STOPPED_STATUS;
					} else if (status.equals("B")) {
						lastAction = PCMConstant.BLOCKED_STATUS;
					}else if (status.equals("C")) {
						lastAction = PCMConstant.CANCELLED_STATUS;
					} else if (status.equals("R")) {
						lastAction = PCMConstant.RELEASED_STATUS;
					}


				} else {
					responseConstructor.createElement("lastActionValue", "");

				}



				String urgentAsString = "";
				if(!SwtUtil.isEmptyOrNull(paymentRequest.getCategoryId())) {

					CategoryMaintenanceManager categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil
							.getBean("categoryMaintenanceManager");

					category  = categoryMaintenanceManager.getCategoryDetailById(paymentRequest.getCategoryId());
					if("U".equals(category.getUrgentSpreadInd())) {
						if(!SwtUtil.isEmptyOrNull(category.getSetReleaseTime())) {
							if("K".equals(category.getSetReleaseTime())) {
								urgentAsString  = "Urgent (Kick-off)";
							}else if("U".equals(category.getSetReleaseTime())) {
								urgentAsString  = "Urgent (From Payment Request)";
							}else if("I".equals(category.getSetReleaseTime())) {
								urgentAsString  = "Urgent (Immediate)";
							}else if("T".equals(category.getSetReleaseTime())) {
								urgentAsString  = "Urgent (Specific time)";
							}
						}
					}else {
						urgentAsString = "Spreadable";
					}

					offset = category.getReleaseValueDateOffset();
					if(offset == null) {
						offset = 0;
					}

				}

				responseConstructor.createElement("sourceurgencyindicator", urgentAsString);

				stopReason = paymentDisplayManager.getStopUnstopReason(payReqIdInt, true, SwtUtil.getCurrentDateFormat(request.getSession()));
				if(SwtUtil.isEmptyOrNull(stopReason))
					stopReason = "";
				else {
					String[] stopReasonAndTimeArray = null;
					stopReasonAndTimeArray = stopReason.split("\\|");
					if(stopReasonAndTimeArray.length == 2) {
						String stoptime = stopReasonAndTimeArray[1];
						stopReason = stopReasonAndTimeArray[0];
						Date parsedDate = SwtUtil.parseDate(stoptime, SwtUtil.getCurrentDateFormat(request.getSession())+" HH:mm:ss");
						paymentRequest.setStopDate(parsedDate);
					}
				}

				if(stopReason.length()>0) {
					responseConstructor.createElement("stoppedUserLabel", "SYSTEM");
				}else {
					responseConstructor.createElement("stoppedUserLabel", "");
				}
				responseConstructor.createElement("stoppedReasonLabel", stopReason);


				long differenceInMinutes  = 0;
				long differenceEntityCcyInMinutes  = 0;
				if("C".equals(selectedTimeFrame)) {
					Date sysDateInCcyTimeframe = PCMUtil.getDateInCcyTimeframe(
							paymentRequest.getEntityId(), paymentRequest.getCurrencyCode(), SwtUtil.getSystemDateFromDB(), false);

					differenceInMinutes = getDifferenceBetweenDateInMinutes(sysDateInCcyTimeframe, SwtUtil.getSystemDateFromDB());



					paymentRequest.setInputDate(applyTimeFrameToDateAsDate(paymentRequest.getInputDate(),(int) differenceInMinutes));
					paymentRequest.setBlockDate(applyTimeFrameToDateAsDate(paymentRequest.getBlockDate(),(int) differenceInMinutes));
					paymentRequest.setUnstopDate(applyTimeFrameToDateAsDate(paymentRequest.getUnstopDate(),(int) differenceInMinutes));
					paymentRequest.setStopDate(applyTimeFrameToDateAsDate(paymentRequest.getStopDate(),(int) differenceInMinutes));
					paymentRequest.setCancelDate(applyTimeFrameToDateAsDate(paymentRequest.getCancelDate(),(int) differenceInMinutes));
					paymentRequest.setReleaseDate(applyTimeFrameToDateAsDate(paymentRequest.getReleaseDate(),(int) differenceInMinutes));

					if(!SwtUtil.isEmptyOrNull(paymentRequest.getCutoffTime())) {
						Date cuttoffDate = SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getValueDate(), dateFormat)
															 +" "+paymentRequest.getCutoffTime(), dateFormat +" HH:mm");

						if(offset>0) {
							cuttoffDate = applyTimeFrameToDateAsDate(cuttoffDate, -(offset * 1440));
						}

						cuttOffTime = SwtUtil.formatDate(cuttoffDate, dateFormat +" HH:mm");
					}else {
						cuttOffTime = "";
					}
					if(!SwtUtil.isEmptyOrNull(paymentRequest.getRequiredReleaseTime())) {
						Date requiredRelease = null;
						if(category != null && category.getUrgentSpreadInd().equals("U") && category.getSetReleaseTime().equals("I") && category.getUseLiqCheck().equals("N")){
							requiredRelease = SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getInputDate(), dateFormat)
																+" "+paymentRequest.getRequiredReleaseTime(), dateFormat +" HH:mm");
						}else {

							requiredRelease = SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getValueDate(), dateFormat)
																+" "+paymentRequest.getRequiredReleaseTime(), dateFormat +" HH:mm");
						}

						if(offset>0) {
							requiredRelease = applyTimeFrameToDateAsDate(requiredRelease, -(offset * 1440));
						}

						requiredReleaseTime = SwtUtil.formatDate(requiredRelease, dateFormat +" HH:mm");
					}else {
						requiredReleaseTime = "";
					}

					if(!SwtUtil.isEmptyOrNull(lastAction)) {
						//TODO
						lastAction += " " + SwtUtil.formatDate(applyTimeFrameToDateAsDate(messageEntry.getLogDate(),(int) differenceInMinutes),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"+ " ")+ " "+messageEntry.getLogUser();

					}


				}else if("E".equals(selectedTimeFrame)) {
					differenceInMinutes = getDifferenceBetweenDateInMinutes(PCMUtil.getDateInEntityOffset(SwtUtil.getUserCurrentEntity(request.getSession()), SwtUtil.getSystemDateFromDB()), SwtUtil.getSystemDateFromDB());


					Date sysDateInCcyTimeframe = PCMUtil.getDateInCcyTimeframe(
							paymentRequest.getEntityId(), paymentRequest.getCurrencyCode(), SwtUtil.getSystemDateFromDB(), true);
					differenceEntityCcyInMinutes = getDifferenceBetweenDateInMinutes(sysDateInCcyTimeframe, SwtUtil.getSystemDateFromDB());

					paymentRequest.setInputDate(applyTimeFrameToDateAsDate(paymentRequest.getInputDate(),(int) differenceInMinutes));
					paymentRequest.setBlockDate(applyTimeFrameToDateAsDate(paymentRequest.getBlockDate(),(int) differenceInMinutes));
					paymentRequest.setUnstopDate(applyTimeFrameToDateAsDate(paymentRequest.getUnstopDate(),(int) differenceInMinutes));
					paymentRequest.setStopDate(applyTimeFrameToDateAsDate(paymentRequest.getStopDate(),(int) differenceInMinutes));
					paymentRequest.setCancelDate(applyTimeFrameToDateAsDate(paymentRequest.getCancelDate(),(int) differenceInMinutes));
					paymentRequest.setReleaseDate(applyTimeFrameToDateAsDate(paymentRequest.getReleaseDate(),(int) differenceInMinutes));

					if(paymentRequest.getCutoffTime() != null) {

						Date cuttOffDateinSystemTimeFrame = applyTimeFrameToDateAsDate(SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getValueDate(), dateFormat)
																										 +" "+paymentRequest.getCutoffTime(),
								dateFormat +" HH:mm") , (int) differenceEntityCcyInMinutes);


						cuttOffTime = applyTimeFrameToDate(cuttOffDateinSystemTimeFrame , (int) differenceInMinutes -(offset * 1440) , dateFormat);
					}

					if(paymentRequest.getRequiredReleaseTime() != null) {
						Date releaseDateinSystemTimeFrame  =  null;
						if(category != null && category.getUrgentSpreadInd().equals("U") && category.getSetReleaseTime().equals("I") && category.getUseLiqCheck().equals("N")){
							releaseDateinSystemTimeFrame = applyTimeFrameToDateAsDate(SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getInputDate(), dateFormat)
																										+" "+paymentRequest.getRequiredReleaseTime(),
									dateFormat +" HH:mm") , (int) differenceEntityCcyInMinutes);
						}else {
							releaseDateinSystemTimeFrame = applyTimeFrameToDateAsDate(SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getValueDate(), dateFormat)
																										+" "+paymentRequest.getRequiredReleaseTime(),
									dateFormat +" HH:mm") , (int) differenceEntityCcyInMinutes);
						}


						requiredReleaseTime = applyTimeFrameToDate(releaseDateinSystemTimeFrame , (int) differenceInMinutes -(offset * 1440) , dateFormat);
					}

					if(!SwtUtil.isEmptyOrNull(lastAction)) {
						//TODO
						lastAction += " " + SwtUtil.formatDate(applyTimeFrameToDateAsDate(messageEntry.getLogDate(),(int) differenceInMinutes),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"+ " ")+ " "+messageEntry.getLogUser();

					}

				}else {

					Date sysDateInCcyTimeframe = PCMUtil.getDateInCcyTimeframe(
							paymentRequest.getEntityId(), paymentRequest.getCurrencyCode(), SwtUtil.getSystemDateFromDB(), true);
					differenceInMinutes = getDifferenceBetweenDateInMinutes(sysDateInCcyTimeframe, SwtUtil.getSystemDateFromDB());



					if(!SwtUtil.isEmptyOrNull(paymentRequest.getCutoffTime())) {
						cuttOffTime = applyTimeFrameToDate(SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getValueDate(), dateFormat)
																			 +" "+paymentRequest.getCutoffTime(),
								dateFormat +" HH:mm") , (int) differenceInMinutes -(offset * 1440), dateFormat);
					}else {
						cuttOffTime = "";
					}

					if(paymentRequest.getRequiredReleaseTime() != null) {
						if(category != null && category.getUrgentSpreadInd().equals("U") && category.getSetReleaseTime().equals("I") && category.getUseLiqCheck().equals("N")){
							requiredReleaseTime = applyTimeFrameToDate(SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getInputDate(), dateFormat)
																						 +" "+paymentRequest.getRequiredReleaseTime(),
									dateFormat +" HH:mm") , (int) differenceInMinutes -(offset * 1440), dateFormat);
						}else {
							requiredReleaseTime = applyTimeFrameToDate(SwtUtil.parseDate(SwtUtil.formatDate(paymentRequest.getValueDate(), dateFormat)
																						 +" "+paymentRequest.getRequiredReleaseTime(),
									dateFormat +" HH:mm") , (int) differenceInMinutes -(offset * 1440), dateFormat);
						}
					}

					if(!SwtUtil.isEmptyOrNull(lastAction)) {
						//TODO
						lastAction += " " + SwtUtil.formatDate(messageEntry.getLogDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"+ " ")+ " "+messageEntry.getLogUser();

					}

				}




				responseConstructor.createElement("cutOffTimeLabel", cuttOffTime);
				responseConstructor.createElement("reqreleasetimeLabel", requiredReleaseTime);


				responseConstructor.createElement("releaseDateLabel",
						SwtUtil.formatDate(paymentRequest.getReleaseDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));

				responseConstructor.createElement("waitingDateLabel", SwtUtil.formatDate(paymentRequest.getInputDate(),
						SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));

				responseConstructor.createElement("cancelledtimeLabel",
						SwtUtil.formatDate(paymentRequest.getCancelDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));


				responseConstructor.createElement("stoppedtimeLabel", SwtUtil.formatDate(paymentRequest.getStopDate(),
						SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));


				responseConstructor.createElement("unstoppedtimeLabel",
						SwtUtil.formatDate(paymentRequest.getUnstopDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createElement("blockedtimeLabel", SwtUtil.formatDate(paymentRequest.getBlockDate(),
						SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createElement("inputdate", SwtUtil.formatDate(paymentRequest.getInputDate(),
						SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));








				if(!SwtUtil.isEmptyOrNull(paymentRequest.getReleaseMethod())){
					if( "G".equals(paymentRequest.getReleaseMethod())) {
						responseConstructor.createElement("releasedBy", paymentRequest.getSourceId());
					}else if( "M".equals(paymentRequest.getReleaseMethod()) || "P".equals(paymentRequest.getReleaseMethod()) || "B".equals(paymentRequest.getReleaseMethod())) {
						responseConstructor.createElement("releasedBy", paymentRequest.getUpdatedBy());
					}else {
						responseConstructor.createElement("releasedBy", "SYSTEM");
					}
				}

				responseConstructor.createElement("releaseMethod", paymentRequest.getReleaseMethod());


				responseConstructor.createElement("foreferenceLabel", paymentRequest.getFoReference());
				responseConstructor.createElement("sourcereferenceLabel", paymentRequest.getSourceReference());


				responseConstructor.createElement("sellerbic", paymentRequest.getSellerBic());
				responseConstructor.createElement("senderbic", paymentRequest.getSenderBic());

				senderReceiverDetails = paymentRequest.getSenderReceiverInfo() != null
						? paymentRequest.getSenderReceiverInfo().replace("\r\n", "<br>")
						: "";
				senderReceiverDetails = "<p style=\"font-weight: normal  \">" + senderReceiverDetails + " </p>";

				responseConstructor.createElement("senderreceiverinfo", senderReceiverDetails);
				responseConstructor.createElement("senderscorresaccount", paymentRequest.getSendersCorresAccount());
				responseConstructor.createElement("senderscorresbic", paymentRequest.getSendersCorresBic());
				responseConstructor.createElement("sourceid", paymentRequest.getSourceId());





				responseConstructor.createElement("entitysubid", paymentRequest.getEntitySubId());
				responseConstructor.createElement("thirdreimbsmntaccount", paymentRequest.getThirdReimbsmntAccount());
				responseConstructor.createElement("thirdreimbsmntbic", paymentRequest.getThirdReimbsmntBic());
				responseConstructor.createElement("valuedate", SwtUtil.formatDate(paymentRequest.getValueDate(),
						SwtUtil.getCurrentDateFormat(request.getSession())));


				Calendar cal1 = Calendar.getInstance();
				Calendar cal2 = Calendar.getInstance();
				cal1.setTime(SwtUtil.removeTime(SwtUtil.getSystemDateFromDB()));
				cal2.setTime(paymentRequest.getValueDate());

				if(cal2.before(cal1)) {
					responseConstructor.createElement("isReleasable", "false");
				}else {
					responseConstructor.createElement("isReleasable", "true");

				}

				if (paymentRequest.getPaymentRequestInfo() == null) {
					paymentRequest.setPaymentRequestInfo(new PaymentRequestInfo());
				}

				responseConstructor.createElement("accountwithinstname",
						paymentRequest.getPaymentRequestInfo().getAccountWithInstName());
				responseConstructor.createElement("beneficiarycustname",
						paymentRequest.getPaymentRequestInfo().getBeneficiaryCustName());
				responseConstructor.createElement("beneficiaryinstname",
						paymentRequest.getPaymentRequestInfo().getBeneficiaryInstName());
				responseConstructor.createElement("delivererscustodianname",
						paymentRequest.getPaymentRequestInfo().getDeliverersCustodianName());
				responseConstructor.createElement("deliveryagentname",
						paymentRequest.getPaymentRequestInfo().getDeliveryAgentName());
				responseConstructor.createElement("intermediaryname",
						paymentRequest.getPaymentRequestInfo().getIntermediaryName());
				responseConstructor.createElement("orderingcustname",
						paymentRequest.getPaymentRequestInfo().getOrderingCustName());
				responseConstructor.createElement("orderinginstname",
						paymentRequest.getPaymentRequestInfo().getOrderingInstName());
				responseConstructor.createElement("placeofsettlementname",
						paymentRequest.getPaymentRequestInfo().getPlaceOfSettlementName());
				responseConstructor.createElement("receiverscorresname",
						paymentRequest.getPaymentRequestInfo().getReceiversCorresName());
				responseConstructor.createElement("sellername", paymentRequest.getPaymentRequestInfo().getSellerName());
				responseConstructor.createElement("senderscorresname",
						paymentRequest.getPaymentRequestInfo().getSendersCorresName());
				responseConstructor.createElement("thirdreimbsmntname",
						paymentRequest.getPaymentRequestInfo().getThirdReimbsmntName());

				//paymentMessages = (List<PaymentMessage>) paymentDisplayManager.getPaymentMessages(payReqIdInt);
				paymentRequest.getPaymentRequestInfo().getPaymentMessage();
				if(!SwtUtil.isEmptyOrNull(paymentRequest.getPaymentRequestInfo().getPaymentMessage())) {
					responseConstructor.createElement("paymentMessage",
							SwtUtil.encode64(paymentRequest.getPaymentRequestInfo().getPaymentMessage()));
				}else {
					responseConstructor.createElement("paymentMessage","");
				}


				if(paymentRequest.getUnstopDate() != null) {

					String unstoppedBy = "";
					String unstopReason = paymentDisplayManager.getStopUnstopReason(payReqIdInt, false, null);
					if(!SwtUtil.isEmptyOrNull(unstopReason)) {
						unstoppedBy = unstopReason.split("\\|")[1];
						unstopReason = unstopReason.split("\\|")[0];
					}

					responseConstructor.createElement("unstoppedUserLabel", unstoppedBy);
					responseConstructor.createElement("unstoppedReasonLabel", unstopReason);

				}else {
					responseConstructor.createElement("unstoppedUserLabel", "");
					responseConstructor.createElement("unstoppedReasonLabel", "");

				}





				if(paymentRequest.getBlockDate() != null && messageEntry != null) {
					responseConstructor.createElement("blockedUserLabel", "SYSTEM");
				}else {
					responseConstructor.createElement("blockedUserLabel", "");
				}
				if(!SwtUtil.isEmptyOrNull(paymentRequest.getBlockReasonCode())) {
					blockReason = PCMUtil.getDictionaryMessageValue(PCMConstant.DIC_CODE_BLOCKED+paymentRequest.getBlockReasonCode(),SwtUtil.getCurrentUser(request.getSession()).getLanguage() , null);
				}
				responseConstructor.createElement("blockedReasonLabel", blockReason);



				if(paymentRequest.getCancelDate() != null && messageEntry != null) {
					responseConstructor.createElement("cancelledUserLabel", "SYSYEM");
				}else {
					responseConstructor.createElement("cancelledUserLabel", "");

				}
				responseConstructor.createElement("cancelledReasonLabel", paymentRequest.getCancellationMsgId());


				responseConstructor.createElement("lastAction", lastAction);

				xmlWriter.endElement(SwtConstants.SINGLETONS);


				lstOptions = new ArrayList<OptionInfo>();
				categoryList = getCategoryList();

				Iterator j = categoryList.iterator();
				LabelValueBean row = null;
				while (j.hasNext()) {
					row = (LabelValueBean) j.next();
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
				}
				lstSelect.add(new SelectInfo(PCMConstant.CATEGORY_LIST, lstOptions));

				responseConstructor.formSelect(lstSelect);


				xmlWriter.endElement(PCMConstant.PAYMENT_REQUEST_ROOT_TAG);

				request.setAttribute("data", xmlWriter.getData());
			} else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message", "INVALID_ID");

				return getView("statechange");
			}
			log.debug(this.getClass().getName() + " - [view] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();

			log.error(this.getClass().getName() + " - Exception Catched in [view] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();

			log.error(this.getClass().getName() + " - Exception Catched in [view] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "view", CurrencyMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}


	private String applyTimeFrameToDate(Date selectedDate, int minutes, String dateFormat) {
		if(selectedDate != null) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(selectedDate);
			cal.add(Calendar.MINUTE, minutes);

			return SwtUtil.formatDate(cal.getTime(), dateFormat +" HH:mm");
		}else {
			return getView("");
		}

	}

	private Date applyTimeFrameToDateAsDate(Date selectedDate, int minutes) {
		if(selectedDate != null) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(selectedDate);
			cal.add(Calendar.MINUTE, minutes);
			return cal.getTime();
		}else {
			return null;
		}

	}


	public int getDifferenceBetweenDateInMinutes(Date firstDate, Date secondDate)  {
		long result = ((firstDate.getTime()/60000) - (secondDate.getTime()/60000));
		return (int) result;

	}
	/**
	 * get pretty view of the xml with removing date format tag and making dates in iso format
	 * @param xml
	 * @return
	 * @throws SwtException
	 */
	public static String getPrettyXML(String xml) throws SwtException{
		String dateForamtAsString = null;
		// Dates are provided by SI on ISO format
		String fromDateAsString = null;
		String toDateAsString = null;
		try {
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setValidating(false);
			DocumentBuilder db = dbf.newDocumentBuilder();
			Document doc = db.parse(new InputSource(new StringReader(xml)));
			if(doc.getElementsByTagName("dateformat").getLength() >0) {
				dateForamtAsString = ((Element)doc.getElementsByTagName("dateformat").item(0)).getTextContent();
				Element dateFormatElement = (Element)doc.getElementsByTagName("dateformat").item(0);
				dateFormatElement.setTextContent("YYYY-MM-DD");

				if(doc.getElementsByTagName("fromdateasstring").getLength() >0) {
					Element fromdateasstring = (Element)doc.getElementsByTagName("fromdateasstring").item(0);
					fromDateAsString = fromdateasstring.getTextContent();
					if(!SwtUtil.isEmptyOrNull(fromDateAsString) && !SysParamsDAOHibernate.keywordQuery.containsKey(fromDateAsString) ) {
						fromDateAsString  = SwtUtil.formatDate(SwtUtil.parseDate(fromDateAsString, dateForamtAsString), "yyyy-MM-dd");
						fromdateasstring.setTextContent(fromDateAsString);
					}
				}

				if(doc.getElementsByTagName("todateasstring").getLength() >0) {
					Element todateasstring = (Element)doc.getElementsByTagName("todateasstring").item(0);
					toDateAsString = todateasstring.getTextContent();
					if(!SwtUtil.isEmptyOrNull(toDateAsString) && !SysParamsDAOHibernate.keywordQuery.containsKey(toDateAsString)) {
						toDateAsString  = SwtUtil.formatDate(SwtUtil.parseDate(toDateAsString, dateForamtAsString), "yyyy-MM-dd");
						todateasstring.setTextContent(toDateAsString);
					}
				}
			}

			Transformer transformer = TransformerFactory.newInstance().newTransformer();
			transformer.setOutputProperty(OutputKeys.INDENT, "yes");
			transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
			transformer.setOutputProperty("omit-xml-declaration", "yes");
			//initialize StreamResult with File object to save to file
			StreamResult result = new StreamResult(new StringWriter());
			DOMSource source = new DOMSource(doc);
			transformer.transform(source, result);
			return result.getWriter().toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return xml;
	}


	/**
	 * Method to bring the account Group POJO using the account id
	 * list to the request attribute
	 *
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private AccountGroup getAccountGroupFromAccountId(String hostId, String entityId,String  accountId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - " + "Entry");
		AccountGroup accountGroupsDetails = null;
		try {
			log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - " + "Entry");
			// Retrieves current system formats
			AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
					.getBean("accountGroupsMaintenanceManager");
			accountGroupsDetails = accountGroupsMaintenanceManager.getAccountGroupFromAccountId(hostId, entityId, accountId);

		} catch (SwtException e) {

		}

		log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - " + "Exit");
		return accountGroupsDetails;
	}
	/**
	 * Method to bring the account Group POJO using the Group Id
	 * list to the request attribute
	 *
	 * @return
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	private AccountGroup getAccountGroupById(String  groupId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountGroupById] - " + "Entry");
		AccountGroup accountGroupsDetails = null;
		try {
			log.debug(this.getClass().getName() + " - [getAccountGroupById] - " + "Entry");
			if(!SwtUtil.isEmptyOrNull(groupId)) {
				AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
						.getBean("accountGroupsMaintenanceManager");
				accountGroupsDetails = accountGroupsMaintenanceManager.getAcctGroupDetail(groupId);
			}

		} catch (SwtException e) {

		}

		log.debug(this.getClass().getName() + " - [getAccountGroupById] - " + "Exit");
		return accountGroupsDetails;
	}

	private Collection getCategoryList() throws SwtException {

		log.debug(this.getClass().getName() + " - [getDefaultCategoryList] - "
				  + "Entry");
		CategoryMaintenanceManager categoryMaintenanceManager = (CategoryMaintenanceManager) SwtUtil.getBean("categoryMaintenanceManager");
		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection categoryColl;
		Iterator categoryItr = null;
		LabelValueBean L1;
		categoryColl = (Collection) categoryMaintenanceManager.getCategoryCombo();

		categoryItr =  categoryColl.iterator();
		categoryColl = new ArrayList();
		L1 = new LabelValueBean("", "");
		Category row = null;
		while (categoryItr.hasNext()) {
			row =(Category) categoryItr.next();
			L1 = new LabelValueBean(row.getId().getCategoryId(), row.getCategoryName());
			categoryColl.add(L1); // None
			index++;

		}

		log.debug(this.getClass().getName() + " - [getCategoryList] - "
				  + "Exit");
		return categoryColl;

	}
	/**
	 * This method is to check if Back valued is stopped or not.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkBlockedStoppedPay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String paymentReqId = null;
		String location = null;
		HashMap<String, String> result = new LinkedHashMap<String, String>();
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [checkBlockedStoppedPay] - Entry");

			location = request.getParameter("location");
			if(location.equals("L") ) {
				paymentReqId = request.getParameter("paymentId");
				DashboardManager dashboardManager = (DashboardManager) SwtUtil.getBean("dashboardManager");
				responseHandler = new ResponseHandler();
				responseConstructor = new SwtResponseConstructor();
				xmlWriter = responseConstructor.getXMLWriter();
				componentId = "payIdStopRule";
				xmlWriter.startElement(componentId);
				xmlWriter.startElement(SwtConstants.SINGLETONS);
				if(!SwtUtil.isEmptyOrNull(paymentReqId)) {
					ArrayList paymentIdList= new ArrayList(Arrays.asList(paymentReqId.split(",")));
					result = dashboardManager.isBlockedStopped(paymentIdList, formatDate);
					String resultMap ="";
					for (Map.Entry<String, String> entry : result.entrySet()) {
						String key = entry.getKey();
						String value = entry.getValue();
						resultMap+=value+";";
						// ...
					}
					if(resultMap.length()>0) {
						resultMap = resultMap.substring(0, resultMap.length()-1);
					}
					responseConstructor.createElement(PCMConstant.PAYID_RULE, resultMap);

				} else {
					responseConstructor.createElement(PCMConstant.PAYID_RULE, "");
				}
				xmlWriter.endElement(SwtConstants.SINGLETONS);
				xmlWriter.endElement(componentId);
				request.setAttribute("data", xmlWriter.getData());
				return getView("data");
			}else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message", "ARCHIVE_LOCATION");
				return getView("statechange");
			}

		} catch (SwtException swtExp) {
			// log error message
			request.setAttribute("reply_status_ok", "false");
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"ERROR_PAYMENT_ENGINE");
			}else {
				request.setAttribute("reply_message",
						"GENERIC_ERROR");
			}


		} finally {
			log.debug(this.getClass().getName() + "- [checkBlockedStoppedPay] - Exit");
		}
		return getView("statechange");
	}
	public String paymentDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

		LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
		MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

		if (menuItem != null)
			menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

		if(menuAccessId !=2) {
			request.setAttribute("method", "paymentDisplay");
			return getView("paymentDisplay");
		}

		return getView("fail");
	}
}
